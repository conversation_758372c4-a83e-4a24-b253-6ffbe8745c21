/* ****************************************************************
 *        CADENCE                    Copyright (c) 2001-2021      *
 *                                   Cadence Design Systems, Inc. *
 *                                   All rights reserved.         *
 ******************************************************************
 *  The values calculated from this script are meant to be        *
 *  representative programmings.   The values may not reflect the *
 *  actual required programming for production use.   Please      *
 *  closely review all programmed values for technical accuracy   *
 *  before use in production parts.                               *
 ******************************************************************
 *
 *   Module:         regconfig.h
 *   Documentation:  Register programming header file
 *
 ******************************************************************
 ******************************************************************
 * WARNING:  This file was automatically generated.  Manual
 * editing may result in undetermined behavior.
 ******************************************************************
 ******************************************************************/
/* REL: texas.dallas.am62a-AM62A_LPDDR4_DDR4_32b_EW__20211118 */



static uint32_t denali_ctl_data[] =
{
	0x00000b00,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000176,
	0x00000004,
	0x00000005,
	0x000000bb,
	0x00000176,
	0x00000004,
	0x00000005,
	0x000000bb,
	0x00000176,
	0x00000004,
	0x00000005,
	0x000000bb,
	0x01010100,
	0x01010000,
	0x01000110,
	0x00010202,
	0x00000176,
	0x000003a5,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00020200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x4b000010,
	0x00004b4b,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00001040,
	0x00000000,
	0x00001040,
	0x00000000,
	0x00001040,
	0x00000000,
	0x0f000804,
	0x00007200,
	0x14140050,
	0x00003a22,
	0x0072000f,
	0x14140050,
	0x00003a22,
	0x0072000f,
	0x14140050,
	0x0f003a22,
	0x001b130f,
	0x0e00ffcd,
	0x0f0f200e,
	0x00001b13,
	0x0e00ffcd,
	0x0f0f200e,
	0x00001b13,
	0x0e00ffcd,
	0x0304200e,
	0x24230002,
	0x24232423,
	0x01010008,
	0x04464646,
	0x28282828,
	0x00002828,
	0x00000101,
	0x00000000,
	0x01000000,
	0x02c50803,
	0x00001c64,
	0x000002c5,
	0x00001c64,
	0x000002c5,
	0x00001c64,
	0x00000005,
	0x00000163,
	0x00000386,
	0x00000163,
	0x00000386,
	0x00000163,
	0x00000386,
	0x03004000,
	0x00001201,
	0x000e000e,
	0x2626000e,
	0x1b0e0a26,
	0x1b0e0a04,
	0x1b0e0a04,
	0x04010404,
	0x00010401,
	0x02d302d3,
	0x02d302d3,
	0x02d302d3,
	0x02d302d3,
	0x041c02d3,
	0x0a0e0a04,
	0x04041c0e,
	0x0e0a0e0a,
	0x0a04041c,
	0x040e0a0e,
	0x00000404,
	0x00000301,
	0x00000001,
	0x00000000,
	0x40020100,
	0x00038010,
	0x00050004,
	0x00000004,
	0x00040003,
	0x00040005,
	0x00030000,
	0x00050004,
	0x00000004,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00000000,
	0x0000c6bc,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00000000,
	0x0000c6bc,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00000000,
	0x0000c6bc,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x040a0000,
	0x040a040a,
	0x00000000,
	0x07010a09,
	0x000e0a09,
	0x010a0900,
	0x0e0a0907,
	0x0a090000,
	0x0a090701,
	0x00012f0e,
	0x00040003,
	0x00000007,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000000,
	0x00000000,
	0x00001700,
	0x0000100e,
	0x00000002,
	0x00000000,
	0x00000001,
	0x00000002,
	0x00000c00,
	0x00008000,
	0x00000c00,
	0x00008000,
	0x00000c00,
	0x00008000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00bb0176,
	0x0e0e01d3,
	0x000001d3,
	0x00bb0176,
	0x0e0e01d3,
	0x000001d3,
	0x00bb0176,
	0x0e0e01d3,
	0x000001d3,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000000,
	0x00000031,
	0x00000031,
	0x00000031,
	0x00000031,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000020,
	0x00010000,
	0x00000100,
	0x00000000,
	0x00000000,
	0x00000101,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0c181511,
	0x00000304,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00020000,
	0x00400100,
	0x0038074a,
	0x01000200,
	0x074a0040,
	0x00020038,
	0x00400100,
	0x0038074a,
	0x005e0000,
	0x005e005e,
	0x00000100,
	0x01010000,
	0x00000101,
	0x1fff0000,
	0x0007ff00,
	0x3fff2000,
	0x0007ff00,
	0x0b000000,
	0x0001ffff,
	0x01010101,
	0x01010101,
	0x00000118,
	0x01000c03,
	0x00040101,
	0x00040100,
	0x00000000,
	0x01010000,
	0x01030303,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000101,
	0x01010001,
	0x00010101,
	0x01090909,
	0x1b020201,
	0x0e0e1b1b,
	0x0b0d020e,
	0x0b0d0206,
	0x0b0d0206,
	0x0c0c0c06,
	0x07000000,
	0x07030703,
	0x04000003,
	0x37000004,
	0x000038c8,
	0x00000200,
	0x00000200,
	0x00000200,
	0x00000200,
	0x0000ff84,
	0x000237d0,
	0x111a0402,
	0x37200c09,
	0x000038c8,
	0x00000200,
	0x00000200,
	0x00000200,
	0x00000200,
	0x0000ff84,
	0x000237d0,
	0x111a0402,
	0x37200c09,
	0x000038c8,
	0x00000200,
	0x00000200,
	0x00000200,
	0x00000200,
	0x0000ff84,
	0x000237d0,
	0x111a0402,
	0x00200c09,
	0x00000000,
	0x02000a00,
	0x01050003,
	0x00010101,
	0x00010101,
	0x00010001,
	0x00000101,
	0x02000201,
	0x02010000,
	0x22000200,
	0x00002222
};
#pragma DATA_SECTION (denali_ctl_data, ".wkupram");



static uint32_t denali_pi_data[] =
{
	0x00000b00,
	0xb0bd6b2a,
	0x00204f40,
	0x01001387,
	0x00000001,
	0x00010064,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000002,
	0x00000007,
	0x000f0001,
	0x08000000,
	0x00010300,
	0x00000005,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x0a000100,
	0x00000028,
	0x0f000000,
	0x00320000,
	0x00000000,
	0x00000000,
	0x01010102,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000001,
	0x000000aa,
	0x00000055,
	0x000000b5,
	0x0000004a,
	0x00000056,
	0x000000a9,
	0x000000a9,
	0x000000b5,
	0x00000000,
	0x00000000,
	0x000f0f00,
	0x0000001a,
	0x000007d0,
	0x00000300,
	0x00000000,
	0x00000000,
	0x01000000,
	0x00010101,
	0x01000000,
	0x03000000,
	0x00000000,
	0x0000170f,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0a0a140a,
	0x10020401,
	0x01000210,
	0x05000404,
	0x00010001,
	0x0001010e,
	0x02040f00,
	0x00010000,
	0x00000034,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000000,
	0x01010001,
	0x02000008,
	0x01000200,
	0x00000100,
	0x02000100,
	0x02000200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000400,
	0x0e0d0f10,
	0x080a0b0c,
	0x01000009,
	0x00010302,
	0x00000008,
	0x08000000,
	0x00000100,
	0x00000000,
	0x0000aa00,
	0x00000000,
	0x00000000,
	0x00010000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00010000,
	0x00000000,
	0x00000000,
	0x00000176,
	0x000003a5,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000000,
	0x00010003,
	0x02000101,
	0x01030001,
	0x00010400,
	0x06000105,
	0x01070001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00010001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00010000,
	0x00000004,
	0x00000000,
	0x00010000,
	0x00000000,
	0x01180000,
	0x01180118,
	0x00262626,
	0x0000005e,
	0x0000005e,
	0x0002005e,
	0x02000200,
	0x00000010,
	0x00001040,
	0x00104000,
	0x00400000,
	0x000002c5,
	0x00001c64,
	0x000002c5,
	0x00001c64,
	0x000002c5,
	0x04001c64,
	0x01010404,
	0x00002801,
	0x00280028,
	0x01000100,
	0x00000100,
	0x00000000,
	0x1b090909,
	0x01011b1b,
	0x01010101,
	0x001b1b1b,
	0x00000000,
	0x00000000,
	0x12000000,
	0x0c0c1212,
	0x0404040c,
	0x001c0044,
	0x001c0044,
	0x001c0044,
	0x03010101,
	0x000301d3,
	0x000301d3,
	0x010001d3,
	0x01d401d4,
	0x01d40100,
	0x010001d4,
	0x01d401d4,
	0x1e1a1e1a,
	0x01011e1a,
	0x1c110901,
	0x1c110913,
	0x1c110913,
	0x000c0013,
	0x00001000,
	0x00000c00,
	0x00001000,
	0x00000c00,
	0x21001000,
	0x002101d3,
	0x002101d3,
	0x000001d3,
	0x00003000,
	0x1e1a005a,
	0x17000101,
	0x00300c12,
	0x1e1a005a,
	0x17000101,
	0x00300c12,
	0x1e1a005a,
	0x17000101,
	0x00000c12,
	0x23280f00,
	0x00241400,
	0x0000e638,
	0x20070050,
	0x1b131b1c,
	0x280f0000,
	0x24140023,
	0x0000e638,
	0x20070050,
	0x1b131b1c,
	0x280f0000,
	0x24140023,
	0x0000e638,
	0x20070050,
	0x1b131b1c,
	0x00000000,
	0x000038c8,
	0x000237d0,
	0x000038c8,
	0x000237d0,
	0x000038c8,
	0x000237d0,
	0x02d302d3,
	0x030302d3,
	0x00000003,
	0x00000000,
	0x0a040a04,
	0x00000a04,
	0x00000176,
	0x00000004,
	0x00000005,
	0x000000bb,
	0x000002d3,
	0x00000176,
	0x00000004,
	0x00000005,
	0x000000bb,
	0x000002d3,
	0x00000176,
	0x00000004,
	0x00000005,
	0x000000bb,
	0x010002d3,
	0x074a0040,
	0x00010038,
	0x074a0040,
	0x00010038,
	0x074a0040,
	0x00005e38,
	0x005e005e,
	0x03040404,
	0x01010303,
	0x01010101,
	0x04040202,
	0x67670808,
	0x67676767,
	0x67676767,
	0x67676767,
	0x00006767,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x55000000,
	0x00000000,
	0x3c00005a,
	0x00005500,
	0x00005a00,
	0x0055003c,
	0x00000000,
	0x3c00005a,
	0x00005500,
	0x00005a00,
	0x1716153c,
	0x13121118,
	0x06131414,
	0x02010007,
	0x00000003,
	0x00000000,
	0x00000000,
	0x01000000,
	0x04020201,
	0x00080804,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000031,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00004d4d
};
#pragma DATA_SECTION (denali_pi_data, ".wkupram");


static uint32_t denali_data_slice0[] =
{
	0x04f00000,
	0x00000000,
	0x00030200,
	0x00000000,
	0x00000000,
	0x01030000,
	0x00010000,
	0x01030004,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x00000000,
	0x00c00004,
	0x00cc0008,
	0x00660601,
	0x00000003,
	0x00000000,
	0x00000001,
	0x0000AAAA,
	0x00005555,
	0x0000B5B5,
	0x00004A4A,
	0x00005656,
	0x0000A9A9,
	0x0000B7B7,
	0x00004848,
	0x00000000,
	0x00000000,
	0x08000000,
	0x04000008,
	0x00000408,
	0x00e4e400,
	0x00071020,
	0x000c0020,
	0x00062000,
	0x00000100,
	0x55555555,
	0xAAAAAAAA,
	0x55555555,
	0xAAAAAAAA,
	0x00005555,
	0x01000100,
	0x00800180,
	0x00000001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x081f07ff,
	0x10200080,
	0x00000008,
	0x00000401,
	0x00000000,
	0x01440801,
	0x20034408,
	0x20000125,
	0x07FF0200,
	0x0000dd01,
	0x00000303,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x02030000,
	0x00000002,
	0x51516042,
	0x31c06000,
	0x061f0004,
	0x00c0c001,
	0x0d000000,
	0x000d0c0c,
	0x42100010,
	0x010c073e,
	0x000f0c32,
	0x01000140,
	0x011e0120,
	0x00000c00,
	0x000002dd,
	0x00000200,
	0x02800000,
	0x80800101,
	0x000d2010,
	0x76543210,
	0x00000008,
	0x045d045d,
	0x045d045d,
	0x045d045d,
	0x045d045d,
	0x0000045d,
	0x0000a000,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x006d00a0,
	0x01A00005,
	0x00000000,
	0x00040000,
	0x00080200,
	0x00000000,
	0x20202020,
	0x20202020,
	0x00002020,
	0x00000000
};
#pragma DATA_SECTION (denali_data_slice0, ".wkupram");

static uint32_t denali_data_slice1[] =
{
	0x04f00000,
	0x00000000,
	0x00030200,
	0x00000000,
	0x00000000,
	0x01030000,
	0x00010000,
	0x01030004,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x00000000,
	0x00c00004,
	0x00cc0008,
	0x00660601,
	0x00000003,
	0x00000000,
	0x00000001,
	0x0000AAAA,
	0x00005555,
	0x0000B5B5,
	0x00004A4A,
	0x00005656,
	0x0000A9A9,
	0x0000B7B7,
	0x00004848,
	0x00000000,
	0x00000000,
	0x08000000,
	0x04000008,
	0x00000408,
	0x00e4e400,
	0x00071020,
	0x000c0020,
	0x00062000,
	0x00000100,
	0x55555555,
	0xAAAAAAAA,
	0x55555555,
	0xAAAAAAAA,
	0x00005555,
	0x01000100,
	0x00800180,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x081f07ff,
	0x10200080,
	0x00000008,
	0x00000401,
	0x00000000,
	0x01440801,
	0x20034408,
	0x20000125,
	0x07FF0200,
	0x0000dd01,
	0x00000303,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x02030000,
	0x00000002,
	0x51516042,
	0x31c06000,
	0x061f0004,
	0x00c0c001,
	0x0d000000,
	0x000d0c0c,
	0x42100010,
	0x010c073e,
	0x000f0c32,
	0x01000140,
	0x011e0120,
	0x00000c00,
	0x000002dd,
	0x00000200,
	0x02800000,
	0x80800101,
	0x000d2010,
	0x76543210,
	0x00000008,
	0x045d045d,
	0x045d045d,
	0x045d045d,
	0x045d045d,
	0x0000045d,
	0x0000a000,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x006d00a0,
	0x01A00005,
	0x00000000,
	0x00040000,
	0x00080200,
	0x00000000,
	0x20202020,
	0x20202020,
	0x00002020,
	0x00000000
};
#pragma DATA_SECTION (denali_data_slice1, ".wkupram");

static uint32_t denali_data_slice2[] =
{
	0x04f00000,
	0x00000000,
	0x00030200,
	0x00000000,
	0x00000000,
	0x01030000,
	0x00010000,
	0x01030004,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x00000000,
	0x00c00004,
	0x00cc0008,
	0x00660601,
	0x00000003,
	0x00000000,
	0x00000001,
	0x0000AAAA,
	0x00005555,
	0x0000B5B5,
	0x00004A4A,
	0x00005656,
	0x0000A9A9,
	0x0000B7B7,
	0x00004848,
	0x00000000,
	0x00000000,
	0x08000000,
	0x04000008,
	0x00000408,
	0x00e4e400,
	0x00071020,
	0x000c0020,
	0x00062000,
	0x00000100,
	0x55555555,
	0xAAAAAAAA,
	0x55555555,
	0xAAAAAAAA,
	0x00005555,
	0x01000100,
	0x00800180,
	0x00000001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x081f07ff,
	0x10200080,
	0x00000008,
	0x00000401,
	0x00000000,
	0x01440801,
	0x20034408,
	0x20000125,
	0x07FF0200,
	0x0000dd01,
	0x00000303,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x02030000,
	0x00000002,
	0x51516042,
	0x31c06000,
	0x061f0004,
	0x00c0c001,
	0x0d000000,
	0x000d0c0c,
	0x42100010,
	0x010c073e,
	0x000f0c32,
	0x01000140,
	0x011e0120,
	0x00000c00,
	0x000002dd,
	0x00000200,
	0x02800000,
	0x80800101,
	0x000d2010,
	0x76543210,
	0x00000008,
	0x045d045d,
	0x045d045d,
	0x045d045d,
	0x045d045d,
	0x0000045d,
	0x0000a000,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x006d00a0,
	0x01A00005,
	0x00000000,
	0x00040000,
	0x00080200,
	0x00000000,
	0x20202020,
	0x20202020,
	0x00002020,
	0x00000000
};
#pragma DATA_SECTION (denali_data_slice2, ".wkupram");

static uint32_t denali_data_slice3[] =
{
	0x04f00000,
	0x00000000,
	0x00030200,
	0x00000000,
	0x00000000,
	0x01030000,
	0x00010000,
	0x01030004,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x00000000,
	0x00c00004,
	0x00cc0008,
	0x00660601,
	0x00000003,
	0x00000000,
	0x00000001,
	0x0000AAAA,
	0x00005555,
	0x0000B5B5,
	0x00004A4A,
	0x00005656,
	0x0000A9A9,
	0x0000B7B7,
	0x00004848,
	0x00000000,
	0x00000000,
	0x08000000,
	0x04000008,
	0x00000408,
	0x00e4e400,
	0x00071020,
	0x000c0020,
	0x00062000,
	0x00000100,
	0x55555555,
	0xAAAAAAAA,
	0x55555555,
	0xAAAAAAAA,
	0x00005555,
	0x01000100,
	0x00800180,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x081f07ff,
	0x10200080,
	0x00000008,
	0x00000401,
	0x00000000,
	0x01440801,
	0x20034408,
	0x20000125,
	0x07FF0200,
	0x0000dd01,
	0x00000303,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x02030000,
	0x00000002,
	0x51516042,
	0x31c06000,
	0x061f0004,
	0x00c0c001,
	0x0d000000,
	0x000d0c0c,
	0x42100010,
	0x010c073e,
	0x000f0c32,
	0x01000140,
	0x011e0120,
	0x00000c00,
	0x000002dd,
	0x00000200,
	0x02800000,
	0x80800101,
	0x000d2010,
	0x76543210,
	0x00000008,
	0x045d045d,
	0x045d045d,
	0x045d045d,
	0x045d045d,
	0x0000045d,
	0x0000a000,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x00a000a0,
	0x006d00a0,
	0x01A00005,
	0x00000000,
	0x00040000,
	0x00080200,
	0x00000000,
	0x20202020,
	0x20202020,
	0x00002020,
	0x00000000
};
#pragma DATA_SECTION (denali_data_slice3, ".wkupram");

static uint32_t denali_addr_slice0[] =
{
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000100,
	0x00000200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00400000,
	0x00000080,
	0x00dcba98,
	0x03000000,
	0x00200000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0000002a,
	0x00000015,
	0x00000015,
	0x0000002a,
	0x00000033,
	0x0000000c,
	0x0000000c,
	0x00000033,
	0x0a418820,
	0x003f0000,
	0x000f013f,
	0x20202000,
	0x00202020,
	0x20008008,
	0x00000810,
	0x00000f00,
	0x00000244,
	0x03000003,
	0x00030000,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x42080010,
	0x0000803e,
	0x00000001,
	0x01000102,
	0x00008001
};
#pragma DATA_SECTION (denali_addr_slice0, ".wkupram");

static uint32_t denali_addr_slice1[] =
{
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000100,
	0x00000200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00400000,
	0x00000080,
	0x00dcba98,
	0x03000000,
	0x00200000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0000002a,
	0x00000015,
	0x00000015,
	0x0000002a,
	0x00000033,
	0x0000000c,
	0x0000000c,
	0x00000033,
	0x0a418820,
	0x00000000,
	0x000f0000,
	0x20202000,
	0x00202020,
	0x20008008,
	0x00000810,
	0x00000f00,
	0x00000244,
	0x03000003,
	0x00030000,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x42080010,
	0x0000803e,
	0x00000001,
	0x01000102,
	0x00008001
};
#pragma DATA_SECTION (denali_addr_slice1, ".wkupram");

static uint32_t denali_addr_slice2[] =
{
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000100,
	0x00000200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00400000,
	0x00000080,
	0x00dcba98,
	0x03000000,
	0x00200000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0000002a,
	0x00000015,
	0x00000015,
	0x0000002a,
	0x00000033,
	0x0000000c,
	0x0000000c,
	0x00000033,
	0x0a418820,
	0x10000000,
	0x000f0000,
	0x20202000,
	0x00202020,
	0x20008008,
	0x00000810,
	0x00000f00,
	0x00000244,
	0x03000003,
	0x00030000,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x42080010,
	0x0000803e,
	0x00000001,
	0x01000102,
	0x00008001
};
#pragma DATA_SECTION (denali_addr_slice2, ".wkupram");

static uint32_t denali_phy_data[] =
{
	0x00000000,
	0x00000100,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00050000,
	0x04000100,
	0x00000055,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00002001,
	0x0000400f,
	0x50020028,
	0x01010000,
	0x80080001,
	0x10200000,
	0x00000008,
	0x00000000,
	0x0f000000,
	0x010f080f,
	0x00040101,
	0x0000010f,
	0x00000000,
	0x00000064,
	0x00000000,
	0x00000000,
	0x0f0f0801,
	0x0f0f0402,
	0x0f0f0201,
	0x0f0f0f0f,
	0x00800020,
	0x00041b42,
	0x00005201,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x05010101,
	0x00540107,
	0x000040a2,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00000000,
	0x00000064,
	0x00000000,
	0x00000208,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x03000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x04102035,
	0x00041020,
	0x01c98c98,
	0x3f400000,
	0x3f3f1f3f,
	0x1f3f3f1f,
	0x001f3f3f,
	0x00000000,
	0x00000000,
	0x00000001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x76543210,
	0x06010198,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00040700,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000002,
	0x00000000,
	0x00000000,
	0x00000000,
	0x03000000,
	0x00000000,
	0x00001142,
	0x01020600,
	0x01000080,
	0x03000300,
	0x03000300,
	0x03000300,
	0x03000300,
	0x03000300,
	0x03000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000002,
	0x00030044,
	0x00030008,
	0x00000180,
	0x00000180,
	0x00018011,
	0x0089ff00,
	0x00018044,
	0x00004011,
	0x00018011,
	0x0089ff00,
	0x00018011,
	0x0089ff00,
	0x00018011,
	0x0089ff00,
	0x00018011,
	0x0089ff00,
	0x00018011,
	0x0089ff00,
	0x20040003
};
#pragma DATA_SECTION (denali_phy_data, ".wkupram");
