%%{
    let module = system.modules['/drivers/watchdog/watchdog'];
%%}
/*
 * watchdog
 */
/* watchdog atrributes */
static Watchdog_HwAttrs gWatchdogHwAttrs[CONFIG_WATCHDOG_NUM_INSTANCES] =
{
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    {
        .instance        = `config.wdtInstance`,
        .baseAddr        = `config.baseAddr`,
        .wdtClkFrequency = `config.funcClk`U,
    },
% }
};
/* Watchdog objects - initialized by the driver */
Watchdog_MCB gWatchdogObjects[CONFIG_WATCHDOG_NUM_INSTANCES];

/* Watchdog driver configuration */
Watchdog_Config gWatchdogConfig[CONFIG_WATCHDOG_NUM_INSTANCES] =
{
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    {
        .object = &gWatchdogObjects[`instance.$name.toUpperCase()`],
        .hwAttrs = &gWatchdogHwAttrs[`instance.$name.toUpperCase()`]
    },
% }
};

uint32_t gWatchdogConfigNum = CONFIG_WATCHDOG_NUM_INSTANCES;
