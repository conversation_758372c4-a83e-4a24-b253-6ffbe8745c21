/*
 * Data version: 230918_161319
 *
 * Copyright (C) 2017-2024 Texas Instruments Incorporated - http://www.ti.com/
 * ALL RIGHTS RESERVED
 */
#include <types/array_size.h>
#include <stddef.h>
#include <device_clk.h>
#include <config.h>
#include <resource.h>
#include <clk.h>
#include <device.h>
#include <build_assert.h>
#include <soc/device.h>
#include <soc/am62x/clk_ids.h>
#include <soc/am62x/clocks.h>
#include <soc/am62x/devices.h>
#include <soc/am62x/regs.h>
#include <soc/am62x/control.h>
#include <psc.h>

BUILD_ASSERT_GLOBAL(sizeof(dev_idx_t) == (size_t) 1, dev_idx_t_is_16bit);

#define AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0 0
#define AM62X_PSC_PD_GP_CORE_CTL_MCU 0
#define AM62X_PSC_PD_PD_M4F 1
#define AM62X_PSC_LPSC_LPSC_MCU_ALWAYSON 0
#define AM62X_PSC_LPSC_LPSC_MAIN2MCU_ISO 1
#define AM62X_PSC_LPSC_LPSC_MCU2MAIN_ISO 2
#define AM62X_PSC_LPSC_LPSC_DM2SAFE_ISO 3
#define AM62X_PSC_LPSC_LPSC_MCU2DM_ISO 4
#define AM62X_PSC_LPSC_LPSC_MCU_TEST 5
#define AM62X_PSC_LPSC_LPSC_MCU_M4F 6
#define AM62X_PSC_LPSC_LPSC_MCU_MCANSS_0 7
#define AM62X_PSC_LPSC_LPSC_MCU_MCANSS_1 8
#define AM62X_PSC_LPSC_LPSC_MCU_COMMON 9
#define AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0 1
#define AM62X_PSC_PD_GP_CORE_CTL 0
#define AM62X_PSC_PD_PD_ICSSM 1
#define AM62X_PSC_PD_PD_CPSW 2
#define AM62X_PSC_PD_PD_A53_CLUSTER_0 3
#define AM62X_PSC_PD_PD_A53_0 4
#define AM62X_PSC_PD_PD_A53_1 5
#define AM62X_PSC_PD_PD_A53_2 6
#define AM62X_PSC_PD_PD_A53_3 7
#define AM62X_PSC_PD_PD_GPU 8
#define AM62X_PSC_PD_PD_DSS 9
#define AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON 0
#define AM62X_PSC_LPSC_LPSC_MAIN_DM 1
#define AM62X_PSC_LPSC_LPSC_DM_PBIST 2
#define AM62X_PSC_LPSC_LPSC_MAIN2DM_ISO 3
#define AM62X_PSC_LPSC_LPSC_DM2MAIN_ISO 4
#define AM62X_PSC_LPSC_LPSC_DM2MAIN_INFRA_ISO 5
#define AM62X_PSC_LPSC_LPSC_DM2CENTRAL_ISO 6
#define AM62X_PSC_LPSC_LPSC_CENTRAL2DM_ISO 7
#define AM62X_PSC_LPSC_LPSC_GP_SPARE0 8
#define AM62X_PSC_LPSC_LPSC_EMIF_LOCAL 9
#define AM62X_PSC_LPSC_LPSC_EMIF_CFG_ISO 10
#define AM62X_PSC_LPSC_LPSC_EMIF_DATA_ISO 11
#define AM62X_PSC_LPSC_LPSC_MAIN_USB0_ISO 12
#define AM62X_PSC_LPSC_LPSC_MAIN_USB1_ISO 13
#define AM62X_PSC_LPSC_LPSC_MAIN_TEST 14
#define AM62X_PSC_LPSC_LPSC_GPMC 15
#define AM62X_PSC_LPSC_LPSC_GP_SPARE1 16
#define AM62X_PSC_LPSC_LPSC_MAIN_MCASP_0 17
#define AM62X_PSC_LPSC_LPSC_MAIN_MCASP_1 18
#define AM62X_PSC_LPSC_LPSC_MAIN_MCASP_2 19
#define AM62X_PSC_LPSC_LPSC_EMMC_8B 20
#define AM62X_PSC_LPSC_LPSC_EMMC_4B_0 21
#define AM62X_PSC_LPSC_LPSC_EMMC_4B_1 22
#define AM62X_PSC_LPSC_LPSC_USB_0 23
#define AM62X_PSC_LPSC_LPSC_USB_1 24
#define AM62X_PSC_LPSC_LPSC_CSI_RX_0 25
#define AM62X_PSC_LPSC_LPSC_DPHY_0 26
#define AM62X_PSC_LPSC_LPSC_SMS_COMMON 27
#define AM62X_PSC_LPSC_LPSC_FSS_OSPI 28
#define AM62X_PSC_LPSC_LPSC_TIFS 29
#define AM62X_PSC_LPSC_LPSC_HSM 30
#define AM62X_PSC_LPSC_LPSC_SA3UL 31
#define AM62X_PSC_LPSC_LPSC_HSM_ISO 32
#define AM62X_PSC_LPSC_LPSC_DEBUGSS 33
#define AM62X_PSC_LPSC_LPSC_MAIN_IP 34
#define AM62X_PSC_LPSC_LPSC_MAIN_MCANSS_0 35
#define AM62X_PSC_LPSC_LPSC_GIC 36
#define AM62X_PSC_LPSC_LPSC_MAIN_PBIST 37
#define AM62X_PSC_LPSC_LPSC_MAIN_SPARE0 38
#define AM62X_PSC_LPSC_LPSC_MAIN_SPARE1 39
#define AM62X_PSC_LPSC_LPSC_ICSSM 40
#define AM62X_PSC_LPSC_LPSC_CPSW3G 41
#define AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0 42
#define AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0_PBIST_0 43
#define AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0_PBIST_1 44
#define AM62X_PSC_LPSC_LPSC_A53_0 45
#define AM62X_PSC_LPSC_LPSC_A53_1 46
#define AM62X_PSC_LPSC_LPSC_A53_2 47
#define AM62X_PSC_LPSC_LPSC_A53_3 48
#define AM62X_PSC_LPSC_LPSC_GPU 49
#define AM62X_PSC_LPSC_LPSC_GPU_PBIST 50
#define AM62X_PSC_LPSC_LPSC_DSS 51

#define AM62X_DEV_AM62_CMP_EVENT_INTROUTER_MAIN_0_CLOCKS 0
#define AM62X_DEV_AM62_DBGSUSPENDROUTER_MAIN_0_CLOCKS 1
#define AM62X_DEV_AM62_MAIN_GPIOMUX_INTROUTER_MAIN_0_CLOCKS 2
#define AM62X_DEV_AM62_MCU_GPIOMUX_INTROUTER_WKUP_0_CLOCKS 0
#define AM62X_DEV_AM62_TIMESYNC_EVENT_INTROUTER_MAIN_0_CLOCKS 3
#define AM62X_DEV_BLAZAR_MCU_0_CBASS_0_CLOCKS 4
#define AM62X_DEV_BLAZAR_MCU_0_CORTEX_M4_0_CLOCKS 5
#define AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS 9
#define AM62X_DEV_CPT2_AGGREGATOR32_MAIN_250MHZ_CLOCKS 39
#define AM62X_DEV_CXSTM500SS_MAIN_0_CLOCKS 40
#define AM62X_DEV_DCC2_MAIN_0_CLOCKS 43
#define AM62X_DEV_DCC2_MAIN_1_CLOCKS 56
#define AM62X_DEV_DCC2_MAIN_2_CLOCKS 69
#define AM62X_DEV_DCC2_MAIN_3_CLOCKS 82
#define AM62X_DEV_DCC2_MAIN_4_CLOCKS 95
#define AM62X_DEV_DCC2_MAIN_5_CLOCKS 108
#define AM62X_DEV_DCC2_MCU_0_CLOCKS 1
#define AM62X_DEV_DEBUGSS_K3_WRAP_CV0_MAIN_0_CLOCKS 121
#define AM62X_DEV_DMSS_AM62_MAIN_0_BCDMA_0_CLOCKS 144
#define AM62X_DEV_DMSS_AM62_MAIN_0_CBASS_0_CLOCKS 145
#define AM62X_DEV_DMSS_AM62_MAIN_0_INTAGGR_0_CLOCKS 146
#define AM62X_DEV_DMSS_AM62_MAIN_0_IPCSS_0_CLOCKS 147
#define AM62X_DEV_DMSS_AM62_MAIN_0_PKTDMA_0_CLOCKS 148
#define AM62X_DEV_DMSS_AM62_MAIN_0_RINGACC_0_CLOCKS 0
#define AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS 14
#define AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS 149
#define AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS 164
#define AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS 179
#define AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS 194
#define AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS 209
#define AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS 224
#define AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS 239
#define AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS 254
#define AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS 25
#define AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS 36
#define AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS 47
#define AM62X_DEV_ECAP_MAIN_0_CLOCKS 269
#define AM62X_DEV_ECAP_MAIN_1_CLOCKS 270
#define AM62X_DEV_ECAP_MAIN_2_CLOCKS 271
#define AM62X_DEV_ELM_MAIN_0_CLOCKS 272
#define AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS 273
#define AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS 282
#define AM62X_DEV_EQEP_T2_MAIN_0_CLOCKS 291
#define AM62X_DEV_EQEP_T2_MAIN_1_CLOCKS 292
#define AM62X_DEV_GTC_R10_WKUP_0_CLOCKS 293
#define AM62X_DEV_EQEP_T2_MAIN_2_CLOCKS 305
#define AM62X_DEV_ESM_AM64_MAIN_MAIN_0_CLOCKS 306
#define AM62X_DEV_ESM_AM64_MCU_WKUP_0_CLOCKS 58
#define AM62X_DEV_FSS_UL_MAIN_0_FSAS_0_CLOCKS 307
#define AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS 308
#define AM62X_DEV_GIC500SS_1_4_MAIN_0_CLOCKS 318
#define AM62X_DEV_GPIO_144_MAIN_0_CLOCKS 319
#define AM62X_DEV_GPIO_144_MAIN_1_CLOCKS 320
#define AM62X_DEV_GPIO_144_MCU_0_CLOCKS 59
#define AM62X_DEV_GPMC_MAIN_0_CLOCKS 321
#define AM62X_DEV_ICSS_M_MAIN_0_CLOCKS 327
#define AM62X_DEV_K3_LED2VBUS_MAIN_0_CLOCKS 342
#define AM62X_DEV_K3_DDPA_MAIN_0_CLOCKS 344
#define AM62X_DEV_K3_EPWM_MAIN_0_CLOCKS 345
#define AM62X_DEV_K3_EPWM_MAIN_1_CLOCKS 346
#define AM62X_DEV_K3_EPWM_MAIN_2_CLOCKS 347
#define AM62X_DEV_K3VTM_N16FFC_WKUP_0_CLOCKS 348
#define AM62X_DEV_MCANSS_MAIN_0_CLOCKS 353
#define AM62X_DEV_MCRC64_MCU_0_CLOCKS 64
#define AM62X_DEV_MSHSI2C_MAIN_0_CLOCKS 360
#define AM62X_DEV_MSHSI2C_MAIN_1_CLOCKS 364
#define AM62X_DEV_MSHSI2C_MAIN_2_CLOCKS 368
#define AM62X_DEV_MSHSI2C_MAIN_3_CLOCKS 372
#define AM62X_DEV_MSHSI2C_MCU_0_CLOCKS 65
#define AM62X_DEV_MSHSI2C_WKUP_0_CLOCKS 69
#define AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS 75
#define AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS 88
#define AM62X_DEV_USART_WKUP_0_CLOCKS 101
#define AM62X_DEV_MCRC64_MAIN_0_CLOCKS 376
#define AM62X_DEV_RTCSS_WKUP_0_CLOCKS 377
#define AM62X_DEV_PULSAR_UL_WKUP_0_R5_0_CLOCKS 386
#define AM62X_DEV_RTI_CFG1_MAIN_A53_0_CLOCKS 390
#define AM62X_DEV_RTI_CFG1_MAIN_A53_1_CLOCKS 396
#define AM62X_DEV_RTI_CFG1_MAIN_A53_2_CLOCKS 402
#define AM62X_DEV_RTI_CFG1_MAIN_A53_3_CLOCKS 408
#define AM62X_DEV_RTI_CFG1_MAIN_GPU_CLOCKS 414
#define AM62X_DEV_RTI_CFG1_MCU_M4_0_CLOCKS 107
#define AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS 113
#define AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_0_CLOCKS 420
#define AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_1_CLOCKS 421
#define AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_2_CLOCKS 422
#define AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_3_CLOCKS 423
#define AM62X_DEV_SAM62_MCU_PSC_WRAP_WKUP_0_CLOCKS 121
#define AM62X_DEV_SPI_MAIN_0_CLOCKS 424
#define AM62X_DEV_SPI_MAIN_1_CLOCKS 430
#define AM62X_DEV_SPI_MAIN_2_CLOCKS 436
#define AM62X_DEV_USART_MAIN_0_CLOCKS 442
#define AM62X_DEV_SPI_MCU_0_CLOCKS 123
#define AM62X_DEV_SPI_MCU_1_CLOCKS 129
#define AM62X_DEV_USART_MCU_0_CLOCKS 135
#define AM62X_DEV_SPINLOCK256_MAIN_0_CLOCKS 448
#define AM62X_DEV_USART_MAIN_1_CLOCKS 449
#define AM62X_DEV_USART_MAIN_2_CLOCKS 455
#define AM62X_DEV_USART_MAIN_3_CLOCKS 461
#define AM62X_DEV_USART_MAIN_4_CLOCKS 467
#define AM62X_DEV_USART_MAIN_5_CLOCKS 473
#define AM62X_DEV_BOARD_0_CLOCKS 479
#define AM62X_DEV_USART_MAIN_6_CLOCKS 648
#define AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS 654
#define AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS 665
#define AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_MAIN_0_CLOCKS 676
#define AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_MAIN_GPU_0_CLOCKS 686
#define AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_WKUP_0_CLOCKS 696
#define AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_CLOCKS 704
#define AM62X_DEV_SAM62_MAIN_PSC_WRAP_MAIN_0_FW_0_CLOCKS 710
#define AM62X_DEV_SAM62_MAIN_PSC_WRAP_MAIN_0_PSC_0_CLOCKS 711
#define AM62X_DEV_SAM62_DDR_WRAP_MAIN_0_CLOCKS 713
#define AM62X_DEV_SAM62_DEBUG_MAIN_CELL_MAIN_0_CLOCKS 716
#define AM62X_DEV_SAM62_A53_RS_BW_LIMITER_MAIN_0_CLOCKS 719
#define AM62X_DEV_SAM62_A53_WS_BW_LIMITER_MAIN_0_CLOCKS 720
#define AM62X_DEV_SAM62_GPU_RS_BW_LIMITER_MAIN_0_CLOCKS 721
#define AM62X_DEV_SAM62_GPU_WS_BW_LIMITER_MAIN_0_CLOCKS 722
#define AM62X_DEV_SAM62_DM_WAKEUP_DEEPSLEEP_SOURCES_WKUP_0_CLOCKS 723
#define AM62X_DEV_SAM62_MCU_16FF_MCU_0_CLOCKS 724
#define AM62X_DEV_CPT2_AGGREGATOR32_MAIN_400MHZ_CLOCKS 728
#define AM62X_DEV_CSI_RX_IF_MAIN_0_CLOCKS 729
#define AM62X_DEV_DCC2_MAIN_6_CLOCKS 734
#define AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS 747
#define AM62X_DEV_K3_DPHY_RX_MAIN_0_CLOCKS 756
#define AM62X_DEV_K3_DSS_UL_MAIN_0_CLOCKS 764
#define AM62X_DEV_K3_GPU_AXE116M_MAIN_0_CLOCKS 771
#define AM62X_DEV_MCANSS_MCU_0_CLOCKS 139
#define AM62X_DEV_MCANSS_MCU_1_CLOCKS 146
#define AM62X_DEV_MCASP_MAIN_0_CLOCKS 772
#define AM62X_DEV_MCASP_MAIN_1_CLOCKS 794
#define AM62X_DEV_MCASP_MAIN_2_CLOCKS 816
#define AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLOCKS 153
#define AM62X_DEV_SMS_MAIN_0_CORTEX_M4F_1_CLOCKS 838
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS 158

static const struct dev_data am62x_dev_am62_cmp_event_introuter_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_AM62_CMP_EVENT_INTROUTER_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_am62_dbgsuspendrouter_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_DEBUGSS,
	},
	.dev_clk_idx		= AM62X_DEV_AM62_DBGSUSPENDROUTER_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_am62_main_gpiomux_introuter_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_AM62_MAIN_GPIOMUX_INTROUTER_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_am62_mcu_gpiomux_introuter_wkup_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_AM62_MCU_GPIOMUX_INTROUTER_WKUP_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_am62_timesync_event_introuter_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_AM62_TIMESYNC_EVENT_INTROUTER_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_blazar_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_blazar_mcu_0_cbass_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_M4F,
	},
	.dev_clk_idx		= AM62X_DEV_BLAZAR_MCU_0_CBASS_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_blazar_mcu_0_cortex_m4_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_M4F,
	},
	.dev_clk_idx		= AM62X_DEV_BLAZAR_MCU_0_CORTEX_M4_0_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_cpsw_3guss_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_CPSW,
		.mod		= AM62X_PSC_LPSC_LPSC_CPSW3G,
	},
	.dev_clk_idx		= AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,
	.n_clocks		= 30,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_cpt2_aggregator32_main_250MHz __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_CPT2_AGGREGATOR32_MAIN_250MHZ_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_cxstm500ss_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_DEBUGSS,
	},
	.dev_clk_idx		= AM62X_DEV_CXSTM500SS_MAIN_0_CLOCKS,
	.n_clocks		= 3,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dcc2_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DCC2_MAIN_0_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dcc2_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DCC2_MAIN_1_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dcc2_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DCC2_MAIN_2_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dcc2_main_3 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DCC2_MAIN_3_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dcc2_main_4 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DCC2_MAIN_4_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dcc2_main_5 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DCC2_MAIN_5_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sms_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_TIFS,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dcc2_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DCC2_MCU_0_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_debugss_k3_wrap_cv0_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_DEBUGSS,
	},
	.dev_clk_idx		= AM62X_DEV_DEBUGSS_K3_WRAP_CV0_MAIN_0_CLOCKS,
	.n_clocks		= 23,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmss_am62_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmss_am62_main_0_bcdma_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMSS_AM62_MAIN_0_BCDMA_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmss_am62_main_0_cbass_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMSS_AM62_MAIN_0_CBASS_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmss_am62_main_0_intaggr_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMSS_AM62_MAIN_0_INTAGGR_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmss_am62_main_0_ipcss_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMSS_AM62_MAIN_0_IPCSS_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmss_am62_main_0_pktdma_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMSS_AM62_MAIN_0_PKTDMA_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmss_am62_main_0_ringacc_0 __attribute__((__section__(".const.devgroup.TIFS_INTERNAL"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMSS_AM62_MAIN_0_RINGACC_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_DMSC,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,
	.n_clocks		= 11,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_main_3 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_main_4 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_main_5 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_main_6 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_main_7 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_mcu_1 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,
	.n_clocks		= 11,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_mcu_2 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,
	.n_clocks		= 11,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_mcu_3 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,
	.n_clocks		= 11,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_ecap_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_ECAP_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_ecap_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_ECAP_MAIN_1_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_ecap_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_ECAP_MAIN_2_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_elm_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_GPMC,
	},
	.dev_clk_idx		= AM62X_DEV_ELM_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_emif_data_iso_VD __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_EMIF_DATA_ISO,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_emmcsd8ss_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_EMMC_8B,
	},
	.dev_clk_idx		= AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,
	.n_clocks		= 9,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_emmcsd4ss_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_EMMC_4B_0,
	},
	.dev_clk_idx		= AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,
	.n_clocks		= 9,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_eqep_t2_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_EQEP_T2_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_eqep_t2_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_EQEP_T2_MAIN_1_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_gtc_r10_wkup_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,
	.n_clocks		= 12,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_eqep_t2_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_EQEP_T2_MAIN_2_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_esm_am64_main_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_ESM_AM64_MAIN_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_esm_am64_mcu_wkup_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_ESM_AM64_MCU_WKUP_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_fss_ul_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_fss_ul_main_0_fsas_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_FSS_OSPI,
	},
	.dev_clk_idx		= AM62X_DEV_FSS_UL_MAIN_0_FSAS_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_fss_ul_main_0_ospi_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_FSS_OSPI,
	},
	.dev_clk_idx		= AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,
	.n_clocks		= 10,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_gic500ss_1_4_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_GIC,
	},
	.dev_clk_idx		= AM62X_DEV_GIC500SS_1_4_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_gpio_144_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_GPIO_144_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_gpio_144_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_GPIO_144_MAIN_1_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_gpio_144_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_GPIO_144_MCU_0_CLOCKS,
	.n_clocks		= 5,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_gpmc_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_GPMC,
	},
	.dev_clk_idx		= AM62X_DEV_GPMC_MAIN_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_icss_m_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_ICSSM,
		.mod		= AM62X_PSC_LPSC_LPSC_ICSSM,
	},
	.dev_clk_idx		= AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,
	.n_clocks		= 15,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_led2vbus_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_K3_LED2VBUS_MAIN_0_CLOCKS,
	.n_clocks		= 2,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_ddpa_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_K3_DDPA_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_epwm_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_K3_EPWM_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_epwm_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_K3_EPWM_MAIN_1_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_epwm_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_K3_EPWM_MAIN_2_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3vtm_n16ffc_wkup_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_K3VTM_N16FFC_WKUP_0_CLOCKS,
	.n_clocks		= 5,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mailbox1_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_main2mcu_VD __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN2MCU_ISO,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mcanss_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_MCANSS_0,
	},
	.dev_clk_idx		= AM62X_DEV_MCANSS_MAIN_0_CLOCKS,
	.n_clocks		= 7,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mcrc64_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_MCRC64_MCU_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_mcu2main_VD __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU2MAIN_ISO,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mshsi2c_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_MSHSI2C_MAIN_0_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mshsi2c_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_MSHSI2C_MAIN_1_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mshsi2c_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_MSHSI2C_MAIN_2_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mshsi2c_main_3 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_MSHSI2C_MAIN_3_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mshsi2c_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_MSHSI2C_MCU_0_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_mshsi2c_wkup_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_MSHSI2C_WKUP_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_wkup_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_dmtimer_dmc1ms_wkup_1 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_usart_wkup_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_USART_WKUP_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_mcrc64_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_MCRC64_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_rtcss_wkup_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_RTCSS_WKUP_0_CLOCKS,
	.n_clocks		= 9,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_pulsar_ul_wkup_0_cortex_r5_ss_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_pulsar_ul_wkup_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_pulsar_ul_wkup_0_R5_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_DM,
	},
	.dev_clk_idx		= AM62X_DEV_PULSAR_UL_WKUP_0_R5_0_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_rti_cfg1_main_a53_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_0,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_0,
	},
	.dev_clk_idx		= AM62X_DEV_RTI_CFG1_MAIN_A53_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_rti_cfg1_main_a53_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_1,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_1,
	},
	.dev_clk_idx		= AM62X_DEV_RTI_CFG1_MAIN_A53_1_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_rti_cfg1_main_a53_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_2,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_2,
	},
	.dev_clk_idx		= AM62X_DEV_RTI_CFG1_MAIN_A53_2_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_rti_cfg1_main_a53_3 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_3,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_3,
	},
	.dev_clk_idx		= AM62X_DEV_RTI_CFG1_MAIN_A53_3_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_rti_cfg1_main_gpu __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_GPU,
		.mod		= AM62X_PSC_LPSC_LPSC_GPU,
	},
	.dev_clk_idx		= AM62X_DEV_RTI_CFG1_MAIN_GPU_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_rti_cfg1_mcu_m4_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_M4F,
	},
	.dev_clk_idx		= AM62X_DEV_RTI_CFG1_MCU_M4_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_rti_cfg1_wkup_dm_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_DM,
	},
	.dev_clk_idx		= AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,
	.n_clocks		= 8,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_sam62_a53_512kb_wrap_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_a53_512kb_wrap_main_0_a53_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_0,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_0,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_a53_512kb_wrap_main_0_a53_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_1,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_1,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_1_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_a53_512kb_wrap_main_0_a53_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_2,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_2,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_2_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_a53_512kb_wrap_main_0_a53_3 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_3,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_3,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_3_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_main_psc_wrap_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static struct psc_data am62x_sam62_mcu_psc_wrap_wkup_0_data __attribute__((__section__(".bss.devgroup.MCU_WAKEUP")));
static const struct psc_pd_data am62x_sam62_mcu_psc_wrap_wkup_0_pd_data[AM62X_PSC_PD_PD_M4F + 1] __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	[AM62X_PSC_PD_GP_CORE_CTL_MCU] = {
		.flags	= PSC_PD_EXISTS,
	},
	[AM62X_PSC_PD_PD_M4F] =		 {
		.depends	= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
};
static struct psc_pd am62x_sam62_mcu_psc_wrap_wkup_0_powerdomains[AM62X_PSC_PD_PD_M4F + 1] __attribute__((__section__(".bss.devgroup.MCU_WAKEUP")));
static const dev_idx_t dev_list_LPSC_MCU_common[10] __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	AM62X_DEV_MCU_TIMER0,
	AM62X_DEV_MCU_TIMER1,
	AM62X_DEV_MCU_TIMER2,
	AM62X_DEV_MCU_TIMER3,
	AM62X_DEV_MCU_MCRC64_0,
	AM62X_DEV_MCU_I2C0,
	AM62X_DEV_MCU_MCSPI0,
	AM62X_DEV_MCU_MCSPI1,
	AM62X_DEV_MCU_UART0,
	DEV_ID_NONE,
};
static const struct lpsc_module_data am62x_sam62_mcu_psc_wrap_wkup_0_mod_data[AM62X_PSC_LPSC_LPSC_MCU_COMMON + 1] __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	[AM62X_PSC_LPSC_LPSC_MCU_ALWAYSON] = {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.lpsc_dev.dev_array	=    {
			AM62X_DEV_WKUP_MCU_GPIOMUX_INTROUTER0,
			AM62X_DEV_MCU_DCC0,
			AM62X_DEV_WKUP_ESM0,
			AM62X_DEV_MCU_GPIO0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN2MCU_ISO] = {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
		.lpsc_dev.dev_array	=    {
			AM62X_DEV_MAIN2MCU_VD,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_MCU2MAIN_ISO] = {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_DM,
		.lpsc_dev.dev_array	=    {
			AM62X_DEV_MCU2MAIN_VD,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_DM2SAFE_ISO] =  {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_DM,
		.lpsc_dev.dev_array	=    {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_MCU2DM_ISO] =   {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2SAFE_ISO,
		.lpsc_dev.dev_array	=    {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_MCU_TEST] =     {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL_MCU,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2SAFE_ISO,
		.lpsc_dev.dev_array	=    {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MCU_M4F] =	     {
		.powerdomain		= AM62X_PSC_PD_PD_M4F,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
		.lpsc_dev.dev_array	=    {
			AM62X_DEV_MCU_M4FSS0_CBASS_0,
			AM62X_DEV_MCU_M4FSS0_CORE0,
			AM62X_DEV_MCU_RTI0,
			DEV_ID_NONE,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_HAS_LOCAL_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_MCU_MCANSS_0] = {
		.powerdomain		= AM62X_PSC_PD_PD_M4F,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
		.lpsc_dev.dev_array	=    {
			AM62X_DEV_MCU_MCAN0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MCU_MCANSS_1] = {
		.powerdomain		= AM62X_PSC_PD_PD_M4F,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
		.lpsc_dev.dev_array	=    {
			AM62X_DEV_MCU_MCAN1,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MCU_COMMON] =   {
		.powerdomain		= AM62X_PSC_PD_PD_M4F,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2SAFE_ISO,
		.lpsc_dev.dev_list	= dev_list_LPSC_MCU_common,
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_DEVICES_LIST,
	},
};
static struct lpsc_module am62x_sam62_mcu_psc_wrap_wkup_0_modules[AM62X_PSC_LPSC_LPSC_MCU_COMMON + 1] __attribute__((__section__(".bss.devgroup.MCU_WAKEUP")));
static const u8 am62x_dev_sam62_mcu_psc_wrap_wkup_0_resources[] __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	RDAT_HDR(RESOURCE_MEM, 1, STRUE),
	RDAT_MEM(0x04000000),
};
static const struct psc_drv_data am62x_dev_sam62_mcu_psc_wrap_wkup_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.data					= &am62x_sam62_mcu_psc_wrap_wkup_0_data,
	.pd_data				= am62x_sam62_mcu_psc_wrap_wkup_0_pd_data,
	.powerdomains				= am62x_sam62_mcu_psc_wrap_wkup_0_powerdomains,
	.pd_count				= ARRAY_SIZE(am62x_sam62_mcu_psc_wrap_wkup_0_pd_data),
	.mod_data				= am62x_sam62_mcu_psc_wrap_wkup_0_mod_data,
	.modules				= am62x_sam62_mcu_psc_wrap_wkup_0_modules,
	.module_count				= ARRAY_SIZE(am62x_sam62_mcu_psc_wrap_wkup_0_mod_data),
	.psc_idx				= 0,
	.drv_data				= {
		.dev_data			= {
			.soc			= {
				.psc_idx	= PSC_DEV_NONE,
			},
			.dev_clk_idx		= AM62X_DEV_SAM62_MCU_PSC_WRAP_WKUP_0_CLOCKS,
			.n_clocks		= 2,
			.pm_devgrp		= PM_DEVGRP_01,
			.flags			= DEVD_FLAG_DO_INIT | DEVD_FLAG_DRV_DATA,
		},
		.drv				= &psc_drv,
		.r				= am62x_dev_sam62_mcu_psc_wrap_wkup_0_resources,
	},
};
static const struct dev_data am62x_dev_spi_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_SPI_MAIN_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_spi_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_SPI_MAIN_1_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_spi_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_SPI_MAIN_2_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usart_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_USART_MAIN_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_spi_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_SPI_MCU_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_spi_mcu_1 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_SPI_MCU_1_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_usart_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_USART_MCU_0_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_spinlock256_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_SMS_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_SPINLOCK256_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usart_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_USART_MAIN_1_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usart_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_USART_MAIN_2_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usart_main_3 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_USART_MAIN_3_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usart_main_4 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_USART_MAIN_4_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usart_main_5 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_USART_MAIN_5_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_board_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_BOARD_0_CLOCKS,
	.n_clocks		= 169,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usart_main_6 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
	},
	.dev_clk_idx		= AM62X_DEV_USART_MAIN_6_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usb2ss_16ffc_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_USB_0,
	},
	.dev_clk_idx		= AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS,
	.n_clocks		= 11,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_usb2ss_16ffc_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_USB_1,
	},
	.dev_clk_idx		= AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS,
	.n_clocks		= 11,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_pbist_8c28p_4bit_wrap_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_PBIST,
	},
	.dev_clk_idx		= AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_MAIN_0_CLOCKS,
	.n_clocks		= 10,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_pbist_8c28p_4bit_wrap_main_GPU_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_GPU,
		.mod		= AM62X_PSC_LPSC_LPSC_GPU_PBIST,
	},
	.dev_clk_idx		= AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_MAIN_GPU_0_CLOCKS,
	.n_clocks		= 10,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_pbist_8c28p_4bit_wrap_wkup_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_DM_PBIST,
	},
	.dev_clk_idx		= AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_WKUP_0_CLOCKS,
	.n_clocks		= 8,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_a53_512kb_wrap_main_0_arm_corepack_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_CLOCKS,
	.n_clocks		= 6,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_a53_512kb_wrap_main_0_pbist_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.mod		= AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0_PBIST_0,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_main_psc_wrap_main_0_fw_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_MAIN_PSC_WRAP_MAIN_0_FW_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static struct psc_data am62x_sam62_main_psc_wrap_main_0_psc_0_data __attribute__((__section__(".bss.devgroup.MAIN")));
static const struct psc_pd_data am62x_sam62_main_psc_wrap_main_0_psc_0_pd_data[AM62X_PSC_PD_PD_DSS + 1] __attribute__((__section__(".const.devgroup.MAIN"))) = {
	[AM62X_PSC_PD_GP_CORE_CTL] =	  {
		.flags	= PSC_PD_EXISTS,
	},
	[AM62X_PSC_PD_PD_ICSSM] =	  {
		.depends	= AM62X_PSC_PD_GP_CORE_CTL,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
	[AM62X_PSC_PD_PD_CPSW] =	  {
		.depends	= AM62X_PSC_PD_GP_CORE_CTL,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
	[AM62X_PSC_PD_PD_A53_CLUSTER_0] = {
		.depends	= AM62X_PSC_PD_GP_CORE_CTL,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
	[AM62X_PSC_PD_PD_A53_0] =	  {
		.depends	= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
	[AM62X_PSC_PD_PD_A53_1] =	  {
		.depends	= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
	[AM62X_PSC_PD_PD_A53_2] =	  {
		.depends	= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
	[AM62X_PSC_PD_PD_A53_3] =	  {
		.depends	= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
	[AM62X_PSC_PD_PD_GPU] =		  {
		.depends	= AM62X_PSC_PD_GP_CORE_CTL,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
	[AM62X_PSC_PD_PD_DSS] =		  {
		.depends	= AM62X_PSC_PD_GP_CORE_CTL,
		.flags		= PSC_PD_EXISTS | PSC_PD_DEPENDS,
	},
};
static struct psc_pd am62x_sam62_main_psc_wrap_main_0_psc_0_powerdomains[AM62X_PSC_PD_PD_DSS + 1] __attribute__((__section__(".bss.devgroup.MAIN")));
static const dev_idx_t dev_list_LPSC_main_alwayson[22] __attribute__((__section__(".const.devgroup.MAIN"))) = {
	AM62X_DEV_CMP_EVENT_INTROUTER0,
	AM62X_DEV_MAIN_GPIOMUX_INTROUTER0,
	AM62X_DEV_TIMESYNC_EVENT_ROUTER0,
	AM62X_DEV_DCC0,
	AM62X_DEV_DCC1,
	AM62X_DEV_DCC2,
	AM62X_DEV_DCC3,
	AM62X_DEV_DCC4,
	AM62X_DEV_DCC5,
	AM62X_DEV_DCC6,
	AM62X_DEV_WKUP_TIMER0,
	AM62X_DEV_WKUP_TIMER1,
	AM62X_DEV_ESM0,
	AM62X_DEV_GPIO0,
	AM62X_DEV_GPIO1,
	AM62X_DEV_WKUP_GTC0,
	AM62X_DEV_DDPA0,
	AM62X_DEV_WKUP_VTM0,
	AM62X_DEV_WKUP_I2C0,
	AM62X_DEV_WKUP_RTCSS0,
	AM62X_DEV_WKUP_UART0,
	DEV_ID_NONE,
};
static const dev_idx_t dev_list_LPSC_main_IP[40] __attribute__((__section__(".const.devgroup.MAIN"))) = {
	AM62X_DEV_CPT2_AGGR0,
	AM62X_DEV_DMASS0_BCDMA_0,
	AM62X_DEV_DMASS0_CBASS_0,
	AM62X_DEV_DMASS0_INTAGGR_0,
	AM62X_DEV_DMASS0_IPCSS_0,
	AM62X_DEV_DMASS0_PKTDMA_0,
	AM62X_DEV_DMASS0_RINGACC_0,
	AM62X_DEV_TIMER0,
	AM62X_DEV_TIMER1,
	AM62X_DEV_TIMER2,
	AM62X_DEV_TIMER3,
	AM62X_DEV_TIMER4,
	AM62X_DEV_TIMER5,
	AM62X_DEV_TIMER6,
	AM62X_DEV_TIMER7,
	AM62X_DEV_ECAP0,
	AM62X_DEV_ECAP1,
	AM62X_DEV_ECAP2,
	AM62X_DEV_EQEP0,
	AM62X_DEV_EQEP1,
	AM62X_DEV_EQEP2,
	AM62X_DEV_EPWM0,
	AM62X_DEV_EPWM1,
	AM62X_DEV_EPWM2,
	AM62X_DEV_MCRC64_0,
	AM62X_DEV_I2C0,
	AM62X_DEV_I2C1,
	AM62X_DEV_I2C2,
	AM62X_DEV_I2C3,
	AM62X_DEV_MCSPI0,
	AM62X_DEV_MCSPI1,
	AM62X_DEV_MCSPI2,
	AM62X_DEV_UART0,
	AM62X_DEV_UART1,
	AM62X_DEV_UART2,
	AM62X_DEV_UART3,
	AM62X_DEV_UART4,
	AM62X_DEV_UART5,
	AM62X_DEV_UART6,
	DEV_ID_NONE,
};
static const struct lpsc_module_data am62x_sam62_main_psc_wrap_main_0_psc_0_mod_data[AM62X_PSC_LPSC_LPSC_DSS + 1] __attribute__((__section__(".const.devgroup.MAIN"))) = {
	[AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.lpsc_dev.dev_list	= dev_list_LPSC_main_alwayson,
		.flags			= LPSC_MODULE_EXISTS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET | LPSC_DEVICES_LIST,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_DM] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_WKUP_R5FSS0_CORE0,
			AM62X_DEV_WKUP_RTI0,
			DEV_ID_NONE,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_HAS_LOCAL_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_DM_PBIST] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2MAIN_INFRA_ISO,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_WKUP_PBIST0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN2DM_ISO] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2MAIN_INFRA_ISO,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_DM2MAIN_ISO] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_DM2MAIN_INFRA_ISO] =     {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_DM,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_DM2CENTRAL_ISO] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_SMS_COMMON,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_CENTRAL2DM_ISO] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2MAIN_INFRA_ISO,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_GP_SPARE0] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2MAIN_INFRA_ISO,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_EMIF_LOCAL] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_DDR16SS0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_EMIF_CFG_ISO] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_EMIF_LOCAL,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_EMIF_CFG_ISO_VD,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_EMIF_DATA_ISO] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_EMIF_CFG_ISO,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_EMIF_DATA_ISO_VD,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_USB0_ISO] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_USB_0,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MAIN_USB0_ISO_VD,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_USB1_ISO] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_USB_1,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MAIN_USB1_ISO_VD,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_TEST] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2MAIN_INFRA_ISO,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_GPMC] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_ELM0,
			AM62X_DEV_GPMC0,
			DEV_ID_NONE,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_GP_SPARE1] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_MCASP_0] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MCASP0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_MCASP_1] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MCASP1,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_MCASP_2] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MCASP2,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_EMMC_8B] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MMCSD0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_EMMC_4B_0] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MMCSD1,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_EMMC_4B_1] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MMCSD2,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_USB_0] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_USB0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_USB_1] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_USB1,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_CSI_RX_0] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DPHY_0,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_CSI_RX_IF0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_DPHY_0] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_DPHY_RX0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_SMS_COMMON] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_CPT2_AGGR1,
			AM62X_DEV_SMS0,
			AM62X_DEV_SPINLOCK0,
			DEV_ID_NONE,
		},
		.flags			= LPSC_MODULE_EXISTS,
	},
	[AM62X_PSC_LPSC_LPSC_FSS_OSPI] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_FSS0_FSAS_0,
			AM62X_DEV_FSS0_OSPI_0,
			DEV_ID_NONE,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_TIFS] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_SMS_COMMON,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_SMS0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_HSM] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_TIFS,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_HSM0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_DEPENDS | LPSC_HAS_LOCAL_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_SA3UL] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_SMS_COMMON,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_HSM_ISO] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_HSM,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_DEPENDS | LPSC_NO_CLOCK_GATING | LPSC_NO_MODULE_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_DEBUGSS] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_DBGSUSPENDROUTER0,
			AM62X_DEV_STM0,
			AM62X_DEV_DEBUGSS_WRAP0,
			AM62X_DEV_DEBUGSS0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_IP] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_DM2MAIN_INFRA_ISO,
		.lpsc_dev.dev_list	= dev_list_LPSC_main_IP,
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_DEVICES_LIST,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_MCANSS_0] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_MCAN0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_GIC] =		      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_GICSS0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_PBIST] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_PBIST0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_SPARE0] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS,
	},
	[AM62X_PSC_LPSC_LPSC_MAIN_SPARE1] =	      {
		.powerdomain		= AM62X_PSC_PD_GP_CORE_CTL,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS,
	},
	[AM62X_PSC_LPSC_LPSC_ICSSM] =		      {
		.powerdomain		= AM62X_PSC_PD_PD_ICSSM,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_SMS_COMMON,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_ICSSM0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_CPSW3G] =		      {
		.powerdomain		= AM62X_PSC_PD_PD_CPSW,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_CPSW0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0] =	      {
		.powerdomain		= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_A53SS0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0_PBIST_0] = {
		.powerdomain		= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_COMPUTE_CLUSTER0_PBIST_0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0_PBIST_1] = {
		.powerdomain		= AM62X_PSC_PD_PD_A53_CLUSTER_0,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0,
		.lpsc_dev.dev_array	=	      {
			DEV_ID_NONE,
			0,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_A53_0] =		      {
		.powerdomain		= AM62X_PSC_PD_PD_A53_0,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_RTI0,
			AM62X_DEV_A53SS0_CORE_0,
			DEV_ID_NONE,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_HAS_LOCAL_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_A53_1] =		      {
		.powerdomain		= AM62X_PSC_PD_PD_A53_1,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_RTI1,
			AM62X_DEV_A53SS0_CORE_1,
			DEV_ID_NONE,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_HAS_LOCAL_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_A53_2] =		      {
		.powerdomain		= AM62X_PSC_PD_PD_A53_2,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_RTI2,
			AM62X_DEV_A53SS0_CORE_2,
			DEV_ID_NONE,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_HAS_LOCAL_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_A53_3] =		      {
		.powerdomain		= AM62X_PSC_PD_PD_A53_3,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_A53_CLUSTER_0,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_RTI3,
			AM62X_DEV_A53SS0_CORE_3,
			DEV_ID_NONE,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS | LPSC_HAS_LOCAL_RESET,
	},
	[AM62X_PSC_LPSC_LPSC_GPU] =		      {
		.powerdomain		= AM62X_PSC_PD_PD_GPU,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_GPU0,
			AM62X_DEV_RTI15,
			DEV_ID_NONE,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_GPU_PBIST] =	      {
		.powerdomain		= AM62X_PSC_PD_PD_GPU,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_PBIST1,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
	[AM62X_PSC_LPSC_LPSC_DSS] =		      {
		.powerdomain		= AM62X_PSC_PD_PD_DSS,
		.depends_psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.depends		= AM62X_PSC_LPSC_LPSC_MAIN_IP,
		.lpsc_dev.dev_array	=	      {
			AM62X_DEV_DSS0,
			DEV_ID_NONE,
			0,
			0,
		},
		.flags			= LPSC_MODULE_EXISTS | LPSC_DEPENDS,
	},
};
static struct lpsc_module am62x_sam62_main_psc_wrap_main_0_psc_0_modules[AM62X_PSC_LPSC_LPSC_DSS + 1] __attribute__((__section__(".bss.devgroup.MAIN")));
static const u8 am62x_dev_sam62_main_psc_wrap_main_0_psc_0_resources[] __attribute__((__section__(".const.devgroup.MAIN"))) = {
	RDAT_HDR(RESOURCE_MEM, 1, STRUE),
	RDAT_MEM(0x00400000),
};
static const struct psc_drv_data am62x_dev_sam62_main_psc_wrap_main_0_psc_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.data					= &am62x_sam62_main_psc_wrap_main_0_psc_0_data,
	.pd_data				= am62x_sam62_main_psc_wrap_main_0_psc_0_pd_data,
	.powerdomains				= am62x_sam62_main_psc_wrap_main_0_psc_0_powerdomains,
	.pd_count				= ARRAY_SIZE(am62x_sam62_main_psc_wrap_main_0_psc_0_pd_data),
	.mod_data				= am62x_sam62_main_psc_wrap_main_0_psc_0_mod_data,
	.modules				= am62x_sam62_main_psc_wrap_main_0_psc_0_modules,
	.module_count				= ARRAY_SIZE(am62x_sam62_main_psc_wrap_main_0_psc_0_mod_data),
	.psc_idx				= 1,
	.drv_data				= {
		.dev_data			= {
			.soc			= {
				.psc_idx	= PSC_DEV_NONE,
			},
			.dev_clk_idx		= AM62X_DEV_SAM62_MAIN_PSC_WRAP_MAIN_0_PSC_0_CLOCKS,
			.n_clocks		= 2,
			.pm_devgrp		= PM_DEVGRP_00,
			.flags			= DEVD_FLAG_DO_INIT | DEVD_FLAG_DRV_DATA,
		},
		.drv				= &psc_drv,
		.r				= am62x_dev_sam62_main_psc_wrap_main_0_psc_0_resources,
	},
};
static const struct dev_data am62x_dev_sam62_ddr_wrap_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_EMIF_LOCAL,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_DDR_WRAP_MAIN_0_CLOCKS,
	.n_clocks		= 3,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_debug_main_cell_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_DEBUGSS,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_DEBUG_MAIN_CELL_MAIN_0_CLOCKS,
	.n_clocks		= 3,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_a53_rs_bw_limiter_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_A53_RS_BW_LIMITER_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_a53_ws_bw_limiter_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_A53_WS_BW_LIMITER_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_gpu_rs_bw_limiter_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_GPU_RS_BW_LIMITER_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_gpu_ws_bw_limiter_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_GPU_WS_BW_LIMITER_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_dm_wakeup_deepsleep_sources_wkup_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_DM_WAKEUP_DEEPSLEEP_SOURCES_WKUP_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_emif_cfg_iso_VD __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_EMIF_CFG_ISO,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_main_usb0_iso_VD __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_USB0_ISO,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_main_usb1_iso_VD __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_USB1_ISO,
	},
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_sam62_mcu_16ff_mcu_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_SAM62_MCU_16FF_MCU_0_CLOCKS,
	.n_clocks		= 4,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_cpt2_aggregator32_main_400MHz __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_SMS_COMMON,
	},
	.dev_clk_idx		= AM62X_DEV_CPT2_AGGREGATOR32_MAIN_400MHZ_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_csi_rx_if_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_CSI_RX_0,
	},
	.dev_clk_idx		= AM62X_DEV_CSI_RX_IF_MAIN_0_CLOCKS,
	.n_clocks		= 5,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_dcc2_main_6 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_ALWAYSON,
	},
	.dev_clk_idx		= AM62X_DEV_DCC2_MAIN_6_CLOCKS,
	.n_clocks		= 13,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_emmcsd4ss_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_EMMC_4B_1,
	},
	.dev_clk_idx		= AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,
	.n_clocks		= 9,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_K3_DPHY_RX_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_DPHY_0,
	},
	.dev_clk_idx		= AM62X_DEV_K3_DPHY_RX_MAIN_0_CLOCKS,
	.n_clocks		= 8,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_dss_ul_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_DSS,
		.mod		= AM62X_PSC_LPSC_LPSC_DSS,
	},
	.dev_clk_idx		= AM62X_DEV_K3_DSS_UL_MAIN_0_CLOCKS,
	.n_clocks		= 7,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_k3_gpu_axe116m_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_PD_GPU,
		.mod		= AM62X_PSC_LPSC_LPSC_GPU,
	},
	.dev_clk_idx		= AM62X_DEV_K3_GPU_AXE116M_MAIN_0_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mcanss_mcu_0 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_MCANSS_0,
	},
	.dev_clk_idx		= AM62X_DEV_MCANSS_MCU_0_CLOCKS,
	.n_clocks		= 7,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_mcanss_mcu_1 __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MCU_PSC_WRAP_WKUP_0,
		.pd		= AM62X_PSC_PD_PD_M4F,
		.mod		= AM62X_PSC_LPSC_LPSC_MCU_MCANSS_1,
	},
	.dev_clk_idx		= AM62X_DEV_MCANSS_MCU_1_CLOCKS,
	.n_clocks		= 7,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_mcasp_main_0 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_MCASP_0,
	},
	.dev_clk_idx		= AM62X_DEV_MCASP_MAIN_0_CLOCKS,
	.n_clocks		= 22,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mcasp_main_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_MCASP_1,
	},
	.dev_clk_idx		= AM62X_DEV_MCASP_MAIN_1_CLOCKS,
	.n_clocks		= 22,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mcasp_main_2 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_MAIN_MCASP_2,
	},
	.dev_clk_idx		= AM62X_DEV_MCASP_MAIN_2_CLOCKS,
	.n_clocks		= 22,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_clk_32k_rc_sel_dev_VD __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLOCKS,
	.n_clocks		= 5,
	.pm_devgrp		= PM_DEVGRP_01,
};
static const struct dev_data am62x_dev_sms_main_0_cortex_m4f_1 __attribute__((__section__(".const.devgroup.MAIN"))) = {
	.soc			= {
		.psc_idx	= AM62X_PSC_INST_SAM62_MAIN_PSC_WRAP_MAIN_0,
		.pd		= AM62X_PSC_PD_GP_CORE_CTL,
		.mod		= AM62X_PSC_LPSC_LPSC_HSM,
	},
	.dev_clk_idx		= AM62X_DEV_SMS_MAIN_0_CORTEX_M4F_1_CLOCKS,
	.n_clocks		= 1,
	.pm_devgrp		= PM_DEVGRP_00,
};
static const struct dev_data am62x_dev_mcu_obsclk_mux_sel_dev_VD __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	.soc			= {
		.psc_idx	= PSC_DEV_NONE,
	},
	.dev_clk_idx		= AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,
	.n_clocks		= 10,
	.pm_devgrp		= PM_DEVGRP_01,
};

static const struct dev_clk_data MCU_WAKEUP_dev_clk_data[168] __attribute__((__section__(".const.devgroup.MCU_WAKEUP"))) = {
	DEV_CLK(AM62X_DEV_AM62_MCU_GPIOMUX_INTROUTER_WKUP_0_CLOCKS,	      AM62X_DEV_WKUP_MCU_GPIOMUX_INTROUTER0_INTR_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_CLKSRC0_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_CLKSRC1_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT1_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_CLKSRC2_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT2_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_CLKSRC3_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_CLKSRC4_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_CLKSRC5_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		384),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_CLKSRC6_CLK,
		CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_CLKSRC7_CLK,
		CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_INPUT00_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_INPUT01_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		384),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_INPUT02_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_DCC_INPUT10_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_DCC2_MCU_0_CLOCKS,				      AM62X_DEV_MCU_DCC0_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,			      AM62X_DEV_MCU_TIMER0_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK,
		    CLK_AM62X_MCU_TIMERCLKN_SEL_OUT0,
		    1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK, 4,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,	      1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_MCU_TIMERCLKN_SEL_OUT0_DIV_CLKOUT,
		       CLK_AM62X_MCU_TIMERCLKN_SEL_OUT0_DIV_CLKOUT,	      1,
		       7),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MCU_0_CLOCKS,		      AM62X_DEV_MCU_TIMER0_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MCU_0_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,			      AM62X_DEV_MCU_TIMER1_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK,
		    CLK_AM62X_MCU_TIMERCLKN_SEL_OUT1,
		    1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK, 4,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,	      1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_MCU_TIMERCLKN_SEL_OUT1_DIV_CLKOUT,
		       CLK_AM62X_MCU_TIMERCLKN_SEL_OUT1_DIV_CLKOUT,	      1,
		       7),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MCU_1_CLOCKS,		      AM62X_DEV_MCU_TIMER1_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MCU_1_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,			      AM62X_DEV_MCU_TIMER2_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK,
		    CLK_AM62X_MCU_TIMERCLKN_SEL_OUT2,
		    1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK, 4,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,	      1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_MCU_TIMERCLKN_SEL_OUT2_DIV_CLKOUT,
		       CLK_AM62X_MCU_TIMERCLKN_SEL_OUT2_DIV_CLKOUT,	      1,
		       7),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MCU_2_CLOCKS,		      AM62X_DEV_MCU_TIMER2_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MCU_2_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,			      AM62X_DEV_MCU_TIMER3_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK,
		    CLK_AM62X_MCU_TIMERCLKN_SEL_OUT3,
		    1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK, 4,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,	      1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_MCU_TIMERCLKN_SEL_OUT3_DIV_CLKOUT,
		       CLK_AM62X_MCU_TIMERCLKN_SEL_OUT3_DIV_CLKOUT,	      1,
		       7),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MCU_3_CLOCKS,		      AM62X_DEV_MCU_TIMER3_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MCU_3_TIMER_PWM),
	DEV_CLK(AM62X_DEV_ESM_AM64_MCU_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_ESM0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_GPIO_144_MCU_0_CLOCKS,			      AM62X_DEV_MCU_GPIO0_MMR_CLK,
		    CLK_AM62X_MCU_GPIO0_CLKSEL_OUT0,
		    4,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_GPIO_144_MCU_0_CLOCKS,			      AM62X_DEV_MCU_GPIO0_MMR_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK, 16,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_GPIO_144_MCU_0_CLOCKS,			      AM62X_DEV_MCU_GPIO0_MMR_CLK_PARENT_GLUELOGIC_LFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_LFOSC0_CLKOUT,
		       4,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_GPIO_144_MCU_0_CLOCKS,			      AM62X_DEV_MCU_GPIO0_MMR_CLK_PARENT_GLUELOGIC_RCOSC_CLK_1P0V_97P65K3,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLK_1P0V_97P65K,	      12,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_GPIO_144_MCU_0_CLOCKS,			      AM62X_DEV_MCU_GPIO0_MMR_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       4,
		       3),
	DEV_CLK(AM62X_DEV_MCRC64_MCU_0_CLOCKS,				      AM62X_DEV_MCU_MCRC64_0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_MSHSI2C_MCU_0_CLOCKS,				      AM62X_DEV_MCU_I2C0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_MSHSI2C_MCU_0_CLOCKS,				      AM62X_DEV_MCU_I2C0_PISCL,
		CLK_AM62X_BOARD_0_MCU_I2C0_SCL_OUT,
		1),
	DEV_CLK(AM62X_DEV_MSHSI2C_MCU_0_CLOCKS,				      AM62X_DEV_MCU_I2C0_PISYS_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT1_CLK,
		1),
	DEV_CLK_OUTPUT(AM62X_DEV_MSHSI2C_MCU_0_CLOCKS,			      AM62X_DEV_MCU_I2C0_PORSCL,
		       CLK_AM62X_MSHSI2C_MCU_0_PORSCL),
	DEV_CLK_MUX(AM62X_DEV_MSHSI2C_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_I2C0_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,
		    4,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_MSHSI2C_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_I2C0_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,	      4,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MSHSI2C_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_I2C0_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,	      4,
		       1),
	DEV_CLK(AM62X_DEV_MSHSI2C_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_I2C0_PISCL,
		CLK_AM62X_MSHSI2C_WKUP_0_PORSCL,
		1),
	DEV_CLK(AM62X_DEV_MSHSI2C_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_I2C0_PISYS_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT1_CLK,
		1),
	DEV_CLK_OUTPUT(AM62X_DEV_MSHSI2C_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_I2C0_PORSCL,
		       CLK_AM62X_MSHSI2C_WKUP_0_PORSCL),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_HCLK_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,
		    2,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_HCLK_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,	      2,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_HCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,	      2,
		       1),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK,
		    CLK_AM62X_WKUP_TIMERCLKN_SEL_OUT0,
		    1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_WKUP_CLKSEL_OUT04,
		       CLK_AM62X_WKUP_CLKSEL_OUT0,
		       4,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,	      1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_WKUP_TIMERCLKN_SEL_OUT0_DIV_CLKOUT,
		       CLK_AM62X_WKUP_TIMERCLKN_SEL_OUT0_DIV_CLKOUT,	      1,
		       7),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_HCLK_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,
		    2,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_HCLK_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,	      2,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_HCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,	      2,
		       1),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK,
		    CLK_AM62X_WKUP_TIMERCLKN_SEL_OUT1,
		    1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_WKUP_CLKSEL_OUT04,
		       CLK_AM62X_WKUP_CLKSEL_OUT0,
		       4,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK,	      1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_WKUP_1_CLOCKS,		      AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_WKUP_TIMERCLKN_SEL_OUT1_DIV_CLKOUT,
		       CLK_AM62X_WKUP_TIMERCLKN_SEL_OUT1_DIV_CLKOUT,	      1,
		       7),
	DEV_CLK(AM62X_DEV_USART_WKUP_0_CLOCKS,				      AM62X_DEV_WKUP_UART0_FCLK_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT2_CLK,
		1),
	DEV_CLK_MUX(AM62X_DEV_USART_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_UART0_VBUSP_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,
		    4,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USART_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_UART0_VBUSP_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,	      4,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USART_WKUP_0_CLOCKS,			      AM62X_DEV_WKUP_UART0_VBUSP_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,	      4,
		       1),
	DEV_CLK_MUX(AM62X_DEV_RTI_CFG1_MCU_M4_0_CLOCKS,			      AM62X_DEV_MCU_RTI0_RTI_CLK,
		    CLK_AM62X_MCU_WWDTCLK_SEL_OUT0,
		    1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MCU_M4_0_CLOCKS,		      AM62X_DEV_MCU_RTI0_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MCU_M4_0_CLOCKS,		      AM62X_DEV_MCU_RTI0_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MCU_M4_0_CLOCKS,		      AM62X_DEV_MCU_RTI0_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MCU_M4_0_CLOCKS,		      AM62X_DEV_MCU_RTI0_RTI_CLK_PARENT_MCU_WWDTCLK_SEL_DIV_CLKOUT,
		       CLK_AM62X_MCU_WWDTCLK_SEL_DIV_CLKOUT,
		       1,
		       3),
	DEV_CLK(AM62X_DEV_RTI_CFG1_MCU_M4_0_CLOCKS,			      AM62X_DEV_MCU_RTI0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,		      AM62X_DEV_WKUP_RTI0_RTI_CLK,
		    CLK_AM62X_WKUP_WWDTCLK_SEL_OUT0,
		    1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,		      AM62X_DEV_WKUP_RTI0_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,		      AM62X_DEV_WKUP_RTI0_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,		      AM62X_DEV_WKUP_RTI0_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,		      AM62X_DEV_WKUP_RTI0_RTI_CLK_PARENT_WKUP_WWDTCLK_SEL_DIV_CLKOUT,
		       CLK_AM62X_WKUP_WWDTCLK_SEL_DIV_CLKOUT,
		       1,
		       3),
	DEV_CLK_MUX(AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,		      AM62X_DEV_WKUP_RTI0_VBUSP_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,
		    4,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,		      AM62X_DEV_WKUP_RTI0_VBUSP_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,	      4,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_WKUP_DM_0_CLOCKS,		      AM62X_DEV_WKUP_RTI0_VBUSP_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,	      4,
		       1),
	DEV_CLK(AM62X_DEV_SAM62_MCU_PSC_WRAP_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_PSC0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_SAM62_MCU_PSC_WRAP_WKUP_0_CLOCKS,		      AM62X_DEV_WKUP_PSC0_SLOW_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		24),
	DEV_CLK(AM62X_DEV_SPI_MCU_0_CLOCKS,				      AM62X_DEV_MCU_MCSPI0_CLKSPIREF_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		8),
	DEV_CLK_MUX(AM62X_DEV_SPI_MCU_0_CLOCKS,				      AM62X_DEV_MCU_MCSPI0_IO_CLKSPII_CLK,
		    CLK_AM62X_MCU_SPI0_MSTR_LP_CLKSEL_OUT0,
		    1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MCU_0_CLOCKS,			      AM62X_DEV_MCU_MCSPI0_IO_CLKSPII_CLK_PARENT_BOARD_0_MCU_SPI0_CLK_OUT,
		       CLK_AM62X_BOARD_0_MCU_SPI0_CLK_OUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MCU_0_CLOCKS,			      AM62X_DEV_MCU_MCSPI0_IO_CLKSPII_CLK_PARENT_SPI_MCU_0_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MCU_0_IO_CLKSPIO_CLK,
		       1,
		       1),
	DEV_CLK(AM62X_DEV_SPI_MCU_0_CLOCKS,				      AM62X_DEV_MCU_MCSPI0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_SPI_MCU_0_CLOCKS,			      AM62X_DEV_MCU_MCSPI0_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MCU_0_IO_CLKSPIO_CLK),
	DEV_CLK(AM62X_DEV_SPI_MCU_1_CLOCKS,				      AM62X_DEV_MCU_MCSPI1_CLKSPIREF_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		8),
	DEV_CLK_MUX(AM62X_DEV_SPI_MCU_1_CLOCKS,				      AM62X_DEV_MCU_MCSPI1_IO_CLKSPII_CLK,
		    CLK_AM62X_MCU_SPI1_MSTR_LP_CLKSEL_OUT0,
		    1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MCU_1_CLOCKS,			      AM62X_DEV_MCU_MCSPI1_IO_CLKSPII_CLK_PARENT_BOARD_0_MCU_SPI1_CLK_OUT,
		       CLK_AM62X_BOARD_0_MCU_SPI1_CLK_OUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MCU_1_CLOCKS,			      AM62X_DEV_MCU_MCSPI1_IO_CLKSPII_CLK_PARENT_SPI_MCU_1_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MCU_1_IO_CLKSPIO_CLK,
		       1,
		       1),
	DEV_CLK(AM62X_DEV_SPI_MCU_1_CLOCKS,				      AM62X_DEV_MCU_MCSPI1_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_SPI_MCU_1_CLOCKS,			      AM62X_DEV_MCU_MCSPI1_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MCU_1_IO_CLKSPIO_CLK),
	DEV_CLK(AM62X_DEV_USART_MCU_0_CLOCKS,				      AM62X_DEV_MCU_UART0_FCLK_CLK,
		CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT2_CLK,
		1),
	DEV_CLK(AM62X_DEV_USART_MCU_0_CLOCKS,				      AM62X_DEV_MCU_UART0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_MCANSS_MCU_0_CLOCKS,			      AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK,
		    CLK_AM62X_MCU_MCANN_CLK_SEL_OUT0,
		    1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MCU_0_CLOCKS,			      AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK,	      1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MCU_0_CLOCKS,			      AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MCU_0_CLOCKS,			      AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MCU_0_CLOCKS,			      AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT_DUP0,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       3),
	DEV_CLK(AM62X_DEV_MCANSS_MCU_0_CLOCKS,				      AM62X_DEV_MCU_MCAN0_MCANSS_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_MCANSS_MCU_1_CLOCKS,			      AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK,
		    CLK_AM62X_MCU_MCANN_CLK_SEL_OUT1,
		    1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MCU_1_CLOCKS,			      AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK,	      1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MCU_1_CLOCKS,			      AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MCU_1_CLOCKS,			      AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MCU_1_CLOCKS,			      AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT_DUP0,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       3),
	DEV_CLK(AM62X_DEV_MCANSS_MCU_1_CLOCKS,				      AM62X_DEV_MCU_MCAN1_MCANSS_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLOCKS,		      AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK,
		    CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		    1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLOCKS,		      AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK_PARENT_GLUELOGIC_RCOSC_CLK_1P0V_97P65K3,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLK_1P0V_97P65K,	      3,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLOCKS,		      AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK_PARENT_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK8,
		       CLK_AM62X_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK,  8,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLOCKS,		      AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK_PARENT_CLK_32K_RC_SEL_DIV_CLKOUT,
		       CLK_AM62X_CLK_32K_RC_SEL_DIV_CLKOUT,
		       1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLOCKS,		      AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK_PARENT_GLUELOGIC_LFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_LFOSC0_CLKOUT,
		       1,
		       3),
	DEV_CLK_MUX(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,		      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK,
		    CLK_AM62X_MCU_OBSCLK_MUX_SEL_OUT0,
		    1,
		    10),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		       1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,	      1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK,	      1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK_DUP0,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,	      1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_MCU_OBSCLK_MUX_SEL_DIV_CLKOUT,
		       CLK_AM62X_MCU_OBSCLK_MUX_SEL_DIV_CLKOUT,
		       1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		       1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK8,
		       CLK_AM62X_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK,  8,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK, 1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLOCKS,	      AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		       1,
		       9),
};
static struct dev_clk MCU_WAKEUP_dev_clk[168] __attribute__((__section__(".bss.devgroup.MCU_WAKEUP")));
static const struct dev_clk_data MAIN_dev_clk_data[839] __attribute__((__section__(".const.devgroup.MAIN"))) = {
	DEV_CLK(AM62X_DEV_AM62_CMP_EVENT_INTROUTER_MAIN_0_CLOCKS,								   AM62X_DEV_CMP_EVENT_INTROUTER0_INTR_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_AM62_DBGSUSPENDROUTER_MAIN_0_CLOCKS,									   AM62X_DEV_DBGSUSPENDROUTER0_INTR_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_AM62_MAIN_GPIOMUX_INTROUTER_MAIN_0_CLOCKS,								   AM62X_DEV_MAIN_GPIOMUX_INTROUTER0_INTR_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_AM62_TIMESYNC_EVENT_INTROUTER_MAIN_0_CLOCKS,								   AM62X_DEV_TIMESYNC_EVENT_ROUTER0_INTR_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_BLAZAR_MCU_0_CBASS_0_CLOCKS,										   AM62X_DEV_MCU_M4FSS0_CBASS_0_CLK,
		CLK_AM62X_MCU_M4FSS_CLKSEL_OUT0,
		1),
	DEV_CLK(AM62X_DEV_BLAZAR_MCU_0_CORTEX_M4_0_CLOCKS,									   AM62X_DEV_MCU_M4FSS0_CORE0_DAP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK_MUX(AM62X_DEV_BLAZAR_MCU_0_CORTEX_M4_0_CLOCKS,									   AM62X_DEV_MCU_M4FSS0_CORE0_VBUS_CLK,
		    CLK_AM62X_MCU_M4FSS_CLKSEL_OUT0,										   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_BLAZAR_MCU_0_CORTEX_M4_0_CLOCKS,
		       AM62X_DEV_MCU_M4FSS0_CORE0_VBUS_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,							   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_BLAZAR_MCU_0_CORTEX_M4_0_CLOCKS,
		       AM62X_DEV_MCU_M4FSS0_CORE0_VBUS_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK2,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,							   2,
		       1),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_CPPI_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_CPTS_RFT_CLK,
		    CLK_AM62X_MAIN_CP_GEMAC_CPTS_CLK_SEL_OUT0,									   1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,
		       AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,
		       AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK,								   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,
		       AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,									   AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,									   AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,
		       AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,							   1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,
		       AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,							   1,
		       7),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_GMII1_MR_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		10),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_GMII1_MT_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		10),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_GMII2_MR_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		10),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_GMII2_MT_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		10),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_GMII_RFT_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		2),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RGMII1_RXC_I,
		CLK_AM62X_BOARD_0_RGMII1_RXC_OUT,
		1),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RGMII1_TXC_I,
		CLK_AM62X_BOARD_0_RGMII1_TXC_OUT,
		1),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RGMII2_RXC_I,
		CLK_AM62X_BOARD_0_RGMII2_RXC_OUT,
		1),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RGMII2_TXC_I,
		CLK_AM62X_BOARD_0_RGMII2_TXC_OUT,
		1),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RGMII_MHZ_250_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		1),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RGMII_MHZ_50_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		5),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RGMII_MHZ_5_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		50),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RMII1_MHZ_50_CLK,
		CLK_AM62X_BOARD_0_RMII1_REF_CLK_OUT,
		1),
	DEV_CLK(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,										   AM62X_DEV_CPSW0_RMII2_MHZ_50_CLK,
		CLK_AM62X_BOARD_0_RMII2_REF_CLK_OUT,
		1),
	DEV_CLK_OUTPUT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,									   AM62X_DEV_CPSW0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0),
	DEV_CLK_OUTPUT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,									   AM62X_DEV_CPSW0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1),
	DEV_CLK_OUTPUT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,									   AM62X_DEV_CPSW0_MDIO_MDCLK_O,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_MDIO_MDCLK_O),
	DEV_CLK_OUTPUT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,									   AM62X_DEV_CPSW0_RGMII1_TXC_O,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_RGMII1_TXC_O),
	DEV_CLK_OUTPUT(AM62X_DEV_CPSW_3GUSS_MAIN_0_CLOCKS,									   AM62X_DEV_CPSW0_RGMII2_TXC_O,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_RGMII2_TXC_O),
	DEV_CLK(AM62X_DEV_CPT2_AGGREGATOR32_MAIN_250MHZ_CLOCKS,									   AM62X_DEV_CPT2_AGGR0_VCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_CXSTM500SS_MAIN_0_CLOCKS,										   AM62X_DEV_STM0_ATB_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_CXSTM500SS_MAIN_0_CLOCKS,										   AM62X_DEV_STM0_CORE_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_CXSTM500SS_MAIN_0_CLOCKS,										   AM62X_DEV_STM0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_CLKSRC0_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT1_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_CLKSRC1_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT2_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_CLKSRC2_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT3_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_CLKSRC3_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT4_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_CLKSRC4_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_CLKSRC5_CLK,
		CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_CLKSRC6_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_CLKSRC7_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_INPUT00_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_INPUT01_CLK,
		CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_INPUT02_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_DCC_INPUT10_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_0_CLOCKS,											   AM62X_DEV_DCC0_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_CLKSRC0_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_CLKSRC1_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_CLKSRC2_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_CLKSRC3_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_CLKSRC4_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT9_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_CLKSRC5_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_CLKSRC6_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_CLKSRC7_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT2_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_INPUT00_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_INPUT01_CLK,
		CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_INPUT02_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_DCC_INPUT10_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_1_CLOCKS,											   AM62X_DEV_DCC1_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_CLKSRC0_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_CLKSRC1_CLK,
		CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_CLKSRC2_CLK,
		CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT5_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_CLKSRC3_CLK,
		CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_CLKSRC4_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_CLKSRC5_CLK,
		CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT1_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_CLKSRC6_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_CLKSRC7_CLK,
		CLK_AM62X_BOARD_0_RMII2_REF_CLK_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_INPUT00_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_INPUT01_CLK,
		CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_INPUT02_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_DCC_INPUT10_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_2_CLOCKS,											   AM62X_DEV_DCC2_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_CLKSRC0_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_CLKSRC1_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_CLKSRC3_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT7_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_CLKSRC4_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_CLKSRC5_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT9_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_CLKSRC6_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_8_HSDIVOUT0_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_CLKSRC7_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_12_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_INPUT00_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_INPUT01_CLK,
		CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_INPUT02_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_DCC_INPUT10_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_3_CLOCKS,											   AM62X_DEV_DCC3_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_CLKSRC0_CLK,
		CLK_AM62X_BOARD_0_GPMC0_CLKLB_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_CLKSRC1_CLK,
		CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_CLKSRC2_CLK,
		CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_CLKSRC3_CLK,
		CLK_AM62X_K3_DPHY_RX_MAIN_0_PPI_RX_BYTE_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_CLKSRC4_CLK,
		CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_CLKSRC5_CLK,
		CLK_AM62X_BOARD_0_RMII1_REF_CLK_OUT,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_CLKSRC6_CLK,
		CLK_AM62X_BOARD_0_RGMII1_RXC_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_CLKSRC7_CLK,
		CLK_AM62X_CLK_32K_RC_SEL_OUT0,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_INPUT00_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_INPUT01_CLK,
		CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_INPUT02_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_DCC_INPUT10_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_4_CLOCKS,											   AM62X_DEV_DCC4_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_CLKSRC0_CLK,
		CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT8_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_CLKSRC1_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT4_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_CLKSRC2_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_CLKSRC3_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT3_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_CLKSRC4_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT4_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_CLKSRC5_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_16_HSDIVOUT0_CLK,
		4),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_CLKSRC6_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_17_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_CLKSRC7_CLK,
		CLK_AM62X_BOARD_0_RGMII2_RXC_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_INPUT00_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_INPUT01_CLK,
		CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_INPUT02_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_DCC_INPUT10_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_5_CLOCKS,											   AM62X_DEV_DCC5_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DEBUGSS_K3_WRAP_CV0_MAIN_0_CLOCKS,									   AM62X_DEV_DEBUGSS_WRAP0_ATB_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_DEBUGSS_K3_WRAP_CV0_MAIN_0_CLOCKS,									   AM62X_DEV_DEBUGSS_WRAP0_CORE_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_DEBUGSS_K3_WRAP_CV0_MAIN_0_CLOCKS,									   AM62X_DEV_DEBUGSS_WRAP0_JTAG_TCK,
		CLK_AM62X_BOARD_0_TCK_OUT,
		1),
	DEV_CLK(AM62X_DEV_DEBUGSS_K3_WRAP_CV0_MAIN_0_CLOCKS,									   AM62X_DEV_DEBUGSS_WRAP0_TREXPT_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT3_CLK,
		1),
	DEV_CLK_OUTPUT(AM62X_DEV_DEBUGSS_K3_WRAP_CV0_MAIN_0_CLOCKS,								   AM62X_DEV_DEBUGSS_WRAP0_CSTPIU_TRACECLK,
		       CLK_AM62X_DEBUGSS_K3_WRAP_CV0_MAIN_0_CSTPIU_TRACECLK),
	DEV_CLK(AM62X_DEV_DMSS_AM62_MAIN_0_BCDMA_0_CLOCKS,									   AM62X_DEV_DMASS0_BCDMA_0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DMSS_AM62_MAIN_0_CBASS_0_CLOCKS,									   AM62X_DEV_DMASS0_CBASS_0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DMSS_AM62_MAIN_0_INTAGGR_0_CLOCKS,									   AM62X_DEV_DMASS0_INTAGGR_0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DMSS_AM62_MAIN_0_IPCSS_0_CLOCKS,									   AM62X_DEV_DMASS0_IPCSS_0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DMSS_AM62_MAIN_0_PKTDMA_0_CLOCKS,									   AM62X_DEV_DMASS0_PKTDMA_0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,										   AM62X_DEV_TIMER0_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_TCLK_CLK,
		    CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT0,										   1,
		    16),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,
		       AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,
		       AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,
		       AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,								   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,
		       AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,								   1,
		       9),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_0_CLOCKS,									   AM62X_DEV_TIMER0_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MAIN_0_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,										   AM62X_DEV_TIMER1_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_TCLK_CLK,
		    CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT1,										   1,
		    16),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,
		       AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,
		       AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,
		       AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,								   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,
		       AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,								   1,
		       9),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_1_CLOCKS,									   AM62X_DEV_TIMER1_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MAIN_1_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,										   AM62X_DEV_TIMER2_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_TCLK_CLK,
		    CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT2,										   1,
		    16),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,
		       AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,
		       AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,
		       AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,								   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,
		       AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,								   1,
		       9),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_2_CLOCKS,									   AM62X_DEV_TIMER2_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MAIN_2_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,										   AM62X_DEV_TIMER3_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_TCLK_CLK,
		    CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT3,										   1,
		    16),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,
		       AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,
		       AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,
		       AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,								   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,
		       AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,								   1,
		       9),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_3_CLOCKS,									   AM62X_DEV_TIMER3_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MAIN_3_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,										   AM62X_DEV_TIMER4_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_TCLK_CLK,
		    CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT4,										   1,
		    16),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,
		       AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,
		       AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,
		       AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,								   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,
		       AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,								   1,
		       9),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_4_CLOCKS,									   AM62X_DEV_TIMER4_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MAIN_4_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,										   AM62X_DEV_TIMER5_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_TCLK_CLK,
		    CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT5,										   1,
		    16),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,
		       AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,
		       AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,
		       AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,								   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,
		       AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,								   1,
		       9),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_5_CLOCKS,									   AM62X_DEV_TIMER5_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MAIN_5_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,										   AM62X_DEV_TIMER6_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_TCLK_CLK,
		    CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT6,										   1,
		    16),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,
		       AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,
		       AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,
		       AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,								   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,
		       AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,								   1,
		       9),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_6_CLOCKS,									   AM62X_DEV_TIMER6_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MAIN_6_TIMER_PWM),
	DEV_CLK(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,										   AM62X_DEV_TIMER7_TIMER_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_TCLK_CLK,
		    CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT7,										   1,
		    16),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,
		       AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,
		       AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,
		       AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK,								   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,
		       AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK,								   1,
		       9),
	DEV_CLK_OUTPUT(AM62X_DEV_DMTIMER_DMC1MS_MAIN_7_CLOCKS,									   AM62X_DEV_TIMER7_TIMER_PWM,
		       CLK_AM62X_DMTIMER_DMC1MS_MAIN_7_TIMER_PWM),
	DEV_CLK(AM62X_DEV_ECAP_MAIN_0_CLOCKS,											   AM62X_DEV_ECAP0_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_ECAP_MAIN_1_CLOCKS,											   AM62X_DEV_ECAP1_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_ECAP_MAIN_2_CLOCKS,											   AM62X_DEV_ECAP2_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_ELM_MAIN_0_CLOCKS,											   AM62X_DEV_ELM0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,										   AM62X_DEV_MMCSD0_EMMCSDSS_IO_CLK_I,
		    CLK_AM62X_MAIN_EMMCSD0_IO_CLKLB_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,									   AM62X_DEV_MMCSD0_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC0_CLKLB_OUT,
		       CLK_AM62X_BOARD_0_MMC0_CLKLB_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,									   AM62X_DEV_MMCSD0_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC0_CLK_OUT,
		       CLK_AM62X_BOARD_0_MMC0_CLK_OUT,										   1,
		       1),
	DEV_CLK(AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,										   AM62X_DEV_MMCSD0_EMMCSDSS_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,										   AM62X_DEV_MMCSD0_EMMCSDSS_XIN_CLK,
		    CLK_AM62X_MAIN_EMMCSD0_REFCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,
		       AM62X_DEV_MMCSD0_EMMCSDSS_XIN_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,
		       AM62X_DEV_MMCSD0_EMMCSDSS_XIN_CLK_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK,								   1,
		       1),
	DEV_CLK_OUTPUT(AM62X_DEV_EMMCSD8SS_MAIN_0_CLOCKS,									   AM62X_DEV_MMCSD0_EMMCSDSS_IO_CLK_O,
		       CLK_AM62X_EMMCSD8SS_MAIN_0_EMMCSDSS_IO_CLK_O),
	DEV_CLK_MUX(AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,										   AM62X_DEV_MMCSD1_EMMCSDSS_IO_CLK_I,
		    CLK_AM62X_MAIN_EMMCSD1_IO_CLKLB_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,									   AM62X_DEV_MMCSD1_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC1_CLKLB_OUT,
		       CLK_AM62X_BOARD_0_MMC1_CLKLB_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,									   AM62X_DEV_MMCSD1_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC1_CLK_OUT,
		       CLK_AM62X_BOARD_0_MMC1_CLK_OUT,										   1,
		       1),
	DEV_CLK(AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,										   AM62X_DEV_MMCSD1_EMMCSDSS_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,										   AM62X_DEV_MMCSD1_EMMCSDSS_XIN_CLK,
		    CLK_AM62X_MAIN_EMMCSD1_REFCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,
		       AM62X_DEV_MMCSD1_EMMCSDSS_XIN_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,
		       AM62X_DEV_MMCSD1_EMMCSDSS_XIN_CLK_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK,								   1,
		       1),
	DEV_CLK_OUTPUT(AM62X_DEV_EMMCSD4SS_MAIN_0_CLOCKS,									   AM62X_DEV_MMCSD1_EMMCSDSS_IO_CLK_O,
		       CLK_AM62X_EMMCSD4SS_MAIN_0_EMMCSDSS_IO_CLK_O),
	DEV_CLK(AM62X_DEV_EQEP_T2_MAIN_0_CLOCKS,										   AM62X_DEV_EQEP0_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_EQEP_T2_MAIN_1_CLOCKS,										   AM62X_DEV_EQEP1_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_GTC0_GTC_CLK,
		    CLK_AM62X_MAIN_GTCCLK_SEL_OUT0,										   1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK,								   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,							   1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,							   1,
		       7),
	DEV_CLK_MUX(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_GTC0_VBUSP_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,											   4,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_GTC0_VBUSP_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,								   4,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_GTC_R10_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_GTC0_VBUSP_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,								   4,
		       1),
	DEV_CLK(AM62X_DEV_EQEP_T2_MAIN_2_CLOCKS,										   AM62X_DEV_EQEP2_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_ESM_AM64_MAIN_MAIN_0_CLOCKS,										   AM62X_DEV_ESM0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_FSS_UL_MAIN_0_FSAS_0_CLOCKS,										   AM62X_DEV_FSS0_FSAS_0_GCLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,										   AM62X_DEV_FSS0_OSPI_0_OSPI_DQS_CLK,
		CLK_AM62X_BOARD_0_OSPI0_DQS_OUT,
		1),
	DEV_CLK(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,										   AM62X_DEV_FSS0_OSPI_0_OSPI_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK_MUX(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,									   AM62X_DEV_FSS0_OSPI_0_OSPI_ICLK_CLK,
		    CLK_AM62X_MAIN_OSPI_LOOPBACK_CLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,									   AM62X_DEV_FSS0_OSPI_0_OSPI_ICLK_CLK_PARENT_BOARD_0_OSPI0_DQS_OUT,
		       CLK_AM62X_BOARD_0_OSPI0_DQS_OUT,										   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,									   AM62X_DEV_FSS0_OSPI_0_OSPI_ICLK_CLK_PARENT_BOARD_0_OSPI0_LBCLKO_OUT,
		       CLK_AM62X_BOARD_0_OSPI0_LBCLKO_OUT,									   1,
		       1),
	DEV_CLK(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,										   AM62X_DEV_FSS0_OSPI_0_OSPI_PCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK_MUX(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,									   AM62X_DEV_FSS0_OSPI_0_OSPI_RCLK_CLK,
		    CLK_AM62X_MAIN_OSPI_REF_CLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,
		       AM62X_DEV_FSS0_OSPI_0_OSPI_RCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT1_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT1_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,
		       AM62X_DEV_FSS0_OSPI_0_OSPI_RCLK_CLK_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT5_CLK,
		       CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT5_CLK,								   1,
		       1),
	DEV_CLK_OUTPUT(AM62X_DEV_FSS_UL_MAIN_0_OSPI_0_CLOCKS,									   AM62X_DEV_FSS0_OSPI_0_OSPI_OCLK_CLK,
		       CLK_AM62X_FSS_UL_MAIN_0_OSPI_0_OSPI_OCLK_CLK),
	DEV_CLK(AM62X_DEV_GIC500SS_1_4_MAIN_0_CLOCKS,										   AM62X_DEV_GICSS0_VCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_GPIO_144_MAIN_0_CLOCKS,										   AM62X_DEV_GPIO0_MMR_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_GPIO_144_MAIN_1_CLOCKS,										   AM62X_DEV_GPIO1_MMR_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_GPMC_MAIN_0_CLOCKS,										   AM62X_DEV_GPMC0_FUNC_CLK,
		    CLK_AM62X_MAIN_GPMC_FCLK_SEL_OUT0,										   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_GPMC_MAIN_0_CLOCKS,										   AM62X_DEV_GPMC0_FUNC_CLK_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT3_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_GPMC_MAIN_0_CLOCKS,										   AM62X_DEV_GPMC0_FUNC_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT7_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_GPMC_MAIN_0_CLOCKS,											   AM62X_DEV_GPMC0_PI_GPMC_RET_CLK,
		CLK_AM62X_BOARD_0_GPMC0_CLKLB_OUT,
		1),
	DEV_CLK(AM62X_DEV_GPMC_MAIN_0_CLOCKS,											   AM62X_DEV_GPMC0_VBUSM_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_GPMC_MAIN_0_CLOCKS,										   AM62X_DEV_GPMC0_PO_GPMC_DEV_CLK,
		       CLK_AM62X_GPMC_MAIN_0_PO_GPMC_DEV_CLK),
	DEV_CLK_MUX(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_CORE_CLK,
		    CLK_AM62X_MAIN_ICSSM_CORE_CLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_CORE_CLK_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT0_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_CORE_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT9_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT9_CLK,								   1,
		       1),
	DEV_CLK_MUX(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_IEP_CLK,
		    CLK_AM62X_MAIN_ICSSM_IEPCLK_SEL_OUT0,									   1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_IEP_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_IEP_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK,								   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_IEP_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_IEP_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,										   AM62X_DEV_ICSSM0_IEP_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,
		       AM62X_DEV_ICSSM0_IEP_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,							   1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,
		       AM62X_DEV_ICSSM0_IEP_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,							   1,
		       7),
	DEV_CLK(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,											   AM62X_DEV_ICSSM0_UCLK_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_ICSS_M_MAIN_0_CLOCKS,											   AM62X_DEV_ICSSM0_VCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_K3_LED2VBUS_MAIN_0_CLOCKS,										   AM62X_DEV_LED0_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_K3_DDPA_MAIN_0_CLOCKS,										   AM62X_DEV_DDPA0_DDPA_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_K3_EPWM_MAIN_0_CLOCKS,										   AM62X_DEV_EPWM0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_K3_EPWM_MAIN_1_CLOCKS,										   AM62X_DEV_EPWM1_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_K3_EPWM_MAIN_2_CLOCKS,										   AM62X_DEV_EPWM2_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_K3VTM_N16FFC_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_VTM0_FIX_REF2_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_K3VTM_N16FFC_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_VTM0_FIX_REF_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK_MUX(AM62X_DEV_K3VTM_N16FFC_WKUP_0_CLOCKS,									   AM62X_DEV_WKUP_VTM0_VBUSP_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,											   4,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_K3VTM_N16FFC_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_VTM0_VBUSP_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,								   4,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_K3VTM_N16FFC_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_VTM0_VBUSP_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,								   4,
		       1),
	DEV_CLK_MUX(AM62X_DEV_MCANSS_MAIN_0_CLOCKS,										   AM62X_DEV_MCAN0_MCANSS_CCLK_CLK,
		    CLK_AM62X_MAIN_MCANN_CLK_SEL_OUT0,										   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MAIN_0_CLOCKS,
		       AM62X_DEV_MCAN0_MCANSS_CCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT4_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT4_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MAIN_0_CLOCKS,										   AM62X_DEV_MCAN0_MCANSS_CCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT,									   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MAIN_0_CLOCKS,										   AM62X_DEV_MCAN0_MCANSS_CCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCANSS_MAIN_0_CLOCKS,										   AM62X_DEV_MCAN0_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       3),
	DEV_CLK(AM62X_DEV_MCANSS_MAIN_0_CLOCKS,											   AM62X_DEV_MCAN0_MCANSS_HCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_0_CLOCKS,										   AM62X_DEV_I2C0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_0_CLOCKS,										   AM62X_DEV_I2C0_PISCL,
		CLK_AM62X_BOARD_0_I2C0_SCL_OUT,
		1),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_0_CLOCKS,										   AM62X_DEV_I2C0_PISYS_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_MSHSI2C_MAIN_0_CLOCKS,										   AM62X_DEV_I2C0_PORSCL,
		       CLK_AM62X_MSHSI2C_MAIN_0_PORSCL),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_1_CLOCKS,										   AM62X_DEV_I2C1_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_1_CLOCKS,										   AM62X_DEV_I2C1_PISCL,
		CLK_AM62X_BOARD_0_I2C1_SCL_OUT,
		1),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_1_CLOCKS,										   AM62X_DEV_I2C1_PISYS_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_MSHSI2C_MAIN_1_CLOCKS,										   AM62X_DEV_I2C1_PORSCL,
		       CLK_AM62X_MSHSI2C_MAIN_1_PORSCL),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_2_CLOCKS,										   AM62X_DEV_I2C2_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_2_CLOCKS,										   AM62X_DEV_I2C2_PISCL,
		CLK_AM62X_BOARD_0_I2C2_SCL_OUT,
		1),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_2_CLOCKS,										   AM62X_DEV_I2C2_PISYS_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_MSHSI2C_MAIN_2_CLOCKS,										   AM62X_DEV_I2C2_PORSCL,
		       CLK_AM62X_MSHSI2C_MAIN_2_PORSCL),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_3_CLOCKS,										   AM62X_DEV_I2C3_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_3_CLOCKS,										   AM62X_DEV_I2C3_PISCL,
		CLK_AM62X_BOARD_0_I2C3_SCL_OUT,
		1),
	DEV_CLK(AM62X_DEV_MSHSI2C_MAIN_3_CLOCKS,										   AM62X_DEV_I2C3_PISYS_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_MSHSI2C_MAIN_3_CLOCKS,										   AM62X_DEV_I2C3_PORSCL,
		       CLK_AM62X_MSHSI2C_MAIN_3_PORSCL),
	DEV_CLK(AM62X_DEV_MCRC64_MAIN_0_CLOCKS,											   AM62X_DEV_MCRC64_0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_RTCSS_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_RTCSS0_ANA_OSC32K_CLK,
		    CLK_AM62X_RTC_CLK_SEL_OUT0,											   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_RTCSS_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_RTCSS0_ANA_OSC32K_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTCSS_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_RTCSS0_ANA_OSC32K_CLK_PARENT_RTC_CLK_SEL_DIV_CLKOUT,
		       CLK_AM62X_RTC_CLK_SEL_DIV_CLKOUT,									   1,
		       1),
	DEV_CLK_MUX(AM62X_DEV_RTCSS_WKUP_0_CLOCKS,										   AM62X_DEV_WKUP_RTCSS0_VCLK_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,											   8,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_RTCSS_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_RTCSS0_VCLK_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,								   8,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTCSS_WKUP_0_CLOCKS,
		       AM62X_DEV_WKUP_RTCSS0_VCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,								   8,
		       1),
	DEV_CLK_MUX(AM62X_DEV_PULSAR_UL_WKUP_0_R5_0_CLOCKS,									   AM62X_DEV_WKUP_R5FSS0_CORE0_CPU_CLK,
		    CLK_AM62X_WKUP_CLKSEL_OUT0,											   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_PULSAR_UL_WKUP_0_R5_0_CLOCKS,
		       AM62X_DEV_WKUP_R5FSS0_CORE0_CPU_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_PULSAR_UL_WKUP_0_R5_0_CLOCKS,
		       AM62X_DEV_WKUP_R5FSS0_CORE0_CPU_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_PULSAR_UL_WKUP_0_R5_0_CLOCKS,										   AM62X_DEV_WKUP_R5FSS0_CORE0_INTERFACE_CLK,
		CLK_AM62X_WKUP_CLKSEL_OUT0,
		1),
	DEV_CLK_MUX(AM62X_DEV_RTI_CFG1_MAIN_A53_0_CLOCKS,									   AM62X_DEV_RTI0_RTI_CLK,
		    CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT0,										   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_0_CLOCKS,									   AM62X_DEV_RTI0_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_0_CLOCKS,									   AM62X_DEV_RTI0_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_0_CLOCKS,									   AM62X_DEV_RTI0_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_0_CLOCKS,									   AM62X_DEV_RTI0_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT0_DIV_CLKOUT,
		       CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT0_DIV_CLKOUT,								   1,
		       3),
	DEV_CLK(AM62X_DEV_RTI_CFG1_MAIN_A53_0_CLOCKS,										   AM62X_DEV_RTI0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_RTI_CFG1_MAIN_A53_1_CLOCKS,									   AM62X_DEV_RTI1_RTI_CLK,
		    CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT1,										   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_1_CLOCKS,									   AM62X_DEV_RTI1_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_1_CLOCKS,									   AM62X_DEV_RTI1_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_1_CLOCKS,									   AM62X_DEV_RTI1_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_1_CLOCKS,									   AM62X_DEV_RTI1_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT1_DIV_CLKOUT,
		       CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT1_DIV_CLKOUT,								   1,
		       3),
	DEV_CLK(AM62X_DEV_RTI_CFG1_MAIN_A53_1_CLOCKS,										   AM62X_DEV_RTI1_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_RTI_CFG1_MAIN_A53_2_CLOCKS,									   AM62X_DEV_RTI2_RTI_CLK,
		    CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT2,										   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_2_CLOCKS,									   AM62X_DEV_RTI2_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_2_CLOCKS,									   AM62X_DEV_RTI2_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_2_CLOCKS,									   AM62X_DEV_RTI2_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_2_CLOCKS,									   AM62X_DEV_RTI2_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT2_DIV_CLKOUT,
		       CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT2_DIV_CLKOUT,								   1,
		       3),
	DEV_CLK(AM62X_DEV_RTI_CFG1_MAIN_A53_2_CLOCKS,										   AM62X_DEV_RTI2_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_RTI_CFG1_MAIN_A53_3_CLOCKS,									   AM62X_DEV_RTI3_RTI_CLK,
		    CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT3,										   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_3_CLOCKS,									   AM62X_DEV_RTI3_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_3_CLOCKS,									   AM62X_DEV_RTI3_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_3_CLOCKS,									   AM62X_DEV_RTI3_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_A53_3_CLOCKS,									   AM62X_DEV_RTI3_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT3_DIV_CLKOUT,
		       CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT3_DIV_CLKOUT,								   1,
		       3),
	DEV_CLK(AM62X_DEV_RTI_CFG1_MAIN_A53_3_CLOCKS,										   AM62X_DEV_RTI3_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_RTI_CFG1_MAIN_GPU_CLOCKS,										   AM62X_DEV_RTI15_RTI_CLK,
		    CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT4,										   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_GPU_CLOCKS,									   AM62X_DEV_RTI15_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_GPU_CLOCKS,									   AM62X_DEV_RTI15_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_GPU_CLOCKS,									   AM62X_DEV_RTI15_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_RTI_CFG1_MAIN_GPU_CLOCKS,									   AM62X_DEV_RTI15_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT4_DIV_CLKOUT,
		       CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT4_DIV_CLKOUT,								   1,
		       3),
	DEV_CLK(AM62X_DEV_RTI_CFG1_MAIN_GPU_CLOCKS,										   AM62X_DEV_RTI15_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_0_CLOCKS,								   AM62X_DEV_A53SS0_CORE_0_A53_CORE0_ARM_CLK_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_8_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_1_CLOCKS,								   AM62X_DEV_A53SS0_CORE_1_A53_CORE1_ARM_CLK_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_8_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_2_CLOCKS,								   AM62X_DEV_A53SS0_CORE_2_A53_CORE2_ARM_CLK_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_8_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_A53_3_CLOCKS,								   AM62X_DEV_A53SS0_CORE_3_A53_CORE3_ARM_CLK_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_8_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_SPI_MAIN_0_CLOCKS,											   AM62X_DEV_MCSPI0_CLKSPIREF_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		10),
	DEV_CLK_MUX(AM62X_DEV_SPI_MAIN_0_CLOCKS,										   AM62X_DEV_MCSPI0_IO_CLKSPII_CLK,
		    CLK_AM62X_MAIN_SPI0_MSTR_LP_CLKSEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MAIN_0_CLOCKS,										   AM62X_DEV_MCSPI0_IO_CLKSPII_CLK_PARENT_BOARD_0_SPI0_CLK_OUT,
		       CLK_AM62X_BOARD_0_SPI0_CLK_OUT,										   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MAIN_0_CLOCKS,										   AM62X_DEV_MCSPI0_IO_CLKSPII_CLK_PARENT_SPI_MAIN_0_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MAIN_0_IO_CLKSPIO_CLK,									   1,
		       1),
	DEV_CLK(AM62X_DEV_SPI_MAIN_0_CLOCKS,											   AM62X_DEV_MCSPI0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_OUTPUT(AM62X_DEV_SPI_MAIN_0_CLOCKS,										   AM62X_DEV_MCSPI0_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MAIN_0_IO_CLKSPIO_CLK),
	DEV_CLK(AM62X_DEV_SPI_MAIN_1_CLOCKS,											   AM62X_DEV_MCSPI1_CLKSPIREF_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		10),
	DEV_CLK_MUX(AM62X_DEV_SPI_MAIN_1_CLOCKS,										   AM62X_DEV_MCSPI1_IO_CLKSPII_CLK,
		    CLK_AM62X_MAIN_SPI1_MSTR_LP_CLKSEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MAIN_1_CLOCKS,										   AM62X_DEV_MCSPI1_IO_CLKSPII_CLK_PARENT_BOARD_0_SPI1_CLK_OUT,
		       CLK_AM62X_BOARD_0_SPI1_CLK_OUT,										   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MAIN_1_CLOCKS,										   AM62X_DEV_MCSPI1_IO_CLKSPII_CLK_PARENT_SPI_MAIN_1_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MAIN_1_IO_CLKSPIO_CLK,									   1,
		       1),
	DEV_CLK(AM62X_DEV_SPI_MAIN_1_CLOCKS,											   AM62X_DEV_MCSPI1_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_OUTPUT(AM62X_DEV_SPI_MAIN_1_CLOCKS,										   AM62X_DEV_MCSPI1_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MAIN_1_IO_CLKSPIO_CLK),
	DEV_CLK(AM62X_DEV_SPI_MAIN_2_CLOCKS,											   AM62X_DEV_MCSPI2_CLKSPIREF_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		10),
	DEV_CLK_MUX(AM62X_DEV_SPI_MAIN_2_CLOCKS,										   AM62X_DEV_MCSPI2_IO_CLKSPII_CLK,
		    CLK_AM62X_MAIN_SPI2_MSTR_LP_CLKSEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MAIN_2_CLOCKS,										   AM62X_DEV_MCSPI2_IO_CLKSPII_CLK_PARENT_BOARD_0_SPI2_CLK_OUT,
		       CLK_AM62X_BOARD_0_SPI2_CLK_OUT,										   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_SPI_MAIN_2_CLOCKS,										   AM62X_DEV_MCSPI2_IO_CLKSPII_CLK_PARENT_SPI_MAIN_2_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MAIN_2_IO_CLKSPIO_CLK,									   1,
		       1),
	DEV_CLK(AM62X_DEV_SPI_MAIN_2_CLOCKS,											   AM62X_DEV_MCSPI2_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_OUTPUT(AM62X_DEV_SPI_MAIN_2_CLOCKS,										   AM62X_DEV_MCSPI2_IO_CLKSPIO_CLK,
		       CLK_AM62X_SPI_MAIN_2_IO_CLKSPIO_CLK),
	DEV_CLK_MUX(AM62X_DEV_USART_MAIN_0_CLOCKS,										   AM62X_DEV_UART0_FCLK_CLK,
		    CLK_AM62X_MAIN_USART0_FCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_0_CLOCKS,
		       AM62X_DEV_UART0_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT0,
		       CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT0,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_0_CLOCKS,										   AM62X_DEV_UART0_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USART_MAIN_0_CLOCKS,											   AM62X_DEV_UART0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_SPINLOCK256_MAIN_0_CLOCKS,										   AM62X_DEV_SPINLOCK0_VCLK_CLK,
		CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_USART_MAIN_1_CLOCKS,										   AM62X_DEV_UART1_FCLK_CLK,
		    CLK_AM62X_MAIN_USART1_FCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_1_CLOCKS,
		       AM62X_DEV_UART1_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT1,
		       CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT1,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_1_CLOCKS,										   AM62X_DEV_UART1_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USART_MAIN_1_CLOCKS,											   AM62X_DEV_UART1_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_USART_MAIN_2_CLOCKS,										   AM62X_DEV_UART2_FCLK_CLK,
		    CLK_AM62X_MAIN_USART2_FCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_2_CLOCKS,
		       AM62X_DEV_UART2_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT2,
		       CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT2,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_2_CLOCKS,										   AM62X_DEV_UART2_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USART_MAIN_2_CLOCKS,											   AM62X_DEV_UART2_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_USART_MAIN_3_CLOCKS,										   AM62X_DEV_UART3_FCLK_CLK,
		    CLK_AM62X_MAIN_USART3_FCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_3_CLOCKS,
		       AM62X_DEV_UART3_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT3,
		       CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT3,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_3_CLOCKS,										   AM62X_DEV_UART3_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USART_MAIN_3_CLOCKS,											   AM62X_DEV_UART3_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_USART_MAIN_4_CLOCKS,										   AM62X_DEV_UART4_FCLK_CLK,
		    CLK_AM62X_MAIN_USART4_FCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_4_CLOCKS,
		       AM62X_DEV_UART4_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT4,
		       CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT4,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_4_CLOCKS,										   AM62X_DEV_UART4_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USART_MAIN_4_CLOCKS,											   AM62X_DEV_UART4_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_USART_MAIN_5_CLOCKS,										   AM62X_DEV_UART5_FCLK_CLK,
		    CLK_AM62X_MAIN_USART5_FCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_5_CLOCKS,
		       AM62X_DEV_UART5_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT5,
		       CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT5,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_5_CLOCKS,										   AM62X_DEV_UART5_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USART_MAIN_5_CLOCKS,											   AM62X_DEV_UART5_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN,
		    CLK_AM62X_AUDIO_REFCLKN_OUT0,										   1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_0_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_AHCLKR_POUT,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_1_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_AHCLKR_POUT,								   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_2_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_AHCLKR_POUT,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_0_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_AHCLKX_POUT,								   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_1_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_AHCLKX_POUT,								   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_2_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_AHCLKX_POUT,								   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,								   1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,								   1,
		       7),
	DEV_CLK_MUX(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN,
		    CLK_AM62X_AUDIO_REFCLKN_OUT1,										   1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_0_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_AHCLKR_POUT,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_1_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_AHCLKR_POUT,								   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_2_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_AHCLKR_POUT,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_0_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_AHCLKX_POUT,								   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_1_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_AHCLKX_POUT,								   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_2_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_AHCLKX_POUT,								   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,								   1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,								   1,
		       7),
	DEV_CLK_MUX(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_CLKOUT0_IN,
		    CLK_AM62X_CLKOUT0_CTRL_OUT0,										   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_CLKOUT0_IN_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK5,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,								   5,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_CLKOUT0_IN_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK10,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK,								   10,
		       1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_DDR0_CK0_N_IN,
		CLK_AM62X_BOARD_0_DDR0_CK0_OUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_DDR0_CK0_IN,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_GPMC0_CLKLB_IN,
		CLK_AM62X_GPMC_MAIN_0_PO_GPMC_DEV_CLK,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_GPMC0_CLK_IN,
		CLK_AM62X_GPMC_MAIN_0_PO_GPMC_DEV_CLK,
		1),
	DEV_CLK_MUX(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_GPMC0_FCLK_MUX_IN,
		    CLK_AM62X_MAIN_GPMC_FCLK_SEL_OUT0,										   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_GPMC0_FCLK_MUX_IN_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT3_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT3_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_GPMC0_FCLK_MUX_IN_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT7_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT7_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_I2C0_SCL_IN,
		CLK_AM62X_MSHSI2C_MAIN_0_PORSCL,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_I2C1_SCL_IN,
		CLK_AM62X_MSHSI2C_MAIN_1_PORSCL,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_I2C2_SCL_IN,
		CLK_AM62X_MSHSI2C_MAIN_2_PORSCL,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_I2C3_SCL_IN,
		CLK_AM62X_MSHSI2C_MAIN_3_PORSCL,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP0_ACLKR_IN,
		CLK_AM62X_MCASP_MAIN_0_MCASP_ACLKR_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP0_ACLKX_IN,
		CLK_AM62X_MCASP_MAIN_0_MCASP_ACLKX_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP0_AFSR_IN,
		CLK_AM62X_MCASP_MAIN_0_MCASP_AFSR_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP0_AFSX_IN,
		CLK_AM62X_MCASP_MAIN_0_MCASP_AFSX_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP1_ACLKR_IN,
		CLK_AM62X_MCASP_MAIN_1_MCASP_ACLKR_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP1_ACLKX_IN,
		CLK_AM62X_MCASP_MAIN_1_MCASP_ACLKX_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP1_AFSR_IN,
		CLK_AM62X_MCASP_MAIN_1_MCASP_AFSR_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP1_AFSX_IN,
		CLK_AM62X_MCASP_MAIN_1_MCASP_AFSX_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP2_ACLKR_IN,
		CLK_AM62X_MCASP_MAIN_2_MCASP_ACLKR_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP2_ACLKX_IN,
		CLK_AM62X_MCASP_MAIN_2_MCASP_ACLKX_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP2_AFSR_IN,
		CLK_AM62X_MCASP_MAIN_2_MCASP_AFSR_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCASP2_AFSX_IN,
		CLK_AM62X_MCASP_MAIN_2_MCASP_AFSX_POUT,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_I2C0_SCL_IN,
		CLK_AM62X_MSHSI2C_MCU_0_PORSCL,
		1),
	DEV_CLK_MUX(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_OBSCLK0_IN,
		    CLK_AM62X_MCU_OBSCLK_OUTMUX_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCU_OBSCLK0_IN_PARENT_MCU_OBSCLK_DIV_OUT0,
		       CLK_AM62X_MCU_OBSCLK_DIV_OUT0,										   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCU_OBSCLK0_IN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_SPI0_CLK_IN,
		CLK_AM62X_SPI_MCU_0_IO_CLKSPIO_CLK,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_SPI1_CLK_IN,
		CLK_AM62X_SPI_MCU_1_IO_CLKSPIO_CLK,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_SYSCLKOUT0_IN,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_SYSCLKOUT_CLK,
		4),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_TIMER_IO0_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MCU_0_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_TIMER_IO1_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MCU_1_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_TIMER_IO2_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MCU_2_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MCU_TIMER_IO3_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MCU_3_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MDIO0_MDC_IN,
		CLK_AM62X_CPSW_3GUSS_MAIN_0_MDIO_MDCLK_O,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MMC0_CLKLB_IN,
		CLK_AM62X_EMMCSD8SS_MAIN_0_EMMCSDSS_IO_CLK_O,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MMC1_CLKLB_IN,
		CLK_AM62X_EMMCSD4SS_MAIN_0_EMMCSDSS_IO_CLK_O,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MMC1_CLK_IN,
		CLK_AM62X_EMMCSD4SS_MAIN_0_EMMCSDSS_IO_CLK_O,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MMC2_CLKLB_IN,
		CLK_AM62X_EMMCSD4SS_MAIN_1_EMMCSDSS_IO_CLK_O,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_MMC2_CLK_IN,
		CLK_AM62X_EMMCSD4SS_MAIN_1_EMMCSDSS_IO_CLK_O,
		1),
	DEV_CLK_MUX(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_OBSCLK0_IN,
		    CLK_AM62X_OSBCLK0_DIV_OUT0,											   1,
		    31),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT0_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK,								   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0,									   1,
		       10),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1,
		       CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1,									   1,
		       11),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK,								   1,
		       12),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,								   1,
		       13),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV0_16FFT_MAIN_16_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV0_16FFT_MAIN_16_HSDIVOUT0_CLK,								   1,
		       14),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV0_16FFT_MAIN_17_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV0_16FFT_MAIN_17_HSDIVOUT0_CLK,								   1,
		       15),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		       CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,							   1,
		       16),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       17),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT0_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_A53_DIVH_CLK4_OBSCLK_OUT_CLK,
		       CLK_AM62X_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_A53_DIVH_CLK4_OBSCLK_OUT_CLK,			   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV0_16FFT_MAIN_12_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV0_16FFT_MAIN_12_HSDIVOUT0_CLK,								   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK8,
		       CLK_AM62X_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK,							   8,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT0_CLK_DUP0,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT0_CLK,								   1,
		       7),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       8),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_MAIN_OBSCLK0_MUX_SEL_DIV_CLKOUT,
		       CLK_AM62X_MAIN_OBSCLK0_MUX_SEL_DIV_CLKOUT,								   1,
		       9),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_OSPI0_LBCLKO_IN,
		CLK_AM62X_FSS_UL_MAIN_0_OSPI_0_OSPI_OCLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_RGMII1_TXC_IN,
		CLK_AM62X_CPSW_3GUSS_MAIN_0_RGMII1_TXC_O,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_RGMII2_TXC_IN,
		CLK_AM62X_CPSW_3GUSS_MAIN_0_RGMII2_TXC_O,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_SPI0_CLK_IN,
		CLK_AM62X_SPI_MAIN_0_IO_CLKSPIO_CLK,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_SPI1_CLK_IN,
		CLK_AM62X_SPI_MAIN_1_IO_CLKSPIO_CLK,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_SPI2_CLK_IN,
		CLK_AM62X_SPI_MAIN_2_IO_CLKSPIO_CLK,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_SYSCLKOUT0_IN,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_SYSCLKOUT_CLK,
		4),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TIMER_IO0_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MAIN_0_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TIMER_IO1_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MAIN_1_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TIMER_IO2_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MAIN_2_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TIMER_IO3_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MAIN_3_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TIMER_IO4_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MAIN_4_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TIMER_IO5_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MAIN_5_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TIMER_IO6_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MAIN_6_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TIMER_IO7_IN,
		CLK_AM62X_DMTIMER_DMC1MS_MAIN_7_TIMER_PWM,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_TRC_CLK_IN,
		CLK_AM62X_DEBUGSS_K3_WRAP_CV0_MAIN_0_CSTPIU_TRACECLK,
		1),
	DEV_CLK(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_VOUT0_PCLK_IN,
		CLK_AM62X_K3_DSS_UL_MAIN_0_DPI_1_OUT_CLK,
		1),
	DEV_CLK_MUX(AM62X_DEV_BOARD_0_CLOCKS,											   AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN,
		    CLK_AM62X_WKUP_CLKOUT_SEL_OUT0,										   1,
		    8),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_GLUELOGIC_LFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_LFOSC0_CLKOUT,									   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT2_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT2_CLK,								   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT2_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT2_CLK,								   1,
		       3),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,
		       AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT9_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT9_CLK,								   1,
		       4),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_CLK_32K_RC_SEL_OUT0,
		       CLK_AM62X_CLK_32K_RC_SEL_OUT0,										   1,
		       5),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_GLUELOGIC_RCOSC_CLKOUT,
		       CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,									   1,
		       6),
	DEV_CLK_PARENT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_GLUELOGIC_HFOSC0_CLKOUT_DUP0,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       7),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_CP_GEMAC_CPTS0_RFT_CLK_OUT,
		       CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_CSI0_RXCLKN_OUT,
		       CLK_AM62X_BOARD_0_CSI0_RXCLKN_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_CSI0_RXCLKP_OUT,
		       CLK_AM62X_BOARD_0_CSI0_RXCLKP_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_DDR0_CK0_OUT,
		       CLK_AM62X_BOARD_0_DDR0_CK0_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_GPMC0_CLKLB_OUT,
		       CLK_AM62X_BOARD_0_GPMC0_CLKLB_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_I2C0_SCL_OUT,
		       CLK_AM62X_BOARD_0_I2C0_SCL_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_I2C1_SCL_OUT,
		       CLK_AM62X_BOARD_0_I2C1_SCL_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_I2C2_SCL_OUT,
		       CLK_AM62X_BOARD_0_I2C2_SCL_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_I2C3_SCL_OUT,
		       CLK_AM62X_BOARD_0_I2C3_SCL_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCASP0_ACLKR_OUT,
		       CLK_AM62X_BOARD_0_MCASP0_ACLKR_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCASP0_ACLKX_OUT,
		       CLK_AM62X_BOARD_0_MCASP0_ACLKX_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCASP1_ACLKR_OUT,
		       CLK_AM62X_BOARD_0_MCASP1_ACLKR_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCASP1_ACLKX_OUT,
		       CLK_AM62X_BOARD_0_MCASP1_ACLKX_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCASP2_ACLKR_OUT,
		       CLK_AM62X_BOARD_0_MCASP2_ACLKR_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCASP2_ACLKX_OUT,
		       CLK_AM62X_BOARD_0_MCASP2_ACLKX_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCU_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCU_I2C0_SCL_OUT,
		       CLK_AM62X_BOARD_0_MCU_I2C0_SCL_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCU_SPI0_CLK_OUT,
		       CLK_AM62X_BOARD_0_MCU_SPI0_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MCU_SPI1_CLK_OUT,
		       CLK_AM62X_BOARD_0_MCU_SPI1_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MMC0_CLKLB_OUT,
		       CLK_AM62X_BOARD_0_MMC0_CLKLB_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MMC0_CLK_OUT,
		       CLK_AM62X_BOARD_0_MMC0_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MMC1_CLKLB_OUT,
		       CLK_AM62X_BOARD_0_MMC1_CLKLB_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MMC1_CLK_OUT,
		       CLK_AM62X_BOARD_0_MMC1_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MMC2_CLKLB_OUT,
		       CLK_AM62X_BOARD_0_MMC2_CLKLB_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_MMC2_CLK_OUT,
		       CLK_AM62X_BOARD_0_MMC2_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OSPI0_DQS_OUT,
		       CLK_AM62X_BOARD_0_OSPI0_DQS_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_OSPI0_LBCLKO_OUT,
		       CLK_AM62X_BOARD_0_OSPI0_LBCLKO_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_RGMII1_RXC_OUT,
		       CLK_AM62X_BOARD_0_RGMII1_RXC_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_RGMII1_TXC_OUT,
		       CLK_AM62X_BOARD_0_RGMII1_TXC_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_RGMII2_RXC_OUT,
		       CLK_AM62X_BOARD_0_RGMII2_RXC_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_RGMII2_TXC_OUT,
		       CLK_AM62X_BOARD_0_RGMII2_TXC_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_RMII1_REF_CLK_OUT,
		       CLK_AM62X_BOARD_0_RMII1_REF_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_RMII2_REF_CLK_OUT,
		       CLK_AM62X_BOARD_0_RMII2_REF_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_SPI0_CLK_OUT,
		       CLK_AM62X_BOARD_0_SPI0_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_SPI1_CLK_OUT,
		       CLK_AM62X_BOARD_0_SPI1_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_SPI2_CLK_OUT,
		       CLK_AM62X_BOARD_0_SPI2_CLK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_TCK_OUT,
		       CLK_AM62X_BOARD_0_TCK_OUT),
	DEV_CLK_OUTPUT(AM62X_DEV_BOARD_0_CLOCKS,										   AM62X_DEV_BOARD0_VOUT0_EXTPCLKIN_OUT,
		       CLK_AM62X_BOARD_0_VOUT0_EXTPCLKIN_OUT),
	DEV_CLK_MUX(AM62X_DEV_USART_MAIN_6_CLOCKS,										   AM62X_DEV_UART6_FCLK_CLK,
		    CLK_AM62X_MAIN_USART6_FCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_6_CLOCKS,
		       AM62X_DEV_UART6_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT6,
		       CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT6,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USART_MAIN_6_CLOCKS,										   AM62X_DEV_UART6_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USART_MAIN_6_CLOCKS,											   AM62X_DEV_UART6_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS,										   AM62X_DEV_USB0_BUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS,										   AM62X_DEV_USB0_CFG_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS,										   AM62X_DEV_USB0_USB2_APB_PCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS,									   AM62X_DEV_USB0_USB2_REFCLOCK_CLK,
		    CLK_AM62X_MAIN_USB0_REFCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS,									   AM62X_DEV_USB0_USB2_REFCLOCK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS,
		       AM62X_DEV_USB0_USB2_REFCLOCK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT8_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT8_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USB2SS_16FFC_MAIN_0_CLOCKS,										   AM62X_DEV_USB0_USB2_TAP_TCK,
		CLK_AM62X_BOARD_0_TCK_OUT,
		1),
	DEV_CLK(AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS,										   AM62X_DEV_USB1_BUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS,										   AM62X_DEV_USB1_CFG_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS,										   AM62X_DEV_USB1_USB2_APB_PCLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS,									   AM62X_DEV_USB1_USB2_REFCLOCK_CLK,
		    CLK_AM62X_MAIN_USB1_REFCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS,									   AM62X_DEV_USB1_USB2_REFCLOCK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS,
		       AM62X_DEV_USB1_USB2_REFCLOCK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT8_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT8_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_USB2SS_16FFC_MAIN_1_CLOCKS,										   AM62X_DEV_USB1_USB2_TAP_TCK,
		CLK_AM62X_BOARD_0_TCK_OUT,
		1),
	DEV_CLK(AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_MAIN_0_CLOCKS,								   AM62X_DEV_PBIST0_CLK8_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_MAIN_0_CLOCKS,								   AM62X_DEV_PBIST0_TCLK_CLK,
		CLK_AM62X_BOARD_0_TCK_OUT,
		1),
	DEV_CLK(AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_MAIN_GPU_0_CLOCKS,								   AM62X_DEV_PBIST1_CLK8_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_MAIN_GPU_0_CLOCKS,								   AM62X_DEV_PBIST1_TCLK_CLK,
		CLK_AM62X_BOARD_0_TCK_OUT,
		1),
	DEV_CLK(AM62X_DEV_K3_PBIST_8C28P_4BIT_WRAP_WKUP_0_CLOCKS,								   AM62X_DEV_WKUP_PBIST0_CLK8_CLK,
		CLK_AM62X_WKUP_CLKSEL_OUT0,
		4),
	DEV_CLK(AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_CLOCKS,							   AM62X_DEV_A53SS0_COREPAC_ARM_CLK_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_8_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_CLOCKS,							   AM62X_DEV_A53SS0_PLL_CTRL_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK_OUTPUT(AM62X_DEV_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_CLOCKS,						   AM62X_DEV_A53SS0_A53_DIVH_CLK4_OBSCLK_OUT_CLK,
		       CLK_AM62X_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_A53_DIVH_CLK4_OBSCLK_OUT_CLK),
	DEV_CLK(AM62X_DEV_SAM62_MAIN_PSC_WRAP_MAIN_0_FW_0_CLOCKS,								   AM62X_DEV_PSC0_FW_0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_MAIN_PSC_WRAP_MAIN_0_PSC_0_CLOCKS,								   AM62X_DEV_PSC0_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_MAIN_PSC_WRAP_MAIN_0_PSC_0_CLOCKS,								   AM62X_DEV_PSC0_SLOW_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_DDR_WRAP_MAIN_0_CLOCKS,										   AM62X_DEV_DDR16SS0_DDRSS_DDR_PLL_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_12_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_DDR_WRAP_MAIN_0_CLOCKS,										   AM62X_DEV_DDR16SS0_DDRSS_TCK,
		CLK_AM62X_BOARD_0_TCK_OUT,
		1),
	DEV_CLK(AM62X_DEV_SAM62_DDR_WRAP_MAIN_0_CLOCKS,										   AM62X_DEV_DDR16SS0_PLL_CTRL_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_SAM62_DEBUG_MAIN_CELL_MAIN_0_CLOCKS,									   AM62X_DEV_DEBUGSS0_CFG_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK(AM62X_DEV_SAM62_DEBUG_MAIN_CELL_MAIN_0_CLOCKS,									   AM62X_DEV_DEBUGSS0_DBG_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_SAM62_DEBUG_MAIN_CELL_MAIN_0_CLOCKS,									   AM62X_DEV_DEBUGSS0_SYS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_SAM62_A53_RS_BW_LIMITER_MAIN_0_CLOCKS,								   AM62X_DEV_A53_RS_BW_LIMITER0_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_SAM62_A53_WS_BW_LIMITER_MAIN_0_CLOCKS,								   AM62X_DEV_A53_WS_BW_LIMITER1_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_SAM62_GPU_RS_BW_LIMITER_MAIN_0_CLOCKS,								   AM62X_DEV_GPU_RS_BW_LIMITER2_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_SAM62_GPU_WS_BW_LIMITER_MAIN_0_CLOCKS,								   AM62X_DEV_GPU_WS_BW_LIMITER3_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_SAM62_DM_WAKEUP_DEEPSLEEP_SOURCES_WKUP_0_CLOCKS,							   AM62X_DEV_WKUP_DEEPSLEEP_SOURCES0_CLK_12M_RC_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_SAM62_MCU_16FF_MCU_0_CLOCKS,										   AM62X_DEV_MCU_MCU_16FF0_PLL_CTRL_MCU_CLK24_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK,
		24),
	DEV_CLK(AM62X_DEV_CPT2_AGGREGATOR32_MAIN_400MHZ_CLOCKS,									   AM62X_DEV_CPT2_AGGR1_VCLK_CLK,
		CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK,
		1),
	DEV_CLK(AM62X_DEV_CSI_RX_IF_MAIN_0_CLOCKS,										   AM62X_DEV_CSI_RX_IF0_MAIN_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_CSI_RX_IF_MAIN_0_CLOCKS,										   AM62X_DEV_CSI_RX_IF0_PPI_RX_BYTE_CLK,
		CLK_AM62X_K3_DPHY_RX_MAIN_0_PPI_RX_BYTE_CLK,
		1),
	DEV_CLK(AM62X_DEV_CSI_RX_IF_MAIN_0_CLOCKS,										   AM62X_DEV_CSI_RX_IF0_VBUS_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK(AM62X_DEV_CSI_RX_IF_MAIN_0_CLOCKS,										   AM62X_DEV_CSI_RX_IF0_VP_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_CLKSRC0_CLK,
		CLK_AM62X_BOARD_0_VOUT0_EXTPCLKIN_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_CLKSRC1_CLK,
		CLK_AM62X_BOARD_0_MCASP0_ACLKX_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_CLKSRC2_CLK,
		CLK_AM62X_BOARD_0_MCASP0_ACLKR_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_CLKSRC3_CLK,
		CLK_AM62X_BOARD_0_MCASP1_ACLKX_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_CLKSRC4_CLK,
		CLK_AM62X_BOARD_0_MCASP1_ACLKR_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_CLKSRC5_CLK,
		CLK_AM62X_BOARD_0_MCASP2_ACLKX_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_CLKSRC6_CLK,
		CLK_AM62X_BOARD_0_MCASP2_ACLKR_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_CLKSRC7_CLK,
		CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_INPUT00_CLK,
		CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_INPUT01_CLK,
		CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_INPUT02_CLK,
		CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_DCC_INPUT10_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
	DEV_CLK(AM62X_DEV_DCC2_MAIN_6_CLOCKS,											   AM62X_DEV_DCC6_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_MUX(AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,										   AM62X_DEV_MMCSD2_EMMCSDSS_IO_CLK_I,
		    CLK_AM62X_MAIN_EMMCSD2_IO_CLKLB_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,									   AM62X_DEV_MMCSD2_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC2_CLKLB_OUT,
		       CLK_AM62X_BOARD_0_MMC2_CLKLB_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,									   AM62X_DEV_MMCSD2_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC2_CLK_OUT,
		       CLK_AM62X_BOARD_0_MMC2_CLK_OUT,										   1,
		       1),
	DEV_CLK(AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,										   AM62X_DEV_MMCSD2_EMMCSDSS_VBUS_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_MUX(AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,										   AM62X_DEV_MMCSD2_EMMCSDSS_XIN_CLK,
		    CLK_AM62X_MAIN_EMMCSD2_REFCLK_SEL_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,
		       AM62X_DEV_MMCSD2_EMMCSDSS_XIN_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,
		       AM62X_DEV_MMCSD2_EMMCSDSS_XIN_CLK_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK,
		       CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK,								   1,
		       1),
	DEV_CLK_OUTPUT(AM62X_DEV_EMMCSD4SS_MAIN_1_CLOCKS,									   AM62X_DEV_MMCSD2_EMMCSDSS_IO_CLK_O,
		       CLK_AM62X_EMMCSD4SS_MAIN_1_EMMCSDSS_IO_CLK_O),
	DEV_CLK(AM62X_DEV_K3_DPHY_RX_MAIN_0_CLOCKS,										   AM62X_DEV_DPHY_RX0_IO_RX_CL_L_M,
		CLK_AM62X_BOARD_0_CSI0_RXCLKN_OUT,
		1),
	DEV_CLK(AM62X_DEV_K3_DPHY_RX_MAIN_0_CLOCKS,										   AM62X_DEV_DPHY_RX0_IO_RX_CL_L_P,
		CLK_AM62X_BOARD_0_CSI0_RXCLKP_OUT,
		1),
	DEV_CLK(AM62X_DEV_K3_DPHY_RX_MAIN_0_CLOCKS,										   AM62X_DEV_DPHY_RX0_JTAG_TCK,
		CLK_AM62X_BOARD_0_TCK_OUT,
		1),
	DEV_CLK(AM62X_DEV_K3_DPHY_RX_MAIN_0_CLOCKS,										   AM62X_DEV_DPHY_RX0_MAIN_CLK_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		4),
	DEV_CLK_OUTPUT(AM62X_DEV_K3_DPHY_RX_MAIN_0_CLOCKS,									   AM62X_DEV_DPHY_RX0_PPI_RX_BYTE_CLK,
		       CLK_AM62X_K3_DPHY_RX_MAIN_0_PPI_RX_BYTE_CLK),
	DEV_CLK(AM62X_DEV_K3_DSS_UL_MAIN_0_CLOCKS,										   AM62X_DEV_DSS0_DPI_0_IN_CLK,
		CLK_AM62X_HSDIV0_16FFT_MAIN_16_HSDIVOUT0_CLK,
		1),
	DEV_CLK_MUX(AM62X_DEV_K3_DSS_UL_MAIN_0_CLOCKS,										   AM62X_DEV_DSS0_DPI_1_IN_CLK,
		    CLK_AM62X_MAIN_DSS_DPI1_OUT0,										   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_K3_DSS_UL_MAIN_0_CLOCKS,
		       AM62X_DEV_DSS0_DPI_1_IN_CLK_PARENT_HSDIV0_16FFT_MAIN_17_HSDIVOUT0_CLK,
		       CLK_AM62X_HSDIV0_16FFT_MAIN_17_HSDIVOUT0_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_K3_DSS_UL_MAIN_0_CLOCKS,									   AM62X_DEV_DSS0_DPI_1_IN_CLK_PARENT_BOARD_0_VOUT0_EXTPCLKIN_OUT,
		       CLK_AM62X_BOARD_0_VOUT0_EXTPCLKIN_OUT,									   1,
		       1),
	DEV_CLK(AM62X_DEV_K3_DSS_UL_MAIN_0_CLOCKS,										   AM62X_DEV_DSS0_DSS_FUNC_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_K3_DSS_UL_MAIN_0_CLOCKS,									   AM62X_DEV_DSS0_DPI_1_OUT_CLK,
		       CLK_AM62X_K3_DSS_UL_MAIN_0_DPI_1_OUT_CLK),
	DEV_CLK(AM62X_DEV_K3_GPU_AXE116M_MAIN_0_CLOCKS,										   AM62X_DEV_GPU0_GPU_CLK,
		CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT4_CLK,
		1),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_AUX_CLK,
		    CLK_AM62X_MCASPN_CLKSEL_AUXCLK_OUT0,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_AUX_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_AUX_CLK_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_0_CLOCKS,											   AM62X_DEV_MCASP0_MCASP_ACLKR_PIN,
		CLK_AM62X_BOARD_0_MCASP0_ACLKR_OUT,
		1),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_0_CLOCKS,											   AM62X_DEV_MCASP0_MCASP_ACLKX_PIN,
		CLK_AM62X_BOARD_0_MCASP0_ACLKX_OUT,
		1),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN,
		    CLK_AM62X_MCASPN_AHCLKSEL_AHCLKR_OUT0,									   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,
		       AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,
		       AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT,									   1,
		       3),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN,
		    CLK_AM62X_MCASPN_AHCLKSEL_AHCLKX_OUT0,									   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,
		       AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,
		       AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT,									   1,
		       3),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_0_CLOCKS,											   AM62X_DEV_MCASP0_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_ACLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_ACLKR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_ACLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_ACLKX_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AFSR_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_AFSR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AFSX_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_AFSX_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_AHCLKR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_0_CLOCKS,										   AM62X_DEV_MCASP0_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_0_MCASP_AHCLKX_POUT),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_AUX_CLK,
		    CLK_AM62X_MCASPN_CLKSEL_AUXCLK_OUT1,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_AUX_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_AUX_CLK_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_1_CLOCKS,											   AM62X_DEV_MCASP1_MCASP_ACLKR_PIN,
		CLK_AM62X_BOARD_0_MCASP1_ACLKR_OUT,
		1),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_1_CLOCKS,											   AM62X_DEV_MCASP1_MCASP_ACLKX_PIN,
		CLK_AM62X_BOARD_0_MCASP1_ACLKX_OUT,
		1),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN,
		    CLK_AM62X_MCASPN_AHCLKSEL_AHCLKR_OUT1,									   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,
		       AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,
		       AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT,									   1,
		       3),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN,
		    CLK_AM62X_MCASPN_AHCLKSEL_AHCLKX_OUT1,									   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,
		       AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,
		       AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT,									   1,
		       3),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_1_CLOCKS,											   AM62X_DEV_MCASP1_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_ACLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_ACLKR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_ACLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_ACLKX_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AFSR_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_AFSR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AFSX_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_AFSX_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_AHCLKR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_1_CLOCKS,										   AM62X_DEV_MCASP1_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_1_MCASP_AHCLKX_POUT),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_AUX_CLK,
		    CLK_AM62X_MCASPN_CLKSEL_AUXCLK_OUT2,									   1,
		    2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_AUX_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,
		       CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK,								   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_AUX_CLK_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,
		       CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK,								   1,
		       1),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_2_CLOCKS,											   AM62X_DEV_MCASP2_MCASP_ACLKR_PIN,
		CLK_AM62X_BOARD_0_MCASP2_ACLKR_OUT,
		1),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_2_CLOCKS,											   AM62X_DEV_MCASP2_MCASP_ACLKX_PIN,
		CLK_AM62X_BOARD_0_MCASP2_ACLKX_OUT,
		1),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN,
		    CLK_AM62X_MCASPN_AHCLKSEL_AHCLKR_OUT2,									   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,
		       AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,
		       AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT,									   1,
		       3),
	DEV_CLK_MUX(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN,
		    CLK_AM62X_MCASPN_AHCLKSEL_AHCLKX_OUT2,									   1,
		    4),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT,									   1,
		       0),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT,
		       CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT,									   1,
		       1),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,
		       AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT,									   1,
		       2),
	DEV_CLK_PARENT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,
		       AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT,
		       CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT,									   1,
		       3),
	DEV_CLK(AM62X_DEV_MCASP_MAIN_2_CLOCKS,											   AM62X_DEV_MCASP2_VBUSP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		2),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_ACLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_ACLKR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_ACLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_ACLKX_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AFSR_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_AFSR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AFSX_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_AFSX_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AHCLKR_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_AHCLKR_POUT),
	DEV_CLK_OUTPUT(AM62X_DEV_MCASP_MAIN_2_CLOCKS,										   AM62X_DEV_MCASP2_MCASP_AHCLKX_POUT,
		       CLK_AM62X_MCASP_MAIN_2_MCASP_AHCLKX_POUT),
	DEV_CLK(AM62X_DEV_SMS_MAIN_0_CORTEX_M4F_1_CLOCKS,									   AM62X_DEV_HSM0_DAP_CLK,
		CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK,
		1),
};
static struct dev_clk MAIN_dev_clk[839] __attribute__((__section__(".bss.devgroup.MAIN")));
static const struct dev_clk_data TIFS_INTERNAL_dev_clk_data[1] __attribute__((__section__(".const.devgroup.TIFS_INTERNAL"))) = {
	DEV_CLK(AM62X_DEV_DMSS_AM62_MAIN_0_RINGACC_0_CLOCKS, AM62X_DEV_DMASS0_RINGACC_0_CLK, CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK, 1),
};
static struct dev_clk TIFS_INTERNAL_dev_clk[1] __attribute__((__section__(".bss.devgroup.TIFS_INTERNAL")));

const struct devgroup soc_devgroups[AM62X_PM_DEVGRP_RANGE_ID_MAX] = {
	[PM_DEVGRP_01] =   {
		.dev_clk_data	= MCU_WAKEUP_dev_clk_data,
		.dev_clk	= MCU_WAKEUP_dev_clk,
		.clk_idx	= 230U,
	},
	[PM_DEVGRP_00] =   {
		.dev_clk_data	= MAIN_dev_clk_data,
		.dev_clk	= MAIN_dev_clk,
		.clk_idx	= 2U,
	},
	[PM_DEVGRP_DMSC] = {
		.dev_clk_data	= TIFS_INTERNAL_dev_clk_data,
		.dev_clk	= TIFS_INTERNAL_dev_clk,
		.clk_idx	= 1U,
	},
};
const size_t soc_devgroup_count = ARRAY_SIZE(soc_devgroups);

const struct soc_device_data *const soc_psc_multiple_domains[0] = {
};

const struct dev_data *const soc_device_data_arr[AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD + 1U] = {
	[AM62X_DEV_CMP_EVENT_INTROUTER0] = &am62x_dev_am62_cmp_event_introuter_main_0,
	[AM62X_DEV_DBGSUSPENDROUTER0] = &am62x_dev_am62_dbgsuspendrouter_main_0,
	[AM62X_DEV_MAIN_GPIOMUX_INTROUTER0] = &am62x_dev_am62_main_gpiomux_introuter_main_0,
	[AM62X_DEV_WKUP_MCU_GPIOMUX_INTROUTER0] = &am62x_dev_am62_mcu_gpiomux_introuter_wkup_0,
	[AM62X_DEV_TIMESYNC_EVENT_ROUTER0] = &am62x_dev_am62_timesync_event_introuter_main_0,
	[AM62X_DEV_MCU_M4FSS0] = &am62x_dev_blazar_mcu_0,
	[AM62X_DEV_MCU_M4FSS0_CBASS_0] = &am62x_dev_blazar_mcu_0_cbass_0,
	[AM62X_DEV_MCU_M4FSS0_CORE0] = &am62x_dev_blazar_mcu_0_cortex_m4_0,
	[AM62X_DEV_CPSW0] = &am62x_dev_cpsw_3guss_main_0,
	[AM62X_DEV_CPT2_AGGR0] = &am62x_dev_cpt2_aggregator32_main_250MHz,
	[AM62X_DEV_STM0] = &am62x_dev_cxstm500ss_main_0,
	[AM62X_DEV_DCC0] = &am62x_dev_dcc2_main_0,
	[AM62X_DEV_DCC1] = &am62x_dev_dcc2_main_1,
	[AM62X_DEV_DCC2] = &am62x_dev_dcc2_main_2,
	[AM62X_DEV_DCC3] = &am62x_dev_dcc2_main_3,
	[AM62X_DEV_DCC4] = &am62x_dev_dcc2_main_4,
	[AM62X_DEV_DCC5] = &am62x_dev_dcc2_main_5,
	[AM62X_DEV_SMS0] = &am62x_dev_sms_main_0,
	[AM62X_DEV_MCU_DCC0] = &am62x_dev_dcc2_mcu_0,
	[AM62X_DEV_DEBUGSS_WRAP0] = &am62x_dev_debugss_k3_wrap_cv0_main_0,
	[AM62X_DEV_DMASS0] = &am62x_dev_dmss_am62_main_0,
	[AM62X_DEV_DMASS0_BCDMA_0] = &am62x_dev_dmss_am62_main_0_bcdma_0,
	[AM62X_DEV_DMASS0_CBASS_0] = &am62x_dev_dmss_am62_main_0_cbass_0,
	[AM62X_DEV_DMASS0_INTAGGR_0] = &am62x_dev_dmss_am62_main_0_intaggr_0,
	[AM62X_DEV_DMASS0_IPCSS_0] = &am62x_dev_dmss_am62_main_0_ipcss_0,
	[AM62X_DEV_DMASS0_PKTDMA_0] = &am62x_dev_dmss_am62_main_0_pktdma_0,
	[AM62X_DEV_DMASS0_RINGACC_0] = &am62x_dev_dmss_am62_main_0_ringacc_0,
	[AM62X_DEV_MCU_TIMER0] = &am62x_dev_dmtimer_dmc1ms_mcu_0,
	[AM62X_DEV_TIMER0] = &am62x_dev_dmtimer_dmc1ms_main_0,
	[AM62X_DEV_TIMER1] = &am62x_dev_dmtimer_dmc1ms_main_1,
	[AM62X_DEV_TIMER2] = &am62x_dev_dmtimer_dmc1ms_main_2,
	[AM62X_DEV_TIMER3] = &am62x_dev_dmtimer_dmc1ms_main_3,
	[AM62X_DEV_TIMER4] = &am62x_dev_dmtimer_dmc1ms_main_4,
	[AM62X_DEV_TIMER5] = &am62x_dev_dmtimer_dmc1ms_main_5,
	[AM62X_DEV_TIMER6] = &am62x_dev_dmtimer_dmc1ms_main_6,
	[AM62X_DEV_TIMER7] = &am62x_dev_dmtimer_dmc1ms_main_7,
	[AM62X_DEV_MCU_TIMER1] = &am62x_dev_dmtimer_dmc1ms_mcu_1,
	[AM62X_DEV_MCU_TIMER2] = &am62x_dev_dmtimer_dmc1ms_mcu_2,
	[AM62X_DEV_MCU_TIMER3] = &am62x_dev_dmtimer_dmc1ms_mcu_3,
	[AM62X_DEV_ECAP0] = &am62x_dev_ecap_main_0,
	[AM62X_DEV_ECAP1] = &am62x_dev_ecap_main_1,
	[AM62X_DEV_ECAP2] = &am62x_dev_ecap_main_2,
	[AM62X_DEV_ELM0] = &am62x_dev_elm_main_0,
	[AM62X_DEV_EMIF_DATA_ISO_VD] = &am62x_dev_emif_data_iso_VD,
	[AM62X_DEV_MMCSD0] = &am62x_dev_emmcsd8ss_main_0,
	[AM62X_DEV_MMCSD1] = &am62x_dev_emmcsd4ss_main_0,
	[AM62X_DEV_EQEP0] = &am62x_dev_eqep_t2_main_0,
	[AM62X_DEV_EQEP1] = &am62x_dev_eqep_t2_main_1,
	[AM62X_DEV_WKUP_GTC0] = &am62x_dev_gtc_r10_wkup_0,
	[AM62X_DEV_EQEP2] = &am62x_dev_eqep_t2_main_2,
	[AM62X_DEV_ESM0] = &am62x_dev_esm_am64_main_main_0,
	[AM62X_DEV_WKUP_ESM0] = &am62x_dev_esm_am64_mcu_wkup_0,
	[AM62X_DEV_FSS0] = &am62x_dev_fss_ul_main_0,
	[AM62X_DEV_FSS0_FSAS_0] = &am62x_dev_fss_ul_main_0_fsas_0,
	[AM62X_DEV_FSS0_OSPI_0] = &am62x_dev_fss_ul_main_0_ospi_0,
	[AM62X_DEV_GICSS0] = &am62x_dev_gic500ss_1_4_main_0,
	[AM62X_DEV_GPIO0] = &am62x_dev_gpio_144_main_0,
	[AM62X_DEV_GPIO1] = &am62x_dev_gpio_144_main_1,
	[AM62X_DEV_MCU_GPIO0] = &am62x_dev_gpio_144_mcu_0,
	[AM62X_DEV_GPMC0] = &am62x_dev_gpmc_main_0,
	[AM62X_DEV_ICSSM0] = &am62x_dev_icss_m_main_0,
	[AM62X_DEV_LED0] = &am62x_dev_k3_led2vbus_main_0,
	[AM62X_DEV_DDPA0] = &am62x_dev_k3_ddpa_main_0,
	[AM62X_DEV_EPWM0] = &am62x_dev_k3_epwm_main_0,
	[AM62X_DEV_EPWM1] = &am62x_dev_k3_epwm_main_1,
	[AM62X_DEV_EPWM2] = &am62x_dev_k3_epwm_main_2,
	[AM62X_DEV_WKUP_VTM0] = &am62x_dev_k3vtm_n16ffc_wkup_0,
	[AM62X_DEV_MAILBOX0] = &am62x_dev_mailbox1_main_0,
	[AM62X_DEV_MAIN2MCU_VD] = &am62x_dev_main2mcu_VD,
	[AM62X_DEV_MCAN0] = &am62x_dev_mcanss_main_0,
	[AM62X_DEV_MCU_MCRC64_0] = &am62x_dev_mcrc64_mcu_0,
	[AM62X_DEV_MCU2MAIN_VD] = &am62x_dev_mcu2main_VD,
	[AM62X_DEV_I2C0] = &am62x_dev_mshsi2c_main_0,
	[AM62X_DEV_I2C1] = &am62x_dev_mshsi2c_main_1,
	[AM62X_DEV_I2C2] = &am62x_dev_mshsi2c_main_2,
	[AM62X_DEV_I2C3] = &am62x_dev_mshsi2c_main_3,
	[AM62X_DEV_MCU_I2C0] = &am62x_dev_mshsi2c_mcu_0,
	[AM62X_DEV_WKUP_I2C0] = &am62x_dev_mshsi2c_wkup_0,
	[AM62X_DEV_WKUP_TIMER0] = &am62x_dev_dmtimer_dmc1ms_wkup_0,
	[AM62X_DEV_WKUP_TIMER1] = &am62x_dev_dmtimer_dmc1ms_wkup_1,
	[AM62X_DEV_WKUP_UART0] = &am62x_dev_usart_wkup_0,
	[AM62X_DEV_MCRC64_0] = &am62x_dev_mcrc64_main_0,
	[AM62X_DEV_WKUP_RTCSS0] = &am62x_dev_rtcss_wkup_0,
	[AM62X_DEV_WKUP_R5FSS0_SS0] = &am62x_dev_pulsar_ul_wkup_0_cortex_r5_ss_0,
	[AM62X_DEV_WKUP_R5FSS0] = &am62x_dev_pulsar_ul_wkup_0,
	[AM62X_DEV_WKUP_R5FSS0_CORE0] = &am62x_dev_pulsar_ul_wkup_0_R5_0,
	[AM62X_DEV_RTI0] = &am62x_dev_rti_cfg1_main_a53_0,
	[AM62X_DEV_RTI1] = &am62x_dev_rti_cfg1_main_a53_1,
	[AM62X_DEV_RTI2] = &am62x_dev_rti_cfg1_main_a53_2,
	[AM62X_DEV_RTI3] = &am62x_dev_rti_cfg1_main_a53_3,
	[AM62X_DEV_RTI15] = &am62x_dev_rti_cfg1_main_gpu,
	[AM62X_DEV_MCU_RTI0] = &am62x_dev_rti_cfg1_mcu_m4_0,
	[AM62X_DEV_WKUP_RTI0] = &am62x_dev_rti_cfg1_wkup_dm_0,
	[AM62X_DEV_COMPUTE_CLUSTER0] = &am62x_dev_sam62_a53_512kb_wrap_main_0,
	[AM62X_DEV_A53SS0_CORE_0] = &am62x_dev_sam62_a53_512kb_wrap_main_0_a53_0,
	[AM62X_DEV_A53SS0_CORE_1] = &am62x_dev_sam62_a53_512kb_wrap_main_0_a53_1,
	[AM62X_DEV_A53SS0_CORE_2] = &am62x_dev_sam62_a53_512kb_wrap_main_0_a53_2,
	[AM62X_DEV_A53SS0_CORE_3] = &am62x_dev_sam62_a53_512kb_wrap_main_0_a53_3,
	[AM62X_DEV_PSCSS0] = &am62x_dev_sam62_main_psc_wrap_main_0,
	[AM62X_DEV_WKUP_PSC0] = &am62x_dev_sam62_mcu_psc_wrap_wkup_0.drv_data.dev_data,
	[AM62X_DEV_MCSPI0] = &am62x_dev_spi_main_0,
	[AM62X_DEV_MCSPI1] = &am62x_dev_spi_main_1,
	[AM62X_DEV_MCSPI2] = &am62x_dev_spi_main_2,
	[AM62X_DEV_UART0] = &am62x_dev_usart_main_0,
	[AM62X_DEV_MCU_MCSPI0] = &am62x_dev_spi_mcu_0,
	[AM62X_DEV_MCU_MCSPI1] = &am62x_dev_spi_mcu_1,
	[AM62X_DEV_MCU_UART0] = &am62x_dev_usart_mcu_0,
	[AM62X_DEV_SPINLOCK0] = &am62x_dev_spinlock256_main_0,
	[AM62X_DEV_UART1] = &am62x_dev_usart_main_1,
	[AM62X_DEV_UART2] = &am62x_dev_usart_main_2,
	[AM62X_DEV_UART3] = &am62x_dev_usart_main_3,
	[AM62X_DEV_UART4] = &am62x_dev_usart_main_4,
	[AM62X_DEV_UART5] = &am62x_dev_usart_main_5,
	[AM62X_DEV_BOARD0] = &am62x_dev_board_0,
	[AM62X_DEV_UART6] = &am62x_dev_usart_main_6,
	[AM62X_DEV_USB0] = &am62x_dev_usb2ss_16ffc_main_0,
	[AM62X_DEV_USB1] = &am62x_dev_usb2ss_16ffc_main_1,
	[AM62X_DEV_PBIST0] = &am62x_dev_k3_pbist_8c28p_4bit_wrap_main_0,
	[AM62X_DEV_PBIST1] = &am62x_dev_k3_pbist_8c28p_4bit_wrap_main_GPU_0,
	[AM62X_DEV_WKUP_PBIST0] = &am62x_dev_k3_pbist_8c28p_4bit_wrap_wkup_0,
	[AM62X_DEV_A53SS0] = &am62x_dev_sam62_a53_512kb_wrap_main_0_arm_corepack_0,
	[AM62X_DEV_COMPUTE_CLUSTER0_PBIST_0] = &am62x_dev_sam62_a53_512kb_wrap_main_0_pbist_0,
	[AM62X_DEV_PSC0_FW_0] = &am62x_dev_sam62_main_psc_wrap_main_0_fw_0,
	[AM62X_DEV_PSC0] = &am62x_dev_sam62_main_psc_wrap_main_0_psc_0.drv_data.dev_data,
	[AM62X_DEV_DDR16SS0] = &am62x_dev_sam62_ddr_wrap_main_0,
	[AM62X_DEV_DEBUGSS0] = &am62x_dev_sam62_debug_main_cell_main_0,
	[AM62X_DEV_A53_RS_BW_LIMITER0] = &am62x_dev_sam62_a53_rs_bw_limiter_main_0,
	[AM62X_DEV_A53_WS_BW_LIMITER1] = &am62x_dev_sam62_a53_ws_bw_limiter_main_0,
	[AM62X_DEV_GPU_RS_BW_LIMITER2] = &am62x_dev_sam62_gpu_rs_bw_limiter_main_0,
	[AM62X_DEV_GPU_WS_BW_LIMITER3] = &am62x_dev_sam62_gpu_ws_bw_limiter_main_0,
	[AM62X_DEV_WKUP_DEEPSLEEP_SOURCES0] = &am62x_dev_sam62_dm_wakeup_deepsleep_sources_wkup_0,
	[AM62X_DEV_EMIF_CFG_ISO_VD] = &am62x_dev_emif_cfg_iso_VD,
	[AM62X_DEV_MAIN_USB0_ISO_VD] = &am62x_dev_main_usb0_iso_VD,
	[AM62X_DEV_MAIN_USB1_ISO_VD] = &am62x_dev_main_usb1_iso_VD,
	[AM62X_DEV_MCU_MCU_16FF0] = &am62x_dev_sam62_mcu_16ff_mcu_0,
	[AM62X_DEV_CPT2_AGGR1] = &am62x_dev_cpt2_aggregator32_main_400MHz,
	[AM62X_DEV_CSI_RX_IF0] = &am62x_dev_csi_rx_if_main_0,
	[AM62X_DEV_DCC6] = &am62x_dev_dcc2_main_6,
	[AM62X_DEV_MMCSD2] = &am62x_dev_emmcsd4ss_main_1,
	[AM62X_DEV_DPHY_RX0] = &am62x_dev_K3_DPHY_RX_main_0,
	[AM62X_DEV_DSS0] = &am62x_dev_k3_dss_ul_main_0,
	[AM62X_DEV_GPU0] = &am62x_dev_k3_gpu_axe116m_main_0,
	[AM62X_DEV_MCU_MCAN0] = &am62x_dev_mcanss_mcu_0,
	[AM62X_DEV_MCU_MCAN1] = &am62x_dev_mcanss_mcu_1,
	[AM62X_DEV_MCASP0] = &am62x_dev_mcasp_main_0,
	[AM62X_DEV_MCASP1] = &am62x_dev_mcasp_main_1,
	[AM62X_DEV_MCASP2] = &am62x_dev_mcasp_main_2,
	[AM62X_DEV_CLK_32K_RC_SEL_DEV_VD] = &am62x_dev_clk_32k_rc_sel_dev_VD,
	[AM62X_DEV_HSM0] = &am62x_dev_sms_main_0_cortex_m4f_1,
	[AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD] = &am62x_dev_mcu_obsclk_mux_sel_dev_VD,
};

struct device soc_devices[AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD + 1U];
const size_t soc_device_count = ARRAY_SIZE(soc_device_data_arr);

struct device *const this_dev = soc_devices + AM62X_DEV_SMS0;
