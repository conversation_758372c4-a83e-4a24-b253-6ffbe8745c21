/*
 * Data version: 230918_161319
 *
 * Copyright (C) 2017-2024 Texas Instruments Incorporated - http://www.ti.com/
 * ALL RIGHTS RESERVED
 */
#ifndef SOC_AM62X_CLOCKS_H
#define SOC_AM62X_CLOCKS_H

#define AM62X_DEV_DPHY_RX0_IO_RX_CL_L_M 2
#define AM62X_DEV_DPHY_RX0_IO_RX_CL_L_P 3
#define AM62X_DEV_DPHY_RX0_JTAG_TCK 4
#define AM62X_DEV_DPHY_RX0_MAIN_CLK_CLK 5
#define AM62X_DEV_DPHY_RX0_PPI_RX_BYTE_CLK 7

#define AM62X_DEV_CMP_EVENT_INTROUTER0_INTR_CLK 0

#define AM62X_DEV_DBGSUSPENDROUTER0_INTR_CLK 0

#define AM62X_DEV_MAIN_GPIOMUX_INTROUTER0_INTR_CLK 0

#define AM62X_DEV_WKUP_MCU_GPIOMUX_INTROUTER0_INTR_CLK 0

#define AM62X_DEV_TIMESYNC_EVENT_ROUTER0_INTR_CLK 0

#define AM62X_DEV_MCU_M4FSS0_CBASS_0_CLK 0

#define AM62X_DEV_MCU_M4FSS0_CORE0_DAP_CLK 0
#define AM62X_DEV_MCU_M4FSS0_CORE0_VBUS_CLK 1
#define AM62X_DEV_MCU_M4FSS0_CORE0_VBUS_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK 2
#define AM62X_DEV_MCU_M4FSS0_CORE0_VBUS_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK2 3

#define AM62X_DEV_CPSW0_CPPI_CLK_CLK 0
#define AM62X_DEV_CPSW0_CPTS_GENF0 1
#define AM62X_DEV_CPSW0_CPTS_GENF1 2
#define AM62X_DEV_CPSW0_CPTS_RFT_CLK 3
#define AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK 4
#define AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK 5
#define AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 6
#define AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 8
#define AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 9
#define AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK 10
#define AM62X_DEV_CPSW0_CPTS_RFT_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK 11
#define AM62X_DEV_CPSW0_GMII1_MR_CLK 13
#define AM62X_DEV_CPSW0_GMII1_MT_CLK 14
#define AM62X_DEV_CPSW0_GMII2_MR_CLK 15
#define AM62X_DEV_CPSW0_GMII2_MT_CLK 16
#define AM62X_DEV_CPSW0_GMII_RFT_CLK 17
#define AM62X_DEV_CPSW0_MDIO_MDCLK_O 18
#define AM62X_DEV_CPSW0_RGMII1_RXC_I 19
#define AM62X_DEV_CPSW0_RGMII1_TXC_I 20
#define AM62X_DEV_CPSW0_RGMII1_TXC_O 21
#define AM62X_DEV_CPSW0_RGMII2_RXC_I 22
#define AM62X_DEV_CPSW0_RGMII2_TXC_I 23
#define AM62X_DEV_CPSW0_RGMII2_TXC_O 24
#define AM62X_DEV_CPSW0_RGMII_MHZ_250_CLK 25
#define AM62X_DEV_CPSW0_RGMII_MHZ_50_CLK 26
#define AM62X_DEV_CPSW0_RGMII_MHZ_5_CLK 27
#define AM62X_DEV_CPSW0_RMII1_MHZ_50_CLK 28
#define AM62X_DEV_CPSW0_RMII2_MHZ_50_CLK 29

#define AM62X_DEV_CPT2_AGGR0_VCLK_CLK 0

#define AM62X_DEV_CPT2_AGGR1_VCLK_CLK 0

#define AM62X_DEV_CSI_RX_IF0_MAIN_CLK_CLK 0
#define AM62X_DEV_CSI_RX_IF0_PPI_RX_BYTE_CLK 2
#define AM62X_DEV_CSI_RX_IF0_VBUS_CLK_CLK 3
#define AM62X_DEV_CSI_RX_IF0_VP_CLK_CLK 4

#define AM62X_DEV_STM0_ATB_CLK 0
#define AM62X_DEV_STM0_CORE_CLK 1
#define AM62X_DEV_STM0_VBUSP_CLK 2

#define AM62X_DEV_DCC0_DCC_CLKSRC0_CLK 0
#define AM62X_DEV_DCC0_DCC_CLKSRC1_CLK 1
#define AM62X_DEV_DCC0_DCC_CLKSRC2_CLK 2
#define AM62X_DEV_DCC0_DCC_CLKSRC3_CLK 3
#define AM62X_DEV_DCC0_DCC_CLKSRC4_CLK 4
#define AM62X_DEV_DCC0_DCC_CLKSRC5_CLK 5
#define AM62X_DEV_DCC0_DCC_CLKSRC6_CLK 6
#define AM62X_DEV_DCC0_DCC_CLKSRC7_CLK 7
#define AM62X_DEV_DCC0_DCC_INPUT00_CLK 8
#define AM62X_DEV_DCC0_DCC_INPUT01_CLK 9
#define AM62X_DEV_DCC0_DCC_INPUT02_CLK 10
#define AM62X_DEV_DCC0_DCC_INPUT10_CLK 11
#define AM62X_DEV_DCC0_VBUS_CLK 12

#define AM62X_DEV_DCC1_DCC_CLKSRC0_CLK 0
#define AM62X_DEV_DCC1_DCC_CLKSRC1_CLK 1
#define AM62X_DEV_DCC1_DCC_CLKSRC2_CLK 2
#define AM62X_DEV_DCC1_DCC_CLKSRC3_CLK 3
#define AM62X_DEV_DCC1_DCC_CLKSRC4_CLK 4
#define AM62X_DEV_DCC1_DCC_CLKSRC5_CLK 5
#define AM62X_DEV_DCC1_DCC_CLKSRC6_CLK 6
#define AM62X_DEV_DCC1_DCC_CLKSRC7_CLK 7
#define AM62X_DEV_DCC1_DCC_INPUT00_CLK 8
#define AM62X_DEV_DCC1_DCC_INPUT01_CLK 9
#define AM62X_DEV_DCC1_DCC_INPUT02_CLK 10
#define AM62X_DEV_DCC1_DCC_INPUT10_CLK 11
#define AM62X_DEV_DCC1_VBUS_CLK 12

#define AM62X_DEV_DCC2_DCC_CLKSRC0_CLK 0
#define AM62X_DEV_DCC2_DCC_CLKSRC1_CLK 1
#define AM62X_DEV_DCC2_DCC_CLKSRC2_CLK 2
#define AM62X_DEV_DCC2_DCC_CLKSRC3_CLK 3
#define AM62X_DEV_DCC2_DCC_CLKSRC4_CLK 4
#define AM62X_DEV_DCC2_DCC_CLKSRC5_CLK 5
#define AM62X_DEV_DCC2_DCC_CLKSRC6_CLK 6
#define AM62X_DEV_DCC2_DCC_CLKSRC7_CLK 7
#define AM62X_DEV_DCC2_DCC_INPUT00_CLK 8
#define AM62X_DEV_DCC2_DCC_INPUT01_CLK 9
#define AM62X_DEV_DCC2_DCC_INPUT02_CLK 10
#define AM62X_DEV_DCC2_DCC_INPUT10_CLK 11
#define AM62X_DEV_DCC2_VBUS_CLK 12

#define AM62X_DEV_DCC3_DCC_CLKSRC0_CLK 0
#define AM62X_DEV_DCC3_DCC_CLKSRC1_CLK 1
#define AM62X_DEV_DCC3_DCC_CLKSRC3_CLK 3
#define AM62X_DEV_DCC3_DCC_CLKSRC4_CLK 4
#define AM62X_DEV_DCC3_DCC_CLKSRC5_CLK 5
#define AM62X_DEV_DCC3_DCC_CLKSRC6_CLK 6
#define AM62X_DEV_DCC3_DCC_CLKSRC7_CLK 7
#define AM62X_DEV_DCC3_DCC_INPUT00_CLK 8
#define AM62X_DEV_DCC3_DCC_INPUT01_CLK 9
#define AM62X_DEV_DCC3_DCC_INPUT02_CLK 10
#define AM62X_DEV_DCC3_DCC_INPUT10_CLK 11
#define AM62X_DEV_DCC3_VBUS_CLK 12

#define AM62X_DEV_DCC4_DCC_CLKSRC0_CLK 0
#define AM62X_DEV_DCC4_DCC_CLKSRC1_CLK 1
#define AM62X_DEV_DCC4_DCC_CLKSRC2_CLK 2
#define AM62X_DEV_DCC4_DCC_CLKSRC3_CLK 3
#define AM62X_DEV_DCC4_DCC_CLKSRC4_CLK 4
#define AM62X_DEV_DCC4_DCC_CLKSRC5_CLK 5
#define AM62X_DEV_DCC4_DCC_CLKSRC6_CLK 6
#define AM62X_DEV_DCC4_DCC_CLKSRC7_CLK 7
#define AM62X_DEV_DCC4_DCC_INPUT00_CLK 8
#define AM62X_DEV_DCC4_DCC_INPUT01_CLK 9
#define AM62X_DEV_DCC4_DCC_INPUT02_CLK 10
#define AM62X_DEV_DCC4_DCC_INPUT10_CLK 11
#define AM62X_DEV_DCC4_VBUS_CLK 12

#define AM62X_DEV_DCC5_DCC_CLKSRC0_CLK 0
#define AM62X_DEV_DCC5_DCC_CLKSRC1_CLK 1
#define AM62X_DEV_DCC5_DCC_CLKSRC2_CLK 2
#define AM62X_DEV_DCC5_DCC_CLKSRC3_CLK 3
#define AM62X_DEV_DCC5_DCC_CLKSRC4_CLK 4
#define AM62X_DEV_DCC5_DCC_CLKSRC5_CLK 5
#define AM62X_DEV_DCC5_DCC_CLKSRC6_CLK 6
#define AM62X_DEV_DCC5_DCC_CLKSRC7_CLK 7
#define AM62X_DEV_DCC5_DCC_INPUT00_CLK 8
#define AM62X_DEV_DCC5_DCC_INPUT01_CLK 9
#define AM62X_DEV_DCC5_DCC_INPUT02_CLK 10
#define AM62X_DEV_DCC5_DCC_INPUT10_CLK 11
#define AM62X_DEV_DCC5_VBUS_CLK 12

#define AM62X_DEV_DCC6_DCC_CLKSRC0_CLK 0
#define AM62X_DEV_DCC6_DCC_CLKSRC1_CLK 1
#define AM62X_DEV_DCC6_DCC_CLKSRC2_CLK 2
#define AM62X_DEV_DCC6_DCC_CLKSRC3_CLK 3
#define AM62X_DEV_DCC6_DCC_CLKSRC4_CLK 4
#define AM62X_DEV_DCC6_DCC_CLKSRC5_CLK 5
#define AM62X_DEV_DCC6_DCC_CLKSRC6_CLK 6
#define AM62X_DEV_DCC6_DCC_CLKSRC7_CLK 7
#define AM62X_DEV_DCC6_DCC_INPUT00_CLK 8
#define AM62X_DEV_DCC6_DCC_INPUT01_CLK 9
#define AM62X_DEV_DCC6_DCC_INPUT02_CLK 10
#define AM62X_DEV_DCC6_DCC_INPUT10_CLK 11
#define AM62X_DEV_DCC6_VBUS_CLK 12

#define AM62X_DEV_MCU_DCC0_DCC_CLKSRC0_CLK 0
#define AM62X_DEV_MCU_DCC0_DCC_CLKSRC1_CLK 1
#define AM62X_DEV_MCU_DCC0_DCC_CLKSRC2_CLK 2
#define AM62X_DEV_MCU_DCC0_DCC_CLKSRC3_CLK 3
#define AM62X_DEV_MCU_DCC0_DCC_CLKSRC4_CLK 4
#define AM62X_DEV_MCU_DCC0_DCC_CLKSRC5_CLK 5
#define AM62X_DEV_MCU_DCC0_DCC_CLKSRC6_CLK 6
#define AM62X_DEV_MCU_DCC0_DCC_CLKSRC7_CLK 7
#define AM62X_DEV_MCU_DCC0_DCC_INPUT00_CLK 8
#define AM62X_DEV_MCU_DCC0_DCC_INPUT01_CLK 9
#define AM62X_DEV_MCU_DCC0_DCC_INPUT02_CLK 10
#define AM62X_DEV_MCU_DCC0_DCC_INPUT10_CLK 11
#define AM62X_DEV_MCU_DCC0_VBUS_CLK 12

#define AM62X_DEV_DEBUGSS_WRAP0_ATB_CLK 0
#define AM62X_DEV_DEBUGSS_WRAP0_CORE_CLK 1
#define AM62X_DEV_DEBUGSS_WRAP0_CSTPIU_TRACECLK 2
#define AM62X_DEV_DEBUGSS_WRAP0_JTAG_TCK 20
#define AM62X_DEV_DEBUGSS_WRAP0_TREXPT_CLK 22

#define AM62X_DEV_DMASS0_BCDMA_0_CLK 0

#define AM62X_DEV_DMASS0_CBASS_0_CLK 0

#define AM62X_DEV_DMASS0_INTAGGR_0_CLK 0

#define AM62X_DEV_DMASS0_IPCSS_0_CLK 0

#define AM62X_DEV_DMASS0_PKTDMA_0_CLK 0

#define AM62X_DEV_DMASS0_RINGACC_0_CLK 0

#define AM62X_DEV_TIMER0_TIMER_HCLK_CLK 0
#define AM62X_DEV_TIMER0_TIMER_PWM 1
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK 2
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 4
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 5
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 6
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 8
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 10
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 11
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 12
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 13
#define AM62X_DEV_TIMER0_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 14

#define AM62X_DEV_TIMER1_TIMER_HCLK_CLK 0
#define AM62X_DEV_TIMER1_TIMER_PWM 1
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK 2
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 4
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 5
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 6
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 8
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 10
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 11
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 12
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 13
#define AM62X_DEV_TIMER1_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 14

#define AM62X_DEV_TIMER2_TIMER_HCLK_CLK 0
#define AM62X_DEV_TIMER2_TIMER_PWM 1
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK 2
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 4
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 5
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 6
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 8
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 10
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 11
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 12
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 13
#define AM62X_DEV_TIMER2_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 14

#define AM62X_DEV_TIMER3_TIMER_HCLK_CLK 0
#define AM62X_DEV_TIMER3_TIMER_PWM 1
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK 2
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 4
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 5
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 6
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 8
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 10
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 11
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 12
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 13
#define AM62X_DEV_TIMER3_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 14

#define AM62X_DEV_TIMER4_TIMER_HCLK_CLK 0
#define AM62X_DEV_TIMER4_TIMER_PWM 1
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK 2
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 4
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 5
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 6
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 8
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 10
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 11
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 12
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 13
#define AM62X_DEV_TIMER4_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 14

#define AM62X_DEV_TIMER5_TIMER_HCLK_CLK 0
#define AM62X_DEV_TIMER5_TIMER_PWM 1
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK 2
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 4
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 5
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 6
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 8
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 10
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 11
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 12
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 13
#define AM62X_DEV_TIMER5_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 14

#define AM62X_DEV_TIMER6_TIMER_HCLK_CLK 0
#define AM62X_DEV_TIMER6_TIMER_PWM 1
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK 2
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 4
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 5
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 6
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 8
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 10
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 11
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 12
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 13
#define AM62X_DEV_TIMER6_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 14

#define AM62X_DEV_TIMER7_TIMER_HCLK_CLK 0
#define AM62X_DEV_TIMER7_TIMER_PWM 1
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK 2
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 4
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 5
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 6
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 8
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 10
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 11
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 12
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 13
#define AM62X_DEV_TIMER7_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 14

#define AM62X_DEV_MCU_TIMER0_TIMER_HCLK_CLK 0
#define AM62X_DEV_MCU_TIMER0_TIMER_PWM 1
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK 2
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4 4
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 5
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK 6
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 8
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 9
#define AM62X_DEV_MCU_TIMER0_TIMER_TCLK_CLK_PARENT_MCU_TIMERCLKN_SEL_OUT0_DIV_CLKOUT 10

#define AM62X_DEV_MCU_TIMER1_TIMER_HCLK_CLK 0
#define AM62X_DEV_MCU_TIMER1_TIMER_PWM 1
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK 2
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4 4
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 5
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK 6
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 8
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 9
#define AM62X_DEV_MCU_TIMER1_TIMER_TCLK_CLK_PARENT_MCU_TIMERCLKN_SEL_OUT1_DIV_CLKOUT 10

#define AM62X_DEV_MCU_TIMER2_TIMER_HCLK_CLK 0
#define AM62X_DEV_MCU_TIMER2_TIMER_PWM 1
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK 2
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4 4
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 5
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK 6
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 8
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 9
#define AM62X_DEV_MCU_TIMER2_TIMER_TCLK_CLK_PARENT_MCU_TIMERCLKN_SEL_OUT2_DIV_CLKOUT 10

#define AM62X_DEV_MCU_TIMER3_TIMER_HCLK_CLK 0
#define AM62X_DEV_MCU_TIMER3_TIMER_PWM 1
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK 2
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 3
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4 4
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 5
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK 6
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 7
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 8
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 9
#define AM62X_DEV_MCU_TIMER3_TIMER_TCLK_CLK_PARENT_MCU_TIMERCLKN_SEL_OUT3_DIV_CLKOUT 10

#define AM62X_DEV_WKUP_TIMER0_TIMER_HCLK_CLK 0
#define AM62X_DEV_WKUP_TIMER0_TIMER_HCLK_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 1
#define AM62X_DEV_WKUP_TIMER0_TIMER_HCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 2
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK 4
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 5
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_WKUP_CLKSEL_OUT04 6
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 7
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK 8
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 9
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 10
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 11
#define AM62X_DEV_WKUP_TIMER0_TIMER_TCLK_CLK_PARENT_WKUP_TIMERCLKN_SEL_OUT0_DIV_CLKOUT 12

#define AM62X_DEV_WKUP_TIMER1_TIMER_HCLK_CLK 0
#define AM62X_DEV_WKUP_TIMER1_TIMER_HCLK_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 1
#define AM62X_DEV_WKUP_TIMER1_TIMER_HCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 2
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK 4
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 5
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_WKUP_CLKSEL_OUT04 6
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 7
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK 8
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 9
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_CLK_32K_RC_SEL_OUT0 10
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 11
#define AM62X_DEV_WKUP_TIMER1_TIMER_TCLK_CLK_PARENT_WKUP_TIMERCLKN_SEL_OUT1_DIV_CLKOUT 12

#define AM62X_DEV_ECAP0_VBUS_CLK 0

#define AM62X_DEV_ECAP1_VBUS_CLK 0

#define AM62X_DEV_ECAP2_VBUS_CLK 0

#define AM62X_DEV_ELM0_VBUSP_CLK 0

#define AM62X_DEV_MMCSD1_EMMCSDSS_IO_CLK_I 0
#define AM62X_DEV_MMCSD1_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC1_CLKLB_OUT 1
#define AM62X_DEV_MMCSD1_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC1_CLK_OUT 2
#define AM62X_DEV_MMCSD1_EMMCSDSS_IO_CLK_O 3
#define AM62X_DEV_MMCSD1_EMMCSDSS_VBUS_CLK 5
#define AM62X_DEV_MMCSD1_EMMCSDSS_XIN_CLK 6
#define AM62X_DEV_MMCSD1_EMMCSDSS_XIN_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK 7
#define AM62X_DEV_MMCSD1_EMMCSDSS_XIN_CLK_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK 8

#define AM62X_DEV_MMCSD2_EMMCSDSS_IO_CLK_I 0
#define AM62X_DEV_MMCSD2_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC2_CLKLB_OUT 1
#define AM62X_DEV_MMCSD2_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC2_CLK_OUT 2
#define AM62X_DEV_MMCSD2_EMMCSDSS_IO_CLK_O 3
#define AM62X_DEV_MMCSD2_EMMCSDSS_VBUS_CLK 5
#define AM62X_DEV_MMCSD2_EMMCSDSS_XIN_CLK 6
#define AM62X_DEV_MMCSD2_EMMCSDSS_XIN_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK 7
#define AM62X_DEV_MMCSD2_EMMCSDSS_XIN_CLK_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK 8

#define AM62X_DEV_MMCSD0_EMMCSDSS_IO_CLK_I 0
#define AM62X_DEV_MMCSD0_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC0_CLKLB_OUT 1
#define AM62X_DEV_MMCSD0_EMMCSDSS_IO_CLK_I_PARENT_BOARD_0_MMC0_CLK_OUT 2
#define AM62X_DEV_MMCSD0_EMMCSDSS_IO_CLK_O 3
#define AM62X_DEV_MMCSD0_EMMCSDSS_VBUS_CLK 5
#define AM62X_DEV_MMCSD0_EMMCSDSS_XIN_CLK 6
#define AM62X_DEV_MMCSD0_EMMCSDSS_XIN_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK 7
#define AM62X_DEV_MMCSD0_EMMCSDSS_XIN_CLK_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK 8

#define AM62X_DEV_EQEP0_VBUS_CLK 0

#define AM62X_DEV_EQEP1_VBUS_CLK 0

#define AM62X_DEV_EQEP2_VBUS_CLK 0

#define AM62X_DEV_ESM0_CLK 0

#define AM62X_DEV_WKUP_ESM0_CLK 0

#define AM62X_DEV_FSS0_FSAS_0_GCLK 0

#define AM62X_DEV_FSS0_OSPI_0_OSPI_DQS_CLK 0
#define AM62X_DEV_FSS0_OSPI_0_OSPI_HCLK_CLK 1
#define AM62X_DEV_FSS0_OSPI_0_OSPI_ICLK_CLK 2
#define AM62X_DEV_FSS0_OSPI_0_OSPI_ICLK_CLK_PARENT_BOARD_0_OSPI0_DQS_OUT 3
#define AM62X_DEV_FSS0_OSPI_0_OSPI_ICLK_CLK_PARENT_BOARD_0_OSPI0_LBCLKO_OUT 4
#define AM62X_DEV_FSS0_OSPI_0_OSPI_OCLK_CLK 5
#define AM62X_DEV_FSS0_OSPI_0_OSPI_PCLK_CLK 6
#define AM62X_DEV_FSS0_OSPI_0_OSPI_RCLK_CLK 7
#define AM62X_DEV_FSS0_OSPI_0_OSPI_RCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT1_CLK 8
#define AM62X_DEV_FSS0_OSPI_0_OSPI_RCLK_CLK_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT5_CLK 9

#define AM62X_DEV_GICSS0_VCLK_CLK 0

#define AM62X_DEV_GPIO0_MMR_CLK 0

#define AM62X_DEV_GPIO1_MMR_CLK 0

#define AM62X_DEV_MCU_GPIO0_MMR_CLK 0
#define AM62X_DEV_MCU_GPIO0_MMR_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK4 1
#define AM62X_DEV_MCU_GPIO0_MMR_CLK_PARENT_GLUELOGIC_LFOSC0_CLKOUT 2
#define AM62X_DEV_MCU_GPIO0_MMR_CLK_PARENT_GLUELOGIC_RCOSC_CLK_1P0V_97P65K3 3
#define AM62X_DEV_MCU_GPIO0_MMR_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 4

#define AM62X_DEV_GPMC0_FUNC_CLK 0
#define AM62X_DEV_GPMC0_FUNC_CLK_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT3_CLK 1
#define AM62X_DEV_GPMC0_FUNC_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT7_CLK 2
#define AM62X_DEV_GPMC0_PI_GPMC_RET_CLK 3
#define AM62X_DEV_GPMC0_PO_GPMC_DEV_CLK 4
#define AM62X_DEV_GPMC0_VBUSM_CLK 5

#define AM62X_DEV_WKUP_GTC0_GTC_CLK 0
#define AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK 1
#define AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK 2
#define AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 3
#define AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 5
#define AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 6
#define AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK 7
#define AM62X_DEV_WKUP_GTC0_GTC_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK 8
#define AM62X_DEV_WKUP_GTC0_VBUSP_CLK 9
#define AM62X_DEV_WKUP_GTC0_VBUSP_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 10
#define AM62X_DEV_WKUP_GTC0_VBUSP_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 11

#define AM62X_DEV_ICSSM0_CORE_CLK 0
#define AM62X_DEV_ICSSM0_CORE_CLK_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT0_CLK 1
#define AM62X_DEV_ICSSM0_CORE_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT9_CLK 2
#define AM62X_DEV_ICSSM0_IEP_CLK 3
#define AM62X_DEV_ICSSM0_IEP_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK 4
#define AM62X_DEV_ICSSM0_IEP_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK 5
#define AM62X_DEV_ICSSM0_IEP_CLK_PARENT_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 6
#define AM62X_DEV_ICSSM0_IEP_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 8
#define AM62X_DEV_ICSSM0_IEP_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 9
#define AM62X_DEV_ICSSM0_IEP_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK 10
#define AM62X_DEV_ICSSM0_IEP_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK 11
#define AM62X_DEV_ICSSM0_UCLK_CLK 13
#define AM62X_DEV_ICSSM0_VCLK_CLK 14

#define AM62X_DEV_DDPA0_DDPA_CLK 0

#define AM62X_DEV_DSS0_DPI_0_IN_CLK 0
#define AM62X_DEV_DSS0_DPI_1_IN_CLK 2
#define AM62X_DEV_DSS0_DPI_1_IN_CLK_PARENT_HSDIV0_16FFT_MAIN_17_HSDIVOUT0_CLK 3
#define AM62X_DEV_DSS0_DPI_1_IN_CLK_PARENT_BOARD_0_VOUT0_EXTPCLKIN_OUT 4
#define AM62X_DEV_DSS0_DPI_1_OUT_CLK 5
#define AM62X_DEV_DSS0_DSS_FUNC_CLK 6

#define AM62X_DEV_EPWM0_VBUSP_CLK 0

#define AM62X_DEV_EPWM1_VBUSP_CLK 0

#define AM62X_DEV_EPWM2_VBUSP_CLK 0

#define AM62X_DEV_GPU0_GPU_CLK 0

#define AM62X_DEV_LED0_VBUS_CLK 1

#define AM62X_DEV_PBIST0_CLK8_CLK 7
#define AM62X_DEV_PBIST0_TCLK_CLK 9

#define AM62X_DEV_PBIST1_CLK8_CLK 7
#define AM62X_DEV_PBIST1_TCLK_CLK 9

#define AM62X_DEV_WKUP_PBIST0_CLK8_CLK 7

#define AM62X_DEV_WKUP_VTM0_FIX_REF2_CLK 0
#define AM62X_DEV_WKUP_VTM0_FIX_REF_CLK 1
#define AM62X_DEV_WKUP_VTM0_VBUSP_CLK 2
#define AM62X_DEV_WKUP_VTM0_VBUSP_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 3
#define AM62X_DEV_WKUP_VTM0_VBUSP_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 4

#define AM62X_DEV_MCAN0_MCANSS_CCLK_CLK 1
#define AM62X_DEV_MCAN0_MCANSS_CCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT4_CLK 2
#define AM62X_DEV_MCAN0_MCANSS_CCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 3
#define AM62X_DEV_MCAN0_MCANSS_CCLK_CLK_PARENT_BOARD_0_EXT_REFCLK1_OUT 4
#define AM62X_DEV_MCAN0_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 5
#define AM62X_DEV_MCAN0_MCANSS_HCLK_CLK 6

#define AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK 1
#define AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK 2
#define AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 3
#define AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 4
#define AM62X_DEV_MCU_MCAN0_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT_DUP0 5
#define AM62X_DEV_MCU_MCAN0_MCANSS_HCLK_CLK 6

#define AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK 1
#define AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK 2
#define AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK_PARENT_BOARD_0_MCU_EXT_REFCLK0_OUT 3
#define AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 4
#define AM62X_DEV_MCU_MCAN1_MCANSS_CCLK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT_DUP0 5
#define AM62X_DEV_MCU_MCAN1_MCANSS_HCLK_CLK 6

#define AM62X_DEV_MCASP0_AUX_CLK 0
#define AM62X_DEV_MCASP0_AUX_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK 1
#define AM62X_DEV_MCASP0_AUX_CLK_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK 2
#define AM62X_DEV_MCASP0_MCASP_ACLKR_PIN 3
#define AM62X_DEV_MCASP0_MCASP_ACLKR_POUT 4
#define AM62X_DEV_MCASP0_MCASP_ACLKX_PIN 5
#define AM62X_DEV_MCASP0_MCASP_ACLKX_POUT 6
#define AM62X_DEV_MCASP0_MCASP_AFSR_POUT 7
#define AM62X_DEV_MCASP0_MCASP_AFSX_POUT 8
#define AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN 9
#define AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT 10
#define AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 11
#define AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT 12
#define AM62X_DEV_MCASP0_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT 13
#define AM62X_DEV_MCASP0_MCASP_AHCLKR_POUT 14
#define AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN 15
#define AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT 16
#define AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 17
#define AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT 18
#define AM62X_DEV_MCASP0_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT 19
#define AM62X_DEV_MCASP0_MCASP_AHCLKX_POUT 20
#define AM62X_DEV_MCASP0_VBUSP_CLK 21

#define AM62X_DEV_MCASP1_AUX_CLK 0
#define AM62X_DEV_MCASP1_AUX_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK 1
#define AM62X_DEV_MCASP1_AUX_CLK_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK 2
#define AM62X_DEV_MCASP1_MCASP_ACLKR_PIN 3
#define AM62X_DEV_MCASP1_MCASP_ACLKR_POUT 4
#define AM62X_DEV_MCASP1_MCASP_ACLKX_PIN 5
#define AM62X_DEV_MCASP1_MCASP_ACLKX_POUT 6
#define AM62X_DEV_MCASP1_MCASP_AFSR_POUT 7
#define AM62X_DEV_MCASP1_MCASP_AFSX_POUT 8
#define AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN 9
#define AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT 10
#define AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 11
#define AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT 12
#define AM62X_DEV_MCASP1_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT 13
#define AM62X_DEV_MCASP1_MCASP_AHCLKR_POUT 14
#define AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN 15
#define AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT 16
#define AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 17
#define AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT 18
#define AM62X_DEV_MCASP1_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT 19
#define AM62X_DEV_MCASP1_MCASP_AHCLKX_POUT 20
#define AM62X_DEV_MCASP1_VBUSP_CLK 21

#define AM62X_DEV_MCASP2_AUX_CLK 0
#define AM62X_DEV_MCASP2_AUX_CLK_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK 1
#define AM62X_DEV_MCASP2_AUX_CLK_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK 2
#define AM62X_DEV_MCASP2_MCASP_ACLKR_PIN 3
#define AM62X_DEV_MCASP2_MCASP_ACLKR_POUT 4
#define AM62X_DEV_MCASP2_MCASP_ACLKX_PIN 5
#define AM62X_DEV_MCASP2_MCASP_ACLKX_POUT 6
#define AM62X_DEV_MCASP2_MCASP_AFSR_POUT 7
#define AM62X_DEV_MCASP2_MCASP_AFSX_POUT 8
#define AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN 9
#define AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT 10
#define AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 11
#define AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT 12
#define AM62X_DEV_MCASP2_MCASP_AHCLKR_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT 13
#define AM62X_DEV_MCASP2_MCASP_AHCLKR_POUT 14
#define AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN 15
#define AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN_PARENT_BOARD_0_EXT_REFCLK1_OUT 16
#define AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 17
#define AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK0_OUT 18
#define AM62X_DEV_MCASP2_MCASP_AHCLKX_PIN_PARENT_BOARD_0_AUDIO_EXT_REFCLK1_OUT 19
#define AM62X_DEV_MCASP2_MCASP_AHCLKX_POUT 20
#define AM62X_DEV_MCASP2_VBUSP_CLK 21

#define AM62X_DEV_MCRC64_0_CLK 0

#define AM62X_DEV_MCU_MCRC64_0_CLK 0

#define AM62X_DEV_I2C0_CLK 0
#define AM62X_DEV_I2C0_PISCL 1
#define AM62X_DEV_I2C0_PISYS_CLK 2
#define AM62X_DEV_I2C0_PORSCL 3

#define AM62X_DEV_I2C1_CLK 0
#define AM62X_DEV_I2C1_PISCL 1
#define AM62X_DEV_I2C1_PISYS_CLK 2
#define AM62X_DEV_I2C1_PORSCL 3

#define AM62X_DEV_I2C2_CLK 0
#define AM62X_DEV_I2C2_PISCL 1
#define AM62X_DEV_I2C2_PISYS_CLK 2
#define AM62X_DEV_I2C2_PORSCL 3

#define AM62X_DEV_I2C3_CLK 0
#define AM62X_DEV_I2C3_PISCL 1
#define AM62X_DEV_I2C3_PISYS_CLK 2
#define AM62X_DEV_I2C3_PORSCL 3

#define AM62X_DEV_MCU_I2C0_CLK 0
#define AM62X_DEV_MCU_I2C0_PISCL 1
#define AM62X_DEV_MCU_I2C0_PISYS_CLK 2
#define AM62X_DEV_MCU_I2C0_PORSCL 3

#define AM62X_DEV_WKUP_I2C0_CLK 0
#define AM62X_DEV_WKUP_I2C0_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 1
#define AM62X_DEV_WKUP_I2C0_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 2
#define AM62X_DEV_WKUP_I2C0_PISCL 3
#define AM62X_DEV_WKUP_I2C0_PISYS_CLK 4
#define AM62X_DEV_WKUP_I2C0_PORSCL 5

#define AM62X_DEV_WKUP_R5FSS0_CORE0_CPU_CLK 0
#define AM62X_DEV_WKUP_R5FSS0_CORE0_CPU_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 1
#define AM62X_DEV_WKUP_R5FSS0_CORE0_CPU_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 2
#define AM62X_DEV_WKUP_R5FSS0_CORE0_INTERFACE_CLK 3

#define AM62X_DEV_WKUP_RTCSS0_ANA_OSC32K_CLK 0
#define AM62X_DEV_WKUP_RTCSS0_ANA_OSC32K_CLK_PARENT_CLK_32K_RC_SEL_OUT0 1
#define AM62X_DEV_WKUP_RTCSS0_ANA_OSC32K_CLK_PARENT_RTC_CLK_SEL_DIV_CLKOUT 2
#define AM62X_DEV_WKUP_RTCSS0_VCLK_CLK 6
#define AM62X_DEV_WKUP_RTCSS0_VCLK_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 7
#define AM62X_DEV_WKUP_RTCSS0_VCLK_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 8

#define AM62X_DEV_RTI0_RTI_CLK 0
#define AM62X_DEV_RTI0_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 1
#define AM62X_DEV_RTI0_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0 2
#define AM62X_DEV_RTI0_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 3
#define AM62X_DEV_RTI0_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT0_DIV_CLKOUT 4
#define AM62X_DEV_RTI0_VBUSP_CLK 5

#define AM62X_DEV_RTI1_RTI_CLK 0
#define AM62X_DEV_RTI1_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 1
#define AM62X_DEV_RTI1_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0 2
#define AM62X_DEV_RTI1_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 3
#define AM62X_DEV_RTI1_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT1_DIV_CLKOUT 4
#define AM62X_DEV_RTI1_VBUSP_CLK 5

#define AM62X_DEV_RTI2_RTI_CLK 0
#define AM62X_DEV_RTI2_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 1
#define AM62X_DEV_RTI2_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0 2
#define AM62X_DEV_RTI2_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 3
#define AM62X_DEV_RTI2_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT2_DIV_CLKOUT 4
#define AM62X_DEV_RTI2_VBUSP_CLK 5

#define AM62X_DEV_RTI3_RTI_CLK 0
#define AM62X_DEV_RTI3_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 1
#define AM62X_DEV_RTI3_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0 2
#define AM62X_DEV_RTI3_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 3
#define AM62X_DEV_RTI3_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT3_DIV_CLKOUT 4
#define AM62X_DEV_RTI3_VBUSP_CLK 5

#define AM62X_DEV_RTI15_RTI_CLK 0
#define AM62X_DEV_RTI15_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 1
#define AM62X_DEV_RTI15_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0 2
#define AM62X_DEV_RTI15_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 3
#define AM62X_DEV_RTI15_RTI_CLK_PARENT_MAIN_WWDTCLKN_SEL_OUT4_DIV_CLKOUT 4
#define AM62X_DEV_RTI15_VBUSP_CLK 5

#define AM62X_DEV_MCU_RTI0_RTI_CLK 0
#define AM62X_DEV_MCU_RTI0_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 1
#define AM62X_DEV_MCU_RTI0_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0 2
#define AM62X_DEV_MCU_RTI0_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 3
#define AM62X_DEV_MCU_RTI0_RTI_CLK_PARENT_MCU_WWDTCLK_SEL_DIV_CLKOUT 4
#define AM62X_DEV_MCU_RTI0_VBUSP_CLK 5

#define AM62X_DEV_WKUP_RTI0_RTI_CLK 0
#define AM62X_DEV_WKUP_RTI0_RTI_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 1
#define AM62X_DEV_WKUP_RTI0_RTI_CLK_PARENT_CLK_32K_RC_SEL_OUT0 2
#define AM62X_DEV_WKUP_RTI0_RTI_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 3
#define AM62X_DEV_WKUP_RTI0_RTI_CLK_PARENT_WKUP_WWDTCLK_SEL_DIV_CLKOUT 4
#define AM62X_DEV_WKUP_RTI0_VBUSP_CLK 5
#define AM62X_DEV_WKUP_RTI0_VBUSP_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 6
#define AM62X_DEV_WKUP_RTI0_VBUSP_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 7

#define AM62X_DEV_A53SS0_CORE_0_A53_CORE0_ARM_CLK_CLK 0

#define AM62X_DEV_A53SS0_CORE_1_A53_CORE1_ARM_CLK_CLK 0

#define AM62X_DEV_A53SS0_CORE_2_A53_CORE2_ARM_CLK_CLK 0

#define AM62X_DEV_A53SS0_CORE_3_A53_CORE3_ARM_CLK_CLK 0

#define AM62X_DEV_A53SS0_A53_DIVH_CLK4_OBSCLK_OUT_CLK 2
#define AM62X_DEV_A53SS0_COREPAC_ARM_CLK_CLK 3
#define AM62X_DEV_A53SS0_PLL_CTRL_CLK 5

#define AM62X_DEV_A53_RS_BW_LIMITER0_CLK_CLK 0

#define AM62X_DEV_A53_WS_BW_LIMITER1_CLK_CLK 0

#define AM62X_DEV_DDR16SS0_DDRSS_DDR_PLL_CLK 0
#define AM62X_DEV_DDR16SS0_DDRSS_TCK 1
#define AM62X_DEV_DDR16SS0_PLL_CTRL_CLK 2

#define AM62X_DEV_DEBUGSS0_CFG_CLK 0
#define AM62X_DEV_DEBUGSS0_DBG_CLK 1
#define AM62X_DEV_DEBUGSS0_SYS_CLK 2

#define AM62X_DEV_WKUP_DEEPSLEEP_SOURCES0_CLK_12M_RC_CLK 0

#define AM62X_DEV_GPU_RS_BW_LIMITER2_CLK_CLK 0

#define AM62X_DEV_GPU_WS_BW_LIMITER3_CLK_CLK 0

#define AM62X_DEV_PSC0_FW_0_CLK 0

#define AM62X_DEV_PSC0_CLK 0
#define AM62X_DEV_PSC0_SLOW_CLK 1

#define AM62X_DEV_MCU_MCU_16FF0_PLL_CTRL_MCU_CLK24_CLK 3

#define AM62X_DEV_WKUP_PSC0_CLK 0
#define AM62X_DEV_WKUP_PSC0_SLOW_CLK 1

#define AM62X_DEV_HSM0_DAP_CLK 0

#define AM62X_DEV_MCSPI0_CLKSPIREF_CLK 0
#define AM62X_DEV_MCSPI0_IO_CLKSPIO_CLK 1
#define AM62X_DEV_MCSPI0_VBUSP_CLK 2
#define AM62X_DEV_MCSPI0_IO_CLKSPII_CLK 3
#define AM62X_DEV_MCSPI0_IO_CLKSPII_CLK_PARENT_BOARD_0_SPI0_CLK_OUT 4
#define AM62X_DEV_MCSPI0_IO_CLKSPII_CLK_PARENT_SPI_MAIN_0_IO_CLKSPIO_CLK 5

#define AM62X_DEV_MCSPI1_CLKSPIREF_CLK 0
#define AM62X_DEV_MCSPI1_IO_CLKSPIO_CLK 1
#define AM62X_DEV_MCSPI1_VBUSP_CLK 2
#define AM62X_DEV_MCSPI1_IO_CLKSPII_CLK 3
#define AM62X_DEV_MCSPI1_IO_CLKSPII_CLK_PARENT_BOARD_0_SPI1_CLK_OUT 4
#define AM62X_DEV_MCSPI1_IO_CLKSPII_CLK_PARENT_SPI_MAIN_1_IO_CLKSPIO_CLK 5

#define AM62X_DEV_MCSPI2_CLKSPIREF_CLK 0
#define AM62X_DEV_MCSPI2_IO_CLKSPIO_CLK 1
#define AM62X_DEV_MCSPI2_VBUSP_CLK 2
#define AM62X_DEV_MCSPI2_IO_CLKSPII_CLK 3
#define AM62X_DEV_MCSPI2_IO_CLKSPII_CLK_PARENT_BOARD_0_SPI2_CLK_OUT 4
#define AM62X_DEV_MCSPI2_IO_CLKSPII_CLK_PARENT_SPI_MAIN_2_IO_CLKSPIO_CLK 5

#define AM62X_DEV_MCU_MCSPI0_CLKSPIREF_CLK 0
#define AM62X_DEV_MCU_MCSPI0_IO_CLKSPIO_CLK 1
#define AM62X_DEV_MCU_MCSPI0_VBUSP_CLK 2
#define AM62X_DEV_MCU_MCSPI0_IO_CLKSPII_CLK 3
#define AM62X_DEV_MCU_MCSPI0_IO_CLKSPII_CLK_PARENT_BOARD_0_MCU_SPI0_CLK_OUT 4
#define AM62X_DEV_MCU_MCSPI0_IO_CLKSPII_CLK_PARENT_SPI_MCU_0_IO_CLKSPIO_CLK 5

#define AM62X_DEV_MCU_MCSPI1_CLKSPIREF_CLK 0
#define AM62X_DEV_MCU_MCSPI1_IO_CLKSPIO_CLK 1
#define AM62X_DEV_MCU_MCSPI1_VBUSP_CLK 2
#define AM62X_DEV_MCU_MCSPI1_IO_CLKSPII_CLK 3
#define AM62X_DEV_MCU_MCSPI1_IO_CLKSPII_CLK_PARENT_BOARD_0_MCU_SPI1_CLK_OUT 4
#define AM62X_DEV_MCU_MCSPI1_IO_CLKSPII_CLK_PARENT_SPI_MCU_1_IO_CLKSPIO_CLK 5

#define AM62X_DEV_SPINLOCK0_VCLK_CLK 0

#define AM62X_DEV_UART0_FCLK_CLK 0
#define AM62X_DEV_UART0_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT0 1
#define AM62X_DEV_UART0_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK 2
#define AM62X_DEV_UART0_VBUSP_CLK 5

#define AM62X_DEV_UART1_FCLK_CLK 0
#define AM62X_DEV_UART1_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT1 1
#define AM62X_DEV_UART1_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK 2
#define AM62X_DEV_UART1_VBUSP_CLK 5

#define AM62X_DEV_UART2_FCLK_CLK 0
#define AM62X_DEV_UART2_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT2 1
#define AM62X_DEV_UART2_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK 2
#define AM62X_DEV_UART2_VBUSP_CLK 5

#define AM62X_DEV_UART3_FCLK_CLK 0
#define AM62X_DEV_UART3_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT3 1
#define AM62X_DEV_UART3_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK 2
#define AM62X_DEV_UART3_VBUSP_CLK 5

#define AM62X_DEV_UART4_FCLK_CLK 0
#define AM62X_DEV_UART4_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT4 1
#define AM62X_DEV_UART4_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK 2
#define AM62X_DEV_UART4_VBUSP_CLK 5

#define AM62X_DEV_UART5_FCLK_CLK 0
#define AM62X_DEV_UART5_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT5 1
#define AM62X_DEV_UART5_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK 2
#define AM62X_DEV_UART5_VBUSP_CLK 5

#define AM62X_DEV_UART6_FCLK_CLK 0
#define AM62X_DEV_UART6_FCLK_CLK_PARENT_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT6 1
#define AM62X_DEV_UART6_FCLK_CLK_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK 2
#define AM62X_DEV_UART6_VBUSP_CLK 5

#define AM62X_DEV_MCU_UART0_FCLK_CLK 0
#define AM62X_DEV_MCU_UART0_VBUSP_CLK 3

#define AM62X_DEV_WKUP_UART0_FCLK_CLK 0
#define AM62X_DEV_WKUP_UART0_VBUSP_CLK 3
#define AM62X_DEV_WKUP_UART0_VBUSP_CLK_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 4
#define AM62X_DEV_WKUP_UART0_VBUSP_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 5

#define AM62X_DEV_USB0_BUS_CLK 0
#define AM62X_DEV_USB0_CFG_CLK 1
#define AM62X_DEV_USB0_USB2_APB_PCLK_CLK 2
#define AM62X_DEV_USB0_USB2_REFCLOCK_CLK 3
#define AM62X_DEV_USB0_USB2_REFCLOCK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 4
#define AM62X_DEV_USB0_USB2_REFCLOCK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT8_CLK 5
#define AM62X_DEV_USB0_USB2_TAP_TCK 10

#define AM62X_DEV_USB1_BUS_CLK 0
#define AM62X_DEV_USB1_CFG_CLK 1
#define AM62X_DEV_USB1_USB2_APB_PCLK_CLK 2
#define AM62X_DEV_USB1_USB2_REFCLOCK_CLK 3
#define AM62X_DEV_USB1_USB2_REFCLOCK_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 4
#define AM62X_DEV_USB1_USB2_REFCLOCK_CLK_PARENT_POSTDIV4_16FF_MAIN_0_HSDIVOUT8_CLK 5
#define AM62X_DEV_USB1_USB2_TAP_TCK 10

#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN 0
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_0_MCASP_AHCLKR_POUT 1
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_1_MCASP_AHCLKR_POUT 2
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_2_MCASP_AHCLKR_POUT 3
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_0_MCASP_AHCLKX_POUT 4
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_1_MCASP_AHCLKX_POUT 5
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_MCASP_MAIN_2_MCASP_AHCLKX_POUT 6
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK 7
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_IN_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK 8
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK0_OUT 9
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN 10
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_0_MCASP_AHCLKR_POUT 11
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_1_MCASP_AHCLKR_POUT 12
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_2_MCASP_AHCLKR_POUT 13
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_0_MCASP_AHCLKX_POUT 14
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_1_MCASP_AHCLKX_POUT 15
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_MCASP_MAIN_2_MCASP_AHCLKX_POUT 16
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK 17
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_IN_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK 18
#define AM62X_DEV_BOARD0_AUDIO_EXT_REFCLK1_OUT 19
#define AM62X_DEV_BOARD0_CLKOUT0_IN 20
#define AM62X_DEV_BOARD0_CLKOUT0_IN_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK5 21
#define AM62X_DEV_BOARD0_CLKOUT0_IN_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK10 22
#define AM62X_DEV_BOARD0_CP_GEMAC_CPTS0_RFT_CLK_OUT 23
#define AM62X_DEV_BOARD0_DDR0_CK0_IN 24
#define AM62X_DEV_BOARD0_DDR0_CK0_N_IN 25
#define AM62X_DEV_BOARD0_DDR0_CK0_OUT 27
#define AM62X_DEV_BOARD0_EXT_REFCLK1_OUT 33
#define AM62X_DEV_BOARD0_GPMC0_CLKLB_IN 34
#define AM62X_DEV_BOARD0_GPMC0_CLKLB_OUT 35
#define AM62X_DEV_BOARD0_GPMC0_CLK_IN 36
#define AM62X_DEV_BOARD0_GPMC0_FCLK_MUX_IN 37
#define AM62X_DEV_BOARD0_GPMC0_FCLK_MUX_IN_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT3_CLK 38
#define AM62X_DEV_BOARD0_GPMC0_FCLK_MUX_IN_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT7_CLK 39
#define AM62X_DEV_BOARD0_I2C0_SCL_IN 40
#define AM62X_DEV_BOARD0_I2C0_SCL_OUT 41
#define AM62X_DEV_BOARD0_I2C1_SCL_IN 42
#define AM62X_DEV_BOARD0_I2C1_SCL_OUT 43
#define AM62X_DEV_BOARD0_I2C2_SCL_IN 44
#define AM62X_DEV_BOARD0_I2C2_SCL_OUT 45
#define AM62X_DEV_BOARD0_I2C3_SCL_IN 46
#define AM62X_DEV_BOARD0_I2C3_SCL_OUT 47
#define AM62X_DEV_BOARD0_MCASP0_ACLKR_IN 49
#define AM62X_DEV_BOARD0_MCASP0_ACLKR_OUT 50
#define AM62X_DEV_BOARD0_MCASP0_ACLKX_IN 51
#define AM62X_DEV_BOARD0_MCASP0_ACLKX_OUT 52
#define AM62X_DEV_BOARD0_MCASP0_AFSR_IN 53
#define AM62X_DEV_BOARD0_MCASP0_AFSX_IN 54
#define AM62X_DEV_BOARD0_MCASP1_ACLKR_IN 55
#define AM62X_DEV_BOARD0_MCASP1_ACLKR_OUT 56
#define AM62X_DEV_BOARD0_MCASP1_ACLKX_IN 57
#define AM62X_DEV_BOARD0_MCASP1_ACLKX_OUT 58
#define AM62X_DEV_BOARD0_MCASP1_AFSR_IN 59
#define AM62X_DEV_BOARD0_MCASP1_AFSX_IN 60
#define AM62X_DEV_BOARD0_MCASP2_ACLKR_IN 61
#define AM62X_DEV_BOARD0_MCASP2_ACLKR_OUT 62
#define AM62X_DEV_BOARD0_MCASP2_ACLKX_IN 63
#define AM62X_DEV_BOARD0_MCASP2_ACLKX_OUT 64
#define AM62X_DEV_BOARD0_MCASP2_AFSR_IN 65
#define AM62X_DEV_BOARD0_MCASP2_AFSX_IN 66
#define AM62X_DEV_BOARD0_MCU_EXT_REFCLK0_OUT 67
#define AM62X_DEV_BOARD0_MCU_I2C0_SCL_IN 68
#define AM62X_DEV_BOARD0_MCU_I2C0_SCL_OUT 69
#define AM62X_DEV_BOARD0_MCU_OBSCLK0_IN 70
#define AM62X_DEV_BOARD0_MCU_OBSCLK0_IN_PARENT_MCU_OBSCLK_DIV_OUT0 71
#define AM62X_DEV_BOARD0_MCU_OBSCLK0_IN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 72
#define AM62X_DEV_BOARD0_MCU_SPI0_CLK_IN 73
#define AM62X_DEV_BOARD0_MCU_SPI0_CLK_OUT 74
#define AM62X_DEV_BOARD0_MCU_SPI1_CLK_IN 75
#define AM62X_DEV_BOARD0_MCU_SPI1_CLK_OUT 76
#define AM62X_DEV_BOARD0_MCU_SYSCLKOUT0_IN 77
#define AM62X_DEV_BOARD0_MCU_TIMER_IO0_IN 78
#define AM62X_DEV_BOARD0_MCU_TIMER_IO1_IN 79
#define AM62X_DEV_BOARD0_MCU_TIMER_IO2_IN 80
#define AM62X_DEV_BOARD0_MCU_TIMER_IO3_IN 81
#define AM62X_DEV_BOARD0_MDIO0_MDC_IN 82
#define AM62X_DEV_BOARD0_MMC0_CLKLB_IN 83
#define AM62X_DEV_BOARD0_MMC0_CLKLB_OUT 84
#define AM62X_DEV_BOARD0_MMC0_CLK_OUT 86
#define AM62X_DEV_BOARD0_MMC1_CLKLB_IN 87
#define AM62X_DEV_BOARD0_MMC1_CLKLB_OUT 88
#define AM62X_DEV_BOARD0_MMC1_CLK_IN 89
#define AM62X_DEV_BOARD0_MMC1_CLK_OUT 90
#define AM62X_DEV_BOARD0_MMC2_CLKLB_IN 91
#define AM62X_DEV_BOARD0_MMC2_CLKLB_OUT 92
#define AM62X_DEV_BOARD0_MMC2_CLK_IN 93
#define AM62X_DEV_BOARD0_MMC2_CLK_OUT 94
#define AM62X_DEV_BOARD0_OBSCLK0_IN 95
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT0_CLK 96
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK 97
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MAIN_2_HSDIVOUT0_CLK 98
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_A53_DIVH_CLK4_OBSCLK_OUT_CLK 99
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV0_16FFT_MAIN_12_HSDIVOUT0_CLK 100
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_GLUELOGIC_RCOSC_CLKOUT 101
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK8 102
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT0_CLK_DUP0 103
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 104
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_MAIN_OBSCLK0_MUX_SEL_DIV_CLKOUT 105
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF0 106
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_CPSW_3GUSS_MAIN_0_CPTS_GENF1 107
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 108
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 109
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV0_16FFT_MAIN_16_HSDIVOUT0_CLK 110
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_HSDIV0_16FFT_MAIN_17_HSDIVOUT0_CLK 111
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK 112
#define AM62X_DEV_BOARD0_OBSCLK0_IN_PARENT_CLK_32K_RC_SEL_OUT0 113
#define AM62X_DEV_BOARD0_OSPI0_DQS_OUT 128
#define AM62X_DEV_BOARD0_OSPI0_LBCLKO_IN 129
#define AM62X_DEV_BOARD0_OSPI0_LBCLKO_OUT 130
#define AM62X_DEV_BOARD0_RGMII1_RXC_OUT 131
#define AM62X_DEV_BOARD0_RGMII1_TXC_IN 132
#define AM62X_DEV_BOARD0_RGMII1_TXC_OUT 133
#define AM62X_DEV_BOARD0_RGMII2_RXC_OUT 134
#define AM62X_DEV_BOARD0_RGMII2_TXC_IN 135
#define AM62X_DEV_BOARD0_RGMII2_TXC_OUT 136
#define AM62X_DEV_BOARD0_RMII1_REF_CLK_OUT 137
#define AM62X_DEV_BOARD0_RMII2_REF_CLK_OUT 138
#define AM62X_DEV_BOARD0_SPI0_CLK_IN 139
#define AM62X_DEV_BOARD0_SPI0_CLK_OUT 140
#define AM62X_DEV_BOARD0_SPI1_CLK_IN 141
#define AM62X_DEV_BOARD0_SPI1_CLK_OUT 142
#define AM62X_DEV_BOARD0_SPI2_CLK_IN 143
#define AM62X_DEV_BOARD0_SPI2_CLK_OUT 144
#define AM62X_DEV_BOARD0_SYSCLKOUT0_IN 145
#define AM62X_DEV_BOARD0_TCK_OUT 146
#define AM62X_DEV_BOARD0_TIMER_IO0_IN 147
#define AM62X_DEV_BOARD0_TIMER_IO1_IN 148
#define AM62X_DEV_BOARD0_TIMER_IO2_IN 149
#define AM62X_DEV_BOARD0_TIMER_IO3_IN 150
#define AM62X_DEV_BOARD0_TIMER_IO4_IN 151
#define AM62X_DEV_BOARD0_TIMER_IO5_IN 152
#define AM62X_DEV_BOARD0_TIMER_IO6_IN 153
#define AM62X_DEV_BOARD0_TIMER_IO7_IN 154
#define AM62X_DEV_BOARD0_TRC_CLK_IN 155
#define AM62X_DEV_BOARD0_VOUT0_EXTPCLKIN_OUT 156
#define AM62X_DEV_BOARD0_VOUT0_PCLK_IN 157
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN 158
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_GLUELOGIC_HFOSC0_CLKOUT 159
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_GLUELOGIC_LFOSC0_CLKOUT 160
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_HSDIV4_16FFT_MAIN_0_HSDIVOUT2_CLK 161
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_HSDIV4_16FFT_MAIN_1_HSDIVOUT2_CLK 162
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_POSTDIV4_16FF_MAIN_2_HSDIVOUT9_CLK 163
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_CLK_32K_RC_SEL_OUT0 164
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_GLUELOGIC_RCOSC_CLKOUT 165
#define AM62X_DEV_BOARD0_WKUP_CLKOUT0_IN_PARENT_GLUELOGIC_HFOSC0_CLKOUT_DUP0 166
#define AM62X_DEV_BOARD0_CSI0_RXCLKP_OUT 167
#define AM62X_DEV_BOARD0_CSI0_RXCLKN_OUT 168

#define AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK 0
#define AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK_PARENT_GLUELOGIC_RCOSC_CLK_1P0V_97P65K3 1
#define AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK_PARENT_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK8 2
#define AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK_PARENT_CLK_32K_RC_SEL_DIV_CLKOUT 3
#define AM62X_DEV_CLK_32K_RC_SEL_DEV_VD_CLK_PARENT_GLUELOGIC_LFOSC0_CLKOUT 4

#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK 0
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_GLUELOGIC_RCOSC_CLKOUT 1
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 2
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK 3
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK_DUP0 4
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_MCU_OBSCLK_MUX_SEL_DIV_CLKOUT 5
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_GLUELOGIC_HFOSC0_CLKOUT 6
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK8 7
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK 8
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD_CLK_PARENT_CLK_32K_RC_SEL_OUT0 9


#endif /* SOC_AM62X_CLOCKS_H */
