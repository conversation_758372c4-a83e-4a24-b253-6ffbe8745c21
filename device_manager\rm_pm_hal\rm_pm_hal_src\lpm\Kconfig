
menu "Low Power Mode support"

config LPM_DM
	bool "Enable DM Low Power Mode support"
	depends on LIBRARY_BUILD_DM
	help
	 Enable required features in the DM to support low power modes.

config LPM_INCLUDE_OSAL
	bool "Enable OSAL support for LPM"
	depends on LPM_DM
	help
	 Enable OSAL layer calls for LPM

config LPM_DM_STUB
	bool "Build an LPM Stub binary"
	depends on LPM_DM
	help
	 Enable Low Power Mode Stub Generation

config LNK_NSPS_BASE
	hex "LNK_NSPS base address"
	default 0x00040000
	depends on LPM_DM_STUB
	help
	  Default base address of Non-secure Preserved Storage Memory

config LNK_NSPS_LEN
	hex "Size of NSPS"
	default 0x00008000
	depends on LPM_DM_STUB
	help
	  Set the maximum size of Non-secure Preserved Storage Memory

config LPM_DM_TRACE
	bool "Enable LPM Debug Trace"
	depends on LPM_DM
	help
	 Enable Low Power Mode Debug Output

config LPM_DM_TRACE_UART
	bool "Enable LPM Debug Trace Over UART"
	depends on LPM_DM_TRACE
	help
	 Enable Low Power Mode Debug Output over wakeup UART

config LPM_DM_TRACE_BUFFER
	bool "Enable LPM Debug Trace Over Buffer"
	depends on LPM_DM_TRACE
	help
	 Enable Low Power Mode Debug Output over Memory Buffer

config LPM_CLK
	bool "Enable Clock Layer LPM"
	depends on LPM_DM
	help
	 Enable Clock Layer LPM with enables all suspend and resume
	 handlers for clock drivers.

config LPM_LIMIT_IR_TRACKING
	bool "Reduce amount of IR entries tracked"
	default n
	depends on LPM_DM
	help
	 Reduce the amount of IR entries tracked due to available memory.
	 Enable for sysfw/sysfw-test, but disable for linux.

config LPM_32_BIT_DDR
	bool "Enable 32 bit DDR driver"
	depends on LPM_DM
	help
	 Enable 32 bit DDR driver for DDR save and restore.

menu "Low Power Debug Options"

config LPM_DM_STUB_STANDALONE
	bool "Build a standalone DM Stub"
	depends on LPM_DM
	help
	 Enable build of a standalone DM Stub binary that can be loaded
	 directly and used, can be useful for very specific debug
	 scenarios.

config LPM_DM_STUB_NO_FS_STUB
	bool "Disable all attempted communication with TIFS/FS Stub"
	depends on LPM_DM
	help
	 Disable attempted communication with TIFS or FS Stub using
	 secure proxy. Useful for very specific debug scenarios.

endmenu

endmenu
