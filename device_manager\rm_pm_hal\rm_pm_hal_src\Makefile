#
# Top level Makefile
#
# Top level Makefile for rm_pm_hal project
#
# Copyright (C) 2020 Texas Instruments Incorporated - http://www.ti.com/
# ALL RIGHTS RESERVED
#

# No make, you have no idea what you are doing
.SUFFIXES:

# Please don't remove intermediate files
.SECONDARY:

_lparen := (
_rparen := )
filter_var = $(if $(strip $(foreach chr,% $(_lparen) $(_rparen) * ^ [ ] + < > @ ?,$(findstring $(chr),$(1)))),,$(1))
_VARIABLES := $(foreach v,$(.VARIABLES),$(call filter_var,$(v)))
$(foreach v,$(_VARIABLES),$(if $(findstring environment,$(origin $v)),$(eval override $v := $(subst \,/,$(strip $($(v)))))))
$(foreach v,$(_VARIABLES),$(if $(findstring command line,$(origin $v)),$(eval override $v := $(subst \,/,$(strip $($(v)))))))

ifneq ($(call realpath,$(if $O,$O,.)),$(call realpath,$(CURDIR)))
# User is trying to build in special directory
_objtree_build:
	@mkdir -p $O
	@$(MAKE) -C $O -f $(CURDIR)/$(word 1,$(MAKEFILE_LIST)) \
		--no-print-directory O= BUILD_SRC=$(CURDIR) $(MAKECMDGOALS)

PHONY += _objtree_build

Makefile: ;

%:: _objtree_build ; @:
else

DEFAULT_TARGET=am62x_lpm_stub_gp

all: targets
PHONY += all

V ?= 0
build_quiet = $V

TARGET_SOC = $(shell echo $(CONFIG_SOC_FOLDER_STRING))
BUILD_SRC := .
srctree = $(BUILD_SRC)
srcroot = $(srctree)

include $(srcroot)/build/build.paths
include $(srcroot)/build/build.inc
include $(srcroot)/build/build.config
include $(srcroot)/build/build.githooks

# Process top-level build.mk
ifeq ($(fresh_clean),0)
$(call _recurse_inc,$(srctree)/)

# Need vars assigned for build.rules
include $(srctree)/build/build.rules

# Temporary workaround, add built-in.o
extra-y += $(obj)/built-in.o

# Include autogenerated dependancies
-include $(foreach p,$(build_files),$(call add_dot,\
	$(addsuffix .cmd,$($p)) $(addsuffix .d,$($p)))))

# Separate any nonsecurity components into a separate object list
obj-nonsec-y=$(filter-out tifs/src/hs/%,$(filter-out tifs/src/security/%,$(obj-y)))

# Build a list of security objects
obj-security-y=$(filter tifs/src/security/%,$(obj-y))
obj-security-y+=$(filter tifs/src/hs/%,$(obj-y))

# Build list of security objects relevant for boot
# On some k3 variants, this stays in DMSC IRAM
obj-security-boot-y=$(filter tifs/src/security/core/%,$(obj-security-y))
obj-security-boot-y+=$(filter tifs/src/security/boot/%,$(obj-security-y))
obj-security-boot-y+=$(filter tifs/src/security/secure_rm/%,$(obj-security-y))

# Remove security objects related to boot from the list
obj-security-other-y=$(filter-out tifs/src/security/boot/%,$(filter-out tifs/src/security/core/%,$(filter-out tifs/src/security/secure_rm/%,$(obj-security-y))))

# Collect all obj-y's
$(obj)/built-in.o: $(obj-nonsec-y) FORCE
	$(call make,rule_ld_o_o)

$(obj)/security_boot.o: $(obj-security-boot-y) FORCE
	$(call make,rule_ld_o_o)

$(obj)/security_other.o: $(obj-security-other-y) FORCE
	$(call make,rule_ld_o_o)

# Make sure all our destination directories exist
$(shell mkdir -p $(foreach f,$(build_files),$(dir $($f))))

# Ensure that autodep headers get a chance to rebuild before they are
# dependancy checked
prepare_headers: $(prepare_headers-y)
PHONY += prepare_headers

targets: $(target-y)
PHONY += targets
endif

define NL


endef

$(objtree)/include/config.h: $(objtree)/.config
	@echo 'GEN     '$@
	@$(SH) $(srctree)/scripts/generate_config $^ $(SCMVERSION) > $@

rwildcard=$(foreach d,$(wildcard $1*),$(call rwildcard,$d/,$2)) $(wildcard $(patsubst %,$1%,$2))
foreach_cmd=$(foreach f,$2,$1 $(f)$(NL))

clean:
	$(call foreach_cmd,@-rm -f,$(foreach f,$(build_files),$($f)))
	@-rm -f tags ctags
	@-rm -f $(clean_scripts)
	$(call foreach_cmd,@rm -f,$(call rwildcard,$(srctree)/,*.map))
	$(call foreach_cmd,@rm -f,$(call rwildcard,$(srctree)/,*.xml))
	$(call foreach_cmd,@rm -f,$(call rwildcard,$(srctree)/,*.pyc))
	@-rm -f .config
	@-rm -fr scripts/git-hooks/indent/stageFiles
	@-rm -fr scripts/git-hooks/indent/temp
	@-rm -f $(call rwildcard,$(srctree)/,.*.cmd .*.d)
	$(call foreach_cmd,@rm -f,$(call rwildcard,$(srctree)/,.*.cmd .*.d))
	@-rm -f cscope.files cscope.out cscope.in.out cscope.po.out
PHONY += clean

distclean: clean clean_docs
	@-rm -f .config
	@rm -f $(call rwildcard,$(srctree)/,.*.cmd .*.d)
	$(call foreach_cmd,@rm -f,$(call rwildcard,$(srctree)/,.*.cmd .*.d))
PHONY += distclean

ifneq ($(WIN_GITPATH),)
	git_cmd = "$(WIN_GITPATH)/git.exe"
else
	git_cmd = "git"
endif

gitsync:
	$(git_cmd) submodule init && $(git_cmd) submodule sync && \
		$(git_cmd) submodule update --init && \
                echo 'Git submodules: nothin to sync'

endif

#
# ctags and cscope args
#
TAG_SUBDIRS := $(srctree)

FIND := find
FINDFLAGS := -L

tags ctags:
	@echo -n "Generating ctags..."
	@ctags -w -o ctags `$(FIND) $(FINDFLAGS) $(TAG_SUBDIRS) \
		-name '*.[chS]' -print`
	@ln -s ctags tags
	@echo "Done!"

cscope:
	@echo -n "Generating cscope data..."
	@$(FIND) $(FINDFLAGS) $(TAG_SUBDIRS) -name '*.[chS]' -print > \
		cscope.files
	@cscope -b -q -k
	@echo "Done!"

FORCE:
PHONY += FORCE
.PHONY: $(PHONY)
