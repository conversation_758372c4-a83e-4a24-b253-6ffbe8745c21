/*
 *  Copyright (C) 2020 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF ME<PERSON><PERSON>NTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#ifndef CSLR_SOC_IN_H_
#define CSLR_SOC_IN_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <drivers/hw_include/am62x/cslr_soc_baseaddress.h>
#include <drivers/hw_include/am62x/cslr_soc_defines.h>
#include <drivers/hw_include/am62x/cslr_intr_mcu_m4fss0_core0.h>
#include <drivers/hw_include/am62x/cslr_intr_r5fss0_core0.h>
#include <drivers/hw_include/am62x/cslr_intr_wkup_mcu_gpiomux_introuter0.h>
#include <drivers/hw_include/am62x/cslr_intr_main_gpiomux_introuter0.h>
#include <drivers/hw_include/am62x/cslr_soc_ctrl_mmr.h>
#include <drivers/hw_include/am62x/cslr_mcu_ctrl_mmr.h>
#include <drivers/hw_include/am62x/cslr_r5fss0_baseaddress.h>
#include <drivers/hw_include/am62x/cslr_dmss_defines.h>
#include <drivers/hw_include/am62x/csl_psilcfg_thread_map.h>
#include <drivers/hw_include/am62x/cslr_psc.h>
#include <drivers/hw_include/am62x/csl_soc_psc.h>
#include <drivers/hw_include/am62x/cslr_intr_gicss0_common_0.h>

#ifdef __cplusplus
}
#endif
#endif /* CSLR_SOC_IN_H_ */
