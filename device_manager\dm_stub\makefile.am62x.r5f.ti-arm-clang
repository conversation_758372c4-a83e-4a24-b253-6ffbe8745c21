include BuildConfigurationAM62X.mk
export SRCS_COMMON
export CFLAGS_LOCAL_COMMON
export SRCS_ASM_S
export DEFINES
export MCU_PLUS_SDK_PATH?=$(abspath ../../../..)
include $(MCU_PLUS_SDK_PATH)/imports.mak

CG_TOOL_ROOT=$(CGT_TI_ARM_CLANG_PATH)

CC=$(CGT_TI_ARM_CLANG_PATH)/bin/tiarmclang
AR=$(CGT_TI_ARM_CLANG_PATH)/bin/tiarmar

PROFILE?=release
ConfigName:=$(PROFILE)

LIBNAME:=dm_stub.am62x.r5f.ti-arm-clang.$(PROFILE).lib

#FileName := ../rm_pm_hal/rm_pm_hal_src/app/dmstub/entry.S
#Variable := $(shell sed 's/;/\/\//g' ../rm_pm_hal/rm_pm_hal_src/app/dmstub/entry.S > entry)
#Variable1 := $(shell sed 's/.retainrefs/ /g' entry > temp_entry)
#Variable2 := $(shell sed 's/.retain/ /g' temp_entry > ../rm_pm_hal/rm_pm_hal_src/app/dmstub/entry.S)
#Variable3 := $(shell rm temp_entry entry)

#Variable := $(shell sed -i 's/s32 dm_stub_entry(void)/s32 __attribute__((noinline)) dm_stub_entry(void)/g' ../rm_pm_hal/rm_pm_hal_src/lpm/main.c)
FILTER_FILES_ONLY = $(filter-out $(CFLAGS_LOCAL),$(CFLAGS_LOCAL_COMMON))
CFLAGS_LOCAL_COMMONN = $(subst -I/,-I../rm_pm_hal/rm_pm_hal_src/, $(FILTER_FILES_ONLY))
CFGG = $(subst -I../rm_pm_hal/rm_pm_hal_src/,-I, $(CFLAGS_LOCAL_COMMONN))
Final_FILES_common = $(foreach var,$(SRCS_COMMON), $(lastword $(subst /, ,$(var))))
FInd_dir = $(shell find . -maxdepth 5 -type d -not -name "*j7*" -not -name "*am65*" \
    -not -name "*am62a*" -not -name "*am64*")
CFLAGS_LOCAL_COMMONN += \
    -I${CG_TOOL_ROOT}/include/c \
    -I${MCU_PLUS_SDK_PATH}/source \
    -I../priv \
    -I../sciclient_direct/soc/am62x/ \

DEFINES += -DSOC_AM62X -DMCU_PLUS_SDK

FILES_common := $(Final_FILES_common)

ASMFILES_common := $(SRCS_ASM_S)

FILES_PATH_common = \
    ../rm_pm_hal/rm_pm_hal_src/app/dmstub/ \
    ../rm_pm_hal/rm_pm_hal_src/lpm/ \
    ../rm_pm_hal/rm_pm_hal_src/lpm/soc/am62x/ \
    . \

INCLUDES_common := $(CFLAGS_LOCAL_COMMONN)

DEFINES_common := $(DEFINES)

CFLAGS_common := \
    -mcpu=cortex-r5 \
    -mfloat-abi=hard \
    -mfpu=vfpv3-d16 \
    -mthumb \
    -Wall \
    -Werror \
    -g \

CFLAGS_cpp_common := \
    -Wno-c99-designator \
    -Wno-extern-c-compat \
    -Wno-c++11-narrowing \
    -Wno-reorder-init-list \
    -Wno-deprecated-register \
    -Wno-writable-strings \
    -Wno-enum-compare \
    -Wno-reserved-user-defined-literal \
    -Wno-unused-const-variable \
    -x c++ \

CFLAGS_debug := \
    -D_DEBUG_=1 \

CFLAGS_release := \
    -Os \

ARFLAGS_common := \
    rc \

FILES := $(FILES_common) $(FILES_$(PROFILE))
ASMFILES := $(ASMFILES_common) $(ASMFILES_$(PROFILE))
FILES_PATH := $(FILES_PATH_common) $(FILES_PATH_$(PROFILE))
CFLAGS := $(CFLAGS_common) $(CFLAGS_$(PROFILE))
ASMFLAGS := $(CFLAGS_common) $(CFLAGS_$(PROFILE))
ifeq ($(CPLUSPLUS_BUILD), yes)
CFLAGS += $(CFLAGS_cpp_common)
endif
DEFINES := $(DEFINES_common) $(DEFINES_$(PROFILE))
INCLUDES := $(INCLUDES_common) $(INCLUDE_$(PROFILE))
ARFLAGS := $(ARFLAGS_common) $(ARFLAGS_$(PROFILE))

LIBDIR := lib
OBJDIR := obj/am62x/ti-arm-clang/$(PROFILE)/r5f/dm_stub/
OBJS := $(FILES:%.c=%.obj)
OBJS += $(ASMFILES:%.S=%.obj)
DEPS := $(FILES:%.c=%.d)

vpath %.obj $(OBJDIR)
vpath %.c $(FILES_PATH)
vpath %.S $(FILES_PATH)

$(OBJDIR)/%.obj %.obj: %.c
	@echo  Compiling: $(LIBNAME): $<
	$(CC) -c $(CFLAGS) $(INCLUDES) $(DEFINES) -MMD -o $(OBJDIR)/$@ $<

$(OBJDIR)/%.obj %.obj: %.S
	@echo  Compiling: $(LIBNAME): $<
	$(CC) -c $(ASMFLAGS) -o $(OBJDIR)/$@ $<

all: $(LIBDIR)/$(LIBNAME)

$(LIBDIR)/$(LIBNAME): $(OBJS) | $(LIBDIR)
	@echo  .
	@echo  Archiving: $(LIBNAME) to $@ ...
	$(AR) $(ARFLAGS) $@ $(addprefix $(OBJDIR), $(OBJS))
	@echo  Archiving: $(LIBNAME) Done !!!
	@echo  .

clean:
	@echo  Cleaning: $(LIBNAME) ...
	$(RMDIR) $(OBJDIR)
	$(RM) $(LIBDIR)/$(LIBNAME)

scrub:
	@echo  Scrubing: $(LIBNAME) ...
	-$(RMDIR) obj/
	-$(RMDIR) lib/

$(OBJS): | $(OBJDIR)

$(LIBDIR) $(OBJDIR):
	$(MKDIR) $@

-include $(addprefix $(OBJDIR)/, $(DEPS))
