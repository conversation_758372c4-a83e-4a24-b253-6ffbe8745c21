%%{
    let module = system.modules['/drivers/mcasp/mcasp'];
    let cpuName = system.getScript('/common').getSelfSysCfgCoreName();
%%}
/*
 * MCASP
 */
/* MCASP atrributes */
static MCASP_Attrs gMcaspAttrs[CONFIG_MCASP_NUM_INSTANCES] =
{
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    % let ser = [];
    % let serOutput = 0;
    % let rFifoEn = ((config.rxAfifoEnable == 1) ? 1 : 0) << 16;
    % let wFifoEn = ((config.txAfifoEnable == 1) ? 1 : 0) << 16;
    % serOutput |= ((config.txAclkSource == 1) ? 1 : 0) << 26;
    % serOutput |= ((config.txHclkSource == 1) ? 1 : 0) << 27;
    % serOutput |= ((config.txFsSource == 1) ? 1 : 0) << 28;
    % serOutput |= ((config.rxAclkSource == 1) ? 1 : 0) << 29;
    % serOutput |= ((config.rxFsSource == 1) ? 1 : 0) << 31;
    % for(let j = 0; j < config.numSerializers; j++) {
        % ser[j] = 0;
    % }
    % let serInstances = instance.mcaspSer;
    % for (let si = 0; si < serInstances.length; si++) {
        % let serInstance = serInstances[si];
        % if (serInstance.dataDir == "Transmit") {
            % ser[serInstance.serNum] = 1;
            % serOutput |= (1 << serInstance.serNum);
        % } else {
            % ser[serInstance.serNum] = 2;
        % }
    % }
    % let numTxSer = 0, numRxSer = 0;
    % for (let i= 0; i < instance.mcaspSer.length; i++) {
        % if(instance.mcaspSer[i].dataDir == "Transmit") {
            % numTxSer++;
        %} else {
            % numRxSer++;
        %}
    %}

    % let txIntNum = 0;
    % let rxIntNum = 0;
    % let txEvtNum = 0;
    % let rxEvtNum = 0;
    % if(cpuName.match(/c7/)) {
        % txIntNum = config.c7xTxIntr;
        % rxIntNum = config.c7xRxIntr;
        % txEvtNum = config.c7xTxEvent;
        % rxEvtNum = config.c7xRxEvent;
    % } else if(cpuName.match(/r5/)) {
        % txIntNum = config.wkupR5TxIntr;
        % rxIntNum = config.wkupR5RxIntr;
    % } else if(cpuName.match(/a53/)) {
        % txIntNum = config.a53TxIntr;
        % rxIntNum = config.a53RxIntr;
    % }


    {
        .instNum               = `config.name`,
        .baseAddr              = (uintptr_t) `config.regBaseAddr`,
        .dataBaseAddr          = (uintptr_t) `config.dataRegBaseAddr`,
        % if(config.clkSyncMode == "SYNC") {
        .isSynchronous          = 1U,
        % } else {
        .isSynchronous          = 0U,
        % }
        /* MCASP Data port Address  */
        .numOfSerializers = (uint32_t)`config.numSerializers`,
        /* Serializers available in MCASP */
        .intCfgTx =
        {
            .intrNum = (uint32_t)`txIntNum`,
            .evntNum = (uint32_t)`txEvtNum`,
            .intrPriority = 4,
        },
        /* Tx Int params for McASP */
        .intCfgRx =
        {
            .intrNum = (uint32_t)`rxIntNum`,
            .evntNum = (uint32_t)`rxEvtNum`,
            .intrPriority = 3,
        },
        .txSlotSize = (uint32_t)`config.TxSlotSize`,
        .rxSlotSize = (uint32_t)`config.RxSlotSize`,
        .hwCfg =
        {
            .gbl =
            {
                (uint32_t)0x0,  /* MCASP_PFUNC */
                % serOutput = serOutput >>> 32;
                (uint32_t)0x`serOutput.toString(16).toUpperCase()`,  /* MCASP_PDIR */
                (uint32_t)0x0,  /* MCASP_GBLCTL */
                (uint32_t)0x0,  /* MCASP_TXDITCTL */
                (uint32_t)0x`((instance.enableLoopback == true)?7:0)`,  /* MCASP_LBCTL */
                (uint32_t)0x0,  /* MCASP_TXDITCTL */
                {   /* serializer setup (MCASP_XRSRCTLn) */
                % for (let si = 0; si < config.numSerializers; si++) {
                    % let comment;
                    % (ser[si] == 1)?(comment="- Tx "):((ser[si] == 2)?(comment="- Rx "):(comment="- Inactive "));
                    (uint32_t)0x`ser[si]`, /* [`si`] `comment`*/
                % }
                },
            },
            .rx =
            {
                (uint32_t)0x`(instance.rxDataMask).toString(16).toUpperCase()`U, /* MCASP_RXMASK */
                % {
                    % let rxFmt = 0;
                    % rxFmt |= ((config.transferMode != "DMA") ? 8 : 0);
                    % rxFmt |= ((instance.rxDataDelay << 16) | (instance.rxDataOrder << 15) | (((instance.RxSlotSize/2) -1) << 4) | (instance.rxDataRotation));
                (uint32_t)0x`(rxFmt).toString(16).toUpperCase()`U, /* MCASP_RXFMT */
                % }
                % {
                    % let rxFmtCtl = 0;
                    % rxFmtCtl = ((instance.NumRxSlots << 7) | (instance.rxFsWidth << 4) | (instance.rxFsSource << 1) | instance.rxFsPolarity);
                (uint32_t)0x`(rxFmtCtl).toString(16).toUpperCase()`U, /* MCASP_RXFMCTL */
                % }
                (uint32_t)0x`(instance.rxActiveSlotMask).toString(16).toUpperCase()`U, /* MCASP_RXTDM */
                (uint32_t)0x`((config.transferMode == "INTERRUPT")?20:0)`U, /* MCASP_EVTCTLR */
                (uint32_t)0xffffU, /* MCASP_RXSTAT */
                (uint32_t)0x`((config.transferMode == "DMA")?1:0)`U, /* MCASP_REVTCTL */
                {
%{
% let bitClkrHz = instance.NumRxSlots * instance.RxSlotSize * instance.fsr * 1000;
% let masterClkrHz = instance.masterClkr * instance.fsr * 1000;
% let hiClkr = Math.round(config.inputClkFreq / masterClkrHz) - 1;
% let aclkr = Math.round(masterClkrHz/bitClkrHz) - 1;
% if (instance.rxAclkSource == 0)
% {
%      aclkr = (instance.rxAclkSource << 5);
% }
% else
% {
%      aclkr |= (instance.rxAclkSource << 5);
% }
% aclkr |= (instance.rxBitClkPolarity << 7);
% if (instance.rxHclkSource == 0)
% {
%      hiClkr = (instance.rxHclkSource << 15);
% }
% else
% {
%      hiClkr |= (instance.rxHclkSource << 15);
% }
                    (uint32_t)0x`(aclkr).toString(16).toUpperCase()`U, /* MCASP_ACLKRCTL */
                    (uint32_t)0x`(hiClkr).toString(16).toUpperCase()`U, /* MCASP_AHCLKRCTL */
%}
                    (uint32_t)0x0, /* MCASP_RXCLKCHK */
% if (instance.rxHclkSource == 0) {
                    (uint32_t)0x1,  /* HCLK is external */
                    (uint32_t)`instance.rxHclkSourceMux`,  /* External HCLK source */
% } else {
                    (uint32_t)0x0,  /* HCLK is internal */
% }
                },
                {
                    (uint32_t)0x`(rFifoEn | (instance.rxAfifoNumEvt << 8) | numRxSer).toString(16).toUpperCase()`U, /* RFIFOCTL */
                    (uint32_t)0x0, /* RFIFOSTS */
                },
            },
            .tx =
            {
                (uint32_t)0x`(instance.txDataMask).toString(16).toUpperCase()`U, /* MCASP_TXMASK */
                % {
                    % let txFmt = 0;
                    % txFmt |= ((config.transferMode != "DMA") ? 8 : 0);
                    % txFmt |= ((instance.txDataDelay << 16) | (instance.txDataOrder << 15) | (((instance.TxSlotSize/2) -1) << 4) | (instance.txDataRotation));
                (uint32_t)0x`(txFmt).toString(16).toUpperCase()`U, /* MCASP_TXFMT */
                % }
                % {
                    % let txFmtCtl = 0;
                    % txFmtCtl = ((instance.NumTxSlots << 7) | (instance.txFsWidth << 4) | (instance.txFsSource << 1) | instance.txFsPolarity);
                (uint32_t)0x`(txFmtCtl).toString(16).toUpperCase()`U, /* MCASP_TXFMCTL */
                % }
                (uint32_t)0x`(instance.txActiveSlotMask).toString(16).toUpperCase()`U, /* MCASP_TXTDM */
                (uint32_t)0x`((config.transferMode == "INTERRUPT")?20:0)`U, /* MCASP_EVTCTLX */
                (uint32_t)0xffffU, /* MCASP_TXSTAT */
                (uint32_t)0x`((config.transferMode == "DMA")?1:0)`U, /* MCASP_XEVTCTL */
                {
%{
% let bitClkxHz = instance.NumTxSlots * instance.TxSlotSize * instance.fsx * 1000;
% let masterClkxHz = instance.masterClkx * instance.fsx * 1000;
% let hiClkx = Math.round(config.inputClkFreq / masterClkxHz) - 1;
% let aclkx = Math.round(masterClkxHz/bitClkxHz) - 1;
% if (instance.txAclkSource == 0)
% {
%      aclkx = (instance.txAclkSource << 5);
% }
% else
% {
%      aclkx |= (instance.txAclkSource << 5);
% }
% aclkx |= (instance.txBitClkPolarity << 7);
% if (instance.txHclkSource == 0)
% {
%      hiClkx = (instance.txHclkSource << 15);
% }
% else
% {
%      hiClkx |= (instance.txHclkSource << 15);
% }
% aclkx |= (((instance.clkSyncMode == "SYNC")?0:1) << 6);
                    (uint32_t)0x`(aclkx).toString(16).toUpperCase()`U, /* MCASP_ACLKXCTL */
                    (uint32_t)0x`(hiClkx).toString(16).toUpperCase()`U, /* MCASP_AHCLKXCTL */
%}
                    (uint32_t)0x0, /* MCASP_TXCLKCHK */
% if (instance.txHclkSource == 0) {
                    (uint32_t)0x1,  /* HCLK is external */
                    (uint32_t)`instance.txHclkSourceMux`, /* External HCLK source */
% } else {
                    (uint32_t)0x0,  /* HCLK is internal */
% }
                },
                {
                    (uint32_t)0x`(wFifoEn | (instance.txAfifoNumEvt << 8)| numTxSer).toString(16).toUpperCase()`U, /* WFIFOCTL */
                    (uint32_t)0x0, /* WFIFOSTS */
                },
            },
        },
    },
%}
};

/* MCASP objects - initialized by the driver */
static MCASP_Object gMcaspObjects[CONFIG_MCASP_NUM_INSTANCES];
/* MCASP driver configuration */
MCASP_Config gMcaspConfig[CONFIG_MCASP_NUM_INSTANCES] =
{
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    {
        &gMcaspAttrs[`instance.$name.toUpperCase()`],
        &gMcaspObjects[`instance.$name.toUpperCase()`],
    },
% }
};

uint32_t gMcaspConfigNum = CONFIG_MCASP_NUM_INSTANCES;
