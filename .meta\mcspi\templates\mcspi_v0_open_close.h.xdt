%%{
    let common = system.getScript("/common");
    let module = system.modules['/drivers/mcspi/mcspi'];
%%}
/*
 * MCSPI
 */
#include <drivers/mcspi.h>

/* MCSPI Driver handles */
extern MCSPI_Handle gMcspiHandle[CONFIG_MCSPI_NUM_INSTANCES];

/*
 * <PERSON><PERSON>I Driver Advance Parameters - to be used only when Driver_open() and
 * Driver_close() is not used by the application
 */
/* MCSPI Driver Open Parameters */
extern MCSPI_OpenParams gMcspiOpenParams[CONFIG_MCSPI_NUM_INSTANCES];
/* MCSPI Channel Macros */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let ch_instances = instance.mcspiChannel;
#define `instance.$name.toUpperCase()`_NUM_CH (`ch_instances.length`U)
% }
/* MCSPI Driver Channel Configurations */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let instNameCamelCase = common.camelSentence(instance.$name);
extern MCSPI_ChConfig g`instNameCamelCase`ChCfg[`instance.$name.toUpperCase()`_NUM_CH];
%      if(instance.intrEnable == "DMA") {
extern MCSPI_DmaChConfig g`instNameCamelCase`DmaChCfg[`instance.$name.toUpperCase()`_NUM_CH];
% }
% }
/* MCSPI Driver open/close - can be used by application when Driver_open() and
 * Driver_close() is not used directly and app wants to control the various driver
 * open/close sequences */
void Drivers_mcspiOpen(void);
void Drivers_mcspiClose(void);
