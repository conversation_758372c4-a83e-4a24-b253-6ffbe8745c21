/* Copyright (c) 2023, Texas Instruments Incorporated
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. */
#define DDR_TYPE LPDDR4

#ifndef BOARD_DDRREGINIT_H_
#define BOARD_DDRREGINIT_H_

#ifdef __cplusplus
extern "C" {
#endif

/*
 * This file was generated with the
 * AM62x SysConfig DDR Subsystem Register Configuration Tool v0.09.03 
 * Mon Nov 21 2022 09:04:01 GMT-0600 (Central Standard Time)
 * DDR Type: LPDDR4   
 * F0 = 50MHz    F1 = NA     F2 = 800MHz
 * Density (per channel): 4Gb
 * Write DBI: Enable
 * Number of Ranks: 1
*/

#define DDRSS_PLL_FHS_CNT 3
#define DDRSS_PLL_FREQUENCY_1 400000000
#define DDRSS_PLL_FREQUENCY_2 400000000

#define DDRSS_CTL_REG_INIT_COUNT (423U)
#define DDRSS_PHY_INDEP_REG_INIT_COUNT (345U)
#define DDRSS_PHY_REG_INIT_COUNT (507U)

uint32_t DDRSS_ctlReg[] = {
    0x00000B00U, // DDRSS_CTL_0_VAL
    0x00000000U, // DDRSS_CTL_1_VAL
    0x00000000U, // DDRSS_CTL_2_VAL
    0x00000000U, // DDRSS_CTL_3_VAL
    0x00000000U, // DDRSS_CTL_4_VAL
    0x00000000U, // DDRSS_CTL_5_VAL
    0x00000000U, // DDRSS_CTL_6_VAL
    0x00002710U, // DDRSS_CTL_7_VAL
    0x000186A0U, // DDRSS_CTL_8_VAL
    0x00000005U, // DDRSS_CTL_9_VAL
    0x00000064U, // DDRSS_CTL_10_VAL
    0x00027100U, // DDRSS_CTL_11_VAL
    0x00186A00U, // DDRSS_CTL_12_VAL
    0x00000005U, // DDRSS_CTL_13_VAL
    0x00000640U, // DDRSS_CTL_14_VAL
    0x00027100U, // DDRSS_CTL_15_VAL
    0x00186A00U, // DDRSS_CTL_16_VAL
    0x00000005U, // DDRSS_CTL_17_VAL
    0x00000640U, // DDRSS_CTL_18_VAL
    0x01010100U, // DDRSS_CTL_19_VAL
    0x01010100U, // DDRSS_CTL_20_VAL
    0x01000110U, // DDRSS_CTL_21_VAL
    0x02010002U, // DDRSS_CTL_22_VAL
    0x0000000AU, // DDRSS_CTL_23_VAL
    0x000186A0U, // DDRSS_CTL_24_VAL
    0x00000000U, // DDRSS_CTL_25_VAL
    0x00000000U, // DDRSS_CTL_26_VAL
    0x00000000U, // DDRSS_CTL_27_VAL
    0x00000000U, // DDRSS_CTL_28_VAL
    0x00020200U, // DDRSS_CTL_29_VAL
    0x00000000U, // DDRSS_CTL_30_VAL
    0x00000000U, // DDRSS_CTL_31_VAL
    0x00000000U, // DDRSS_CTL_32_VAL
    0x00000000U, // DDRSS_CTL_33_VAL
    0x08000010U, // DDRSS_CTL_34_VAL
    0x00002020U, // DDRSS_CTL_35_VAL
    0x00000000U, // DDRSS_CTL_36_VAL
    0x00000000U, // DDRSS_CTL_37_VAL
    0x0000040CU, // DDRSS_CTL_38_VAL
    0x00000000U, // DDRSS_CTL_39_VAL
    0x0000081CU, // DDRSS_CTL_40_VAL
    0x00000000U, // DDRSS_CTL_41_VAL
    0x0000081CU, // DDRSS_CTL_42_VAL
    0x00000000U, // DDRSS_CTL_43_VAL
    0x05000804U, // DDRSS_CTL_44_VAL
    0x00000700U, // DDRSS_CTL_45_VAL
    0x09090004U, // DDRSS_CTL_46_VAL
    0x00000303U, // DDRSS_CTL_47_VAL
    0x00320007U, // DDRSS_CTL_48_VAL
    0x09090023U, // DDRSS_CTL_49_VAL
    0x0000210FU, // DDRSS_CTL_50_VAL
    0x00320007U, // DDRSS_CTL_51_VAL
    0x09090023U, // DDRSS_CTL_52_VAL
    0x0900210FU, // DDRSS_CTL_53_VAL
    0x000A0A09U, // DDRSS_CTL_54_VAL
    0x040006DBU, // DDRSS_CTL_55_VAL
    0x09092004U, // DDRSS_CTL_56_VAL
    0x00000C0AU, // DDRSS_CTL_57_VAL
    0x06006DB0U, // DDRSS_CTL_58_VAL
    0x09092006U, // DDRSS_CTL_59_VAL
    0x00000C0AU, // DDRSS_CTL_60_VAL
    0x06006DB0U, // DDRSS_CTL_61_VAL
    0x03042006U, // DDRSS_CTL_62_VAL
    0x04050002U, // DDRSS_CTL_63_VAL
    0x100F100FU, // DDRSS_CTL_64_VAL
    0x01010008U, // DDRSS_CTL_65_VAL
    0x041F1F07U, // DDRSS_CTL_66_VAL
    0x03111103U, // DDRSS_CTL_67_VAL
    0x00001111U, // DDRSS_CTL_68_VAL
    0x00000101U, // DDRSS_CTL_69_VAL
    0x00000000U, // DDRSS_CTL_70_VAL
    0x01000000U, // DDRSS_CTL_71_VAL
    0x00090803U, // DDRSS_CTL_72_VAL
    0x000000BBU, // DDRSS_CTL_73_VAL
    0x00000090U, // DDRSS_CTL_74_VAL
    0x00000C2BU, // DDRSS_CTL_75_VAL
    0x00000090U, // DDRSS_CTL_76_VAL
    0x00000C2BU, // DDRSS_CTL_77_VAL
    0x00000005U, // DDRSS_CTL_78_VAL
    0x00000005U, // DDRSS_CTL_79_VAL
    0x00000010U, // DDRSS_CTL_80_VAL
    0x00000048U, // DDRSS_CTL_81_VAL
    0x0000017EU, // DDRSS_CTL_82_VAL
    0x00000048U, // DDRSS_CTL_83_VAL
    0x0000017EU, // DDRSS_CTL_84_VAL
    0x03004000U, // DDRSS_CTL_85_VAL
    0x00001201U, // DDRSS_CTL_86_VAL
    0x00060005U, // DDRSS_CTL_87_VAL
    0x00000006U, // DDRSS_CTL_88_VAL
    0x00000000U, // DDRSS_CTL_89_VAL
    0x05121208U, // DDRSS_CTL_90_VAL
    0x05030A05U, // DDRSS_CTL_91_VAL
    0x05030C06U, // DDRSS_CTL_92_VAL
    0x01030C06U, // DDRSS_CTL_93_VAL
    0x02010201U, // DDRSS_CTL_94_VAL
    0x00000A01U, // DDRSS_CTL_95_VAL
    0x0096000AU, // DDRSS_CTL_96_VAL
    0x00960096U, // DDRSS_CTL_97_VAL
    0x00000096U, // DDRSS_CTL_98_VAL
    0x00000000U, // DDRSS_CTL_99_VAL
    0x05010303U, // DDRSS_CTL_100_VAL
    0x0C040505U, // DDRSS_CTL_101_VAL
    0x06050203U, // DDRSS_CTL_102_VAL
    0x030C0605U, // DDRSS_CTL_103_VAL
    0x05060502U, // DDRSS_CTL_104_VAL
    0x03030306U, // DDRSS_CTL_105_VAL
    0x03010000U, // DDRSS_CTL_106_VAL
    0x00010000U, // DDRSS_CTL_107_VAL
    0x00000000U, // DDRSS_CTL_108_VAL
    0x01000000U, // DDRSS_CTL_109_VAL
    0x80104002U, // DDRSS_CTL_110_VAL
    0x00040003U, // DDRSS_CTL_111_VAL
    0x00040005U, // DDRSS_CTL_112_VAL
    0x00030000U, // DDRSS_CTL_113_VAL
    0x00050004U, // DDRSS_CTL_114_VAL
    0x00000004U, // DDRSS_CTL_115_VAL
    0x00040003U, // DDRSS_CTL_116_VAL
    0x00040005U, // DDRSS_CTL_117_VAL
    0x00000000U, // DDRSS_CTL_118_VAL
    0x00002EC0U, // DDRSS_CTL_119_VAL
    0x00002EC0U, // DDRSS_CTL_120_VAL
    0x00002EC0U, // DDRSS_CTL_121_VAL
    0x00002EC0U, // DDRSS_CTL_122_VAL
    0x00002EC0U, // DDRSS_CTL_123_VAL
    0x00000000U, // DDRSS_CTL_124_VAL
    0x0000051DU, // DDRSS_CTL_125_VAL
    0x00030AC0U, // DDRSS_CTL_126_VAL
    0x00030AC0U, // DDRSS_CTL_127_VAL
    0x00030AC0U, // DDRSS_CTL_128_VAL
    0x00030AC0U, // DDRSS_CTL_129_VAL
    0x00030AC0U, // DDRSS_CTL_130_VAL
    0x00000000U, // DDRSS_CTL_131_VAL
    0x0000552DU, // DDRSS_CTL_132_VAL
    0x00030AC0U, // DDRSS_CTL_133_VAL
    0x00030AC0U, // DDRSS_CTL_134_VAL
    0x00030AC0U, // DDRSS_CTL_135_VAL
    0x00030AC0U, // DDRSS_CTL_136_VAL
    0x00030AC0U, // DDRSS_CTL_137_VAL
    0x00000000U, // DDRSS_CTL_138_VAL
    0x0000552DU, // DDRSS_CTL_139_VAL
    0x00000000U, // DDRSS_CTL_140_VAL
    0x00000000U, // DDRSS_CTL_141_VAL
    0x00000000U, // DDRSS_CTL_142_VAL
    0x00000000U, // DDRSS_CTL_143_VAL
    0x00000000U, // DDRSS_CTL_144_VAL
    0x00000000U, // DDRSS_CTL_145_VAL
    0x00000000U, // DDRSS_CTL_146_VAL
    0x00000000U, // DDRSS_CTL_147_VAL
    0x00000000U, // DDRSS_CTL_148_VAL
    0x00000000U, // DDRSS_CTL_149_VAL
    0x00000000U, // DDRSS_CTL_150_VAL
    0x00000000U, // DDRSS_CTL_151_VAL
    0x00000000U, // DDRSS_CTL_152_VAL
    0x00000000U, // DDRSS_CTL_153_VAL
    0x00000000U, // DDRSS_CTL_154_VAL
    0x00000000U, // DDRSS_CTL_155_VAL
    0x03050000U, // DDRSS_CTL_156_VAL
    0x03050305U, // DDRSS_CTL_157_VAL
    0x00000000U, // DDRSS_CTL_158_VAL
    0x08010000U, // DDRSS_CTL_159_VAL
    0x000E0808U, // DDRSS_CTL_160_VAL
    0x01000000U, // DDRSS_CTL_161_VAL
    0x0E080808U, // DDRSS_CTL_162_VAL
    0x00000000U, // DDRSS_CTL_163_VAL
    0x08080801U, // DDRSS_CTL_164_VAL
    0x0000080EU, // DDRSS_CTL_165_VAL
    0x00040003U, // DDRSS_CTL_166_VAL
    0x00000007U, // DDRSS_CTL_167_VAL
    0x00000000U, // DDRSS_CTL_168_VAL
    0x00000000U, // DDRSS_CTL_169_VAL
    0x00000000U, // DDRSS_CTL_170_VAL
    0x00000000U, // DDRSS_CTL_171_VAL
    0x00000000U, // DDRSS_CTL_172_VAL
    0x00000000U, // DDRSS_CTL_173_VAL
    0x01000000U, // DDRSS_CTL_174_VAL
    0x00000000U, // DDRSS_CTL_175_VAL
    0x00001500U, // DDRSS_CTL_176_VAL
    0x0000100EU, // DDRSS_CTL_177_VAL
    0x00000002U, // DDRSS_CTL_178_VAL
    0x00000000U, // DDRSS_CTL_179_VAL
    0x00000001U, // DDRSS_CTL_180_VAL
    0x00000002U, // DDRSS_CTL_181_VAL
    0x00000C00U, // DDRSS_CTL_182_VAL
    0x00001000U, // DDRSS_CTL_183_VAL
    0x00000C00U, // DDRSS_CTL_184_VAL
    0x00001000U, // DDRSS_CTL_185_VAL
    0x00000C00U, // DDRSS_CTL_186_VAL
    0x00001000U, // DDRSS_CTL_187_VAL
    0x00000000U, // DDRSS_CTL_188_VAL
    0x00000000U, // DDRSS_CTL_189_VAL
    0x00000000U, // DDRSS_CTL_190_VAL
    0x00000000U, // DDRSS_CTL_191_VAL
    0x0005000AU, // DDRSS_CTL_192_VAL
    0x0404000DU, // DDRSS_CTL_193_VAL
    0x0000000DU, // DDRSS_CTL_194_VAL
    0x005000A0U, // DDRSS_CTL_195_VAL
    0x060600C8U, // DDRSS_CTL_196_VAL
    0x000000C8U, // DDRSS_CTL_197_VAL
    0x005000A0U, // DDRSS_CTL_198_VAL
    0x060600C8U, // DDRSS_CTL_199_VAL
    0x000000C8U, // DDRSS_CTL_200_VAL
    0x00000000U, // DDRSS_CTL_201_VAL
    0x00000000U, // DDRSS_CTL_202_VAL
    0x00000000U, // DDRSS_CTL_203_VAL
    0x00000000U, // DDRSS_CTL_204_VAL
    0x00000004U, // DDRSS_CTL_205_VAL
    0x00000000U, // DDRSS_CTL_206_VAL
    0x00000000U, // DDRSS_CTL_207_VAL
    0x00000024U, // DDRSS_CTL_208_VAL
    0x00000012U, // DDRSS_CTL_209_VAL
    0x00000000U, // DDRSS_CTL_210_VAL
    0x00000024U, // DDRSS_CTL_211_VAL
    0x00000012U, // DDRSS_CTL_212_VAL
    0x00000000U, // DDRSS_CTL_213_VAL
    0x00000004U, // DDRSS_CTL_214_VAL
    0x00000000U, // DDRSS_CTL_215_VAL
    0x00000000U, // DDRSS_CTL_216_VAL
    0x00000024U, // DDRSS_CTL_217_VAL
    0x00000012U, // DDRSS_CTL_218_VAL
    0x00000000U, // DDRSS_CTL_219_VAL
    0x00000024U, // DDRSS_CTL_220_VAL
    0x00000012U, // DDRSS_CTL_221_VAL
    0x00000000U, // DDRSS_CTL_222_VAL
    0x00000000U, // DDRSS_CTL_223_VAL
    0x00000031U, // DDRSS_CTL_224_VAL
    0x000000B1U, // DDRSS_CTL_225_VAL
    0x000000B1U, // DDRSS_CTL_226_VAL
    0x00000031U, // DDRSS_CTL_227_VAL
    0x000000B1U, // DDRSS_CTL_228_VAL
    0x000000B1U, // DDRSS_CTL_229_VAL
    0x00000000U, // DDRSS_CTL_230_VAL
    0x00000000U, // DDRSS_CTL_231_VAL
    0x00000000U, // DDRSS_CTL_232_VAL
    0x00000000U, // DDRSS_CTL_233_VAL
    0x00000000U, // DDRSS_CTL_234_VAL
    0x00000000U, // DDRSS_CTL_235_VAL
    0x00000000U, // DDRSS_CTL_236_VAL
    0x00000000U, // DDRSS_CTL_237_VAL
    0x00000000U, // DDRSS_CTL_238_VAL
    0x00000000U, // DDRSS_CTL_239_VAL
    0x00000000U, // DDRSS_CTL_240_VAL
    0x00000000U, // DDRSS_CTL_241_VAL
    0x00000000U, // DDRSS_CTL_242_VAL
    0x00000000U, // DDRSS_CTL_243_VAL
    0x00000000U, // DDRSS_CTL_244_VAL
    0x00000000U, // DDRSS_CTL_245_VAL
    0x00000000U, // DDRSS_CTL_246_VAL
    0x00000000U, // DDRSS_CTL_247_VAL
    0x00000000U, // DDRSS_CTL_248_VAL
    0x00000000U, // DDRSS_CTL_249_VAL
    0x00000000U, // DDRSS_CTL_250_VAL
    0x00000000U, // DDRSS_CTL_251_VAL
    0x00000000U, // DDRSS_CTL_252_VAL
    0x00000000U, // DDRSS_CTL_253_VAL
    0x65006565U, // DDRSS_CTL_254_VAL
    0x00002765U, // DDRSS_CTL_255_VAL
    0x00000027U, // DDRSS_CTL_256_VAL
    0x00000027U, // DDRSS_CTL_257_VAL
    0x00000027U, // DDRSS_CTL_258_VAL
    0x00000027U, // DDRSS_CTL_259_VAL
    0x00000027U, // DDRSS_CTL_260_VAL
    0x00000000U, // DDRSS_CTL_261_VAL
    0x00000000U, // DDRSS_CTL_262_VAL
    0x0000000FU, // DDRSS_CTL_263_VAL
    0x0000000FU, // DDRSS_CTL_264_VAL
    0x0000000FU, // DDRSS_CTL_265_VAL
    0x0000000FU, // DDRSS_CTL_266_VAL
    0x0000000FU, // DDRSS_CTL_267_VAL
    0x0000000FU, // DDRSS_CTL_268_VAL
    0x00000000U, // DDRSS_CTL_269_VAL
    0x00001000U, // DDRSS_CTL_270_VAL
    0x00000015U, // DDRSS_CTL_271_VAL
    0x00000015U, // DDRSS_CTL_272_VAL
    0x00000010U, // DDRSS_CTL_273_VAL
    0x00000015U, // DDRSS_CTL_274_VAL
    0x00000015U, // DDRSS_CTL_275_VAL
    0x00000020U, // DDRSS_CTL_276_VAL
    0x00010000U, // DDRSS_CTL_277_VAL
    0x00000100U, // DDRSS_CTL_278_VAL
    0x00000000U, // DDRSS_CTL_279_VAL
    0x00000000U, // DDRSS_CTL_280_VAL
    0x00000101U, // DDRSS_CTL_281_VAL
    0x00000000U, // DDRSS_CTL_282_VAL
    0x00000000U, // DDRSS_CTL_283_VAL
    0x00000000U, // DDRSS_CTL_284_VAL
    0x00000000U, // DDRSS_CTL_285_VAL
    0x00000000U, // DDRSS_CTL_286_VAL
    0x00000000U, // DDRSS_CTL_287_VAL
    0x00000000U, // DDRSS_CTL_288_VAL
    0x00000000U, // DDRSS_CTL_289_VAL
    0x0C181511U, // DDRSS_CTL_290_VAL
    0x00000304U, // DDRSS_CTL_291_VAL
    0x00000000U, // DDRSS_CTL_292_VAL
    0x00000000U, // DDRSS_CTL_293_VAL
    0x00000000U, // DDRSS_CTL_294_VAL
    0x00000000U, // DDRSS_CTL_295_VAL
    0x00000000U, // DDRSS_CTL_296_VAL
    0x00000000U, // DDRSS_CTL_297_VAL
    0x00000000U, // DDRSS_CTL_298_VAL
    0x00000000U, // DDRSS_CTL_299_VAL
    0x00000000U, // DDRSS_CTL_300_VAL
    0x00000000U, // DDRSS_CTL_301_VAL
    0x00000000U, // DDRSS_CTL_302_VAL
    0x00000000U, // DDRSS_CTL_303_VAL
    0x00000000U, // DDRSS_CTL_304_VAL
    0x00020000U, // DDRSS_CTL_305_VAL
    0x00400100U, // DDRSS_CTL_306_VAL
    0x00080032U, // DDRSS_CTL_307_VAL
    0x01000200U, // DDRSS_CTL_308_VAL
    0x03200040U, // DDRSS_CTL_309_VAL
    0x00020018U, // DDRSS_CTL_310_VAL
    0x00400100U, // DDRSS_CTL_311_VAL
    0x00180320U, // DDRSS_CTL_312_VAL
    0x00030000U, // DDRSS_CTL_313_VAL
    0x00280028U, // DDRSS_CTL_314_VAL
    0x00000100U, // DDRSS_CTL_315_VAL
    0x01010000U, // DDRSS_CTL_316_VAL
    0x00000202U, // DDRSS_CTL_317_VAL
    0x0FFF0000U, // DDRSS_CTL_318_VAL
    0x000FFF00U, // DDRSS_CTL_319_VAL
    0xFFFFFFFFU, // DDRSS_CTL_320_VAL
    0x00FFFF00U, // DDRSS_CTL_321_VAL
    0x0B000000U, // DDRSS_CTL_322_VAL
    0x0001FFFFU, // DDRSS_CTL_323_VAL
    0x01010101U, // DDRSS_CTL_324_VAL
    0x01010101U, // DDRSS_CTL_325_VAL
    0x00000118U, // DDRSS_CTL_326_VAL
    0x00000C01U, // DDRSS_CTL_327_VAL
    0x01000100U, // DDRSS_CTL_328_VAL
    0x00000000U, // DDRSS_CTL_329_VAL
    0x00000000U, // DDRSS_CTL_330_VAL
    0x01030303U, // DDRSS_CTL_331_VAL
    0x00000001U, // DDRSS_CTL_332_VAL
    0x00000000U, // DDRSS_CTL_333_VAL
    0x00000000U, // DDRSS_CTL_334_VAL
    0x00000000U, // DDRSS_CTL_335_VAL
    0x00000000U, // DDRSS_CTL_336_VAL
    0x00000000U, // DDRSS_CTL_337_VAL
    0x00000000U, // DDRSS_CTL_338_VAL
    0x00000000U, // DDRSS_CTL_339_VAL
    0x00000000U, // DDRSS_CTL_340_VAL
    0x00000000U, // DDRSS_CTL_341_VAL
    0x00000000U, // DDRSS_CTL_342_VAL
    0x00000000U, // DDRSS_CTL_343_VAL
    0x00000000U, // DDRSS_CTL_344_VAL
    0x00000000U, // DDRSS_CTL_345_VAL
    0x00000000U, // DDRSS_CTL_346_VAL
    0x00000000U, // DDRSS_CTL_347_VAL
    0x00000000U, // DDRSS_CTL_348_VAL
    0x00000000U, // DDRSS_CTL_349_VAL
    0x00000000U, // DDRSS_CTL_350_VAL
    0x00000000U, // DDRSS_CTL_351_VAL
    0x00000000U, // DDRSS_CTL_352_VAL
    0x00000000U, // DDRSS_CTL_353_VAL
    0x00000000U, // DDRSS_CTL_354_VAL
    0x00000000U, // DDRSS_CTL_355_VAL
    0x00000000U, // DDRSS_CTL_356_VAL
    0x00000000U, // DDRSS_CTL_357_VAL
    0x00000000U, // DDRSS_CTL_358_VAL
    0x00000000U, // DDRSS_CTL_359_VAL
    0x00000000U, // DDRSS_CTL_360_VAL
    0x00000000U, // DDRSS_CTL_361_VAL
    0x00000000U, // DDRSS_CTL_362_VAL
    0x00000000U, // DDRSS_CTL_363_VAL
    0x00000000U, // DDRSS_CTL_364_VAL
    0x00000000U, // DDRSS_CTL_365_VAL
    0x00000000U, // DDRSS_CTL_366_VAL
    0x00000000U, // DDRSS_CTL_367_VAL
    0x00000000U, // DDRSS_CTL_368_VAL
    0x00000000U, // DDRSS_CTL_369_VAL
    0x00000000U, // DDRSS_CTL_370_VAL
    0x01000101U, // DDRSS_CTL_371_VAL
    0x01010001U, // DDRSS_CTL_372_VAL
    0x00010101U, // DDRSS_CTL_373_VAL
    0x01050503U, // DDRSS_CTL_374_VAL
    0x05020201U, // DDRSS_CTL_375_VAL
    0x08080C0CU, // DDRSS_CTL_376_VAL
    0x00080308U, // DDRSS_CTL_377_VAL
    0x000B030EU, // DDRSS_CTL_378_VAL
    0x000B0310U, // DDRSS_CTL_379_VAL
    0x0B0B0810U, // DDRSS_CTL_380_VAL
    0x01000000U, // DDRSS_CTL_381_VAL
    0x03020301U, // DDRSS_CTL_382_VAL
    0x04000102U, // DDRSS_CTL_383_VAL
    0x1B000004U, // DDRSS_CTL_384_VAL
    0x00000176U, // DDRSS_CTL_385_VAL
    0x00000200U, // DDRSS_CTL_386_VAL
    0x00000200U, // DDRSS_CTL_387_VAL
    0x00000200U, // DDRSS_CTL_388_VAL
    0x00000200U, // DDRSS_CTL_389_VAL
    0x00000693U, // DDRSS_CTL_390_VAL
    0x00000E9CU, // DDRSS_CTL_391_VAL
    0x03050202U, // DDRSS_CTL_392_VAL
    0x00250201U, // DDRSS_CTL_393_VAL
    0x00001856U, // DDRSS_CTL_394_VAL
    0x00000200U, // DDRSS_CTL_395_VAL
    0x00000200U, // DDRSS_CTL_396_VAL
    0x00000200U, // DDRSS_CTL_397_VAL
    0x00000200U, // DDRSS_CTL_398_VAL
    0x00006D83U, // DDRSS_CTL_399_VAL
    0x0000F35CU, // DDRSS_CTL_400_VAL
    0x070D0402U, // DDRSS_CTL_401_VAL
    0x00250405U, // DDRSS_CTL_402_VAL
    0x00001856U, // DDRSS_CTL_403_VAL
    0x00000200U, // DDRSS_CTL_404_VAL
    0x00000200U, // DDRSS_CTL_405_VAL
    0x00000200U, // DDRSS_CTL_406_VAL
    0x00000200U, // DDRSS_CTL_407_VAL
    0x00006D83U, // DDRSS_CTL_408_VAL
    0x0000F35CU, // DDRSS_CTL_409_VAL
    0x070D0402U, // DDRSS_CTL_410_VAL
    0x00000405U, // DDRSS_CTL_411_VAL
    0x00000000U, // DDRSS_CTL_412_VAL
    0x0302000AU, // DDRSS_CTL_413_VAL
    0x01000500U, // DDRSS_CTL_414_VAL
    0x01010001U, // DDRSS_CTL_415_VAL
    0x00010001U, // DDRSS_CTL_416_VAL
    0x01010001U, // DDRSS_CTL_417_VAL
    0x02010000U, // DDRSS_CTL_418_VAL
    0x00000200U, // DDRSS_CTL_419_VAL
    0x02000201U, // DDRSS_CTL_420_VAL
    0x10100600U, // DDRSS_CTL_421_VAL
    0x00202020U, // DDRSS_CTL_422_VAL
};

uint32_t DDRSS_phyIndepReg[] = {
    0x00000B00U, // DDRSS_PI_0_VAL
    0x00000000U, // DDRSS_PI_1_VAL
    0x00000000U, // DDRSS_PI_2_VAL
    0x01000000U, // DDRSS_PI_3_VAL
    0x00000001U, // DDRSS_PI_4_VAL
    0x00010064U, // DDRSS_PI_5_VAL
    0x00000000U, // DDRSS_PI_6_VAL
    0x00000000U, // DDRSS_PI_7_VAL
    0x00000000U, // DDRSS_PI_8_VAL
    0x00000000U, // DDRSS_PI_9_VAL
    0x00000000U, // DDRSS_PI_10_VAL
    0x00000002U, // DDRSS_PI_11_VAL
    0x00000005U, // DDRSS_PI_12_VAL
    0x00010001U, // DDRSS_PI_13_VAL
    0x08000000U, // DDRSS_PI_14_VAL
    0x00010300U, // DDRSS_PI_15_VAL
    0x00000005U, // DDRSS_PI_16_VAL
    0x00000000U, // DDRSS_PI_17_VAL
    0x00000000U, // DDRSS_PI_18_VAL
    0x00000000U, // DDRSS_PI_19_VAL
    0x00000000U, // DDRSS_PI_20_VAL
    0x00000000U, // DDRSS_PI_21_VAL
    0x00000000U, // DDRSS_PI_22_VAL
    0x00010000U, // DDRSS_PI_23_VAL
    0x280A0001U, // DDRSS_PI_24_VAL
    0x00000000U, // DDRSS_PI_25_VAL
    0x00010000U, // DDRSS_PI_26_VAL
    0x00003200U, // DDRSS_PI_27_VAL
    0x00000000U, // DDRSS_PI_28_VAL
    0x00000000U, // DDRSS_PI_29_VAL
    0x01010102U, // DDRSS_PI_30_VAL
    0x00000000U, // DDRSS_PI_31_VAL
    0x00000000U, // DDRSS_PI_32_VAL
    0x00000000U, // DDRSS_PI_33_VAL
    0x00000001U, // DDRSS_PI_34_VAL
    0x000000AAU, // DDRSS_PI_35_VAL
    0x00000055U, // DDRSS_PI_36_VAL
    0x000000B5U, // DDRSS_PI_37_VAL
    0x0000004AU, // DDRSS_PI_38_VAL
    0x00000056U, // DDRSS_PI_39_VAL
    0x000000A9U, // DDRSS_PI_40_VAL
    0x000000A9U, // DDRSS_PI_41_VAL
    0x000000B5U, // DDRSS_PI_42_VAL
    0x00000000U, // DDRSS_PI_43_VAL
    0x00000000U, // DDRSS_PI_44_VAL
    0x00010100U, // DDRSS_PI_45_VAL
    0x00000015U, // DDRSS_PI_46_VAL
    0x000007D0U, // DDRSS_PI_47_VAL
    0x00000300U, // DDRSS_PI_48_VAL
    0x00000000U, // DDRSS_PI_49_VAL
    0x00000000U, // DDRSS_PI_50_VAL
    0x01000000U, // DDRSS_PI_51_VAL
    0x00010101U, // DDRSS_PI_52_VAL
    0x01000000U, // DDRSS_PI_53_VAL
    0x03000000U, // DDRSS_PI_54_VAL
    0x00000000U, // DDRSS_PI_55_VAL
    0x00001701U, // DDRSS_PI_56_VAL
    0x00000000U, // DDRSS_PI_57_VAL
    0x00000000U, // DDRSS_PI_58_VAL
    0x00000000U, // DDRSS_PI_59_VAL
    0x0A0A140AU, // DDRSS_PI_60_VAL
    0x10020101U, // DDRSS_PI_61_VAL
    0x01000210U, // DDRSS_PI_62_VAL
    0x05000404U, // DDRSS_PI_63_VAL
    0x00010001U, // DDRSS_PI_64_VAL
    0x0001000EU, // DDRSS_PI_65_VAL
    0x01010100U, // DDRSS_PI_66_VAL
    0x00010000U, // DDRSS_PI_67_VAL
    0x00000034U, // DDRSS_PI_68_VAL
    0x00000000U, // DDRSS_PI_69_VAL
    0x00000000U, // DDRSS_PI_70_VAL
    0x0000FFFFU, // DDRSS_PI_71_VAL
    0x00000000U, // DDRSS_PI_72_VAL
    0x00000000U, // DDRSS_PI_73_VAL
    0x00000000U, // DDRSS_PI_74_VAL
    0x00000000U, // DDRSS_PI_75_VAL
    0x01000000U, // DDRSS_PI_76_VAL
    0x08020100U, // DDRSS_PI_77_VAL
    0x00020000U, // DDRSS_PI_78_VAL
    0x00010002U, // DDRSS_PI_79_VAL
    0x00000001U, // DDRSS_PI_80_VAL
    0x00020001U, // DDRSS_PI_81_VAL
    0x00020002U, // DDRSS_PI_82_VAL
    0x00000000U, // DDRSS_PI_83_VAL
    0x00000000U, // DDRSS_PI_84_VAL
    0x00000000U, // DDRSS_PI_85_VAL
    0x00000000U, // DDRSS_PI_86_VAL
    0x00000000U, // DDRSS_PI_87_VAL
    0x00000000U, // DDRSS_PI_88_VAL
    0x00000000U, // DDRSS_PI_89_VAL
    0x00000000U, // DDRSS_PI_90_VAL
    0x00000400U, // DDRSS_PI_91_VAL
    0x0A090B0CU, // DDRSS_PI_92_VAL
    0x04060708U, // DDRSS_PI_93_VAL
    0x01000005U, // DDRSS_PI_94_VAL
    0x00000800U, // DDRSS_PI_95_VAL
    0x00000000U, // DDRSS_PI_96_VAL
    0x00010008U, // DDRSS_PI_97_VAL
    0x00000000U, // DDRSS_PI_98_VAL
    0x0000AA00U, // DDRSS_PI_99_VAL
    0x00000000U, // DDRSS_PI_100_VAL
    0x00010000U, // DDRSS_PI_101_VAL
    0x00000000U, // DDRSS_PI_102_VAL
    0x00000000U, // DDRSS_PI_103_VAL
    0x00000000U, // DDRSS_PI_104_VAL
    0x00000000U, // DDRSS_PI_105_VAL
    0x00000000U, // DDRSS_PI_106_VAL
    0x00000000U, // DDRSS_PI_107_VAL
    0x00000000U, // DDRSS_PI_108_VAL
    0x00000000U, // DDRSS_PI_109_VAL
    0x00000000U, // DDRSS_PI_110_VAL
    0x00000000U, // DDRSS_PI_111_VAL
    0x00000000U, // DDRSS_PI_112_VAL
    0x00000000U, // DDRSS_PI_113_VAL
    0x00000000U, // DDRSS_PI_114_VAL
    0x00000000U, // DDRSS_PI_115_VAL
    0x00000000U, // DDRSS_PI_116_VAL
    0x00000000U, // DDRSS_PI_117_VAL
    0x00000000U, // DDRSS_PI_118_VAL
    0x00000000U, // DDRSS_PI_119_VAL
    0x00000000U, // DDRSS_PI_120_VAL
    0x00000000U, // DDRSS_PI_121_VAL
    0x00000000U, // DDRSS_PI_122_VAL
    0x00000000U, // DDRSS_PI_123_VAL
    0x00000008U, // DDRSS_PI_124_VAL
    0x00000000U, // DDRSS_PI_125_VAL
    0x00000000U, // DDRSS_PI_126_VAL
    0x00000000U, // DDRSS_PI_127_VAL
    0x00000000U, // DDRSS_PI_128_VAL
    0x00000000U, // DDRSS_PI_129_VAL
    0x00000000U, // DDRSS_PI_130_VAL
    0x00000000U, // DDRSS_PI_131_VAL
    0x00000000U, // DDRSS_PI_132_VAL
    0x00010000U, // DDRSS_PI_133_VAL
    0x00000000U, // DDRSS_PI_134_VAL
    0x00000000U, // DDRSS_PI_135_VAL
    0x0000000AU, // DDRSS_PI_136_VAL
    0x000186A0U, // DDRSS_PI_137_VAL
    0x00000100U, // DDRSS_PI_138_VAL
    0x00000000U, // DDRSS_PI_139_VAL
    0x00000000U, // DDRSS_PI_140_VAL
    0x00000000U, // DDRSS_PI_141_VAL
    0x00000000U, // DDRSS_PI_142_VAL
    0x00000000U, // DDRSS_PI_143_VAL
    0x01000000U, // DDRSS_PI_144_VAL
    0x00010003U, // DDRSS_PI_145_VAL
    0x02000101U, // DDRSS_PI_146_VAL
    0x01030001U, // DDRSS_PI_147_VAL
    0x00010400U, // DDRSS_PI_148_VAL
    0x06000105U, // DDRSS_PI_149_VAL
    0x01070001U, // DDRSS_PI_150_VAL
    0x00000000U, // DDRSS_PI_151_VAL
    0x00000000U, // DDRSS_PI_152_VAL
    0x00000000U, // DDRSS_PI_153_VAL
    0x00010001U, // DDRSS_PI_154_VAL
    0x00000000U, // DDRSS_PI_155_VAL
    0x00000000U, // DDRSS_PI_156_VAL
    0x00000000U, // DDRSS_PI_157_VAL
    0x00000000U, // DDRSS_PI_158_VAL
    0x00010000U, // DDRSS_PI_159_VAL
    0x00000004U, // DDRSS_PI_160_VAL
    0x00000000U, // DDRSS_PI_161_VAL
    0x00000000U, // DDRSS_PI_162_VAL
    0x00000000U, // DDRSS_PI_163_VAL
    0x00000800U, // DDRSS_PI_164_VAL
    0x00780078U, // DDRSS_PI_165_VAL
    0x00101001U, // DDRSS_PI_166_VAL
    0x00000034U, // DDRSS_PI_167_VAL
    0x00000042U, // DDRSS_PI_168_VAL
    0x00020042U, // DDRSS_PI_169_VAL
    0x02000200U, // DDRSS_PI_170_VAL
    0x00000004U, // DDRSS_PI_171_VAL
    0x0000080CU, // DDRSS_PI_172_VAL
    0x00081C00U, // DDRSS_PI_173_VAL
    0x001C0000U, // DDRSS_PI_174_VAL
    0x00000009U, // DDRSS_PI_175_VAL
    0x000000BBU, // DDRSS_PI_176_VAL
    0x00000090U, // DDRSS_PI_177_VAL
    0x00000C2BU, // DDRSS_PI_178_VAL
    0x00000090U, // DDRSS_PI_179_VAL
    0x04000C2BU, // DDRSS_PI_180_VAL
    0x01010404U, // DDRSS_PI_181_VAL
    0x00001501U, // DDRSS_PI_182_VAL
    0x001D001DU, // DDRSS_PI_183_VAL
    0x01000100U, // DDRSS_PI_184_VAL
    0x00000100U, // DDRSS_PI_185_VAL
    0x00000000U, // DDRSS_PI_186_VAL
    0x05050503U, // DDRSS_PI_187_VAL
    0x01010C0CU, // DDRSS_PI_188_VAL
    0x01010101U, // DDRSS_PI_189_VAL
    0x000C0C0AU, // DDRSS_PI_190_VAL
    0x00000000U, // DDRSS_PI_191_VAL
    0x00000000U, // DDRSS_PI_192_VAL
    0x04000000U, // DDRSS_PI_193_VAL
    0x04020808U, // DDRSS_PI_194_VAL
    0x04040204U, // DDRSS_PI_195_VAL
    0x00090031U, // DDRSS_PI_196_VAL
    0x00110039U, // DDRSS_PI_197_VAL
    0x00110039U, // DDRSS_PI_198_VAL
    0x01010101U, // DDRSS_PI_199_VAL
    0x0002000DU, // DDRSS_PI_200_VAL
    0x000200C8U, // DDRSS_PI_201_VAL
    0x010000C8U, // DDRSS_PI_202_VAL
    0x000E000EU, // DDRSS_PI_203_VAL
    0x00C90100U, // DDRSS_PI_204_VAL
    0x010000C9U, // DDRSS_PI_205_VAL
    0x00C900C9U, // DDRSS_PI_206_VAL
    0x32103200U, // DDRSS_PI_207_VAL
    0x01013210U, // DDRSS_PI_208_VAL
    0x0A070601U, // DDRSS_PI_209_VAL
    0x0D09070DU, // DDRSS_PI_210_VAL
    0x0D09070DU, // DDRSS_PI_211_VAL
    0x000C000DU, // DDRSS_PI_212_VAL
    0x00001000U, // DDRSS_PI_213_VAL
    0x00000C00U, // DDRSS_PI_214_VAL
    0x00001000U, // DDRSS_PI_215_VAL
    0x00000C00U, // DDRSS_PI_216_VAL
    0x02001000U, // DDRSS_PI_217_VAL
    0x0016000DU, // DDRSS_PI_218_VAL
    0x001600C8U, // DDRSS_PI_219_VAL
    0x000000C8U, // DDRSS_PI_220_VAL
    0x00001900U, // DDRSS_PI_221_VAL
    0x32000056U, // DDRSS_PI_222_VAL
    0x06000101U, // DDRSS_PI_223_VAL
    0x001D0204U, // DDRSS_PI_224_VAL
    0x32120058U, // DDRSS_PI_225_VAL
    0x05000101U, // DDRSS_PI_226_VAL
    0x001D0408U, // DDRSS_PI_227_VAL
    0x32120058U, // DDRSS_PI_228_VAL
    0x05000101U, // DDRSS_PI_229_VAL
    0x00000408U, // DDRSS_PI_230_VAL
    0x05030900U, // DDRSS_PI_231_VAL
    0x00040900U, // DDRSS_PI_232_VAL
    0x0000062BU, // DDRSS_PI_233_VAL
    0x20010004U, // DDRSS_PI_234_VAL
    0x0A0A0A03U, // DDRSS_PI_235_VAL
    0x11090000U, // DDRSS_PI_236_VAL
    0x1009000FU, // DDRSS_PI_237_VAL
    0x000062B8U, // DDRSS_PI_238_VAL
    0x20030023U, // DDRSS_PI_239_VAL
    0x0C0A0C0CU, // DDRSS_PI_240_VAL
    0x11090000U, // DDRSS_PI_241_VAL
    0x1009000FU, // DDRSS_PI_242_VAL
    0x000062B8U, // DDRSS_PI_243_VAL
    0x20030023U, // DDRSS_PI_244_VAL
    0x0C0A0C0CU, // DDRSS_PI_245_VAL
    0x00000000U, // DDRSS_PI_246_VAL
    0x00000176U, // DDRSS_PI_247_VAL
    0x00000E9CU, // DDRSS_PI_248_VAL
    0x00001856U, // DDRSS_PI_249_VAL
    0x0000F35CU, // DDRSS_PI_250_VAL
    0x00001856U, // DDRSS_PI_251_VAL
    0x0000F35CU, // DDRSS_PI_252_VAL
    0x0096000AU, // DDRSS_PI_253_VAL
    0x03030096U, // DDRSS_PI_254_VAL
    0x00000003U, // DDRSS_PI_255_VAL
    0x00000000U, // DDRSS_PI_256_VAL
    0x05030503U, // DDRSS_PI_257_VAL
    0x00000503U, // DDRSS_PI_258_VAL
    0x00002710U, // DDRSS_PI_259_VAL
    0x000186A0U, // DDRSS_PI_260_VAL
    0x00000005U, // DDRSS_PI_261_VAL
    0x00000064U, // DDRSS_PI_262_VAL
    0x0000000AU, // DDRSS_PI_263_VAL
    0x00027100U, // DDRSS_PI_264_VAL
    0x000186A0U, // DDRSS_PI_265_VAL
    0x00000005U, // DDRSS_PI_266_VAL
    0x00000640U, // DDRSS_PI_267_VAL
    0x00000096U, // DDRSS_PI_268_VAL
    0x00027100U, // DDRSS_PI_269_VAL
    0x000186A0U, // DDRSS_PI_270_VAL
    0x00000005U, // DDRSS_PI_271_VAL
    0x00000640U, // DDRSS_PI_272_VAL
    0x01000096U, // DDRSS_PI_273_VAL
    0x00320040U, // DDRSS_PI_274_VAL
    0x00010008U, // DDRSS_PI_275_VAL
    0x03200040U, // DDRSS_PI_276_VAL
    0x00010018U, // DDRSS_PI_277_VAL
    0x03200040U, // DDRSS_PI_278_VAL
    0x00000318U, // DDRSS_PI_279_VAL
    0x00280028U, // DDRSS_PI_280_VAL
    0x03040404U, // DDRSS_PI_281_VAL
    0x00000303U, // DDRSS_PI_282_VAL
    0x02020101U, // DDRSS_PI_283_VAL
    0x67676767U, // DDRSS_PI_284_VAL
    0x00000000U, // DDRSS_PI_285_VAL
    0x55000000U, // DDRSS_PI_286_VAL
    0x00000000U, // DDRSS_PI_287_VAL
    0x3C00005AU, // DDRSS_PI_288_VAL
    0x00005500U, // DDRSS_PI_289_VAL
    0x00005A00U, // DDRSS_PI_290_VAL
    0x0D100F3CU, // DDRSS_PI_291_VAL
    0x0003020EU, // DDRSS_PI_292_VAL
    0x00000001U, // DDRSS_PI_293_VAL
    0x01000000U, // DDRSS_PI_294_VAL
    0x00020201U, // DDRSS_PI_295_VAL
    0x00000000U, // DDRSS_PI_296_VAL
    0x00000000U, // DDRSS_PI_297_VAL
    0x00000004U, // DDRSS_PI_298_VAL
    0x00000000U, // DDRSS_PI_299_VAL
    0x00000031U, // DDRSS_PI_300_VAL
    0x00000000U, // DDRSS_PI_301_VAL
    0x00000000U, // DDRSS_PI_302_VAL
    0x00000000U, // DDRSS_PI_303_VAL
    0x00100F27U, // DDRSS_PI_304_VAL
    0x00000000U, // DDRSS_PI_305_VAL
    0x00000024U, // DDRSS_PI_306_VAL
    0x00000012U, // DDRSS_PI_307_VAL
    0x000000B1U, // DDRSS_PI_308_VAL
    0x00000000U, // DDRSS_PI_309_VAL
    0x00000000U, // DDRSS_PI_310_VAL
    0x65000000U, // DDRSS_PI_311_VAL
    0x00150F27U, // DDRSS_PI_312_VAL
    0x00000000U, // DDRSS_PI_313_VAL
    0x00000024U, // DDRSS_PI_314_VAL
    0x00000012U, // DDRSS_PI_315_VAL
    0x000000B1U, // DDRSS_PI_316_VAL
    0x00000000U, // DDRSS_PI_317_VAL
    0x00000000U, // DDRSS_PI_318_VAL
    0x65000000U, // DDRSS_PI_319_VAL
    0x00150F27U, // DDRSS_PI_320_VAL
    0x00000000U, // DDRSS_PI_321_VAL
    0x00000004U, // DDRSS_PI_322_VAL
    0x00000000U, // DDRSS_PI_323_VAL
    0x00000031U, // DDRSS_PI_324_VAL
    0x00000000U, // DDRSS_PI_325_VAL
    0x00000000U, // DDRSS_PI_326_VAL
    0x00000000U, // DDRSS_PI_327_VAL
    0x00100F27U, // DDRSS_PI_328_VAL
    0x00000000U, // DDRSS_PI_329_VAL
    0x00000024U, // DDRSS_PI_330_VAL
    0x00000012U, // DDRSS_PI_331_VAL
    0x000000B1U, // DDRSS_PI_332_VAL
    0x00000000U, // DDRSS_PI_333_VAL
    0x00000000U, // DDRSS_PI_334_VAL
    0x65000000U, // DDRSS_PI_335_VAL
    0x00150F27U, // DDRSS_PI_336_VAL
    0x00000000U, // DDRSS_PI_337_VAL
    0x00000024U, // DDRSS_PI_338_VAL
    0x00000012U, // DDRSS_PI_339_VAL
    0x000000B1U, // DDRSS_PI_340_VAL
    0x00000000U, // DDRSS_PI_341_VAL
    0x00000000U, // DDRSS_PI_342_VAL
    0x65000000U, // DDRSS_PI_343_VAL
    0x00150F27U, // DDRSS_PI_344_VAL
};

uint32_t DDRSS_phyReg[] = {
    0x04F00000U, // DDRSS_PHY_0_VAL
    0x00000000U, // DDRSS_PHY_1_VAL
    0x00030200U, // DDRSS_PHY_2_VAL
    0x00000000U, // DDRSS_PHY_3_VAL
    0x00000000U, // DDRSS_PHY_4_VAL
    0x01000000U, // DDRSS_PHY_5_VAL
    0x03000400U, // DDRSS_PHY_6_VAL
    0x00000001U, // DDRSS_PHY_7_VAL
    0x00000001U, // DDRSS_PHY_8_VAL
    0x00000000U, // DDRSS_PHY_9_VAL
    0x00000000U, // DDRSS_PHY_10_VAL
    0x01010000U, // DDRSS_PHY_11_VAL
    0x00010000U, // DDRSS_PHY_12_VAL
    0x00C00001U, // DDRSS_PHY_13_VAL
    0x00CC0008U, // DDRSS_PHY_14_VAL
    0x00660601U, // DDRSS_PHY_15_VAL
    0x00000003U, // DDRSS_PHY_16_VAL
    0x00000000U, // DDRSS_PHY_17_VAL
    0x00000000U, // DDRSS_PHY_18_VAL
    0x0000AAAAU, // DDRSS_PHY_19_VAL
    0x00005555U, // DDRSS_PHY_20_VAL
    0x0000B5B5U, // DDRSS_PHY_21_VAL
    0x00004A4AU, // DDRSS_PHY_22_VAL
    0x00005656U, // DDRSS_PHY_23_VAL
    0x0000A9A9U, // DDRSS_PHY_24_VAL
    0x0000B7B7U, // DDRSS_PHY_25_VAL
    0x00004848U, // DDRSS_PHY_26_VAL
    0x00000000U, // DDRSS_PHY_27_VAL
    0x00000000U, // DDRSS_PHY_28_VAL
    0x08000000U, // DDRSS_PHY_29_VAL
    0x0F000008U, // DDRSS_PHY_30_VAL
    0x00000F0FU, // DDRSS_PHY_31_VAL
    0x00E4E400U, // DDRSS_PHY_32_VAL
    0x00071020U, // DDRSS_PHY_33_VAL
    0x000C0020U, // DDRSS_PHY_34_VAL
    0x00062000U, // DDRSS_PHY_35_VAL
    0x00000000U, // DDRSS_PHY_36_VAL
    0x55555555U, // DDRSS_PHY_37_VAL
    0xAAAAAAAAU, // DDRSS_PHY_38_VAL
    0x55555555U, // DDRSS_PHY_39_VAL
    0xAAAAAAAAU, // DDRSS_PHY_40_VAL
    0x00005555U, // DDRSS_PHY_41_VAL
    0x01000100U, // DDRSS_PHY_42_VAL
    0x00800180U, // DDRSS_PHY_43_VAL
    0x00000000U, // DDRSS_PHY_44_VAL // [SWAP] PHY_CALVL_VREF_DRIVING_SLICE_0 //0x00000001 Normal
    0x00000000U, // DDRSS_PHY_45_VAL
    0x00000000U, // DDRSS_PHY_46_VAL
    0x00000000U, // DDRSS_PHY_47_VAL
    0x00000000U, // DDRSS_PHY_48_VAL
    0x00000000U, // DDRSS_PHY_49_VAL
    0x00000000U, // DDRSS_PHY_50_VAL
    0x00000000U, // DDRSS_PHY_51_VAL
    0x00000000U, // DDRSS_PHY_52_VAL
    0x00000000U, // DDRSS_PHY_53_VAL
    0x00000000U, // DDRSS_PHY_54_VAL
    0x00000000U, // DDRSS_PHY_55_VAL
    0x00000000U, // DDRSS_PHY_56_VAL
    0x00000000U, // DDRSS_PHY_57_VAL
    0x00000000U, // DDRSS_PHY_58_VAL
    0x00000000U, // DDRSS_PHY_59_VAL
    0x00000000U, // DDRSS_PHY_60_VAL
    0x00000000U, // DDRSS_PHY_61_VAL
    0x00000000U, // DDRSS_PHY_62_VAL
    0x00000000U, // DDRSS_PHY_63_VAL
    0x00000000U, // DDRSS_PHY_64_VAL
    0x00000004U, // DDRSS_PHY_65_VAL
    0x00000000U, // DDRSS_PHY_66_VAL
    0x00000000U, // DDRSS_PHY_67_VAL
    0x00000000U, // DDRSS_PHY_68_VAL
    0x00000000U, // DDRSS_PHY_69_VAL
    0x00000000U, // DDRSS_PHY_70_VAL
    0x00000000U, // DDRSS_PHY_71_VAL
    0x041F07FFU, // DDRSS_PHY_72_VAL
    0x00000000U, // DDRSS_PHY_73_VAL
    0x01CC0B01U, // DDRSS_PHY_74_VAL
    0x1003CC0BU, // DDRSS_PHY_75_VAL
    0x20000140U, // DDRSS_PHY_76_VAL
    0x07FF0200U, // DDRSS_PHY_77_VAL
    0x0000DD01U, // DDRSS_PHY_78_VAL
    0x00100303U, // DDRSS_PHY_79_VAL
    0x00000000U, // DDRSS_PHY_80_VAL
    0x00000000U, // DDRSS_PHY_81_VAL
    0x00021000U, // DDRSS_PHY_82_VAL
    0x00100010U, // DDRSS_PHY_83_VAL
    0x00100010U, // DDRSS_PHY_84_VAL
    0x00100010U, // DDRSS_PHY_85_VAL
    0x00100010U, // DDRSS_PHY_86_VAL
    0x02020010U, // DDRSS_PHY_87_VAL
    0x51516041U, // DDRSS_PHY_88_VAL
    0x31C06000U, // DDRSS_PHY_89_VAL
    0x07AB0340U, // DDRSS_PHY_90_VAL
    0x0000C0C0U, // DDRSS_PHY_91_VAL
    0x04050000U, // DDRSS_PHY_92_VAL
    0x00000504U, // DDRSS_PHY_93_VAL
    0x42100010U, // DDRSS_PHY_94_VAL
    0x010C053EU, // DDRSS_PHY_95_VAL
    0x000F0C1DU, // DDRSS_PHY_96_VAL
    0x01000140U, // DDRSS_PHY_97_VAL
    0x007A0120U, // DDRSS_PHY_98_VAL
    0x00000C00U, // DDRSS_PHY_99_VAL
    0x000001CCU, // DDRSS_PHY_100_VAL
    0x20100200U, // DDRSS_PHY_101_VAL
    0x00000005U, // DDRSS_PHY_102_VAL
    0x56743210U, // DDRSS_PHY_103_VAL  // [SWIZZLE] PHY_DQ_DM_SWIZZLE0_0 //0x76543210 Swizzle DV 
    0x00000008U, // DDRSS_PHY_104_VAL
    0x034C034CU, // DDRSS_PHY_105_VAL
    0x034C034CU, // DDRSS_PHY_106_VAL
    0x034C034CU, // DDRSS_PHY_107_VAL
    0x034C034CU, // DDRSS_PHY_108_VAL
    0x0000034CU, // DDRSS_PHY_109_VAL
    0x00008000U, // DDRSS_PHY_110_VAL
    0x00800080U, // DDRSS_PHY_111_VAL
    0x00800080U, // DDRSS_PHY_112_VAL
    0x00800080U, // DDRSS_PHY_113_VAL
    0x00800080U, // DDRSS_PHY_114_VAL
    0x00800080U, // DDRSS_PHY_115_VAL
    0x00800080U, // DDRSS_PHY_116_VAL
    0x00800080U, // DDRSS_PHY_117_VAL
    0x00800080U, // DDRSS_PHY_118_VAL
    0x01800080U, // DDRSS_PHY_119_VAL
    0x01000000U, // DDRSS_PHY_120_VAL
    0x00000000U, // DDRSS_PHY_121_VAL
    0x00000000U, // DDRSS_PHY_122_VAL
    0x00080200U, // DDRSS_PHY_123_VAL
    0x00000000U, // DDRSS_PHY_124_VAL
    0x0000F0F0U, // DDRSS_PHY_125_VAL
    0x04F00000U, // DDRSS_PHY_256_VAL
    0x00000000U, // DDRSS_PHY_257_VAL
    0x00030200U, // DDRSS_PHY_258_VAL
    0x00000000U, // DDRSS_PHY_259_VAL
    0x00000000U, // DDRSS_PHY_260_VAL
    0x01000000U, // DDRSS_PHY_261_VAL
    0x03000400U, // DDRSS_PHY_262_VAL
    0x00000001U, // DDRSS_PHY_263_VAL
    0x00000001U, // DDRSS_PHY_264_VAL
    0x00000000U, // DDRSS_PHY_265_VAL
    0x00000000U, // DDRSS_PHY_266_VAL
    0x01010000U, // DDRSS_PHY_267_VAL
    0x00010000U, // DDRSS_PHY_268_VAL
    0x00C00001U, // DDRSS_PHY_269_VAL
    0x00CC0008U, // DDRSS_PHY_270_VAL
    0x00660601U, // DDRSS_PHY_271_VAL
    0x00000003U, // DDRSS_PHY_272_VAL
    0x00000000U, // DDRSS_PHY_273_VAL
    0x00000000U, // DDRSS_PHY_274_VAL
    0x0000AAAAU, // DDRSS_PHY_275_VAL
    0x00005555U, // DDRSS_PHY_276_VAL
    0x0000B5B5U, // DDRSS_PHY_277_VAL
    0x00004A4AU, // DDRSS_PHY_278_VAL
    0x00005656U, // DDRSS_PHY_279_VAL
    0x0000A9A9U, // DDRSS_PHY_280_VAL
    0x0000B7B7U, // DDRSS_PHY_281_VAL
    0x00004848U, // DDRSS_PHY_282_VAL
    0x00000000U, // DDRSS_PHY_283_VAL
    0x00000000U, // DDRSS_PHY_284_VAL
    0x08000000U, // DDRSS_PHY_285_VAL
    0x0F000008U, // DDRSS_PHY_286_VAL
    0x00000F0FU, // DDRSS_PHY_287_VAL
    0x00E4E400U, // DDRSS_PHY_288_VAL
    0x00071020U, // DDRSS_PHY_289_VAL
    0x000C0020U, // DDRSS_PHY_290_VAL
    0x00062000U, // DDRSS_PHY_291_VAL
    0x00000000U, // DDRSS_PHY_292_VAL
    0x55555555U, // DDRSS_PHY_293_VAL
    0xAAAAAAAAU, // DDRSS_PHY_294_VAL
    0x55555555U, // DDRSS_PHY_295_VAL
    0xAAAAAAAAU, // DDRSS_PHY_296_VAL
    0x00005555U, // DDRSS_PHY_297_VAL
    0x01000100U, // DDRSS_PHY_298_VAL
    0x00800180U, // DDRSS_PHY_299_VAL
    0x00000001U, // DDRSS_PHY_300_VAL // [SWAP] PHY_CALVL_VREF_DRIVING_SLICE_1 //0x00000000 Normal
    0x00000000U, // DDRSS_PHY_301_VAL
    0x00000000U, // DDRSS_PHY_302_VAL
    0x00000000U, // DDRSS_PHY_303_VAL
    0x00000000U, // DDRSS_PHY_304_VAL
    0x00000000U, // DDRSS_PHY_305_VAL
    0x00000000U, // DDRSS_PHY_306_VAL
    0x00000000U, // DDRSS_PHY_307_VAL
    0x00000000U, // DDRSS_PHY_308_VAL
    0x00000000U, // DDRSS_PHY_309_VAL
    0x00000000U, // DDRSS_PHY_310_VAL
    0x00000000U, // DDRSS_PHY_311_VAL
    0x00000000U, // DDRSS_PHY_312_VAL
    0x00000000U, // DDRSS_PHY_313_VAL
    0x00000000U, // DDRSS_PHY_314_VAL
    0x00000000U, // DDRSS_PHY_315_VAL
    0x00000000U, // DDRSS_PHY_316_VAL
    0x00000000U, // DDRSS_PHY_317_VAL
    0x00000000U, // DDRSS_PHY_318_VAL
    0x00000000U, // DDRSS_PHY_319_VAL
    0x00000000U, // DDRSS_PHY_320_VAL
    0x00000004U, // DDRSS_PHY_321_VAL
    0x00000000U, // DDRSS_PHY_322_VAL
    0x00000000U, // DDRSS_PHY_323_VAL
    0x00000000U, // DDRSS_PHY_324_VAL
    0x00000000U, // DDRSS_PHY_325_VAL
    0x00000000U, // DDRSS_PHY_326_VAL
    0x00000000U, // DDRSS_PHY_327_VAL
    0x041F07FFU, // DDRSS_PHY_328_VAL
    0x00000000U, // DDRSS_PHY_329_VAL
    0x01CC0B01U, // DDRSS_PHY_330_VAL
    0x1003CC0BU, // DDRSS_PHY_331_VAL
    0x20000140U, // DDRSS_PHY_332_VAL
    0x07FF0200U, // DDRSS_PHY_333_VAL
    0x0000DD01U, // DDRSS_PHY_334_VAL
    0x00100303U, // DDRSS_PHY_335_VAL
    0x00000000U, // DDRSS_PHY_336_VAL
    0x00000000U, // DDRSS_PHY_337_VAL
    0x00021000U, // DDRSS_PHY_338_VAL
    0x00100010U, // DDRSS_PHY_339_VAL
    0x00100010U, // DDRSS_PHY_340_VAL
    0x00100010U, // DDRSS_PHY_341_VAL
    0x00100010U, // DDRSS_PHY_342_VAL
    0x02020010U, // DDRSS_PHY_343_VAL
    0x51516041U, // DDRSS_PHY_344_VAL
    0x31C06000U, // DDRSS_PHY_345_VAL
    0x07AB0340U, // DDRSS_PHY_346_VAL
    0x0000C0C0U, // DDRSS_PHY_347_VAL
    0x04050000U, // DDRSS_PHY_348_VAL
    0x00000504U, // DDRSS_PHY_349_VAL
    0x42100010U, // DDRSS_PHY_350_VAL
    0x010C053EU, // DDRSS_PHY_351_VAL
    0x000F0C1DU, // DDRSS_PHY_352_VAL
    0x01000140U, // DDRSS_PHY_353_VAL
    0x007A0120U, // DDRSS_PHY_354_VAL
    0x00000C00U, // DDRSS_PHY_355_VAL
    0x000001CCU, // DDRSS_PHY_356_VAL
    0x20100200U, // DDRSS_PHY_357_VAL
    0x00000005U, // DDRSS_PHY_358_VAL
    0x01324567U, // DDRSS_PHY_359_VAL // [SWIZZLE] PHY_DQ_DM_SWIZZLE0_1 //0x76543210 //Normal 
    0x00000008U, // DDRSS_PHY_360_VAL
    0x034C034CU, // DDRSS_PHY_361_VAL
    0x034C034CU, // DDRSS_PHY_362_VAL
    0x034C034CU, // DDRSS_PHY_363_VAL
    0x034C034CU, // DDRSS_PHY_364_VAL
    0x0000034CU, // DDRSS_PHY_365_VAL
    0x00008000U, // DDRSS_PHY_366_VAL
    0x00800080U, // DDRSS_PHY_367_VAL
    0x00800080U, // DDRSS_PHY_368_VAL
    0x00800080U, // DDRSS_PHY_369_VAL
    0x00800080U, // DDRSS_PHY_370_VAL
    0x00800080U, // DDRSS_PHY_371_VAL
    0x00800080U, // DDRSS_PHY_372_VAL
    0x00800080U, // DDRSS_PHY_373_VAL
    0x00800080U, // DDRSS_PHY_374_VAL
    0x01800080U, // DDRSS_PHY_375_VAL
    0x01000000U, // DDRSS_PHY_376_VAL
    0x00000000U, // DDRSS_PHY_377_VAL
    0x00000000U, // DDRSS_PHY_378_VAL
    0x00080200U, // DDRSS_PHY_379_VAL
    0x00000000U, // DDRSS_PHY_380_VAL
    0x0000F0F0U, // DDRSS_PHY_381_VAL
    0x00000000U, // DDRSS_PHY_512_VAL
    0x00000000U, // DDRSS_PHY_513_VAL
    0x00000000U, // DDRSS_PHY_514_VAL
    0x00000000U, // DDRSS_PHY_515_VAL
    0x00000000U, // DDRSS_PHY_516_VAL
    0x00000100U, // DDRSS_PHY_517_VAL
    0x00000200U, // DDRSS_PHY_518_VAL
    0x00000000U, // DDRSS_PHY_519_VAL
    0x00000000U, // DDRSS_PHY_520_VAL
    0x00000000U, // DDRSS_PHY_521_VAL
    0x00000000U, // DDRSS_PHY_522_VAL
    0x00400000U, // DDRSS_PHY_523_VAL
    0x00000080U, // DDRSS_PHY_524_VAL
    0x00DCBA98U, // DDRSS_PHY_525_VAL
    0x03000000U, // DDRSS_PHY_526_VAL
    0x00200000U, // DDRSS_PHY_527_VAL
    0x00000000U, // DDRSS_PHY_528_VAL
    0x00000000U, // DDRSS_PHY_529_VAL
    0x00000000U, // DDRSS_PHY_530_VAL
    0x00000000U, // DDRSS_PHY_531_VAL
    0x0000002AU, // DDRSS_PHY_532_VAL
    0x00000015U, // DDRSS_PHY_533_VAL
    0x00000015U, // DDRSS_PHY_534_VAL
    0x0000002AU, // DDRSS_PHY_535_VAL
    0x00000033U, // DDRSS_PHY_536_VAL
    0x0000000CU, // DDRSS_PHY_537_VAL
    0x0000000CU, // DDRSS_PHY_538_VAL
    0x00000033U, // DDRSS_PHY_539_VAL
    0x0A418820U, // DDRSS_PHY_540_VAL
    0x003F0000U, // DDRSS_PHY_541_VAL
    0x000F013FU, // DDRSS_PHY_542_VAL
    0x0000000FU, // DDRSS_PHY_543_VAL
    0x020002CCU, // DDRSS_PHY_544_VAL
    0x00030000U, // DDRSS_PHY_545_VAL
    0x00000300U, // DDRSS_PHY_546_VAL
    0x00000300U, // DDRSS_PHY_547_VAL
    0x00000300U, // DDRSS_PHY_548_VAL
    0x00000300U, // DDRSS_PHY_549_VAL
    0x00000300U, // DDRSS_PHY_550_VAL
    0x42080010U, // DDRSS_PHY_551_VAL
    0x0000803EU, // DDRSS_PHY_552_VAL
    0x00000003U, // DDRSS_PHY_553_VAL
    0x00000002U, // DDRSS_PHY_554_VAL
    0x00000000U, // DDRSS_PHY_768_VAL
    0x00000000U, // DDRSS_PHY_769_VAL
    0x00000000U, // DDRSS_PHY_770_VAL
    0x00000000U, // DDRSS_PHY_771_VAL
    0x00000000U, // DDRSS_PHY_772_VAL
    0x00000100U, // DDRSS_PHY_773_VAL
    0x00000200U, // DDRSS_PHY_774_VAL
    0x00000000U, // DDRSS_PHY_775_VAL
    0x00000000U, // DDRSS_PHY_776_VAL
    0x00000000U, // DDRSS_PHY_777_VAL
    0x00000000U, // DDRSS_PHY_778_VAL
    0x00400000U, // DDRSS_PHY_779_VAL
    0x00000080U, // DDRSS_PHY_780_VAL
    0x00DCBA98U, // DDRSS_PHY_781_VAL
    0x03000000U, // DDRSS_PHY_782_VAL
    0x00200000U, // DDRSS_PHY_783_VAL
    0x00000000U, // DDRSS_PHY_784_VAL
    0x00000000U, // DDRSS_PHY_785_VAL
    0x00000000U, // DDRSS_PHY_786_VAL
    0x00000000U, // DDRSS_PHY_787_VAL
    0x0000002AU, // DDRSS_PHY_788_VAL
    0x00000015U, // DDRSS_PHY_789_VAL
    0x00000015U, // DDRSS_PHY_790_VAL
    0x0000002AU, // DDRSS_PHY_791_VAL
    0x00000033U, // DDRSS_PHY_792_VAL
    0x0000000CU, // DDRSS_PHY_793_VAL
    0x0000000CU, // DDRSS_PHY_794_VAL
    0x00000033U, // DDRSS_PHY_795_VAL
    0x00000000U, // DDRSS_PHY_796_VAL
    0x00000000U, // DDRSS_PHY_797_VAL
    0x000F0000U, // DDRSS_PHY_798_VAL
    0x0000000FU, // DDRSS_PHY_799_VAL
    0x020002CCU, // DDRSS_PHY_800_VAL
    0x00030000U, // DDRSS_PHY_801_VAL
    0x00000300U, // DDRSS_PHY_802_VAL
    0x00000300U, // DDRSS_PHY_803_VAL
    0x00000300U, // DDRSS_PHY_804_VAL
    0x00000300U, // DDRSS_PHY_805_VAL
    0x00000300U, // DDRSS_PHY_806_VAL
    0x42080010U, // DDRSS_PHY_807_VAL
    0x0000803EU, // DDRSS_PHY_808_VAL
    0x00000003U, // DDRSS_PHY_809_VAL
    0x00000002U, // DDRSS_PHY_810_VAL
    0x00000000U, // DDRSS_PHY_1024_VAL
    0x00000000U, // DDRSS_PHY_1025_VAL
    0x00000000U, // DDRSS_PHY_1026_VAL
    0x00000000U, // DDRSS_PHY_1027_VAL
    0x00000000U, // DDRSS_PHY_1028_VAL
    0x00000100U, // DDRSS_PHY_1029_VAL
    0x00000200U, // DDRSS_PHY_1030_VAL
    0x00000000U, // DDRSS_PHY_1031_VAL
    0x00000000U, // DDRSS_PHY_1032_VAL
    0x00000000U, // DDRSS_PHY_1033_VAL
    0x00000000U, // DDRSS_PHY_1034_VAL
    0x00400000U, // DDRSS_PHY_1035_VAL
    0x00000080U, // DDRSS_PHY_1036_VAL
    0x00DCBA98U, // DDRSS_PHY_1037_VAL
    0x03000000U, // DDRSS_PHY_1038_VAL
    0x00200000U, // DDRSS_PHY_1039_VAL
    0x00000000U, // DDRSS_PHY_1040_VAL
    0x00000000U, // DDRSS_PHY_1041_VAL
    0x00000000U, // DDRSS_PHY_1042_VAL
    0x00000000U, // DDRSS_PHY_1043_VAL
    0x0000002AU, // DDRSS_PHY_1044_VAL
    0x00000015U, // DDRSS_PHY_1045_VAL
    0x00000015U, // DDRSS_PHY_1046_VAL
    0x0000002AU, // DDRSS_PHY_1047_VAL
    0x00000033U, // DDRSS_PHY_1048_VAL
    0x0000000CU, // DDRSS_PHY_1049_VAL
    0x0000000CU, // DDRSS_PHY_1050_VAL
    0x00000033U, // DDRSS_PHY_1051_VAL
    0x2307B9ACU, // DDRSS_PHY_1052_VAL
    0x10000000U, // DDRSS_PHY_1053_VAL
    0x000F0000U, // DDRSS_PHY_1054_VAL
    0x0000000FU, // DDRSS_PHY_1055_VAL
    0x020002CCU, // DDRSS_PHY_1056_VAL
    0x00030000U, // DDRSS_PHY_1057_VAL
    0x00000300U, // DDRSS_PHY_1058_VAL
    0x00000300U, // DDRSS_PHY_1059_VAL
    0x00000300U, // DDRSS_PHY_1060_VAL
    0x00000300U, // DDRSS_PHY_1061_VAL
    0x00000300U, // DDRSS_PHY_1062_VAL
    0x42080010U, // DDRSS_PHY_1063_VAL
    0x0000803EU, // DDRSS_PHY_1064_VAL
    0x00000003U, // DDRSS_PHY_1065_VAL
    0x00000002U, // DDRSS_PHY_1066_VAL
    0x00000000U, // DDRSS_PHY_1280_VAL
    0x00010100U, // DDRSS_PHY_1281_VAL
    0x00000000U, // DDRSS_PHY_1282_VAL
    0x00000000U, // DDRSS_PHY_1283_VAL
    0x00000000U, // DDRSS_PHY_1284_VAL
    0x00000000U, // DDRSS_PHY_1285_VAL
    0x00050000U, // DDRSS_PHY_1286_VAL
    0x04000000U, // DDRSS_PHY_1287_VAL
    0x00000055U, // DDRSS_PHY_1288_VAL
    0x00000000U, // DDRSS_PHY_1289_VAL
    0x00000000U, // DDRSS_PHY_1290_VAL
    0x00000000U, // DDRSS_PHY_1291_VAL
    0x00000000U, // DDRSS_PHY_1292_VAL
    0x00002001U, // DDRSS_PHY_1293_VAL
    0x00004001U, // DDRSS_PHY_1294_VAL
    0x00020028U, // DDRSS_PHY_1295_VAL
    0x01010100U, // DDRSS_PHY_1296_VAL
    0x00000000U, // DDRSS_PHY_1297_VAL
    0x00000000U, // DDRSS_PHY_1298_VAL
    0x0F0F0E06U, // DDRSS_PHY_1299_VAL
    0x00010101U, // DDRSS_PHY_1300_VAL
    0x010F0004U, // DDRSS_PHY_1301_VAL
    0x00000000U, // DDRSS_PHY_1302_VAL
    0x00000000U, // DDRSS_PHY_1303_VAL
    0x00000064U, // DDRSS_PHY_1304_VAL
    0x00000000U, // DDRSS_PHY_1305_VAL
    0x00000000U, // DDRSS_PHY_1306_VAL
    0x01020103U, // DDRSS_PHY_1307_VAL
    0x0F020102U, // DDRSS_PHY_1308_VAL
    0x03030303U, // DDRSS_PHY_1309_VAL
    0x03030303U, // DDRSS_PHY_1310_VAL
    0x00041B42U, // DDRSS_PHY_1311_VAL
    0x00005201U, // DDRSS_PHY_1312_VAL
    0x00000000U, // DDRSS_PHY_1313_VAL
    0x00000000U, // DDRSS_PHY_1314_VAL
    0x00000000U, // DDRSS_PHY_1315_VAL
    0x00000000U, // DDRSS_PHY_1316_VAL
    0x00000000U, // DDRSS_PHY_1317_VAL
    0x00000000U, // DDRSS_PHY_1318_VAL
    0x07030101U, // DDRSS_PHY_1319_VAL
    0x00005400U, // DDRSS_PHY_1320_VAL
    0x000040A2U, // DDRSS_PHY_1321_VAL
    0x00024410U, // DDRSS_PHY_1322_VAL
    0x00004410U, // DDRSS_PHY_1323_VAL
    0x00004410U, // DDRSS_PHY_1324_VAL
    0x00004410U, // DDRSS_PHY_1325_VAL
    0x00004410U, // DDRSS_PHY_1326_VAL
    0x00004410U, // DDRSS_PHY_1327_VAL
    0x00004410U, // DDRSS_PHY_1328_VAL
    0x00004410U, // DDRSS_PHY_1329_VAL
    0x00004410U, // DDRSS_PHY_1330_VAL
    0x00004410U, // DDRSS_PHY_1331_VAL
    0x00000000U, // DDRSS_PHY_1332_VAL
    0x00000076U, // DDRSS_PHY_1333_VAL
    0x00000400U, // DDRSS_PHY_1334_VAL
    0x00000008U, // DDRSS_PHY_1335_VAL
    0x00000000U, // DDRSS_PHY_1336_VAL
    0x00000000U, // DDRSS_PHY_1337_VAL
    0x00000000U, // DDRSS_PHY_1338_VAL
    0x00000000U, // DDRSS_PHY_1339_VAL
    0x00000000U, // DDRSS_PHY_1340_VAL
    0x03000000U, // DDRSS_PHY_1341_VAL
    0x00000000U, // DDRSS_PHY_1342_VAL
    0x00000000U, // DDRSS_PHY_1343_VAL
    0x00000000U, // DDRSS_PHY_1344_VAL
    0x04102006U, // DDRSS_PHY_1345_VAL
    0x00041020U, // DDRSS_PHY_1346_VAL
    0x01C98C98U, // DDRSS_PHY_1347_VAL
    0x3F400000U, // DDRSS_PHY_1348_VAL
    0x3F3F1F3FU, // DDRSS_PHY_1349_VAL
    0x0000001FU, // DDRSS_PHY_1350_VAL
    0x00000000U, // DDRSS_PHY_1351_VAL
    0x00000000U, // DDRSS_PHY_1352_VAL
    0x00000000U, // DDRSS_PHY_1353_VAL
    0x00000001U, // DDRSS_PHY_1354_VAL
    0x00000000U, // DDRSS_PHY_1355_VAL
    0x00000000U, // DDRSS_PHY_1356_VAL
    0x00000000U, // DDRSS_PHY_1357_VAL
    0x00000000U, // DDRSS_PHY_1358_VAL
    0x76543201U, // DDRSS_PHY_1359_VAL  // [SWAP] PHY_DATA_BYTE_ORDER_SEL //0x76543210 //Normal
    0x00040198U, // DDRSS_PHY_1360_VAL
    0x00000000U, // DDRSS_PHY_1361_VAL
    0x00000000U, // DDRSS_PHY_1362_VAL
    0x00000000U, // DDRSS_PHY_1363_VAL
    0x00040700U, // DDRSS_PHY_1364_VAL
    0x00000000U, // DDRSS_PHY_1365_VAL
    0x00000000U, // DDRSS_PHY_1366_VAL
    0x00000000U, // DDRSS_PHY_1367_VAL
    0x00000002U, // DDRSS_PHY_1368_VAL
    0x00000000U, // DDRSS_PHY_1369_VAL
    0x00000000U, // DDRSS_PHY_1370_VAL
    0x00000AC3U, // DDRSS_PHY_1371_VAL
    0x00020002U, // DDRSS_PHY_1372_VAL
    0x00000000U, // DDRSS_PHY_1373_VAL
    0x00001142U, // DDRSS_PHY_1374_VAL
    0x03020000U, // DDRSS_PHY_1375_VAL
    0x00000080U, // DDRSS_PHY_1376_VAL
    0x03900390U, // DDRSS_PHY_1377_VAL
    0x03900390U, // DDRSS_PHY_1378_VAL
    0x03900390U, // DDRSS_PHY_1379_VAL
    0x03900390U, // DDRSS_PHY_1380_VAL
    0x03000300U, // DDRSS_PHY_1381_VAL
    0x03000300U, // DDRSS_PHY_1382_VAL
    0x00000300U, // DDRSS_PHY_1383_VAL
    0x00000300U, // DDRSS_PHY_1384_VAL
    0x00000300U, // DDRSS_PHY_1385_VAL
    0x00000300U, // DDRSS_PHY_1386_VAL
    0x3183BF77U, // DDRSS_PHY_1387_VAL
    0x00000000U, // DDRSS_PHY_1388_VAL
    0x0C000DFFU, // DDRSS_PHY_1389_VAL
    0x30000DFFU, // DDRSS_PHY_1390_VAL
    0x3F0DFF11U, // DDRSS_PHY_1391_VAL
    0x01990000U, // DDRSS_PHY_1392_VAL
    0x780DFFCCU, // DDRSS_PHY_1393_VAL
    0x00000C11U, // DDRSS_PHY_1394_VAL
    0x00018011U, // DDRSS_PHY_1395_VAL
    0x0089FF00U, // DDRSS_PHY_1396_VAL
    0x000C3F11U, // DDRSS_PHY_1397_VAL
    0x01990000U, // DDRSS_PHY_1398_VAL
    0x000C3F11U, // DDRSS_PHY_1399_VAL
    0x01990000U, // DDRSS_PHY_1400_VAL
    0x3F0DFF11U, // DDRSS_PHY_1401_VAL
    0x01990000U, // DDRSS_PHY_1402_VAL
    0x00018011U, // DDRSS_PHY_1403_VAL
    0x0089FF00U, // DDRSS_PHY_1404_VAL
    0x20040002U, // DDRSS_PHY_1405_VAL
};

uint16_t DDRSS_ctlRegNum[] = {
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    89,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    134,
    135,
    136,
    137,
    138,
    139,
    140,
    141,
    142,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    153,
    154,
    155,
    156,
    157,
    158,
    159,
    160,
    161,
    162,
    163,
    164,
    165,
    166,
    167,
    168,
    169,
    170,
    171,
    172,
    173,
    174,
    175,
    176,
    177,
    178,
    179,
    180,
    181,
    182,
    183,
    184,
    185,
    186,
    187,
    188,
    189,
    190,
    191,
    192,
    193,
    194,
    195,
    196,
    197,
    198,
    199,
    200,
    201,
    202,
    203,
    204,
    205,
    206,
    207,
    208,
    209,
    210,
    211,
    212,
    213,
    214,
    215,
    216,
    217,
    218,
    219,
    220,
    221,
    222,
    223,
    224,
    225,
    226,
    227,
    228,
    229,
    230,
    231,
    232,
    233,
    234,
    235,
    236,
    237,
    238,
    239,
    240,
    241,
    242,
    243,
    244,
    245,
    246,
    247,
    248,
    249,
    250,
    251,
    252,
    253,
    254,
    255,
    256,
    257,
    258,
    259,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    267,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    278,
    279,
    280,
    281,
    282,
    283,
    284,
    285,
    286,
    287,
    288,
    289,
    290,
    291,
    292,
    293,
    294,
    295,
    296,
    297,
    298,
    299,
    300,
    301,
    302,
    303,
    304,
    305,
    306,
    307,
    308,
    309,
    310,
    311,
    312,
    313,
    314,
    315,
    316,
    317,
    318,
    319,
    320,
    321,
    322,
    323,
    324,
    325,
    326,
    327,
    328,
    329,
    330,
    331,
    332,
    333,
    334,
    335,
    336,
    337,
    338,
    339,
    340,
    341,
    342,
    343,
    344,
    345,
    346,
    347,
    348,
    349,
    350,
    351,
    352,
    353,
    354,
    355,
    356,
    357,
    358,
    359,
    360,
    361,
    362,
    363,
    364,
    365,
    366,
    367,
    368,
    369,
    370,
    371,
    372,
    373,
    374,
    375,
    376,
    377,
    378,
    379,
    380,
    381,
    382,
    383,
    384,
    385,
    386,
    387,
    388,
    389,
    390,
    391,
    392,
    393,
    394,
    395,
    396,
    397,
    398,
    399,
    400,
    401,
    402,
    403,
    404,
    405,
    406,
    407,
    408,
    409,
    410,
    411,
    412,
    413,
    414,
    415,
    416,
    417,
    418,
    419,
    420,
    421,
    422,
};

uint16_t DDRSS_phyIndepRegNum[] = {
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    89,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    134,
    135,
    136,
    137,
    138,
    139,
    140,
    141,
    142,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    153,
    154,
    155,
    156,
    157,
    158,
    159,
    160,
    161,
    162,
    163,
    164,
    165,
    166,
    167,
    168,
    169,
    170,
    171,
    172,
    173,
    174,
    175,
    176,
    177,
    178,
    179,
    180,
    181,
    182,
    183,
    184,
    185,
    186,
    187,
    188,
    189,
    190,
    191,
    192,
    193,
    194,
    195,
    196,
    197,
    198,
    199,
    200,
    201,
    202,
    203,
    204,
    205,
    206,
    207,
    208,
    209,
    210,
    211,
    212,
    213,
    214,
    215,
    216,
    217,
    218,
    219,
    220,
    221,
    222,
    223,
    224,
    225,
    226,
    227,
    228,
    229,
    230,
    231,
    232,
    233,
    234,
    235,
    236,
    237,
    238,
    239,
    240,
    241,
    242,
    243,
    244,
    245,
    246,
    247,
    248,
    249,
    250,
    251,
    252,
    253,
    254,
    255,
    256,
    257,
    258,
    259,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    267,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    278,
    279,
    280,
    281,
    282,
    283,
    284,
    285,
    286,
    287,
    288,
    289,
    290,
    291,
    292,
    293,
    294,
    295,
    296,
    297,
    298,
    299,
    300,
    301,
    302,
    303,
    304,
    305,
    306,
    307,
    308,
    309,
    310,
    311,
    312,
    313,
    314,
    315,
    316,
    317,
    318,
    319,
    320,
    321,
    322,
    323,
    324,
    325,
    326,
    327,
    328,
    329,
    330,
    331,
    332,
    333,
    334,
    335,
    336,
    337,
    338,
    339,
    340,
    341,
    342,
    343,
    344,
};

uint16_t DDRSS_phyRegNum[] = {
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    89,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    256,
    257,
    258,
    259,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    267,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    278,
    279,
    280,
    281,
    282,
    283,
    284,
    285,
    286,
    287,
    288,
    289,
    290,
    291,
    292,
    293,
    294,
    295,
    296,
    297,
    298,
    299,
    300,
    301,
    302,
    303,
    304,
    305,
    306,
    307,
    308,
    309,
    310,
    311,
    312,
    313,
    314,
    315,
    316,
    317,
    318,
    319,
    320,
    321,
    322,
    323,
    324,
    325,
    326,
    327,
    328,
    329,
    330,
    331,
    332,
    333,
    334,
    335,
    336,
    337,
    338,
    339,
    340,
    341,
    342,
    343,
    344,
    345,
    346,
    347,
    348,
    349,
    350,
    351,
    352,
    353,
    354,
    355,
    356,
    357,
    358,
    359,
    360,
    361,
    362,
    363,
    364,
    365,
    366,
    367,
    368,
    369,
    370,
    371,
    372,
    373,
    374,
    375,
    376,
    377,
    378,
    379,
    380,
    381,
    512,
    513,
    514,
    515,
    516,
    517,
    518,
    519,
    520,
    521,
    522,
    523,
    524,
    525,
    526,
    527,
    528,
    529,
    530,
    531,
    532,
    533,
    534,
    535,
    536,
    537,
    538,
    539,
    540,
    541,
    542,
    543,
    544,
    545,
    546,
    547,
    548,
    549,
    550,
    551,
    552,
    553,
    554,
    768,
    769,
    770,
    771,
    772,
    773,
    774,
    775,
    776,
    777,
    778,
    779,
    780,
    781,
    782,
    783,
    784,
    785,
    786,
    787,
    788,
    789,
    790,
    791,
    792,
    793,
    794,
    795,
    796,
    797,
    798,
    799,
    800,
    801,
    802,
    803,
    804,
    805,
    806,
    807,
    808,
    809,
    810,
    1024,
    1025,
    1026,
    1027,
    1028,
    1029,
    1030,
    1031,
    1032,
    1033,
    1034,
    1035,
    1036,
    1037,
    1038,
    1039,
    1040,
    1041,
    1042,
    1043,
    1044,
    1045,
    1046,
    1047,
    1048,
    1049,
    1050,
    1051,
    1052,
    1053,
    1054,
    1055,
    1056,
    1057,
    1058,
    1059,
    1060,
    1061,
    1062,
    1063,
    1064,
    1065,
    1066,
    1280,
    1281,
    1282,
    1283,
    1284,
    1285,
    1286,
    1287,
    1288,
    1289,
    1290,
    1291,
    1292,
    1293,
    1294,
    1295,
    1296,
    1297,
    1298,
    1299,
    1300,
    1301,
    1302,
    1303,
    1304,
    1305,
    1306,
    1307,
    1308,
    1309,
    1310,
    1311,
    1312,
    1313,
    1314,
    1315,
    1316,
    1317,
    1318,
    1319,
    1320,
    1321,
    1322,
    1323,
    1324,
    1325,
    1326,
    1327,
    1328,
    1329,
    1330,
    1331,
    1332,
    1333,
    1334,
    1335,
    1336,
    1337,
    1338,
    1339,
    1340,
    1341,
    1342,
    1343,
    1344,
    1345,
    1346,
    1347,
    1348,
    1349,
    1350,
    1351,
    1352,
    1353,
    1354,
    1355,
    1356,
    1357,
    1358,
    1359,
    1360,
    1361,
    1362,
    1363,
    1364,
    1365,
    1366,
    1367,
    1368,
    1369,
    1370,
    1371,
    1372,
    1373,
    1374,
    1375,
    1376,
    1377,
    1378,
    1379,
    1380,
    1381,
    1382,
    1383,
    1384,
    1385,
    1386,
    1387,
    1388,
    1389,
    1390,
    1391,
    1392,
    1393,
    1394,
    1395,
    1396,
    1397,
    1398,
    1399,
    1400,
    1401,
    1402,
    1403,
    1404,
    1405,
};
#ifdef __cplusplus
} 
#endif

#endif
