

/*
 *  Copyright (C) 2022 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */


#ifndef SCICLIENT_DEFAULTBOARDCFG_SECURITY_HEXHS_H_
#define SCICLIENT_DEFAULTBOARDCFG_SECURITY_HEXHS_H_

#ifdef __cplusplus
extern "C"
{
#endif

#define SCICLIENT_BOARDCFG_SECURITY_SIZE_IN_BYTES (1961U)

#define SCICLIENT_BOARDCFG_SECURITY { \
    0x30U, 0x82U, 0x06U, 0x48U, 0x30U, 0x82U, 0x04U, 0x30U, 0xa0U, 0x03U, 0x02U, 0x01U, 0x02U, 0x02U, 0x14U, 0x0aU,  \
    0xe1U, 0xbaU, 0x18U, 0x18U, 0x6aU, 0x7aU, 0x6aU, 0x7fU, 0x55U, 0x7bU, 0xafU, 0xfdU, 0xdbU, 0x8bU, 0x17U, 0x1cU,  \
    0x20U, 0x41U, 0x41U, 0x30U, 0x0dU, 0x06U, 0x09U, 0x2aU, 0x86U, 0x48U, 0x86U, 0xf7U, 0x0dU, 0x01U, 0x01U, 0x0dU,  \
    0x05U, 0x00U, 0x30U, 0x81U, 0x8bU, 0x31U, 0x0bU, 0x30U, 0x09U, 0x06U, 0x03U, 0x55U, 0x04U, 0x06U, 0x13U, 0x02U,  \
    0x55U, 0x53U, 0x31U, 0x0bU, 0x30U, 0x09U, 0x06U, 0x03U, 0x55U, 0x04U, 0x08U, 0x0cU, 0x02U, 0x53U, 0x43U, 0x31U,  \
    0x0fU, 0x30U, 0x0dU, 0x06U, 0x03U, 0x55U, 0x04U, 0x07U, 0x0cU, 0x06U, 0x44U, 0x61U, 0x6cU, 0x6cU, 0x61U, 0x73U,  \
    0x31U, 0x21U, 0x30U, 0x1fU, 0x06U, 0x03U, 0x55U, 0x04U, 0x0aU, 0x0cU, 0x18U, 0x54U, 0x65U, 0x78U, 0x61U, 0x73U,  \
    0x20U, 0x49U, 0x6eU, 0x73U, 0x74U, 0x72U, 0x75U, 0x6dU, 0x65U, 0x6eU, 0x74U, 0x73U, 0x2eU, 0x2cU, 0x20U, 0x49U,  \
    0x6eU, 0x63U, 0x2eU, 0x31U, 0x0cU, 0x30U, 0x0aU, 0x06U, 0x03U, 0x55U, 0x04U, 0x0bU, 0x0cU, 0x03U, 0x50U, 0x42U,  \
    0x55U, 0x31U, 0x0fU, 0x30U, 0x0dU, 0x06U, 0x03U, 0x55U, 0x04U, 0x03U, 0x0cU, 0x06U, 0x41U, 0x6cU, 0x62U, 0x65U,  \
    0x72U, 0x74U, 0x31U, 0x1cU, 0x30U, 0x1aU, 0x06U, 0x09U, 0x2aU, 0x86U, 0x48U, 0x86U, 0xf7U, 0x0dU, 0x01U, 0x09U,  \
    0x01U, 0x16U, 0x0dU, 0x41U, 0x6cU, 0x62U, 0x65U, 0x72U, 0x74U, 0x40U, 0x74U, 0x69U, 0x2eU, 0x63U, 0x6fU, 0x6dU,  \
    0x30U, 0x1eU, 0x17U, 0x0dU, 0x32U, 0x33U, 0x30U, 0x31U, 0x31U, 0x38U, 0x30U, 0x38U, 0x33U, 0x34U, 0x34U, 0x38U,  \
    0x5aU, 0x17U, 0x0dU, 0x32U, 0x33U, 0x30U, 0x32U, 0x31U, 0x37U, 0x30U, 0x38U, 0x33U, 0x34U, 0x34U, 0x38U, 0x5aU,  \
    0x30U, 0x81U, 0x8bU, 0x31U, 0x0bU, 0x30U, 0x09U, 0x06U, 0x03U, 0x55U, 0x04U, 0x06U, 0x13U, 0x02U, 0x55U, 0x53U,  \
    0x31U, 0x0bU, 0x30U, 0x09U, 0x06U, 0x03U, 0x55U, 0x04U, 0x08U, 0x0cU, 0x02U, 0x53U, 0x43U, 0x31U, 0x0fU, 0x30U,  \
    0x0dU, 0x06U, 0x03U, 0x55U, 0x04U, 0x07U, 0x0cU, 0x06U, 0x44U, 0x61U, 0x6cU, 0x6cU, 0x61U, 0x73U, 0x31U, 0x21U,  \
    0x30U, 0x1fU, 0x06U, 0x03U, 0x55U, 0x04U, 0x0aU, 0x0cU, 0x18U, 0x54U, 0x65U, 0x78U, 0x61U, 0x73U, 0x20U, 0x49U,  \
    0x6eU, 0x73U, 0x74U, 0x72U, 0x75U, 0x6dU, 0x65U, 0x6eU, 0x74U, 0x73U, 0x2eU, 0x2cU, 0x20U, 0x49U, 0x6eU, 0x63U,  \
    0x2eU, 0x31U, 0x0cU, 0x30U, 0x0aU, 0x06U, 0x03U, 0x55U, 0x04U, 0x0bU, 0x0cU, 0x03U, 0x50U, 0x42U, 0x55U, 0x31U,  \
    0x0fU, 0x30U, 0x0dU, 0x06U, 0x03U, 0x55U, 0x04U, 0x03U, 0x0cU, 0x06U, 0x41U, 0x6cU, 0x62U, 0x65U, 0x72U, 0x74U,  \
    0x31U, 0x1cU, 0x30U, 0x1aU, 0x06U, 0x09U, 0x2aU, 0x86U, 0x48U, 0x86U, 0xf7U, 0x0dU, 0x01U, 0x09U, 0x01U, 0x16U,  \
    0x0dU, 0x41U, 0x6cU, 0x62U, 0x65U, 0x72U, 0x74U, 0x40U, 0x74U, 0x69U, 0x2eU, 0x63U, 0x6fU, 0x6dU, 0x30U, 0x82U,  \
    0x02U, 0x22U, 0x30U, 0x0dU, 0x06U, 0x09U, 0x2aU, 0x86U, 0x48U, 0x86U, 0xf7U, 0x0dU, 0x01U, 0x01U, 0x01U, 0x05U,  \
    0x00U, 0x03U, 0x82U, 0x02U, 0x0fU, 0x00U, 0x30U, 0x82U, 0x02U, 0x0aU, 0x02U, 0x82U, 0x02U, 0x01U, 0x00U, 0xbfU,  \
    0x14U, 0xaeU, 0x49U, 0xd8U, 0x7fU, 0x72U, 0xd3U, 0x6bU, 0x23U, 0xcdU, 0xebU, 0x48U, 0x0eU, 0x65U, 0xdcU, 0x22U,  \
    0x4dU, 0xf2U, 0x0eU, 0x4fU, 0x82U, 0xf6U, 0xedU, 0xb5U, 0xf2U, 0xddU, 0xdbU, 0x7cU, 0x91U, 0xfaU, 0x6eU, 0x59U,  \
    0xffU, 0xd5U, 0xf7U, 0xb6U, 0xdeU, 0x04U, 0x1dU, 0x8aU, 0xccU, 0xd2U, 0x95U, 0xd9U, 0xd1U, 0xe0U, 0xc4U, 0xc1U,  \
    0xf8U, 0x50U, 0xbfU, 0xffU, 0x48U, 0x0cU, 0x91U, 0x22U, 0x50U, 0x9aU, 0x4cU, 0x7bU, 0x8bU, 0xf3U, 0x96U, 0x0aU,  \
    0x28U, 0x26U, 0xb3U, 0xa4U, 0xd9U, 0xe0U, 0xa9U, 0x55U, 0x41U, 0x1aU, 0xfbU, 0x3eU, 0x5bU, 0x27U, 0x6cU, 0xbfU,  \
    0xcaU, 0xc0U, 0x71U, 0xafU, 0x2fU, 0x72U, 0x22U, 0xeeU, 0x46U, 0x01U, 0x25U, 0x62U, 0xadU, 0x3eU, 0xc7U, 0x04U,  \
    0xf6U, 0xb1U, 0x18U, 0xb6U, 0x2cU, 0xc0U, 0x12U, 0x6eU, 0x0fU, 0xe2U, 0x9bU, 0x3eU, 0xe5U, 0xa6U, 0xa0U, 0xa8U,  \
    0x06U, 0x45U, 0x03U, 0x41U, 0x17U, 0x4eU, 0x16U, 0x1fU, 0xa9U, 0x74U, 0xd6U, 0x84U, 0x4eU, 0xd6U, 0x79U, 0xa7U,  \
    0x10U, 0xb8U, 0x11U, 0xa9U, 0x0eU, 0x92U, 0x1fU, 0x25U, 0xddU, 0x7fU, 0xb1U, 0xf2U, 0xd1U, 0xb9U, 0xf2U, 0x68U,  \
    0xd8U, 0x33U, 0x59U, 0x4bU, 0x82U, 0x7dU, 0x77U, 0xccU, 0xd1U, 0x9cU, 0xfaU, 0x23U, 0xb4U, 0xfbU, 0x58U, 0x88U,  \
    0xf2U, 0xcdU, 0xeaU, 0xd5U, 0x16U, 0xf2U, 0x2cU, 0x75U, 0x2dU, 0xfaU, 0x62U, 0xc3U, 0xc1U, 0x09U, 0x6eU, 0xe0U,  \
    0x06U, 0x70U, 0xe0U, 0xb5U, 0x07U, 0x09U, 0x99U, 0x62U, 0xd9U, 0xd6U, 0xe4U, 0xe7U, 0x6cU, 0x6dU, 0xc8U, 0x82U,  \
    0x07U, 0x50U, 0x93U, 0xf7U, 0xe2U, 0xd8U, 0xedU, 0xd1U, 0x5fU, 0xe3U, 0xd0U, 0x9eU, 0xcfU, 0x93U, 0x54U, 0xd9U,  \
    0x5fU, 0xddU, 0x5dU, 0xceU, 0x37U, 0x60U, 0xf1U, 0xabU, 0x14U, 0x8aU, 0x04U, 0x7bU, 0x65U, 0xa7U, 0xbaU, 0x7fU,  \
    0xdfU, 0x45U, 0x45U, 0x7cU, 0x4bU, 0xa1U, 0x5bU, 0xaeU, 0x4eU, 0xc6U, 0x94U, 0x3dU, 0x8cU, 0x4eU, 0x87U, 0xd2U,  \
    0x94U, 0x3cU, 0xa4U, 0xf3U, 0x9fU, 0xdaU, 0xfcU, 0xf2U, 0x36U, 0x7cU, 0xe7U, 0x0dU, 0xadU, 0x5aU, 0x42U, 0x37U,  \
    0xf1U, 0x2aU, 0x81U, 0xd0U, 0x6eU, 0xa1U, 0xa7U, 0x67U, 0x03U, 0x1eU, 0x87U, 0xedU, 0x00U, 0xbbU, 0x73U, 0x4aU,  \
    0x68U, 0x28U, 0x31U, 0xa2U, 0x82U, 0x9aU, 0xa3U, 0x04U, 0xc1U, 0xe8U, 0x87U, 0xffU, 0x45U, 0x7eU, 0xaaU, 0xc1U,  \
    0x9fU, 0xd4U, 0x3bU, 0x05U, 0xc7U, 0x83U, 0xfdU, 0x21U, 0x71U, 0xfeU, 0xbdU, 0x7fU, 0x38U, 0xc9U, 0x16U, 0x19U,  \
    0x52U, 0x0eU, 0xe6U, 0x03U, 0x33U, 0x8dU, 0x1dU, 0x1eU, 0xc9U, 0x36U, 0x1cU, 0xcdU, 0x4eU, 0x9dU, 0x82U, 0x29U,  \
    0x88U, 0xcdU, 0x9bU, 0x2aU, 0xbeU, 0x6cU, 0x5fU, 0x7bU, 0xb2U, 0xb2U, 0x3aU, 0x79U, 0x00U, 0x6aU, 0x7dU, 0xf5U,  \
    0xadU, 0x1aU, 0x9dU, 0x1eU, 0xcdU, 0x58U, 0x2aU, 0xcfU, 0x5eU, 0xf4U, 0x4eU, 0x80U, 0xabU, 0x3bU, 0x4fU, 0xddU,  \
    0xf8U, 0xd4U, 0xdeU, 0x34U, 0xa2U, 0xc4U, 0x20U, 0xd9U, 0x59U, 0x19U, 0x2dU, 0x85U, 0x02U, 0x5eU, 0x1fU, 0x68U,  \
    0xb1U, 0x4cU, 0x8dU, 0xb9U, 0x11U, 0x06U, 0xe9U, 0x2dU, 0x76U, 0xb5U, 0x58U, 0x8cU, 0x50U, 0xa8U, 0x37U, 0x6eU,  \
    0x66U, 0x78U, 0x6fU, 0x83U, 0x30U, 0x46U, 0x4dU, 0x34U, 0x9fU, 0xb4U, 0x18U, 0x4aU, 0xb9U, 0xbbU, 0xfaU, 0x7bU,  \
    0xc5U, 0xaeU, 0xd6U, 0x32U, 0x10U, 0x84U, 0x84U, 0x6cU, 0x3fU, 0x9aU, 0x80U, 0x33U, 0x35U, 0xfcU, 0x4dU, 0xbcU,  \
    0xd5U, 0x6eU, 0x60U, 0x54U, 0x50U, 0xcfU, 0x7eU, 0x6dU, 0x80U, 0x97U, 0x04U, 0xfaU, 0x8fU, 0x0bU, 0x20U, 0xfdU,  \
    0xbdU, 0x98U, 0x2bU, 0xa1U, 0x37U, 0xbdU, 0x59U, 0xfdU, 0x4aU, 0xecU, 0x45U, 0xa1U, 0x09U, 0x8bU, 0x17U, 0xc9U,  \
    0x72U, 0x14U, 0x33U, 0xb7U, 0x05U, 0x5eU, 0x12U, 0x5dU, 0xe2U, 0x5aU, 0x1dU, 0xceU, 0x21U, 0x54U, 0xf6U, 0xe1U,  \
    0xeaU, 0xd5U, 0x55U, 0xaaU, 0x27U, 0xebU, 0x4dU, 0x09U, 0xdfU, 0x19U, 0x40U, 0x82U, 0x2eU, 0x66U, 0x89U, 0x17U,  \
    0x65U, 0xd9U, 0x6eU, 0xb3U, 0xd6U, 0x38U, 0x4eU, 0x8dU, 0x61U, 0x16U, 0xd6U, 0x74U, 0xd4U, 0xdeU, 0x16U, 0x5fU,  \
    0x51U, 0x19U, 0xd5U, 0x42U, 0xb8U, 0x83U, 0xd2U, 0xc8U, 0xdeU, 0x4bU, 0xa9U, 0x69U, 0x97U, 0xb6U, 0x8dU, 0x02U,  \
    0x03U, 0x01U, 0x00U, 0x01U, 0xa3U, 0x81U, 0xa1U, 0x30U, 0x81U, 0x9eU, 0x30U, 0x0cU, 0x06U, 0x03U, 0x55U, 0x1dU,  \
    0x13U, 0x04U, 0x05U, 0x30U, 0x03U, 0x01U, 0x01U, 0xffU, 0x30U, 0x12U, 0x06U, 0x09U, 0x2bU, 0x06U, 0x01U, 0x04U,  \
    0x01U, 0x82U, 0x26U, 0x01U, 0x03U, 0x04U, 0x05U, 0x30U, 0x03U, 0x02U, 0x01U, 0x01U, 0x30U, 0x60U, 0x06U, 0x09U,  \
    0x2bU, 0x06U, 0x01U, 0x04U, 0x01U, 0x82U, 0x26U, 0x01U, 0x22U, 0x04U, 0x53U, 0x30U, 0x51U, 0x06U, 0x09U, 0x60U,  \
    0x86U, 0x48U, 0x01U, 0x65U, 0x03U, 0x04U, 0x02U, 0x03U, 0x04U, 0x40U, 0xeeU, 0x46U, 0x40U, 0x9cU, 0x06U, 0xa6U,  \
    0x0cU, 0x06U, 0x42U, 0x3eU, 0x7cU, 0x89U, 0xabU, 0x2fU, 0xb7U, 0x91U, 0x9dU, 0x4dU, 0x42U, 0x59U, 0x37U, 0x0eU,  \
    0xc7U, 0xfcU, 0x51U, 0xefU, 0x4eU, 0x7cU, 0xf4U, 0x94U, 0x44U, 0x05U, 0xc5U, 0x3fU, 0x43U, 0xb7U, 0x3cU, 0xc3U,  \
    0x3fU, 0x01U, 0x2bU, 0xd0U, 0x84U, 0x97U, 0x7bU, 0x4bU, 0xefU, 0xe0U, 0x60U, 0x5bU, 0x91U, 0x67U, 0x39U, 0x08U,  \
    0x9cU, 0xa8U, 0x70U, 0xb1U, 0x26U, 0x7bU, 0xa2U, 0x46U, 0x20U, 0x95U, 0x02U, 0x02U, 0x01U, 0x5dU, 0x30U, 0x18U,  \
    0x06U, 0x09U, 0x2bU, 0x06U, 0x01U, 0x04U, 0x01U, 0x82U, 0x26U, 0x01U, 0x23U, 0x04U, 0x0bU, 0x30U, 0x09U, 0x04U,  \
    0x04U, 0x00U, 0x00U, 0x00U, 0x00U, 0x02U, 0x01U, 0x02U, 0x30U, 0x0dU, 0x06U, 0x09U, 0x2aU, 0x86U, 0x48U, 0x86U,  \
    0xf7U, 0x0dU, 0x01U, 0x01U, 0x0dU, 0x05U, 0x00U, 0x03U, 0x82U, 0x02U, 0x01U, 0x00U, 0x8dU, 0x4fU, 0xefU, 0xf0U,  \
    0x36U, 0x8bU, 0x3cU, 0x2bU, 0x3cU, 0x99U, 0x41U, 0x16U, 0x50U, 0xefU, 0xcfU, 0x67U, 0x6eU, 0x62U, 0x16U, 0xf0U,  \
    0x52U, 0xf4U, 0x7bU, 0xd3U, 0xf0U, 0xc0U, 0x92U, 0x2dU, 0x77U, 0x28U, 0x59U, 0xd0U, 0xe0U, 0xfbU, 0xa2U, 0xd9U,  \
    0xbdU, 0xc5U, 0xa5U, 0x7aU, 0x54U, 0x4cU, 0x84U, 0xbbU, 0x65U, 0xc0U, 0xd2U, 0x22U, 0x3aU, 0x16U, 0x0dU, 0x38U,  \
    0xa3U, 0x48U, 0x23U, 0xa8U, 0xf3U, 0xeeU, 0x18U, 0x07U, 0xebU, 0xb7U, 0x31U, 0x9cU, 0xfaU, 0x38U, 0x56U, 0xd7U,  \
    0x60U, 0x22U, 0x6dU, 0xdeU, 0x30U, 0xedU, 0x12U, 0x40U, 0xbeU, 0xdcU, 0x14U, 0x34U, 0x1aU, 0x75U, 0x1aU, 0xf4U,  \
    0xcfU, 0xeaU, 0xaaU, 0xf6U, 0xb6U, 0x7eU, 0xdaU, 0x42U, 0x11U, 0x96U, 0x2fU, 0x1bU, 0x0bU, 0x47U, 0x43U, 0x1fU,  \
    0x9fU, 0xddU, 0x53U, 0xe7U, 0xa7U, 0xfcU, 0xbbU, 0xbaU, 0xecU, 0xd7U, 0x94U, 0xe5U, 0xc7U, 0xffU, 0xd1U, 0xcbU,  \
    0xd1U, 0xe7U, 0xa3U, 0x84U, 0x46U, 0x8cU, 0xa4U, 0x4cU, 0xc9U, 0xd6U, 0xabU, 0xdaU, 0x2bU, 0xeaU, 0xabU, 0xaeU,  \
    0x5aU, 0xa7U, 0x34U, 0x97U, 0x5eU, 0x8fU, 0x44U, 0xe0U, 0x05U, 0x24U, 0xb9U, 0xb0U, 0x46U, 0x38U, 0x94U, 0x6cU,  \
    0x12U, 0xdcU, 0xf6U, 0xc9U, 0xd0U, 0x8aU, 0x67U, 0xd1U, 0x7bU, 0x00U, 0x88U, 0x76U, 0x2eU, 0xb0U, 0x43U, 0xdfU,  \
    0xb9U, 0xfcU, 0xafU, 0xa3U, 0xcaU, 0x57U, 0xcbU, 0x5bU, 0x1fU, 0xb8U, 0x49U, 0xb9U, 0xa9U, 0x91U, 0xaeU, 0x8cU,  \
    0xe9U, 0x54U, 0x1cU, 0x36U, 0x34U, 0x1cU, 0xd7U, 0x29U, 0x9dU, 0x78U, 0x2aU, 0x70U, 0x1cU, 0x6dU, 0xd6U, 0xe9U,  \
    0x4fU, 0x6fU, 0x61U, 0x6dU, 0x2aU, 0x86U, 0x05U, 0x1aU, 0x6eU, 0x22U, 0xacU, 0x40U, 0xdeU, 0x7fU, 0x65U, 0xf4U,  \
    0x60U, 0x28U, 0xcdU, 0x72U, 0x26U, 0x1dU, 0x3fU, 0x66U, 0x19U, 0x42U, 0x90U, 0x1dU, 0x13U, 0x3dU, 0x43U, 0x28U,  \
    0x3eU, 0x85U, 0x9cU, 0xc6U, 0xa5U, 0x71U, 0x02U, 0x76U, 0x27U, 0x27U, 0xa5U, 0x34U, 0x88U, 0x2bU, 0xdbU, 0xabU,  \
    0x6bU, 0xf8U, 0xd1U, 0xb7U, 0x71U, 0x6eU, 0x27U, 0x68U, 0xa7U, 0x1cU, 0x6cU, 0x3bU, 0x20U, 0x10U, 0x74U, 0x28U,  \
    0x9aU, 0xfbU, 0xa4U, 0x57U, 0x32U, 0x11U, 0xa4U, 0xc8U, 0x8aU, 0xa2U, 0x7cU, 0x79U, 0x22U, 0x9cU, 0x6eU, 0xb3U,  \
    0xfdU, 0x6eU, 0x86U, 0x92U, 0x3dU, 0x5fU, 0x74U, 0xc4U, 0xecU, 0x54U, 0x87U, 0x09U, 0x5eU, 0x4cU, 0xdbU, 0x63U,  \
    0x7bU, 0x71U, 0xadU, 0xaeU, 0x19U, 0xa1U, 0x93U, 0x4dU, 0x87U, 0x3fU, 0xa0U, 0x6eU, 0x0cU, 0xc0U, 0xa2U, 0x0fU,  \
    0x2aU, 0x0eU, 0x6eU, 0x9dU, 0x00U, 0xf3U, 0x92U, 0x6fU, 0xe7U, 0xa6U, 0x50U, 0x1aU, 0x4eU, 0x09U, 0x8fU, 0xe6U,  \
    0xcdU, 0x47U, 0xc3U, 0x1dU, 0xd1U, 0x33U, 0xfbU, 0xedU, 0x47U, 0x2dU, 0xb5U, 0x88U, 0x45U, 0x7cU, 0xecU, 0x41U,  \
    0xd5U, 0x65U, 0x62U, 0xceU, 0x26U, 0x91U, 0x39U, 0x01U, 0x74U, 0xdaU, 0xbaU, 0xecU, 0xf4U, 0x8dU, 0x5cU, 0xa7U,  \
    0xc4U, 0x13U, 0x09U, 0xb1U, 0x64U, 0x84U, 0x15U, 0x3eU, 0xb9U, 0x58U, 0xedU, 0x4dU, 0x9aU, 0xf1U, 0x57U, 0xe3U,  \
    0xf1U, 0x05U, 0xd7U, 0xd4U, 0xb6U, 0x3eU, 0x1eU, 0xecU, 0x2bU, 0xfbU, 0x31U, 0x59U, 0xc4U, 0x80U, 0x3fU, 0xe5U,  \
    0xc8U, 0x2fU, 0xebU, 0x03U, 0xb1U, 0x6fU, 0xafU, 0xb2U, 0xd4U, 0x3eU, 0x79U, 0x8bU, 0xe0U, 0x91U, 0x6cU, 0xe5U,  \
    0x82U, 0xffU, 0x17U, 0x82U, 0x05U, 0x36U, 0x80U, 0x36U, 0x06U, 0xbdU, 0xf9U, 0x9eU, 0xefU, 0x8eU, 0x09U, 0x41U,  \
    0x9bU, 0x69U, 0x8aU, 0xd1U, 0x25U, 0x7fU, 0x69U, 0x60U, 0x9dU, 0x56U, 0xb5U, 0x6fU, 0x48U, 0x10U, 0xaaU, 0xcdU,  \
    0xdcU, 0x3cU, 0x5cU, 0xf9U, 0x04U, 0x8dU, 0x25U, 0x20U, 0x16U, 0x65U, 0x1aU, 0x26U, 0x25U, 0x65U, 0x48U, 0xd0U,  \
    0xc0U, 0xb2U, 0x4bU, 0xd1U, 0xe1U, 0x98U, 0xf5U, 0x9cU, 0xb6U, 0x7dU, 0x14U, 0x38U, 0x8aU, 0x0bU, 0x2aU, 0xb8U,  \
    0x24U, 0x0bU, 0x06U, 0xcaU, 0x4cU, 0x0eU, 0x9bU, 0x91U, 0x6eU, 0x40U, 0xa4U, 0xa8U, 0x33U, 0x8eU, 0x3cU, 0x14U,  \
    0x55U, 0x6fU, 0xa5U, 0x50U, 0x85U, 0x0cU, 0xd3U, 0xceU, 0x90U, 0x8eU, 0xbbU, 0x4aU, 0x51U, 0x87U, 0x20U, 0xedU,  \
    0x53U, 0xb7U, 0xa6U, 0x74U, 0xb6U, 0xacU, 0x17U, 0xe5U, 0x38U, 0xa4U, 0x79U, 0x2bU, 0x00U, 0x01U, 0xeaU, 0xf1U,  \
    0xa4U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x27U, 0x8dU, 0x44U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x81U, 0x40U, 0x45U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x70U, 0x51U, 0x0cU, 0x00U, 0x80U,  \
    0x00U, 0x00U, 0x00U, 0x5aU, 0x00U, 0x00U, 0x00U, 0xbeU, 0x23U, 0x00U, 0x00U, 0x00U, 0x5aU, 0x00U, 0x00U, 0xafU,  \
    0x42U, 0x10U, 0x00U, 0x5aU, 0x5aU, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x8fU,  \
    0x60U, 0x0aU, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
} /* 1961 bytes */

#ifdef __cplusplus
}
#endif

#endif /* SCICLIENT_DEFAULTBOARDCFG_SECURITY_HEXHS_H_ */
