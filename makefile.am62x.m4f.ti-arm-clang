#
# Auto generated makefile
#

export MCU_PLUS_SDK_PATH?=$(abspath ../..)
include $(MCU_PLUS_SDK_PATH)/imports.mak

CG_TOOL_ROOT=$(CGT_TI_ARM_CLANG_PATH)

CC=$(CGT_TI_ARM_CLANG_PATH)/bin/tiarmclang
AR=$(CGT_TI_ARM_CLANG_PATH)/bin/tiarmar

PROFILE?=release
ConfigName:=$(PROFILE)

LIBNAME:=drivers.am62x.m4f.ti-arm-clang.$(PROFILE).lib

FILES_common := \
    csl_emif.c \
    csl_sec_proxy.c \
    ddr.c \
    ddr_soc.c \
    epwm.c \
    i2c_v0.c \
    gpio.c \
    pinmux.c \
    mcspi_v0.c \
    mcspi_dma.c \
    mcspi_dma_udma.c \
    ipc_notify_v0.c \
    ipc_notify_v0_cfg.c \
    ipc_rpmsg.c \
    ipc_rpmsg_vring.c \
    mcan.c \
    sciclient.c \
    sciclient_pm.c \
    sciclient_rm.c \
    sciclient_rm_irq.c \
    sciclient_procboot.c \
    sciclient_firewall.c \
    sciclient_irq_rm.c \
    sciclient_fmwSecureProxyMap.c \
    sciclient_soc_priv.c \
    soc.c \
    uart_v0.c \
    uart_dma.c \
    utils.c \

FILES_PATH_common = \
    i2c/v0 \
    pinmux/am62x \
    mcan/v0 \
    ipc_notify/v0 \
    epwm/v0 \
    gpio/v0 \
    mcspi/v0 \
    mcspi/v0/dma \
    mcspi/v0/dma/udma \
    ipc_notify/v0/soc/am62x \
    ipc_rpmsg/ \
    sciclient \
    sciclient/soc/am62x \
    soc/am62x \
    device_manager/sciclient_direct \
    gpio/v0 \
    gtc/v0 \
    gtc/v0/soc/am62x \
    bootloader \
    bootloader/soc/am62x \
    ddr/v0 \
    ddr/cdn_drv/ \
    ddr/cdn_drv/common/include \
    ddr/cdn_drv/v0/include \
    ddr/cdn_drv/common \
    ddr/cdn_drv/v0 \
    ddr \
    ddr/v0/soc/am62x \
    mmcsd \
    mmcsd/v1 \
    ospi \
    ospi/v0 \
    ospi/v0/dma \
    ospi/v0/dma/udma \
    uart/v0 \
    uart/v0/dma \
    udma \
    udma/soc \
    udma/hw_include \
    udma/soc/am62x \
    soc/am62x \
    elm/v0 \
    gpmc/v0 \
    gpmc/v0/dma \
    gpmc/v0/dma/udma \
    utils \
    dss \
    dss/v0 \
    dss/v0/common \
    dss/v0/dctrl \
    dss/v0/disp \
    dss/v0/hw_include \
    dss/v0/hw_include/V3 \
    dss/v0/include \
    dss/v0/soc \
    dss/v0/soc/am62x \
    fvid2/v0 \

INCLUDES_common := \
    -I${CG_TOOL_ROOT}/include/c \
    -I${MCU_PLUS_SDK_PATH}/source \

DEFINES_common := \
    -DSOC_AM62X \

CFLAGS_common := \
    -mcpu=cortex-m4 \
    -mfloat-abi=hard \
    -mthumb \
    -Wall \
    -Werror \
    -g \
    -Wno-gnu-variable-sized-type-not-at-end \
    -Wno-unused-function \

CFLAGS_cpp_common := \
    -Wno-c99-designator \
    -Wno-extern-c-compat \
    -Wno-c++11-narrowing \
    -Wno-reorder-init-list \
    -Wno-deprecated-register \
    -Wno-writable-strings \
    -Wno-enum-compare \
    -Wno-reserved-user-defined-literal \
    -Wno-unused-const-variable \
    -x c++ \

CFLAGS_debug := \
    -D_DEBUG_=1 \

CFLAGS_release := \
    -Os \

ARFLAGS_common := \
    rc \

FILES := $(FILES_common) $(FILES_$(PROFILE))
ASMFILES := $(ASMFILES_common) $(ASMFILES_$(PROFILE))
FILES_PATH := $(FILES_PATH_common) $(FILES_PATH_$(PROFILE))
CFLAGS := $(CFLAGS_common) $(CFLAGS_$(PROFILE))
ASMFLAGS := $(CFLAGS_common) $(CFLAGS_$(PROFILE))
ifeq ($(CPLUSPLUS_BUILD), yes)
CFLAGS += $(CFLAGS_cpp_common)
endif
DEFINES := $(DEFINES_common) $(DEFINES_$(PROFILE))
INCLUDES := $(INCLUDES_common) $(INCLUDE_$(PROFILE))
ARFLAGS := $(ARFLAGS_common) $(ARFLAGS_$(PROFILE))

LIBDIR := lib
OBJDIR := obj/am62x/ti-arm-clang/$(PROFILE)/m4f/drivers/
OBJS := $(FILES:%.c=%.obj)
OBJS += $(ASMFILES:%.S=%.obj)
DEPS := $(FILES:%.c=%.d)

vpath %.obj $(OBJDIR)
vpath %.c $(FILES_PATH)
vpath %.S $(FILES_PATH)

$(OBJDIR)/%.obj %.obj: %.c
	@echo  Compiling: $(LIBNAME): $<
	$(CC) -c $(CFLAGS) $(INCLUDES) $(DEFINES) -MMD -o $(OBJDIR)/$@ $<

$(OBJDIR)/%.obj %.obj: %.S
	@echo  Compiling: $(LIBNAME): $<
	$(CC) -c $(ASMFLAGS) -o $(OBJDIR)/$@ $<

all: $(LIBDIR)/$(LIBNAME)

$(LIBDIR)/$(LIBNAME): $(OBJS) | $(LIBDIR)
	@echo  .
	@echo  Archiving: $(LIBNAME) to $@ ...
	$(AR) $(ARFLAGS) $@ $(addprefix $(OBJDIR), $(OBJS))
	@echo  Archiving: $(LIBNAME) Done !!!
	@echo  .

clean:
	@echo  Cleaning: $(LIBNAME) ...
	$(RMDIR) $(OBJDIR)
	$(RM) $(LIBDIR)/$(LIBNAME)

scrub:
	@echo  Scrubing: $(LIBNAME) ...
	-$(RMDIR) obj/
	-$(RMDIR) lib/

$(OBJS): | $(OBJDIR)

$(LIBDIR) $(OBJDIR):
	$(MKDIR) $@

-include $(addprefix $(OBJDIR)/, $(DEPS))
