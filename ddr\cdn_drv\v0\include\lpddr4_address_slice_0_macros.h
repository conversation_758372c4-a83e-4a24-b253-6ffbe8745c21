/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_ADDRESS_SLICE_0_MACROS_H_
#define REG_LPDDR4_ADDRESS_SLICE_0_MACROS_H_

#define LPDDR4__DENALI_PHY_512_READ_MASK                             0x000107FFU
#define LPDDR4__DENALI_PHY_512_WRITE_MASK                            0x000107FFU
#define LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0_WIDTH    11U
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0__REG DENALI_PHY_512
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_BYPASS_OVERRIDE_0_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_BYPASS_OVERRIDE_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_BYPASS_OVERRIDE_0_WIDTH           1U
#define LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_BYPASS_OVERRIDE_0_WOCLR           0U
#define LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_BYPASS_OVERRIDE_0_WOSET           0U
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_0__REG DENALI_PHY_512
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_0__FLD LPDDR4__DENALI_PHY_512__PHY_ADR_CLK_BYPASS_OVERRIDE_0

#define LPDDR4__DENALI_PHY_512__SC_PHY_ADR_MANUAL_CLEAR_0_MASK       0x07000000U
#define LPDDR4__DENALI_PHY_512__SC_PHY_ADR_MANUAL_CLEAR_0_SHIFT              24U
#define LPDDR4__DENALI_PHY_512__SC_PHY_ADR_MANUAL_CLEAR_0_WIDTH               3U
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_0__REG DENALI_PHY_512
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_0__FLD LPDDR4__DENALI_PHY_512__SC_PHY_ADR_MANUAL_CLEAR_0

#define LPDDR4__DENALI_PHY_513_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_513_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_513__PHY_ADR_LPBK_RESULT_OBS_0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_513__PHY_ADR_LPBK_RESULT_OBS_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_513__PHY_ADR_LPBK_RESULT_OBS_0_WIDTH              32U
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_0__REG DENALI_PHY_513
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_0__FLD LPDDR4__DENALI_PHY_513__PHY_ADR_LPBK_RESULT_OBS_0

#define LPDDR4__DENALI_PHY_514_READ_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_514_WRITE_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_514__PHY_ADR_LPBK_ERROR_COUNT_OBS_0_MASK  0x0000FFFFU
#define LPDDR4__DENALI_PHY_514__PHY_ADR_LPBK_ERROR_COUNT_OBS_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_514__PHY_ADR_LPBK_ERROR_COUNT_OBS_0_WIDTH         16U
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_0__REG DENALI_PHY_514
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_0__FLD LPDDR4__DENALI_PHY_514__PHY_ADR_LPBK_ERROR_COUNT_OBS_0

#define LPDDR4__DENALI_PHY_514__PHY_ADR_MEAS_DLY_STEP_VALUE_0_MASK   0x00FF0000U
#define LPDDR4__DENALI_PHY_514__PHY_ADR_MEAS_DLY_STEP_VALUE_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_514__PHY_ADR_MEAS_DLY_STEP_VALUE_0_WIDTH           8U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_0__REG DENALI_PHY_514
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_0__FLD LPDDR4__DENALI_PHY_514__PHY_ADR_MEAS_DLY_STEP_VALUE_0

#define LPDDR4__DENALI_PHY_514__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0_MASK 0x0F000000U
#define LPDDR4__DENALI_PHY_514__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0_SHIFT   24U
#define LPDDR4__DENALI_PHY_514__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0_WIDTH    4U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0__REG DENALI_PHY_514
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_514__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_515_READ_MASK                             0xFF7F07FFU
#define LPDDR4__DENALI_PHY_515_WRITE_MASK                            0xFF7F07FFU
#define LPDDR4__DENALI_PHY_515__PHY_ADR_MASTER_DLY_LOCK_OBS_0_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_515__PHY_ADR_MASTER_DLY_LOCK_OBS_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_515__PHY_ADR_MASTER_DLY_LOCK_OBS_0_WIDTH          11U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_0__REG DENALI_PHY_515
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_0__FLD LPDDR4__DENALI_PHY_515__PHY_ADR_MASTER_DLY_LOCK_OBS_0

#define LPDDR4__DENALI_PHY_515__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0_MASK  0x007F0000U
#define LPDDR4__DENALI_PHY_515__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0_SHIFT         16U
#define LPDDR4__DENALI_PHY_515__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0_WIDTH          7U
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_515
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_515__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_515__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_515__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0_SHIFT        24U
#define LPDDR4__DENALI_PHY_515__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0_WIDTH         8U
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_515
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_515__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_516_READ_MASK                             0x01000707U
#define LPDDR4__DENALI_PHY_516_WRITE_MASK                            0x01000707U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0_MASK 0x00000007U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0_WIDTH         3U
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0__REG DENALI_PHY_516
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0__FLD LPDDR4__DENALI_PHY_516__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0

#define LPDDR4__DENALI_PHY_516__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0_MASK 0x00000700U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0_SHIFT        8U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0_WIDTH        3U
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0__REG DENALI_PHY_516
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_516__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_516__SC_PHY_ADR_SNAP_OBS_REGS_0_MASK      0x00010000U
#define LPDDR4__DENALI_PHY_516__SC_PHY_ADR_SNAP_OBS_REGS_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_516__SC_PHY_ADR_SNAP_OBS_REGS_0_WIDTH              1U
#define LPDDR4__DENALI_PHY_516__SC_PHY_ADR_SNAP_OBS_REGS_0_WOCLR              0U
#define LPDDR4__DENALI_PHY_516__SC_PHY_ADR_SNAP_OBS_REGS_0_WOSET              0U
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_0__REG DENALI_PHY_516
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_0__FLD LPDDR4__DENALI_PHY_516__SC_PHY_ADR_SNAP_OBS_REGS_0

#define LPDDR4__DENALI_PHY_516__PHY_ADR_TSEL_ENABLE_0_MASK           0x01000000U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_TSEL_ENABLE_0_SHIFT                  24U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_TSEL_ENABLE_0_WIDTH                   1U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_TSEL_ENABLE_0_WOCLR                   0U
#define LPDDR4__DENALI_PHY_516__PHY_ADR_TSEL_ENABLE_0_WOSET                   0U
#define LPDDR4__PHY_ADR_TSEL_ENABLE_0__REG DENALI_PHY_516
#define LPDDR4__PHY_ADR_TSEL_ENABLE_0__FLD LPDDR4__DENALI_PHY_516__PHY_ADR_TSEL_ENABLE_0

#define LPDDR4__DENALI_PHY_517_READ_MASK                             0x011F7F7FU
#define LPDDR4__DENALI_PHY_517_WRITE_MASK                            0x011F7F7FU
#define LPDDR4__DENALI_PHY_517__PHY_ADR_LPBK_CONTROL_0_MASK          0x0000007FU
#define LPDDR4__DENALI_PHY_517__PHY_ADR_LPBK_CONTROL_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_LPBK_CONTROL_0_WIDTH                  7U
#define LPDDR4__PHY_ADR_LPBK_CONTROL_0__REG DENALI_PHY_517
#define LPDDR4__PHY_ADR_LPBK_CONTROL_0__FLD LPDDR4__DENALI_PHY_517__PHY_ADR_LPBK_CONTROL_0

#define LPDDR4__DENALI_PHY_517__PHY_ADR_PRBS_PATTERN_START_0_MASK    0x00007F00U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_PRBS_PATTERN_START_0_SHIFT            8U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_PRBS_PATTERN_START_0_WIDTH            7U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_0__REG DENALI_PHY_517
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_0__FLD LPDDR4__DENALI_PHY_517__PHY_ADR_PRBS_PATTERN_START_0

#define LPDDR4__DENALI_PHY_517__PHY_ADR_PRBS_PATTERN_MASK_0_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_PRBS_PATTERN_MASK_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_PRBS_PATTERN_MASK_0_WIDTH             5U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_0__REG DENALI_PHY_517
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_0__FLD LPDDR4__DENALI_PHY_517__PHY_ADR_PRBS_PATTERN_MASK_0

#define LPDDR4__DENALI_PHY_517__PHY_ADR_PWR_RDC_DISABLE_0_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_PWR_RDC_DISABLE_0_SHIFT              24U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_PWR_RDC_DISABLE_0_WIDTH               1U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_PWR_RDC_DISABLE_0_WOCLR               0U
#define LPDDR4__DENALI_PHY_517__PHY_ADR_PWR_RDC_DISABLE_0_WOSET               0U
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_0__REG DENALI_PHY_517
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_0__FLD LPDDR4__DENALI_PHY_517__PHY_ADR_PWR_RDC_DISABLE_0

#define LPDDR4__DENALI_PHY_518_READ_MASK                             0x01070301U
#define LPDDR4__DENALI_PHY_518_WRITE_MASK                            0x01070301U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_WIDTH     1U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_WOCLR     0U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_WOSET     0U
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0__REG DENALI_PHY_518
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0__FLD LPDDR4__DENALI_PHY_518__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0

#define LPDDR4__DENALI_PHY_518__PHY_ADR_TYPE_0_MASK                  0x00000300U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_TYPE_0_SHIFT                          8U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_TYPE_0_WIDTH                          2U
#define LPDDR4__PHY_ADR_TYPE_0__REG DENALI_PHY_518
#define LPDDR4__PHY_ADR_TYPE_0__FLD LPDDR4__DENALI_PHY_518__PHY_ADR_TYPE_0

#define LPDDR4__DENALI_PHY_518__PHY_ADR_WRADDR_SHIFT_OBS_0_MASK      0x00070000U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_WRADDR_SHIFT_OBS_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_WRADDR_SHIFT_OBS_0_WIDTH              3U
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_0__REG DENALI_PHY_518
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_0__FLD LPDDR4__DENALI_PHY_518__PHY_ADR_WRADDR_SHIFT_OBS_0

#define LPDDR4__DENALI_PHY_518__PHY_ADR_IE_MODE_0_MASK               0x01000000U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_IE_MODE_0_SHIFT                      24U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_IE_MODE_0_WIDTH                       1U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_IE_MODE_0_WOCLR                       0U
#define LPDDR4__DENALI_PHY_518__PHY_ADR_IE_MODE_0_WOSET                       0U
#define LPDDR4__PHY_ADR_IE_MODE_0__REG DENALI_PHY_518
#define LPDDR4__PHY_ADR_IE_MODE_0__FLD LPDDR4__DENALI_PHY_518__PHY_ADR_IE_MODE_0

#define LPDDR4__DENALI_PHY_519_READ_MASK                             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_519_WRITE_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_519__PHY_ADR_DDL_MODE_0_MASK              0x07FFFFFFU
#define LPDDR4__DENALI_PHY_519__PHY_ADR_DDL_MODE_0_SHIFT                      0U
#define LPDDR4__DENALI_PHY_519__PHY_ADR_DDL_MODE_0_WIDTH                     27U
#define LPDDR4__PHY_ADR_DDL_MODE_0__REG DENALI_PHY_519
#define LPDDR4__PHY_ADR_DDL_MODE_0__FLD LPDDR4__DENALI_PHY_519__PHY_ADR_DDL_MODE_0

#define LPDDR4__DENALI_PHY_520_READ_MASK                             0x0000003FU
#define LPDDR4__DENALI_PHY_520_WRITE_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_520__PHY_ADR_DDL_MASK_0_MASK              0x0000003FU
#define LPDDR4__DENALI_PHY_520__PHY_ADR_DDL_MASK_0_SHIFT                      0U
#define LPDDR4__DENALI_PHY_520__PHY_ADR_DDL_MASK_0_WIDTH                      6U
#define LPDDR4__PHY_ADR_DDL_MASK_0__REG DENALI_PHY_520
#define LPDDR4__PHY_ADR_DDL_MASK_0__FLD LPDDR4__DENALI_PHY_520__PHY_ADR_DDL_MASK_0

#define LPDDR4__DENALI_PHY_521_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_521_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_521__PHY_ADR_DDL_TEST_OBS_0_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_521__PHY_ADR_DDL_TEST_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_521__PHY_ADR_DDL_TEST_OBS_0_WIDTH                 32U
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_0__REG DENALI_PHY_521
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_0__FLD LPDDR4__DENALI_PHY_521__PHY_ADR_DDL_TEST_OBS_0

#define LPDDR4__DENALI_PHY_522_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_522_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_522__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_522__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_522__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0_WIDTH        32U
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0__REG DENALI_PHY_522
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0__FLD LPDDR4__DENALI_PHY_522__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0

#define LPDDR4__DENALI_PHY_523_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_523_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_523__PHY_ADR_CALVL_START_0_MASK           0x000007FFU
#define LPDDR4__DENALI_PHY_523__PHY_ADR_CALVL_START_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_523__PHY_ADR_CALVL_START_0_WIDTH                  11U
#define LPDDR4__PHY_ADR_CALVL_START_0__REG DENALI_PHY_523
#define LPDDR4__PHY_ADR_CALVL_START_0__FLD LPDDR4__DENALI_PHY_523__PHY_ADR_CALVL_START_0

#define LPDDR4__DENALI_PHY_523__PHY_ADR_CALVL_COARSE_DLY_0_MASK      0x07FF0000U
#define LPDDR4__DENALI_PHY_523__PHY_ADR_CALVL_COARSE_DLY_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_523__PHY_ADR_CALVL_COARSE_DLY_0_WIDTH             11U
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_0__REG DENALI_PHY_523
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_0__FLD LPDDR4__DENALI_PHY_523__PHY_ADR_CALVL_COARSE_DLY_0

#define LPDDR4__DENALI_PHY_524_READ_MASK                             0x000007FFU
#define LPDDR4__DENALI_PHY_524_WRITE_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_524__PHY_ADR_CALVL_QTR_0_MASK             0x000007FFU
#define LPDDR4__DENALI_PHY_524__PHY_ADR_CALVL_QTR_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_524__PHY_ADR_CALVL_QTR_0_WIDTH                    11U
#define LPDDR4__PHY_ADR_CALVL_QTR_0__REG DENALI_PHY_524
#define LPDDR4__PHY_ADR_CALVL_QTR_0__FLD LPDDR4__DENALI_PHY_524__PHY_ADR_CALVL_QTR_0

#define LPDDR4__DENALI_PHY_525_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PHY_525_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_525__PHY_ADR_CALVL_SWIZZLE0_0_MASK        0x00FFFFFFU
#define LPDDR4__DENALI_PHY_525__PHY_ADR_CALVL_SWIZZLE0_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_525__PHY_ADR_CALVL_SWIZZLE0_0_WIDTH               24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_0__REG DENALI_PHY_525
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_0__FLD LPDDR4__DENALI_PHY_525__PHY_ADR_CALVL_SWIZZLE0_0

#define LPDDR4__DENALI_PHY_526_READ_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PHY_526_WRITE_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_526__PHY_ADR_CALVL_SWIZZLE1_0_MASK        0x00FFFFFFU
#define LPDDR4__DENALI_PHY_526__PHY_ADR_CALVL_SWIZZLE1_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_526__PHY_ADR_CALVL_SWIZZLE1_0_WIDTH               24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_0__REG DENALI_PHY_526
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_0__FLD LPDDR4__DENALI_PHY_526__PHY_ADR_CALVL_SWIZZLE1_0

#define LPDDR4__DENALI_PHY_526__PHY_ADR_CALVL_RANK_CTRL_0_MASK       0x03000000U
#define LPDDR4__DENALI_PHY_526__PHY_ADR_CALVL_RANK_CTRL_0_SHIFT              24U
#define LPDDR4__DENALI_PHY_526__PHY_ADR_CALVL_RANK_CTRL_0_WIDTH               2U
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_0__REG DENALI_PHY_526
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_0__FLD LPDDR4__DENALI_PHY_526__PHY_ADR_CALVL_RANK_CTRL_0

#define LPDDR4__DENALI_PHY_527_READ_MASK                             0x01FF0F03U
#define LPDDR4__DENALI_PHY_527_WRITE_MASK                            0x01FF0F03U
#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_NUM_PATTERNS_0_MASK    0x00000003U
#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_NUM_PATTERNS_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_NUM_PATTERNS_0_WIDTH            2U
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_0__REG DENALI_PHY_527
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_0__FLD LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_NUM_PATTERNS_0

#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_RESP_WAIT_CNT_0_MASK   0x00000F00U
#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_RESP_WAIT_CNT_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_RESP_WAIT_CNT_0_WIDTH           4U
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_0__REG DENALI_PHY_527
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_0__FLD LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_RESP_WAIT_CNT_0

#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0_MASK 0x01FF0000U
#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0_SHIFT  16U
#define LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0_WIDTH   9U
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0__REG DENALI_PHY_527
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0__FLD LPDDR4__DENALI_PHY_527__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0

#define LPDDR4__DENALI_PHY_528_READ_MASK                             0x07000001U
#define LPDDR4__DENALI_PHY_528_WRITE_MASK                            0x07000001U
#define LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_DEBUG_MODE_0_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_DEBUG_MODE_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_DEBUG_MODE_0_WIDTH              1U
#define LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_DEBUG_MODE_0_WOCLR              0U
#define LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_DEBUG_MODE_0_WOSET              0U
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_0__REG DENALI_PHY_528
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_0__FLD LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_DEBUG_MODE_0

#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_DEBUG_CONT_0_MASK   0x00000100U
#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_DEBUG_CONT_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_DEBUG_CONT_0_WIDTH           1U
#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_DEBUG_CONT_0_WOCLR           0U
#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_DEBUG_CONT_0_WOSET           0U
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_0__REG DENALI_PHY_528
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_0__FLD LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_DEBUG_CONT_0

#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_ERROR_CLR_0_MASK    0x00010000U
#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_ERROR_CLR_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_ERROR_CLR_0_WIDTH            1U
#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_ERROR_CLR_0_WOCLR            0U
#define LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_ERROR_CLR_0_WOSET            0U
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_0__REG DENALI_PHY_528
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_0__FLD LPDDR4__DENALI_PHY_528__SC_PHY_ADR_CALVL_ERROR_CLR_0

#define LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_OBS_SELECT_0_MASK      0x07000000U
#define LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_OBS_SELECT_0_SHIFT             24U
#define LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_OBS_SELECT_0_WIDTH              3U
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_0__REG DENALI_PHY_528
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_528__PHY_ADR_CALVL_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_529_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_529_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_529__PHY_ADR_CALVL_OBS0_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_529__PHY_ADR_CALVL_OBS0_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_529__PHY_ADR_CALVL_OBS0_0_WIDTH                   32U
#define LPDDR4__PHY_ADR_CALVL_OBS0_0__REG DENALI_PHY_529
#define LPDDR4__PHY_ADR_CALVL_OBS0_0__FLD LPDDR4__DENALI_PHY_529__PHY_ADR_CALVL_OBS0_0

#define LPDDR4__DENALI_PHY_530_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_530_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_530__PHY_ADR_CALVL_OBS1_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_530__PHY_ADR_CALVL_OBS1_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_530__PHY_ADR_CALVL_OBS1_0_WIDTH                   32U
#define LPDDR4__PHY_ADR_CALVL_OBS1_0__REG DENALI_PHY_530
#define LPDDR4__PHY_ADR_CALVL_OBS1_0__FLD LPDDR4__DENALI_PHY_530__PHY_ADR_CALVL_OBS1_0

#define LPDDR4__DENALI_PHY_531_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_531_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_531__PHY_ADR_CALVL_OBS2_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_531__PHY_ADR_CALVL_OBS2_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_531__PHY_ADR_CALVL_OBS2_0_WIDTH                   32U
#define LPDDR4__PHY_ADR_CALVL_OBS2_0__REG DENALI_PHY_531
#define LPDDR4__PHY_ADR_CALVL_OBS2_0__FLD LPDDR4__DENALI_PHY_531__PHY_ADR_CALVL_OBS2_0

#define LPDDR4__DENALI_PHY_532_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_532_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_532__PHY_ADR_CALVL_FG_0_0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_532__PHY_ADR_CALVL_FG_0_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_532__PHY_ADR_CALVL_FG_0_0_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_FG_0_0__REG DENALI_PHY_532
#define LPDDR4__PHY_ADR_CALVL_FG_0_0__FLD LPDDR4__DENALI_PHY_532__PHY_ADR_CALVL_FG_0_0

#define LPDDR4__DENALI_PHY_533_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_533_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_533__PHY_ADR_CALVL_BG_0_0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_533__PHY_ADR_CALVL_BG_0_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_533__PHY_ADR_CALVL_BG_0_0_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_BG_0_0__REG DENALI_PHY_533
#define LPDDR4__PHY_ADR_CALVL_BG_0_0__FLD LPDDR4__DENALI_PHY_533__PHY_ADR_CALVL_BG_0_0

#define LPDDR4__DENALI_PHY_534_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_534_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_534__PHY_ADR_CALVL_FG_1_0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_534__PHY_ADR_CALVL_FG_1_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_534__PHY_ADR_CALVL_FG_1_0_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_FG_1_0__REG DENALI_PHY_534
#define LPDDR4__PHY_ADR_CALVL_FG_1_0__FLD LPDDR4__DENALI_PHY_534__PHY_ADR_CALVL_FG_1_0

#define LPDDR4__DENALI_PHY_535_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_535_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_535__PHY_ADR_CALVL_BG_1_0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_535__PHY_ADR_CALVL_BG_1_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_535__PHY_ADR_CALVL_BG_1_0_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_BG_1_0__REG DENALI_PHY_535
#define LPDDR4__PHY_ADR_CALVL_BG_1_0__FLD LPDDR4__DENALI_PHY_535__PHY_ADR_CALVL_BG_1_0

#define LPDDR4__DENALI_PHY_536_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_536_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_536__PHY_ADR_CALVL_FG_2_0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_536__PHY_ADR_CALVL_FG_2_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_536__PHY_ADR_CALVL_FG_2_0_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_FG_2_0__REG DENALI_PHY_536
#define LPDDR4__PHY_ADR_CALVL_FG_2_0__FLD LPDDR4__DENALI_PHY_536__PHY_ADR_CALVL_FG_2_0

#define LPDDR4__DENALI_PHY_537_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_537_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_537__PHY_ADR_CALVL_BG_2_0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_537__PHY_ADR_CALVL_BG_2_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_537__PHY_ADR_CALVL_BG_2_0_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_BG_2_0__REG DENALI_PHY_537
#define LPDDR4__PHY_ADR_CALVL_BG_2_0__FLD LPDDR4__DENALI_PHY_537__PHY_ADR_CALVL_BG_2_0

#define LPDDR4__DENALI_PHY_538_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_538_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_538__PHY_ADR_CALVL_FG_3_0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_538__PHY_ADR_CALVL_FG_3_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_538__PHY_ADR_CALVL_FG_3_0_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_FG_3_0__REG DENALI_PHY_538
#define LPDDR4__PHY_ADR_CALVL_FG_3_0__FLD LPDDR4__DENALI_PHY_538__PHY_ADR_CALVL_FG_3_0

#define LPDDR4__DENALI_PHY_539_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_539_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_539__PHY_ADR_CALVL_BG_3_0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_539__PHY_ADR_CALVL_BG_3_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_539__PHY_ADR_CALVL_BG_3_0_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_BG_3_0__REG DENALI_PHY_539
#define LPDDR4__PHY_ADR_CALVL_BG_3_0__FLD LPDDR4__DENALI_PHY_539__PHY_ADR_CALVL_BG_3_0

#define LPDDR4__DENALI_PHY_540_READ_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_540_WRITE_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_540__PHY_ADR_ADDR_SEL_0_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_540__PHY_ADR_ADDR_SEL_0_SHIFT                      0U
#define LPDDR4__DENALI_PHY_540__PHY_ADR_ADDR_SEL_0_WIDTH                     30U
#define LPDDR4__PHY_ADR_ADDR_SEL_0__REG DENALI_PHY_540
#define LPDDR4__PHY_ADR_ADDR_SEL_0__FLD LPDDR4__DENALI_PHY_540__PHY_ADR_ADDR_SEL_0

#define LPDDR4__DENALI_PHY_541_READ_MASK                             0x3F3F03FFU
#define LPDDR4__DENALI_PHY_541_WRITE_MASK                            0x3F3F03FFU
#define LPDDR4__DENALI_PHY_541__PHY_ADR_LP4_BOOT_SLV_DELAY_0_MASK    0x000003FFU
#define LPDDR4__DENALI_PHY_541__PHY_ADR_LP4_BOOT_SLV_DELAY_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_541__PHY_ADR_LP4_BOOT_SLV_DELAY_0_WIDTH           10U
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_0__REG DENALI_PHY_541
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_0__FLD LPDDR4__DENALI_PHY_541__PHY_ADR_LP4_BOOT_SLV_DELAY_0

#define LPDDR4__DENALI_PHY_541__PHY_ADR_BIT_MASK_0_MASK              0x003F0000U
#define LPDDR4__DENALI_PHY_541__PHY_ADR_BIT_MASK_0_SHIFT                     16U
#define LPDDR4__DENALI_PHY_541__PHY_ADR_BIT_MASK_0_WIDTH                      6U
#define LPDDR4__PHY_ADR_BIT_MASK_0__REG DENALI_PHY_541
#define LPDDR4__PHY_ADR_BIT_MASK_0__FLD LPDDR4__DENALI_PHY_541__PHY_ADR_BIT_MASK_0

#define LPDDR4__DENALI_PHY_541__PHY_ADR_SEG_MASK_0_MASK              0x3F000000U
#define LPDDR4__DENALI_PHY_541__PHY_ADR_SEG_MASK_0_SHIFT                     24U
#define LPDDR4__DENALI_PHY_541__PHY_ADR_SEG_MASK_0_WIDTH                      6U
#define LPDDR4__PHY_ADR_SEG_MASK_0__REG DENALI_PHY_541
#define LPDDR4__PHY_ADR_SEG_MASK_0__FLD LPDDR4__DENALI_PHY_541__PHY_ADR_SEG_MASK_0

#define LPDDR4__DENALI_PHY_542_READ_MASK                             0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_542_WRITE_MASK                            0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_542__PHY_ADR_CALVL_TRAIN_MASK_0_MASK      0x0000003FU
#define LPDDR4__DENALI_PHY_542__PHY_ADR_CALVL_TRAIN_MASK_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_542__PHY_ADR_CALVL_TRAIN_MASK_0_WIDTH              6U
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_0__REG DENALI_PHY_542
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_0__FLD LPDDR4__DENALI_PHY_542__PHY_ADR_CALVL_TRAIN_MASK_0

#define LPDDR4__DENALI_PHY_542__PHY_ADR_CSLVL_TRAIN_MASK_0_MASK      0x00003F00U
#define LPDDR4__DENALI_PHY_542__PHY_ADR_CSLVL_TRAIN_MASK_0_SHIFT              8U
#define LPDDR4__DENALI_PHY_542__PHY_ADR_CSLVL_TRAIN_MASK_0_WIDTH              6U
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_0__REG DENALI_PHY_542
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_0__FLD LPDDR4__DENALI_PHY_542__PHY_ADR_CSLVL_TRAIN_MASK_0

#define LPDDR4__DENALI_PHY_542__PHY_ADR_STATIC_TOG_DISABLE_0_MASK    0x000F0000U
#define LPDDR4__DENALI_PHY_542__PHY_ADR_STATIC_TOG_DISABLE_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_542__PHY_ADR_STATIC_TOG_DISABLE_0_WIDTH            4U
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_0__REG DENALI_PHY_542
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_0__FLD LPDDR4__DENALI_PHY_542__PHY_ADR_STATIC_TOG_DISABLE_0

#define LPDDR4__DENALI_PHY_542__PHY_ADR_SW_TXIO_CTRL_0_MASK          0x3F000000U
#define LPDDR4__DENALI_PHY_542__PHY_ADR_SW_TXIO_CTRL_0_SHIFT                 24U
#define LPDDR4__DENALI_PHY_542__PHY_ADR_SW_TXIO_CTRL_0_WIDTH                  6U
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_0__REG DENALI_PHY_542
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_0__FLD LPDDR4__DENALI_PHY_542__PHY_ADR_SW_TXIO_CTRL_0

#define LPDDR4__DENALI_PHY_543_READ_MASK                             0x0000003FU
#define LPDDR4__DENALI_PHY_543_WRITE_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_543__PHY_ADR_SW_TXPWR_CTRL_0_MASK         0x0000003FU
#define LPDDR4__DENALI_PHY_543__PHY_ADR_SW_TXPWR_CTRL_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_543__PHY_ADR_SW_TXPWR_CTRL_0_WIDTH                 6U
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_0__REG DENALI_PHY_543
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_0__FLD LPDDR4__DENALI_PHY_543__PHY_ADR_SW_TXPWR_CTRL_0

#define LPDDR4__DENALI_PHY_544_READ_MASK                             0x0707FFFFU
#define LPDDR4__DENALI_PHY_544_WRITE_MASK                            0x0707FFFFU
#define LPDDR4__DENALI_PHY_544__PHY_ADR_TSEL_SELECT_0_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_544__PHY_ADR_TSEL_SELECT_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_544__PHY_ADR_TSEL_SELECT_0_WIDTH                   8U
#define LPDDR4__PHY_ADR_TSEL_SELECT_0__REG DENALI_PHY_544
#define LPDDR4__PHY_ADR_TSEL_SELECT_0__FLD LPDDR4__DENALI_PHY_544__PHY_ADR_TSEL_SELECT_0

#define LPDDR4__DENALI_PHY_544__PHY_PAD_ADR_IO_CFG_0_MASK            0x0007FF00U
#define LPDDR4__DENALI_PHY_544__PHY_PAD_ADR_IO_CFG_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_544__PHY_PAD_ADR_IO_CFG_0_WIDTH                   11U
#define LPDDR4__PHY_PAD_ADR_IO_CFG_0__REG DENALI_PHY_544
#define LPDDR4__PHY_PAD_ADR_IO_CFG_0__FLD LPDDR4__DENALI_PHY_544__PHY_PAD_ADR_IO_CFG_0

#define LPDDR4__DENALI_PHY_544__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0_MASK   0x07000000U
#define LPDDR4__DENALI_PHY_544__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0_SHIFT          24U
#define LPDDR4__DENALI_PHY_544__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0_WIDTH           3U
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0__REG DENALI_PHY_544
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0__FLD LPDDR4__DENALI_PHY_544__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0

#define LPDDR4__DENALI_PHY_545_READ_MASK                             0x1F07FF1FU
#define LPDDR4__DENALI_PHY_545_WRITE_MASK                            0x1F07FF1FU
#define LPDDR4__DENALI_PHY_545__PHY_ADR0_SW_WRADDR_SHIFT_0_MASK      0x0000001FU
#define LPDDR4__DENALI_PHY_545__PHY_ADR0_SW_WRADDR_SHIFT_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_545__PHY_ADR0_SW_WRADDR_SHIFT_0_WIDTH              5U
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_0__REG DENALI_PHY_545
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_545__PHY_ADR0_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_545__PHY_ADR0_CLK_WR_SLAVE_DELAY_0_MASK   0x0007FF00U
#define LPDDR4__DENALI_PHY_545__PHY_ADR0_CLK_WR_SLAVE_DELAY_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_545__PHY_ADR0_CLK_WR_SLAVE_DELAY_0_WIDTH          11U
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_545
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_545__PHY_ADR0_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_545__PHY_ADR1_SW_WRADDR_SHIFT_0_MASK      0x1F000000U
#define LPDDR4__DENALI_PHY_545__PHY_ADR1_SW_WRADDR_SHIFT_0_SHIFT             24U
#define LPDDR4__DENALI_PHY_545__PHY_ADR1_SW_WRADDR_SHIFT_0_WIDTH              5U
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_0__REG DENALI_PHY_545
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_545__PHY_ADR1_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_546_READ_MASK                             0x001F07FFU
#define LPDDR4__DENALI_PHY_546_WRITE_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_546__PHY_ADR1_CLK_WR_SLAVE_DELAY_0_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_546__PHY_ADR1_CLK_WR_SLAVE_DELAY_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_546__PHY_ADR1_CLK_WR_SLAVE_DELAY_0_WIDTH          11U
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_546
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_546__PHY_ADR1_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_546__PHY_ADR2_SW_WRADDR_SHIFT_0_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_546__PHY_ADR2_SW_WRADDR_SHIFT_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_546__PHY_ADR2_SW_WRADDR_SHIFT_0_WIDTH              5U
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_0__REG DENALI_PHY_546
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_546__PHY_ADR2_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_547_READ_MASK                             0x001F07FFU
#define LPDDR4__DENALI_PHY_547_WRITE_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_547__PHY_ADR2_CLK_WR_SLAVE_DELAY_0_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_547__PHY_ADR2_CLK_WR_SLAVE_DELAY_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_547__PHY_ADR2_CLK_WR_SLAVE_DELAY_0_WIDTH          11U
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_547
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_547__PHY_ADR2_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_547__PHY_ADR3_SW_WRADDR_SHIFT_0_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_547__PHY_ADR3_SW_WRADDR_SHIFT_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_547__PHY_ADR3_SW_WRADDR_SHIFT_0_WIDTH              5U
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_0__REG DENALI_PHY_547
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_547__PHY_ADR3_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_548_READ_MASK                             0x001F07FFU
#define LPDDR4__DENALI_PHY_548_WRITE_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_548__PHY_ADR3_CLK_WR_SLAVE_DELAY_0_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_548__PHY_ADR3_CLK_WR_SLAVE_DELAY_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_548__PHY_ADR3_CLK_WR_SLAVE_DELAY_0_WIDTH          11U
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_548
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_548__PHY_ADR3_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_548__PHY_ADR4_SW_WRADDR_SHIFT_0_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_548__PHY_ADR4_SW_WRADDR_SHIFT_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_548__PHY_ADR4_SW_WRADDR_SHIFT_0_WIDTH              5U
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_0__REG DENALI_PHY_548
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_548__PHY_ADR4_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_549_READ_MASK                             0x001F07FFU
#define LPDDR4__DENALI_PHY_549_WRITE_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_549__PHY_ADR4_CLK_WR_SLAVE_DELAY_0_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_549__PHY_ADR4_CLK_WR_SLAVE_DELAY_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_549__PHY_ADR4_CLK_WR_SLAVE_DELAY_0_WIDTH          11U
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_549
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_549__PHY_ADR4_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_549__PHY_ADR5_SW_WRADDR_SHIFT_0_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_549__PHY_ADR5_SW_WRADDR_SHIFT_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_549__PHY_ADR5_SW_WRADDR_SHIFT_0_WIDTH              5U
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_0__REG DENALI_PHY_549
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_549__PHY_ADR5_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_550_READ_MASK                             0x000F07FFU
#define LPDDR4__DENALI_PHY_550_WRITE_MASK                            0x000F07FFU
#define LPDDR4__DENALI_PHY_550__PHY_ADR5_CLK_WR_SLAVE_DELAY_0_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_550__PHY_ADR5_CLK_WR_SLAVE_DELAY_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_550__PHY_ADR5_CLK_WR_SLAVE_DELAY_0_WIDTH          11U
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_550
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_550__PHY_ADR5_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_550__PHY_ADR_SW_MASTER_MODE_0_MASK        0x000F0000U
#define LPDDR4__DENALI_PHY_550__PHY_ADR_SW_MASTER_MODE_0_SHIFT               16U
#define LPDDR4__DENALI_PHY_550__PHY_ADR_SW_MASTER_MODE_0_WIDTH                4U
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_0__REG DENALI_PHY_550
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_0__FLD LPDDR4__DENALI_PHY_550__PHY_ADR_SW_MASTER_MODE_0

#define LPDDR4__DENALI_PHY_551_READ_MASK                             0xFF3F07FFU
#define LPDDR4__DENALI_PHY_551_WRITE_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_START_0_MASK    0x000007FFU
#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_START_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_START_0_WIDTH           11U
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_0__REG DENALI_PHY_551
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_0__FLD LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_START_0

#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_STEP_0_MASK     0x003F0000U
#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_STEP_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_STEP_0_WIDTH             6U
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_0__REG DENALI_PHY_551
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_0__FLD LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_STEP_0

#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_WAIT_0_MASK     0xFF000000U
#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_WAIT_0_SHIFT            24U
#define LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_WAIT_0_WIDTH             8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_0__REG DENALI_PHY_551
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_0__FLD LPDDR4__DENALI_PHY_551__PHY_ADR_MASTER_DELAY_WAIT_0

#define LPDDR4__DENALI_PHY_552_READ_MASK                             0x0103FFFFU
#define LPDDR4__DENALI_PHY_552_WRITE_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_552__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_552__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_552__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0_WIDTH     8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0__REG DENALI_PHY_552
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0__FLD LPDDR4__DENALI_PHY_552__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0

#define LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_0_MASK      0x0003FF00U
#define LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_0_SHIFT              8U
#define LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_0_WIDTH             10U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_0__REG DENALI_PHY_552
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_0__FLD LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_0

#define LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_MASK   0x01000000U
#define LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_SHIFT          24U
#define LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_WIDTH           1U
#define LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_WOCLR           0U
#define LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_WOSET           0U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_0__REG DENALI_PHY_552
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_0__FLD LPDDR4__DENALI_PHY_552__PHY_ADR_SW_CALVL_DVW_MIN_EN_0

#define LPDDR4__DENALI_PHY_553_READ_MASK                             0x0000000FU
#define LPDDR4__DENALI_PHY_553_WRITE_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_553__PHY_ADR_CALVL_DLY_STEP_0_MASK        0x0000000FU
#define LPDDR4__DENALI_PHY_553__PHY_ADR_CALVL_DLY_STEP_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_553__PHY_ADR_CALVL_DLY_STEP_0_WIDTH                4U
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_0__REG DENALI_PHY_553
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_0__FLD LPDDR4__DENALI_PHY_553__PHY_ADR_CALVL_DLY_STEP_0

#define LPDDR4__DENALI_PHY_554_READ_MASK                             0x0000010FU
#define LPDDR4__DENALI_PHY_554_WRITE_MASK                            0x0000010FU
#define LPDDR4__DENALI_PHY_554__PHY_ADR_CALVL_CAPTURE_CNT_0_MASK     0x0000000FU
#define LPDDR4__DENALI_PHY_554__PHY_ADR_CALVL_CAPTURE_CNT_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_554__PHY_ADR_CALVL_CAPTURE_CNT_0_WIDTH             4U
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_0__REG DENALI_PHY_554
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_0__FLD LPDDR4__DENALI_PHY_554__PHY_ADR_CALVL_CAPTURE_CNT_0

#define LPDDR4__DENALI_PHY_554__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_MASK  0x00000100U
#define LPDDR4__DENALI_PHY_554__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_SHIFT          8U
#define LPDDR4__DENALI_PHY_554__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_WIDTH          1U
#define LPDDR4__DENALI_PHY_554__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_WOCLR          0U
#define LPDDR4__DENALI_PHY_554__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_WOSET          0U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_0__REG DENALI_PHY_554
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_0__FLD LPDDR4__DENALI_PHY_554__PHY_ADR_MEAS_DLY_STEP_ENABLE_0

#endif /* REG_LPDDR4_ADDRESS_SLICE_0_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

