/********************************************************************
 * Copyright (C) 2014-21 Texas Instruments Incorporated.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *  Name        : cslr_ospi.h
*/
#ifndef CSLR_OSPI_H_
#define CSLR_OSPI_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>

/**************************************************************************
* Module Base Offset Values
**************************************************************************/

#define CSL_OSPI_CFG_REGS_BASE                                                 (0x00000000U)
#define CSL_OSPI_ECC_AGGR_REGS_BASE                                            (0x00000000U)
#define CSL_OSPI_FLASH_CFG_REGS_BASE                                           (0x00000000U)
#define CSL_OSPI_FLASH_MEM0_REGS_BASE                                          (0x00000000U)
#define CSL_OSPI_FLASH_MEM1_REGS_BASE                                          (0x00000000U)


/**************************************************************************
* Hardware Region  : Global Control Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t PID;                       /* Revision Register */
    volatile uint32_t CTRL;                      /* Control Register */
    volatile uint32_t STAT;                      /* Status Regsiter */
    volatile uint8_t  Resv_32[20];
    volatile uint32_t EOI;                       /* End Of Interrupt */
} CSL_ospi_cfgRegs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_OSPI_CFG_PID                                                       (0x00000000U)
#define CSL_OSPI_CFG_CTRL                                                      (0x00000004U)
#define CSL_OSPI_CFG_STAT                                                      (0x00000008U)
#define CSL_OSPI_CFG_EOI                                                       (0x00000020U)

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* PID */

#define CSL_OSPI_CFG_PID_MINOR_MASK                                            (0x0000003FU)
#define CSL_OSPI_CFG_PID_MINOR_SHIFT                                           (0x00000000U)
#define CSL_OSPI_CFG_PID_MINOR_MAX                                             (0x0000003FU)

#define CSL_OSPI_CFG_PID_CUSTOM_MASK                                           (0x000000C0U)
#define CSL_OSPI_CFG_PID_CUSTOM_SHIFT                                          (0x00000006U)
#define CSL_OSPI_CFG_PID_CUSTOM_MAX                                            (0x00000003U)

#define CSL_OSPI_CFG_PID_MAJOR_MASK                                            (0x00000700U)
#define CSL_OSPI_CFG_PID_MAJOR_SHIFT                                           (0x00000008U)
#define CSL_OSPI_CFG_PID_MAJOR_MAX                                             (0x00000007U)

#define CSL_OSPI_CFG_PID_RTL_MASK                                              (0x0000F800U)
#define CSL_OSPI_CFG_PID_RTL_SHIFT                                             (0x0000000BU)
#define CSL_OSPI_CFG_PID_RTL_MAX                                               (0x0000001FU)

#define CSL_OSPI_CFG_PID_MODULE_ID_MASK                                        (0x0FFF0000U)
#define CSL_OSPI_CFG_PID_MODULE_ID_SHIFT                                       (0x00000010U)
#define CSL_OSPI_CFG_PID_MODULE_ID_MAX                                         (0x00000FFFU)

#define CSL_OSPI_CFG_PID_BU_MASK                                               (0x30000000U)
#define CSL_OSPI_CFG_PID_BU_SHIFT                                              (0x0000001CU)
#define CSL_OSPI_CFG_PID_BU_MAX                                                (0x00000003U)

#define CSL_OSPI_CFG_PID_SCHEME_MASK                                           (0xC0000000U)
#define CSL_OSPI_CFG_PID_SCHEME_SHIFT                                          (0x0000001EU)
#define CSL_OSPI_CFG_PID_SCHEME_MAX                                            (0x00000003U)

/* CTRL */

#define CSL_OSPI_CFG_CTRL_PIPELINE_MODE_FLUSH_MASK                             (0x00000008U)
#define CSL_OSPI_CFG_CTRL_PIPELINE_MODE_FLUSH_SHIFT                            (0x00000003U)
#define CSL_OSPI_CFG_CTRL_PIPELINE_MODE_FLUSH_MAX                              (0x00000001U)

/* STAT */

#define CSL_OSPI_CFG_STAT_MEM_INIT_DONE_MASK                                   (0x00000002U)
#define CSL_OSPI_CFG_STAT_MEM_INIT_DONE_SHIFT                                  (0x00000001U)
#define CSL_OSPI_CFG_STAT_MEM_INIT_DONE_MAX                                    (0x00000001U)

/* EOI */

#define CSL_OSPI_CFG_EOI_EOI_MASK                                              (0x000000FFU)
#define CSL_OSPI_CFG_EOI_EOI_SHIFT                                             (0x00000000U)
#define CSL_OSPI_CFG_EOI_EOI_MAX                                               (0x000000FFU)

/**************************************************************************
* Hardware Region  :
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t CONFIG_REG;
    volatile uint32_t DEV_INSTR_RD_CONFIG_REG;
    volatile uint32_t DEV_INSTR_WR_CONFIG_REG;
    volatile uint32_t DEV_DELAY_REG;
    volatile uint32_t RD_DATA_CAPTURE_REG;
    volatile uint32_t DEV_SIZE_CONFIG_REG;
    volatile uint32_t SRAM_PARTITION_CFG_REG;
    volatile uint32_t IND_AHB_ADDR_TRIGGER_REG;
    volatile uint32_t DMA_PERIPH_CONFIG_REG;
    volatile uint32_t REMAP_ADDR_REG;
    volatile uint32_t MODE_BIT_CONFIG_REG;
    volatile uint32_t SRAM_FILL_REG;
    volatile uint32_t TX_THRESH_REG;
    volatile uint32_t RX_THRESH_REG;
    volatile uint32_t WRITE_COMPLETION_CTRL_REG;
    volatile uint32_t NO_OF_POLLS_BEF_EXP_REG;
    volatile uint32_t IRQ_STATUS_REG;
    volatile uint32_t IRQ_MASK_REG;
    volatile uint8_t  Resv_80[8];
    volatile uint32_t LOWER_WR_PROT_REG;
    volatile uint32_t UPPER_WR_PROT_REG;
    volatile uint32_t WR_PROT_CTRL_REG;
    volatile uint8_t  Resv_96[4];
    volatile uint32_t INDIRECT_READ_XFER_CTRL_REG;
    volatile uint32_t INDIRECT_READ_XFER_WATERMARK_REG;
    volatile uint32_t INDIRECT_READ_XFER_START_REG;
    volatile uint32_t INDIRECT_READ_XFER_NUM_BYTES_REG;
    volatile uint32_t INDIRECT_WRITE_XFER_CTRL_REG;
    volatile uint32_t INDIRECT_WRITE_XFER_WATERMARK_REG;
    volatile uint32_t INDIRECT_WRITE_XFER_START_REG;
    volatile uint32_t INDIRECT_WRITE_XFER_NUM_BYTES_REG;
    volatile uint32_t INDIRECT_TRIGGER_ADDR_RANGE_REG;
    volatile uint8_t  Resv_140[8];
    volatile uint32_t FLASH_COMMAND_CTRL_MEM_REG;
    volatile uint32_t FLASH_CMD_CTRL_REG;
    volatile uint32_t FLASH_CMD_ADDR_REG;
    volatile uint8_t  Resv_160[8];
    volatile uint32_t FLASH_RD_DATA_LOWER_REG;
    volatile uint32_t FLASH_RD_DATA_UPPER_REG;
    volatile uint32_t FLASH_WR_DATA_LOWER_REG;
    volatile uint32_t FLASH_WR_DATA_UPPER_REG;
    volatile uint32_t POLLING_FLASH_STATUS_REG;
    volatile uint32_t PHY_CONFIGURATION_REG;
    volatile uint32_t PHY_MASTER_CONTROL_REG;
    volatile uint32_t DLL_OBSERVABLE_LOWER_REG;
    volatile uint32_t DLL_OBSERVABLE_UPPER_REG;
    volatile uint8_t  Resv_224[28];
    volatile uint32_t OPCODE_EXT_LOWER_REG;
    volatile uint32_t OPCODE_EXT_UPPER_REG;
    volatile uint8_t  Resv_252[20];
    volatile uint32_t MODULE_ID_REG;
} CSL_ospi_flash_cfgRegs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_OSPI_FLASH_CFG_CONFIG_REG                                          (0x00000000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG                             (0x00000004U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG                             (0x00000008U)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG                                       (0x0000000CU)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG                                 (0x00000010U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG                                 (0x00000014U)
#define CSL_OSPI_FLASH_CFG_SRAM_PARTITION_CFG_REG                              (0x00000018U)
#define CSL_OSPI_FLASH_CFG_IND_AHB_ADDR_TRIGGER_REG                            (0x0000001CU)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG                               (0x00000020U)
#define CSL_OSPI_FLASH_CFG_REMAP_ADDR_REG                                      (0x00000024U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG                                 (0x00000028U)
#define CSL_OSPI_FLASH_CFG_SRAM_FILL_REG                                       (0x0000002CU)
#define CSL_OSPI_FLASH_CFG_TX_THRESH_REG                                       (0x00000030U)
#define CSL_OSPI_FLASH_CFG_RX_THRESH_REG                                       (0x00000034U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG                           (0x00000038U)
#define CSL_OSPI_FLASH_CFG_NO_OF_POLLS_BEF_EXP_REG                             (0x0000003CU)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG                                      (0x00000040U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG                                        (0x00000044U)
#define CSL_OSPI_FLASH_CFG_LOWER_WR_PROT_REG                                   (0x00000050U)
#define CSL_OSPI_FLASH_CFG_UPPER_WR_PROT_REG                                   (0x00000054U)
#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG                                    (0x00000058U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG                         (0x00000060U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_WATERMARK_REG                    (0x00000064U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_START_REG                        (0x00000068U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_NUM_BYTES_REG                    (0x0000006CU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG                        (0x00000070U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_WATERMARK_REG                   (0x00000074U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_START_REG                       (0x00000078U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_NUM_BYTES_REG                   (0x0000007CU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_TRIGGER_ADDR_RANGE_REG                     (0x00000080U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG                          (0x0000008CU)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG                                  (0x00000090U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_ADDR_REG                                  (0x00000094U)
#define CSL_OSPI_FLASH_CFG_FLASH_RD_DATA_LOWER_REG                             (0x000000A0U)
#define CSL_OSPI_FLASH_CFG_FLASH_RD_DATA_UPPER_REG                             (0x000000A4U)
#define CSL_OSPI_FLASH_CFG_FLASH_WR_DATA_LOWER_REG                             (0x000000A8U)
#define CSL_OSPI_FLASH_CFG_FLASH_WR_DATA_UPPER_REG                             (0x000000ACU)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG                            (0x000000B0U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG                               (0x000000B4U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG                              (0x000000B8U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG                            (0x000000BCU)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG                            (0x000000C0U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG                                (0x000000E0U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG                                (0x000000E4U)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG                                       (0x000000FCU)

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* CONFIG_REG */

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_SPI_FLD_MASK                         (0x00000001U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_SPI_FLD_SHIFT                        (0x00000000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_SPI_FLD_MAX                          (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_SEL_CLK_POL_FLD_MASK                     (0x00000002U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_SEL_CLK_POL_FLD_SHIFT                    (0x00000001U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_SEL_CLK_POL_FLD_MAX                      (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_SEL_CLK_PHASE_FLD_MASK                   (0x00000004U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_SEL_CLK_PHASE_FLD_SHIFT                  (0x00000002U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_SEL_CLK_PHASE_FLD_MAX                    (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PHY_MODE_ENABLE_FLD_MASK                 (0x00000008U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PHY_MODE_ENABLE_FLD_SHIFT                (0x00000003U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PHY_MODE_ENABLE_FLD_MAX                  (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_HOLD_PIN_FLD_MASK                        (0x00000010U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_HOLD_PIN_FLD_SHIFT                       (0x00000004U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_HOLD_PIN_FLD_MAX                         (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_RESET_PIN_FLD_MASK                       (0x00000020U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_RESET_PIN_FLD_SHIFT                      (0x00000005U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_RESET_PIN_FLD_MAX                        (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_RESET_CFG_FLD_MASK                       (0x00000040U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_RESET_CFG_FLD_SHIFT                      (0x00000006U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_RESET_CFG_FLD_MAX                        (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_DIR_ACC_CTLR_FLD_MASK                (0x00000080U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_DIR_ACC_CTLR_FLD_SHIFT               (0x00000007U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_DIR_ACC_CTLR_FLD_MAX                 (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_LEGACY_IP_MODE_FLD_MASK              (0x00000100U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_LEGACY_IP_MODE_FLD_SHIFT             (0x00000008U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_LEGACY_IP_MODE_FLD_MAX               (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PERIPH_SEL_DEC_FLD_MASK                  (0x00000200U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PERIPH_SEL_DEC_FLD_SHIFT                 (0x00000009U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PERIPH_SEL_DEC_FLD_MAX                   (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PERIPH_CS_LINES_FLD_MASK                 (0x00003C00U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PERIPH_CS_LINES_FLD_SHIFT                (0x0000000AU)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PERIPH_CS_LINES_FLD_MAX                  (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_WR_PROT_FLASH_FLD_MASK                   (0x00004000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_WR_PROT_FLASH_FLD_SHIFT                  (0x0000000EU)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_WR_PROT_FLASH_FLD_MAX                    (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_DMA_IF_FLD_MASK                      (0x00008000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_DMA_IF_FLD_SHIFT                     (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_DMA_IF_FLD_MAX                       (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_AHB_ADDR_REMAP_FLD_MASK              (0x00010000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_AHB_ADDR_REMAP_FLD_SHIFT             (0x00000010U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENB_AHB_ADDR_REMAP_FLD_MAX               (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENTER_XIP_MODE_FLD_MASK                  (0x00020000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENTER_XIP_MODE_FLD_SHIFT                 (0x00000011U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENTER_XIP_MODE_FLD_MAX                   (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENTER_XIP_MODE_IMM_FLD_MASK              (0x00040000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENTER_XIP_MODE_IMM_FLD_SHIFT             (0x00000012U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENTER_XIP_MODE_IMM_FLD_MAX               (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_MSTR_BAUD_DIV_FLD_MASK                   (0x00780000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_MSTR_BAUD_DIV_FLD_SHIFT                  (0x00000013U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_MSTR_BAUD_DIV_FLD_MAX                    (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENABLE_AHB_DECODER_FLD_MASK              (0x00800000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENABLE_AHB_DECODER_FLD_SHIFT             (0x00000017U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENABLE_AHB_DECODER_FLD_MAX               (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENABLE_DTR_PROTOCOL_FLD_MASK             (0x01000000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENABLE_DTR_PROTOCOL_FLD_SHIFT            (0x00000018U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_ENABLE_DTR_PROTOCOL_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PIPELINE_PHY_FLD_MASK                    (0x02000000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PIPELINE_PHY_FLD_SHIFT                   (0x00000019U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_PIPELINE_PHY_FLD_MAX                     (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_CONFIG_RESV2_FLD_MASK                    (0x1C000000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_CONFIG_RESV2_FLD_SHIFT                   (0x0000001AU)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_CONFIG_RESV2_FLD_MAX                     (0x00000007U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_CRC_ENABLE_FLD_MASK                      (0x20000000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_CRC_ENABLE_FLD_SHIFT                     (0x0000001DU)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_CRC_ENABLE_FLD_MAX                       (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_DUAL_BYTE_OPCODE_EN_FLD_MASK             (0x40000000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_DUAL_BYTE_OPCODE_EN_FLD_SHIFT            (0x0000001EU)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_DUAL_BYTE_OPCODE_EN_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_CONFIG_REG_IDLE_FLD_MASK                            (0x80000000U)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_IDLE_FLD_SHIFT                           (0x0000001FU)
#define CSL_OSPI_FLASH_CFG_CONFIG_REG_IDLE_FLD_MAX                             (0x00000001U)

/* DEV_INSTR_RD_CONFIG_REG */

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_OPCODE_NON_XIP_FLD_MASK  (0x000000FFU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_OPCODE_NON_XIP_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_OPCODE_NON_XIP_FLD_MAX   (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_INSTR_TYPE_FLD_MASK         (0x00000300U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_INSTR_TYPE_FLD_SHIFT        (0x00000008U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_INSTR_TYPE_FLD_MAX          (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DDR_EN_FLD_MASK             (0x00000400U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DDR_EN_FLD_SHIFT            (0x0000000AU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DDR_EN_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV1_FLD_MASK     (0x00000800U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV1_FLD_SHIFT    (0x0000000BU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV1_FLD_MAX      (0x00000001U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_ADDR_XFER_TYPE_STD_MODE_FLD_MASK (0x00003000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_ADDR_XFER_TYPE_STD_MODE_FLD_SHIFT (0x0000000CU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_ADDR_XFER_TYPE_STD_MODE_FLD_MAX (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV2_FLD_MASK     (0x0000C000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV2_FLD_SHIFT    (0x0000000EU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV2_FLD_MAX      (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DATA_XFER_TYPE_EXT_MODE_FLD_MASK (0x00030000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DATA_XFER_TYPE_EXT_MODE_FLD_SHIFT (0x00000010U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DATA_XFER_TYPE_EXT_MODE_FLD_MAX (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV3_FLD_MASK     (0x000C0000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV3_FLD_SHIFT    (0x00000012U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV3_FLD_MAX      (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_MODE_BIT_ENABLE_FLD_MASK    (0x00100000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_MODE_BIT_ENABLE_FLD_SHIFT   (0x00000014U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_MODE_BIT_ENABLE_FLD_MAX     (0x00000001U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV4_FLD_MASK     (0x00E00000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV4_FLD_SHIFT    (0x00000015U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV4_FLD_MAX      (0x00000007U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DUMMY_RD_CLK_CYCLES_FLD_MASK (0x1F000000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DUMMY_RD_CLK_CYCLES_FLD_SHIFT (0x00000018U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_DUMMY_RD_CLK_CYCLES_FLD_MAX (0x0000001FU)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV5_FLD_MASK     (0xE0000000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV5_FLD_SHIFT    (0x0000001DU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_RD_CONFIG_REG_RD_INSTR_RESV5_FLD_MAX      (0x00000007U)

/* DEV_INSTR_WR_CONFIG_REG */

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_OPCODE_FLD_MASK          (0x000000FFU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_OPCODE_FLD_SHIFT         (0x00000000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_OPCODE_FLD_MAX           (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WEL_DIS_FLD_MASK            (0x00000100U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WEL_DIS_FLD_SHIFT           (0x00000008U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WEL_DIS_FLD_MAX             (0x00000001U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV1_FLD_MASK     (0x00000E00U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV1_FLD_SHIFT    (0x00000009U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV1_FLD_MAX      (0x00000007U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_ADDR_XFER_TYPE_STD_MODE_FLD_MASK (0x00003000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_ADDR_XFER_TYPE_STD_MODE_FLD_SHIFT (0x0000000CU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_ADDR_XFER_TYPE_STD_MODE_FLD_MAX (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV2_FLD_MASK     (0x0000C000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV2_FLD_SHIFT    (0x0000000EU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV2_FLD_MAX      (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_DATA_XFER_TYPE_EXT_MODE_FLD_MASK (0x00030000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_DATA_XFER_TYPE_EXT_MODE_FLD_SHIFT (0x00000010U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_DATA_XFER_TYPE_EXT_MODE_FLD_MAX (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV3_FLD_MASK     (0x00FC0000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV3_FLD_SHIFT    (0x00000012U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV3_FLD_MAX      (0x0000003FU)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_DUMMY_WR_CLK_CYCLES_FLD_MASK (0x1F000000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_DUMMY_WR_CLK_CYCLES_FLD_SHIFT (0x00000018U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_DUMMY_WR_CLK_CYCLES_FLD_MAX (0x0000001FU)

#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV4_FLD_MASK     (0xE0000000U)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV4_FLD_SHIFT    (0x0000001DU)
#define CSL_OSPI_FLASH_CFG_DEV_INSTR_WR_CONFIG_REG_WR_INSTR_RESV4_FLD_MAX      (0x00000007U)

/* DEV_DELAY_REG */

#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_INIT_FLD_MASK                       (0x000000FFU)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_INIT_FLD_SHIFT                      (0x00000000U)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_INIT_FLD_MAX                        (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_AFTER_FLD_MASK                      (0x0000FF00U)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_AFTER_FLD_SHIFT                     (0x00000008U)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_AFTER_FLD_MAX                       (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_BTWN_FLD_MASK                       (0x00FF0000U)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_BTWN_FLD_SHIFT                      (0x00000010U)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_BTWN_FLD_MAX                        (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_NSS_FLD_MASK                        (0xFF000000U)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_NSS_FLD_SHIFT                       (0x00000018U)
#define CSL_OSPI_FLASH_CFG_DEV_DELAY_REG_D_NSS_FLD_MAX                         (0x000000FFU)

/* RD_DATA_CAPTURE_REG */

#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_BYPASS_FLD_MASK                 (0x00000001U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_BYPASS_FLD_SHIFT                (0x00000000U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_BYPASS_FLD_MAX                  (0x00000001U)

#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DELAY_FLD_MASK                  (0x0000001EU)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DELAY_FLD_SHIFT                 (0x00000001U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DELAY_FLD_MAX                   (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_SAMPLE_EDGE_SEL_FLD_MASK        (0x00000020U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_SAMPLE_EDGE_SEL_FLD_SHIFT       (0x00000005U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_SAMPLE_EDGE_SEL_FLD_MAX         (0x00000001U)

#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV1_FLD_MASK          (0x000000C0U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV1_FLD_SHIFT         (0x00000006U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV1_FLD_MAX           (0x00000003U)

#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DQS_ENABLE_FLD_MASK             (0x00000100U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DQS_ENABLE_FLD_SHIFT            (0x00000008U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DQS_ENABLE_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV2_FLD_MASK          (0x0000FE00U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV2_FLD_SHIFT         (0x00000009U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV2_FLD_MAX           (0x0000007FU)

#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DDR_READ_DELAY_FLD_MASK         (0x000F0000U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DDR_READ_DELAY_FLD_SHIFT        (0x00000010U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_DDR_READ_DELAY_FLD_MAX          (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV3_FLD_MASK          (0xFFF00000U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV3_FLD_SHIFT         (0x00000014U)
#define CSL_OSPI_FLASH_CFG_RD_DATA_CAPTURE_REG_RD_DATA_RESV3_FLD_MAX           (0x00000FFFU)

/* DEV_SIZE_CONFIG_REG */

#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_NUM_ADDR_BYTES_FLD_MASK         (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_NUM_ADDR_BYTES_FLD_SHIFT        (0x00000000U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_NUM_ADDR_BYTES_FLD_MAX          (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_BYTES_PER_DEVICE_PAGE_FLD_MASK  (0x0000FFF0U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_BYTES_PER_DEVICE_PAGE_FLD_SHIFT (0x00000004U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_BYTES_PER_DEVICE_PAGE_FLD_MAX   (0x00000FFFU)

#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_BYTES_PER_SUBSECTOR_FLD_MASK    (0x001F0000U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_BYTES_PER_SUBSECTOR_FLD_SHIFT   (0x00000010U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_BYTES_PER_SUBSECTOR_FLD_MAX     (0x0000001FU)

#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS0_FLD_MASK        (0x00600000U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS0_FLD_SHIFT       (0x00000015U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS0_FLD_MAX         (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS1_FLD_MASK        (0x01800000U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS1_FLD_SHIFT       (0x00000017U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS1_FLD_MAX         (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS2_FLD_MASK        (0x06000000U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS2_FLD_SHIFT       (0x00000019U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS2_FLD_MAX         (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS3_FLD_MASK        (0x18000000U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS3_FLD_SHIFT       (0x0000001BU)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_MEM_SIZE_ON_CS3_FLD_MAX         (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_DEV_SIZE_RESV_FLD_MASK          (0xE0000000U)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_DEV_SIZE_RESV_FLD_SHIFT         (0x0000001DU)
#define CSL_OSPI_FLASH_CFG_DEV_SIZE_CONFIG_REG_DEV_SIZE_RESV_FLD_MAX           (0x00000007U)

/* SRAM_PARTITION_CFG_REG */

#define CSL_OSPI_FLASH_CFG_SRAM_PARTITION_CFG_REG_ADDR_FLD_MASK                (0x000000FFU)
#define CSL_OSPI_FLASH_CFG_SRAM_PARTITION_CFG_REG_ADDR_FLD_SHIFT               (0x00000000U)
#define CSL_OSPI_FLASH_CFG_SRAM_PARTITION_CFG_REG_ADDR_FLD_MAX                 (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_SRAM_PARTITION_CFG_REG_SRAM_PARTITION_RESV_FLD_MASK (0xFFFFFF00U)
#define CSL_OSPI_FLASH_CFG_SRAM_PARTITION_CFG_REG_SRAM_PARTITION_RESV_FLD_SHIFT (0x00000008U)
#define CSL_OSPI_FLASH_CFG_SRAM_PARTITION_CFG_REG_SRAM_PARTITION_RESV_FLD_MAX  (0x00FFFFFFU)

/* IND_AHB_ADDR_TRIGGER_REG */

#define CSL_OSPI_FLASH_CFG_IND_AHB_ADDR_TRIGGER_REG_ADDR_FLD_MASK              (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_IND_AHB_ADDR_TRIGGER_REG_ADDR_FLD_SHIFT             (0x00000000U)
#define CSL_OSPI_FLASH_CFG_IND_AHB_ADDR_TRIGGER_REG_ADDR_FLD_MAX               (0xFFFFFFFFU)

/* DMA_PERIPH_CONFIG_REG */

#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_NUM_SINGLE_REQ_BYTES_FLD_MASK (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_NUM_SINGLE_REQ_BYTES_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_NUM_SINGLE_REQ_BYTES_FLD_MAX  (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_DMA_PERIPH_RESV1_FLD_MASK     (0x000000F0U)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_DMA_PERIPH_RESV1_FLD_SHIFT    (0x00000004U)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_DMA_PERIPH_RESV1_FLD_MAX      (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_NUM_BURST_REQ_BYTES_FLD_MASK  (0x00000F00U)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_NUM_BURST_REQ_BYTES_FLD_SHIFT (0x00000008U)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_NUM_BURST_REQ_BYTES_FLD_MAX   (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_DMA_PERIPH_RESV2_FLD_MASK     (0xFFFFF000U)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_DMA_PERIPH_RESV2_FLD_SHIFT    (0x0000000CU)
#define CSL_OSPI_FLASH_CFG_DMA_PERIPH_CONFIG_REG_DMA_PERIPH_RESV2_FLD_MAX      (0x000FFFFFU)

/* REMAP_ADDR_REG */

#define CSL_OSPI_FLASH_CFG_REMAP_ADDR_REG_VALUE_FLD_MASK                       (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_REMAP_ADDR_REG_VALUE_FLD_SHIFT                      (0x00000000U)
#define CSL_OSPI_FLASH_CFG_REMAP_ADDR_REG_VALUE_FLD_MAX                        (0xFFFFFFFFU)

/* MODE_BIT_CONFIG_REG */

#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_MODE_FLD_MASK                   (0x000000FFU)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_MODE_FLD_SHIFT                  (0x00000000U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_MODE_FLD_MAX                    (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_CHUNK_SIZE_FLD_MASK             (0x00000700U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_CHUNK_SIZE_FLD_SHIFT            (0x00000008U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_CHUNK_SIZE_FLD_MAX              (0x00000007U)

#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_MODE_BIT_RESV1_FLD_MASK         (0x00007800U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_MODE_BIT_RESV1_FLD_SHIFT        (0x0000000BU)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_MODE_BIT_RESV1_FLD_MAX          (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_CRC_OUT_ENABLE_FLD_MASK         (0x00008000U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_CRC_OUT_ENABLE_FLD_SHIFT        (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_CRC_OUT_ENABLE_FLD_MAX          (0x00000001U)

#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_RX_CRC_DATA_UP_FLD_MASK         (0x00FF0000U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_RX_CRC_DATA_UP_FLD_SHIFT        (0x00000010U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_RX_CRC_DATA_UP_FLD_MAX          (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_RX_CRC_DATA_LOW_FLD_MASK        (0xFF000000U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_RX_CRC_DATA_LOW_FLD_SHIFT       (0x00000018U)
#define CSL_OSPI_FLASH_CFG_MODE_BIT_CONFIG_REG_RX_CRC_DATA_LOW_FLD_MAX         (0x000000FFU)

/* SRAM_FILL_REG */

#define CSL_OSPI_FLASH_CFG_SRAM_FILL_REG_SRAM_FILL_INDAC_READ_FLD_MASK         (0x0000FFFFU)
#define CSL_OSPI_FLASH_CFG_SRAM_FILL_REG_SRAM_FILL_INDAC_READ_FLD_SHIFT        (0x00000000U)
#define CSL_OSPI_FLASH_CFG_SRAM_FILL_REG_SRAM_FILL_INDAC_READ_FLD_MAX          (0x0000FFFFU)

#define CSL_OSPI_FLASH_CFG_SRAM_FILL_REG_SRAM_FILL_INDAC_WRITE_FLD_MASK        (0xFFFF0000U)
#define CSL_OSPI_FLASH_CFG_SRAM_FILL_REG_SRAM_FILL_INDAC_WRITE_FLD_SHIFT       (0x00000010U)
#define CSL_OSPI_FLASH_CFG_SRAM_FILL_REG_SRAM_FILL_INDAC_WRITE_FLD_MAX         (0x0000FFFFU)

/* TX_THRESH_REG */

#define CSL_OSPI_FLASH_CFG_TX_THRESH_REG_LEVEL_FLD_MASK                        (0x0000001FU)
#define CSL_OSPI_FLASH_CFG_TX_THRESH_REG_LEVEL_FLD_SHIFT                       (0x00000000U)
#define CSL_OSPI_FLASH_CFG_TX_THRESH_REG_LEVEL_FLD_MAX                         (0x0000001FU)

#define CSL_OSPI_FLASH_CFG_TX_THRESH_REG_TX_THRESH_RESV_FLD_MASK               (0xFFFFFFE0U)
#define CSL_OSPI_FLASH_CFG_TX_THRESH_REG_TX_THRESH_RESV_FLD_SHIFT              (0x00000005U)
#define CSL_OSPI_FLASH_CFG_TX_THRESH_REG_TX_THRESH_RESV_FLD_MAX                (0x07FFFFFFU)

/* RX_THRESH_REG */

#define CSL_OSPI_FLASH_CFG_RX_THRESH_REG_LEVEL_FLD_MASK                        (0x0000001FU)
#define CSL_OSPI_FLASH_CFG_RX_THRESH_REG_LEVEL_FLD_SHIFT                       (0x00000000U)
#define CSL_OSPI_FLASH_CFG_RX_THRESH_REG_LEVEL_FLD_MAX                         (0x0000001FU)

#define CSL_OSPI_FLASH_CFG_RX_THRESH_REG_RX_THRESH_RESV_FLD_MASK               (0xFFFFFFE0U)
#define CSL_OSPI_FLASH_CFG_RX_THRESH_REG_RX_THRESH_RESV_FLD_SHIFT              (0x00000005U)
#define CSL_OSPI_FLASH_CFG_RX_THRESH_REG_RX_THRESH_RESV_FLD_MAX                (0x07FFFFFFU)

/* WRITE_COMPLETION_CTRL_REG */

#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_OPCODE_FLD_MASK           (0x000000FFU)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_OPCODE_FLD_SHIFT          (0x00000000U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_OPCODE_FLD_MAX            (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLLING_BIT_INDEX_FLD_MASK (0x00000700U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLLING_BIT_INDEX_FLD_SHIFT (0x00000008U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLLING_BIT_INDEX_FLD_MAX (0x00000007U)

#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_WR_COMP_CTRL_RESV1_FLD_MASK (0x00001800U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_WR_COMP_CTRL_RESV1_FLD_SHIFT (0x0000000BU)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_WR_COMP_CTRL_RESV1_FLD_MAX (0x00000003U)

#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLLING_POLARITY_FLD_MASK (0x00002000U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLLING_POLARITY_FLD_SHIFT (0x0000000DU)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLLING_POLARITY_FLD_MAX  (0x00000001U)

#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_DISABLE_POLLING_FLD_MASK  (0x00004000U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_DISABLE_POLLING_FLD_SHIFT (0x0000000EU)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_DISABLE_POLLING_FLD_MAX   (0x00000001U)

#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_ENABLE_POLLING_EXP_FLD_MASK (0x00008000U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_ENABLE_POLLING_EXP_FLD_SHIFT (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_ENABLE_POLLING_EXP_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLL_COUNT_FLD_MASK       (0x00FF0000U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLL_COUNT_FLD_SHIFT      (0x00000010U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLL_COUNT_FLD_MAX        (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLL_REP_DELAY_FLD_MASK   (0xFF000000U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLL_REP_DELAY_FLD_SHIFT  (0x00000018U)
#define CSL_OSPI_FLASH_CFG_WRITE_COMPLETION_CTRL_REG_POLL_REP_DELAY_FLD_MAX    (0x000000FFU)

/* NO_OF_POLLS_BEF_EXP_REG */

#define CSL_OSPI_FLASH_CFG_NO_OF_POLLS_BEF_EXP_REG_NO_OF_POLLS_BEF_EXP_FLD_MASK (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_NO_OF_POLLS_BEF_EXP_REG_NO_OF_POLLS_BEF_EXP_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_NO_OF_POLLS_BEF_EXP_REG_NO_OF_POLLS_BEF_EXP_FLD_MAX (0xFFFFFFFFU)

/* IRQ_STATUS_REG */

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_MODE_M_FAIL_FLD_MASK                 (0x00000001U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_MODE_M_FAIL_FLD_SHIFT                (0x00000000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_MODE_M_FAIL_FLD_MAX                  (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_UNDERFLOW_DET_FLD_MASK               (0x00000002U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_UNDERFLOW_DET_FLD_SHIFT              (0x00000001U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_UNDERFLOW_DET_FLD_MAX                (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_OP_DONE_FLD_MASK            (0x00000004U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_OP_DONE_FLD_SHIFT           (0x00000002U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_OP_DONE_FLD_MAX             (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_READ_REJECT_FLD_MASK        (0x00000008U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_READ_REJECT_FLD_SHIFT       (0x00000003U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_READ_REJECT_FLD_MAX         (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_PROT_WR_ATTEMPT_FLD_MASK             (0x00000010U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_PROT_WR_ATTEMPT_FLD_SHIFT            (0x00000004U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_PROT_WR_ATTEMPT_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_ILLEGAL_ACCESS_DET_FLD_MASK          (0x00000020U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_ILLEGAL_ACCESS_DET_FLD_SHIFT         (0x00000005U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_ILLEGAL_ACCESS_DET_FLD_MAX           (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_XFER_LEVEL_BREACH_FLD_MASK  (0x00000040U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_XFER_LEVEL_BREACH_FLD_SHIFT (0x00000006U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDIRECT_XFER_LEVEL_BREACH_FLD_MAX   (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RECV_OVERFLOW_FLD_MASK               (0x00000080U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RECV_OVERFLOW_FLD_SHIFT              (0x00000007U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RECV_OVERFLOW_FLD_MAX                (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_FIFO_NOT_FULL_FLD_MASK            (0x00000100U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_FIFO_NOT_FULL_FLD_SHIFT           (0x00000008U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_FIFO_NOT_FULL_FLD_MAX             (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_FIFO_FULL_FLD_MASK                (0x00000200U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_FIFO_FULL_FLD_SHIFT               (0x00000009U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_FIFO_FULL_FLD_MAX                 (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_FIFO_NOT_EMPTY_FLD_MASK           (0x00000400U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_FIFO_NOT_EMPTY_FLD_SHIFT          (0x0000000AU)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_FIFO_NOT_EMPTY_FLD_MAX            (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_FIFO_FULL_FLD_MASK                (0x00000800U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_FIFO_FULL_FLD_SHIFT               (0x0000000BU)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_FIFO_FULL_FLD_MAX                 (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDRD_SRAM_FULL_FLD_MASK             (0x00001000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDRD_SRAM_FULL_FLD_SHIFT            (0x0000000CU)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_INDRD_SRAM_FULL_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_POLL_EXP_INT_FLD_MASK                (0x00002000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_POLL_EXP_INT_FLD_SHIFT               (0x0000000DU)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_POLL_EXP_INT_FLD_MAX                 (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_STIG_REQ_INT_FLD_MASK                (0x00004000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_STIG_REQ_INT_FLD_SHIFT               (0x0000000EU)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_STIG_REQ_INT_FLD_MAX                 (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_IRQ_STAT_RESV1_FLD_MASK              (0x00008000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_IRQ_STAT_RESV1_FLD_SHIFT             (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_IRQ_STAT_RESV1_FLD_MAX               (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_CRC_DATA_ERR_FLD_MASK             (0x00010000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_CRC_DATA_ERR_FLD_SHIFT            (0x00000010U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_CRC_DATA_ERR_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_CRC_DATA_VAL_FLD_MASK             (0x00020000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_CRC_DATA_VAL_FLD_SHIFT            (0x00000011U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_RX_CRC_DATA_VAL_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_CRC_CHUNK_BRK_FLD_MASK            (0x00040000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_CRC_CHUNK_BRK_FLD_SHIFT           (0x00000012U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_TX_CRC_CHUNK_BRK_FLD_MAX             (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_ECC_FAIL_FLD_MASK                    (0x00080000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_ECC_FAIL_FLD_SHIFT                   (0x00000013U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_ECC_FAIL_FLD_MAX                     (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_IRQ_STAT_RESV_FLD_MASK               (0xFFF00000U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_IRQ_STAT_RESV_FLD_SHIFT              (0x00000014U)
#define CSL_OSPI_FLASH_CFG_IRQ_STATUS_REG_IRQ_STAT_RESV_FLD_MAX                (0x00000FFFU)

/* IRQ_MASK_REG */

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_MODE_M_FAIL_MASK_FLD_MASK              (0x00000001U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_MODE_M_FAIL_MASK_FLD_SHIFT             (0x00000000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_MODE_M_FAIL_MASK_FLD_MAX               (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_UNDERFLOW_DET_MASK_FLD_MASK            (0x00000002U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_UNDERFLOW_DET_MASK_FLD_SHIFT           (0x00000001U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_UNDERFLOW_DET_MASK_FLD_MAX             (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_OP_DONE_MASK_FLD_MASK         (0x00000004U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_OP_DONE_MASK_FLD_SHIFT        (0x00000002U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_OP_DONE_MASK_FLD_MAX          (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_READ_REJECT_MASK_FLD_MASK     (0x00000008U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_READ_REJECT_MASK_FLD_SHIFT    (0x00000003U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_READ_REJECT_MASK_FLD_MAX      (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_PROT_WR_ATTEMPT_MASK_FLD_MASK          (0x00000010U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_PROT_WR_ATTEMPT_MASK_FLD_SHIFT         (0x00000004U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_PROT_WR_ATTEMPT_MASK_FLD_MAX           (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_ILLEGAL_ACCESS_DET_MASK_FLD_MASK       (0x00000020U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_ILLEGAL_ACCESS_DET_MASK_FLD_SHIFT      (0x00000005U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_ILLEGAL_ACCESS_DET_MASK_FLD_MAX        (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_XFER_LEVEL_BREACH_MASK_FLD_MASK (0x00000040U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_XFER_LEVEL_BREACH_MASK_FLD_SHIFT (0x00000006U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDIRECT_XFER_LEVEL_BREACH_MASK_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RECV_OVERFLOW_MASK_FLD_MASK            (0x00000080U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RECV_OVERFLOW_MASK_FLD_SHIFT           (0x00000007U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RECV_OVERFLOW_MASK_FLD_MAX             (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_FIFO_NOT_FULL_MASK_FLD_MASK         (0x00000100U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_FIFO_NOT_FULL_MASK_FLD_SHIFT        (0x00000008U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_FIFO_NOT_FULL_MASK_FLD_MAX          (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_FIFO_FULL_MASK_FLD_MASK             (0x00000200U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_FIFO_FULL_MASK_FLD_SHIFT            (0x00000009U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_FIFO_FULL_MASK_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_FIFO_NOT_EMPTY_MASK_FLD_MASK        (0x00000400U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_FIFO_NOT_EMPTY_MASK_FLD_SHIFT       (0x0000000AU)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_FIFO_NOT_EMPTY_MASK_FLD_MAX         (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_FIFO_FULL_MASK_FLD_MASK             (0x00000800U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_FIFO_FULL_MASK_FLD_SHIFT            (0x0000000BU)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_FIFO_FULL_MASK_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDRD_SRAM_FULL_MASK_FLD_MASK          (0x00001000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDRD_SRAM_FULL_MASK_FLD_SHIFT         (0x0000000CU)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_INDRD_SRAM_FULL_MASK_FLD_MAX           (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_POLL_EXP_INT_MASK_FLD_MASK             (0x00002000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_POLL_EXP_INT_MASK_FLD_SHIFT            (0x0000000DU)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_POLL_EXP_INT_MASK_FLD_MAX              (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_STIG_REQ_MASK_FLD_MASK                 (0x00004000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_STIG_REQ_MASK_FLD_SHIFT                (0x0000000EU)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_STIG_REQ_MASK_FLD_MAX                  (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_IRQ_MASK_RESV1_FLD_MASK                (0x00008000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_IRQ_MASK_RESV1_FLD_SHIFT               (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_IRQ_MASK_RESV1_FLD_MAX                 (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_CRC_DATA_ERR_MASK_FLD_MASK          (0x00010000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_CRC_DATA_ERR_MASK_FLD_SHIFT         (0x00000010U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_CRC_DATA_ERR_MASK_FLD_MAX           (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_CRC_DATA_VAL_MASK_FLD_MASK          (0x00020000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_CRC_DATA_VAL_MASK_FLD_SHIFT         (0x00000011U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_RX_CRC_DATA_VAL_MASK_FLD_MAX           (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_CRC_CHUNK_BRK_MASK_FLD_MASK         (0x00040000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_CRC_CHUNK_BRK_MASK_FLD_SHIFT        (0x00000012U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_TX_CRC_CHUNK_BRK_MASK_FLD_MAX          (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_ECC_FAIL_MASK_FLD_MASK                 (0x00080000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_ECC_FAIL_MASK_FLD_SHIFT                (0x00000013U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_ECC_FAIL_MASK_FLD_MAX                  (0x00000001U)

#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_IRQ_MASK_RESV_FLD_MASK                 (0xFFF00000U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_IRQ_MASK_RESV_FLD_SHIFT                (0x00000014U)
#define CSL_OSPI_FLASH_CFG_IRQ_MASK_REG_IRQ_MASK_RESV_FLD_MAX                  (0x00000FFFU)

/* LOWER_WR_PROT_REG */

#define CSL_OSPI_FLASH_CFG_LOWER_WR_PROT_REG_SUBSECTOR_FLD_MASK                (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_LOWER_WR_PROT_REG_SUBSECTOR_FLD_SHIFT               (0x00000000U)
#define CSL_OSPI_FLASH_CFG_LOWER_WR_PROT_REG_SUBSECTOR_FLD_MAX                 (0xFFFFFFFFU)

/* UPPER_WR_PROT_REG */

#define CSL_OSPI_FLASH_CFG_UPPER_WR_PROT_REG_SUBSECTOR_FLD_MASK                (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_UPPER_WR_PROT_REG_SUBSECTOR_FLD_SHIFT               (0x00000000U)
#define CSL_OSPI_FLASH_CFG_UPPER_WR_PROT_REG_SUBSECTOR_FLD_MAX                 (0xFFFFFFFFU)

/* WR_PROT_CTRL_REG */

#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_INV_FLD_MASK                       (0x00000001U)
#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_INV_FLD_SHIFT                      (0x00000000U)
#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_INV_FLD_MAX                        (0x00000001U)

#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_ENB_FLD_MASK                       (0x00000002U)
#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_ENB_FLD_SHIFT                      (0x00000001U)
#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_ENB_FLD_MAX                        (0x00000001U)

#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_WR_PROT_CTRL_RESV_FLD_MASK         (0xFFFFFFFCU)
#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_WR_PROT_CTRL_RESV_FLD_SHIFT        (0x00000002U)
#define CSL_OSPI_FLASH_CFG_WR_PROT_CTRL_REG_WR_PROT_CTRL_RESV_FLD_MAX          (0x3FFFFFFFU)

/* INDIRECT_READ_XFER_CTRL_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_START_FLD_MASK          (0x00000001U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_START_FLD_SHIFT         (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_START_FLD_MAX           (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_CANCEL_FLD_MASK         (0x00000002U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_CANCEL_FLD_SHIFT        (0x00000001U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_CANCEL_FLD_MAX          (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_RD_STATUS_FLD_MASK      (0x00000004U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_RD_STATUS_FLD_SHIFT     (0x00000002U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_RD_STATUS_FLD_MAX       (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_SRAM_FULL_FLD_MASK      (0x00000008U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_SRAM_FULL_FLD_SHIFT     (0x00000003U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_SRAM_FULL_FLD_MAX       (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_RD_QUEUED_FLD_MASK      (0x00000010U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_RD_QUEUED_FLD_SHIFT     (0x00000004U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_RD_QUEUED_FLD_MAX       (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_IND_OPS_DONE_STATUS_FLD_MASK (0x00000020U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_IND_OPS_DONE_STATUS_FLD_SHIFT (0x00000005U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_IND_OPS_DONE_STATUS_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_NUM_IND_OPS_DONE_FLD_MASK (0x000000C0U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_NUM_IND_OPS_DONE_FLD_SHIFT (0x00000006U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_NUM_IND_OPS_DONE_FLD_MAX (0x00000003U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_INDIR_RD_XFER_RESV_FLD_MASK (0xFFFFFF00U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_INDIR_RD_XFER_RESV_FLD_SHIFT (0x00000008U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_CTRL_REG_INDIR_RD_XFER_RESV_FLD_MAX (0x00FFFFFFU)

/* INDIRECT_READ_XFER_WATERMARK_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_WATERMARK_REG_LEVEL_FLD_MASK     (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_WATERMARK_REG_LEVEL_FLD_SHIFT    (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_WATERMARK_REG_LEVEL_FLD_MAX      (0xFFFFFFFFU)

/* INDIRECT_READ_XFER_START_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_START_REG_ADDR_FLD_MASK          (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_START_REG_ADDR_FLD_SHIFT         (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_START_REG_ADDR_FLD_MAX           (0xFFFFFFFFU)

/* INDIRECT_READ_XFER_NUM_BYTES_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_NUM_BYTES_REG_VALUE_FLD_MASK     (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_NUM_BYTES_REG_VALUE_FLD_SHIFT    (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_READ_XFER_NUM_BYTES_REG_VALUE_FLD_MAX      (0xFFFFFFFFU)

/* INDIRECT_WRITE_XFER_CTRL_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_START_FLD_MASK         (0x00000001U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_START_FLD_SHIFT        (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_START_FLD_MAX          (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_CANCEL_FLD_MASK        (0x00000002U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_CANCEL_FLD_SHIFT       (0x00000001U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_CANCEL_FLD_MAX         (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_WR_STATUS_FLD_MASK     (0x00000004U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_WR_STATUS_FLD_SHIFT    (0x00000002U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_WR_STATUS_FLD_MAX      (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_INDIR_WR_XFER_RESV1_FLD_MASK (0x00000008U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_INDIR_WR_XFER_RESV1_FLD_SHIFT (0x00000003U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_INDIR_WR_XFER_RESV1_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_WR_QUEUED_FLD_MASK     (0x00000010U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_WR_QUEUED_FLD_SHIFT    (0x00000004U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_WR_QUEUED_FLD_MAX      (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_IND_OPS_DONE_STATUS_FLD_MASK (0x00000020U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_IND_OPS_DONE_STATUS_FLD_SHIFT (0x00000005U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_IND_OPS_DONE_STATUS_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_NUM_IND_OPS_DONE_FLD_MASK (0x000000C0U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_NUM_IND_OPS_DONE_FLD_SHIFT (0x00000006U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_NUM_IND_OPS_DONE_FLD_MAX (0x00000003U)

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_INDIR_WR_XFER_RESV2_FLD_MASK (0xFFFFFF00U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_INDIR_WR_XFER_RESV2_FLD_SHIFT (0x00000008U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_CTRL_REG_INDIR_WR_XFER_RESV2_FLD_MAX (0x00FFFFFFU)

/* INDIRECT_WRITE_XFER_WATERMARK_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_WATERMARK_REG_LEVEL_FLD_MASK    (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_WATERMARK_REG_LEVEL_FLD_SHIFT   (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_WATERMARK_REG_LEVEL_FLD_MAX     (0xFFFFFFFFU)

/* INDIRECT_WRITE_XFER_START_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_START_REG_ADDR_FLD_MASK         (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_START_REG_ADDR_FLD_SHIFT        (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_START_REG_ADDR_FLD_MAX          (0xFFFFFFFFU)

/* INDIRECT_WRITE_XFER_NUM_BYTES_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_NUM_BYTES_REG_VALUE_FLD_MASK    (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_NUM_BYTES_REG_VALUE_FLD_SHIFT   (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_WRITE_XFER_NUM_BYTES_REG_VALUE_FLD_MAX     (0xFFFFFFFFU)

/* INDIRECT_TRIGGER_ADDR_RANGE_REG */

#define CSL_OSPI_FLASH_CFG_INDIRECT_TRIGGER_ADDR_RANGE_REG_IND_RANGE_WIDTH_FLD_MASK (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_INDIRECT_TRIGGER_ADDR_RANGE_REG_IND_RANGE_WIDTH_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_TRIGGER_ADDR_RANGE_REG_IND_RANGE_WIDTH_FLD_MAX (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_INDIRECT_TRIGGER_ADDR_RANGE_REG_IND_RANGE_RESV1_FLD_MASK (0xFFFFFFF0U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_TRIGGER_ADDR_RANGE_REG_IND_RANGE_RESV1_FLD_SHIFT (0x00000004U)
#define CSL_OSPI_FLASH_CFG_INDIRECT_TRIGGER_ADDR_RANGE_REG_IND_RANGE_RESV1_FLD_MAX (0x0FFFFFFFU)

/* FLASH_COMMAND_CTRL_MEM_REG */

#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_TRIGGER_MEM_BANK_REQ_FLD_MASK (0x00000001U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_TRIGGER_MEM_BANK_REQ_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_TRIGGER_MEM_BANK_REQ_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_REQ_IN_PROGRESS_FLD_MASK (0x00000002U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_REQ_IN_PROGRESS_FLD_SHIFT (0x00000001U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_REQ_IN_PROGRESS_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV3_FLD_MASK (0x000000FCU)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV3_FLD_SHIFT (0x00000002U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV3_FLD_MAX (0x0000003FU)

#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_READ_DATA_FLD_MASK (0x0000FF00U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_READ_DATA_FLD_SHIFT (0x00000008U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_READ_DATA_FLD_MAX (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_NB_OF_STIG_READ_BYTES_FLD_MASK (0x00070000U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_NB_OF_STIG_READ_BYTES_FLD_SHIFT (0x00000010U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_NB_OF_STIG_READ_BYTES_FLD_MAX (0x00000007U)

#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV2_FLD_MASK (0x00080000U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV2_FLD_SHIFT (0x00000013U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV2_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_ADDR_FLD_MASK   (0x1FF00000U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_ADDR_FLD_SHIFT  (0x00000014U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_MEM_BANK_ADDR_FLD_MAX    (0x000001FFU)

#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV1_FLD_MASK (0xE0000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV1_FLD_SHIFT (0x0000001DU)
#define CSL_OSPI_FLASH_CFG_FLASH_COMMAND_CTRL_MEM_REG_FLASH_COMMAND_CTRL_MEM_RESV1_FLD_MAX (0x00000007U)

/* FLASH_CMD_CTRL_REG */

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_EXEC_FLD_MASK                (0x00000001U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_EXEC_FLD_SHIFT               (0x00000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_EXEC_FLD_MAX                 (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_EXEC_STATUS_FLD_MASK         (0x00000002U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_EXEC_STATUS_FLD_SHIFT        (0x00000001U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_EXEC_STATUS_FLD_MAX          (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_STIG_MEM_BANK_EN_FLD_MASK        (0x00000004U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_STIG_MEM_BANK_EN_FLD_SHIFT       (0x00000002U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_STIG_MEM_BANK_EN_FLD_MAX         (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_FLASH_CMD_CTRL_RESV1_FLD_MASK    (0x00000078U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_FLASH_CMD_CTRL_RESV1_FLD_SHIFT   (0x00000003U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_FLASH_CMD_CTRL_RESV1_FLD_MAX     (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_DUMMY_CYCLES_FLD_MASK        (0x00000F80U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_DUMMY_CYCLES_FLD_SHIFT       (0x00000007U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_DUMMY_CYCLES_FLD_MAX         (0x0000001FU)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_WR_DATA_BYTES_FLD_MASK       (0x00007000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_WR_DATA_BYTES_FLD_SHIFT      (0x0000000CU)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_WR_DATA_BYTES_FLD_MAX        (0x00000007U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_WRITE_DATA_FLD_MASK          (0x00008000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_WRITE_DATA_FLD_SHIFT         (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_WRITE_DATA_FLD_MAX           (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_ADDR_BYTES_FLD_MASK          (0x00030000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_ADDR_BYTES_FLD_SHIFT         (0x00000010U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_ADDR_BYTES_FLD_MAX           (0x00000003U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_MODE_BIT_FLD_MASK            (0x00040000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_MODE_BIT_FLD_SHIFT           (0x00000012U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_MODE_BIT_FLD_MAX             (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_COMD_ADDR_FLD_MASK           (0x00080000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_COMD_ADDR_FLD_SHIFT          (0x00000013U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_COMD_ADDR_FLD_MAX            (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_RD_DATA_BYTES_FLD_MASK       (0x00700000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_RD_DATA_BYTES_FLD_SHIFT      (0x00000014U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_NUM_RD_DATA_BYTES_FLD_MAX        (0x00000007U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_READ_DATA_FLD_MASK           (0x00800000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_READ_DATA_FLD_SHIFT          (0x00000017U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_ENB_READ_DATA_FLD_MAX            (0x00000001U)

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_OPCODE_FLD_MASK              (0xFF000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_OPCODE_FLD_SHIFT             (0x00000018U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_CTRL_REG_CMD_OPCODE_FLD_MAX               (0x000000FFU)

/* FLASH_CMD_ADDR_REG */

#define CSL_OSPI_FLASH_CFG_FLASH_CMD_ADDR_REG_ADDR_FLD_MASK                    (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_ADDR_REG_ADDR_FLD_SHIFT                   (0x00000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_CMD_ADDR_REG_ADDR_FLD_MAX                     (0xFFFFFFFFU)

/* FLASH_RD_DATA_LOWER_REG */

#define CSL_OSPI_FLASH_CFG_FLASH_RD_DATA_LOWER_REG_DATA_FLD_MASK               (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_FLASH_RD_DATA_LOWER_REG_DATA_FLD_SHIFT              (0x00000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_RD_DATA_LOWER_REG_DATA_FLD_MAX                (0xFFFFFFFFU)

/* FLASH_RD_DATA_UPPER_REG */

#define CSL_OSPI_FLASH_CFG_FLASH_RD_DATA_UPPER_REG_DATA_FLD_MASK               (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_FLASH_RD_DATA_UPPER_REG_DATA_FLD_SHIFT              (0x00000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_RD_DATA_UPPER_REG_DATA_FLD_MAX                (0xFFFFFFFFU)

/* FLASH_WR_DATA_LOWER_REG */

#define CSL_OSPI_FLASH_CFG_FLASH_WR_DATA_LOWER_REG_DATA_FLD_MASK               (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_FLASH_WR_DATA_LOWER_REG_DATA_FLD_SHIFT              (0x00000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_WR_DATA_LOWER_REG_DATA_FLD_MAX                (0xFFFFFFFFU)

/* FLASH_WR_DATA_UPPER_REG */

#define CSL_OSPI_FLASH_CFG_FLASH_WR_DATA_UPPER_REG_DATA_FLD_MASK               (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_CFG_FLASH_WR_DATA_UPPER_REG_DATA_FLD_SHIFT              (0x00000000U)
#define CSL_OSPI_FLASH_CFG_FLASH_WR_DATA_UPPER_REG_DATA_FLD_MAX                (0xFFFFFFFFU)

/* POLLING_FLASH_STATUS_REG */

#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_FLD_MASK     (0x000000FFU)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_FLD_SHIFT    (0x00000000U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_FLD_MAX      (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_VALID_FLD_MASK (0x00000100U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_VALID_FLD_SHIFT (0x00000008U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_VALID_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_RSVD_FLD1_MASK (0x0000FE00U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_RSVD_FLD1_SHIFT (0x00000009U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_RSVD_FLD1_MAX (0x0000007FU)

#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_NB_DUMMY_MASK (0x000F0000U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_NB_DUMMY_SHIFT (0x00000010U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_NB_DUMMY_MAX (0x0000000FU)

#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_RSVD_FLD2_MASK (0xFFF00000U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_RSVD_FLD2_SHIFT (0x00000014U)
#define CSL_OSPI_FLASH_CFG_POLLING_FLASH_STATUS_REG_DEVICE_STATUS_RSVD_FLD2_MAX (0x00000FFFU)

/* PHY_CONFIGURATION_REG */

#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RX_DLL_DELAY_FLD_MASK (0x0000007FU)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RX_DLL_DELAY_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RX_DLL_DELAY_FLD_MAX (0x0000007FU)

#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESV1_FLD_MASK     (0x0000FF80U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESV1_FLD_SHIFT    (0x00000007U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESV1_FLD_MAX      (0x000001FFU)

#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_TX_DLL_DELAY_FLD_MASK (0x007F0000U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_TX_DLL_DELAY_FLD_SHIFT (0x00000010U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_TX_DLL_DELAY_FLD_MAX (0x0000007FU)

#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESV2_FLD_MASK     (0x1F800000U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESV2_FLD_SHIFT    (0x00000017U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESV2_FLD_MAX      (0x0000003FU)

#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RX_DLL_BYPASS_FLD_MASK (0x20000000U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RX_DLL_BYPASS_FLD_SHIFT (0x0000001DU)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RX_DLL_BYPASS_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESET_FLD_MASK     (0x40000000U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESET_FLD_SHIFT    (0x0000001EU)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESET_FLD_MAX      (0x00000001U)

#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESYNC_FLD_MASK    (0x80000000U)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESYNC_FLD_SHIFT   (0x0000001FU)
#define CSL_OSPI_FLASH_CFG_PHY_CONFIGURATION_REG_PHY_CONFIG_RESYNC_FLD_MAX     (0x00000001U)

/* PHY_MASTER_CONTROL_REG */

#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_INITIAL_DELAY_FLD_MASK (0x0000007FU)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_INITIAL_DELAY_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_INITIAL_DELAY_FLD_MAX (0x0000007FU)

#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV1_FLD_MASK (0x0000FF80U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV1_FLD_SHIFT (0x00000007U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV1_FLD_MAX (0x000001FFU)

#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_NB_INDICATIONS_FLD_MASK (0x00070000U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_NB_INDICATIONS_FLD_SHIFT (0x00000010U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_NB_INDICATIONS_FLD_MAX (0x00000007U)

#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV2_FLD_MASK (0x00080000U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV2_FLD_SHIFT (0x00000013U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV2_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_PHASE_DETECT_SELECTOR_FLD_MASK (0x00700000U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_PHASE_DETECT_SELECTOR_FLD_SHIFT (0x00000014U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_PHASE_DETECT_SELECTOR_FLD_MAX (0x00000007U)

#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_BYPASS_MODE_FLD_MASK (0x00800000U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_BYPASS_MODE_FLD_SHIFT (0x00000017U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_BYPASS_MODE_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_LOCK_MODE_FLD_MASK (0x01000000U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_LOCK_MODE_FLD_SHIFT (0x00000018U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_LOCK_MODE_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV3_FLD_MASK (0xFE000000U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV3_FLD_SHIFT (0x00000019U)
#define CSL_OSPI_FLASH_CFG_PHY_MASTER_CONTROL_REG_PHY_MASTER_CONTROL_RESV3_FLD_MAX (0x0000007FU)

/* DLL_OBSERVABLE_LOWER_REG */

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_FLD_MASK (0x00000001U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOCK_MODE_FLD_MASK (0x00000006U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOCK_MODE_FLD_SHIFT (0x00000001U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOCK_MODE_FLD_MAX (0x00000003U)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_UNLOCK_COUNTER_FLD_MASK (0x000000F8U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_UNLOCK_COUNTER_FLD_SHIFT (0x00000003U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_UNLOCK_COUNTER_FLD_MAX (0x0000001FU)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOCK_VALUE_FLD_MASK (0x00007F00U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOCK_VALUE_FLD_SHIFT (0x00000008U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOCK_VALUE_FLD_MAX (0x0000007FU)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOOPBACK_LOCK_FLD_MASK (0x00008000U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOOPBACK_LOCK_FLD_SHIFT (0x0000000FU)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_LOOPBACK_LOCK_FLD_MAX (0x00000001U)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_DEC_FLD_MASK (0x00FF0000U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_DEC_FLD_SHIFT (0x00000010U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_DEC_FLD_MAX (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_INC_FLD_MASK (0xFF000000U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_INC_FLD_SHIFT (0x00000018U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_LOWER_REG_DLL_OBSERVABLE_LOWER_DLL_LOCK_INC_FLD_MAX (0x000000FFU)

/* DLL_OBSERVABLE_UPPER_REG */

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE__UPPER_RX_DECODER_OUTPUT_FLD_MASK (0x0000007FU)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE__UPPER_RX_DECODER_OUTPUT_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE__UPPER_RX_DECODER_OUTPUT_FLD_MAX (0x0000007FU)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_RESV1_FLD_MASK (0x0000FF80U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_RESV1_FLD_SHIFT (0x00000007U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_RESV1_FLD_MAX (0x000001FFU)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_TX_DECODER_OUTPUT_FLD_MASK (0x007F0000U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_TX_DECODER_OUTPUT_FLD_SHIFT (0x00000010U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_TX_DECODER_OUTPUT_FLD_MAX (0x0000007FU)

#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_RESV2_FLD_MASK (0xFF800000U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_RESV2_FLD_SHIFT (0x00000017U)
#define CSL_OSPI_FLASH_CFG_DLL_OBSERVABLE_UPPER_REG_DLL_OBSERVABLE_UPPER_RESV2_FLD_MAX (0x000001FFU)

/* OPCODE_EXT_LOWER_REG */

#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_STIG_OPCODE_FLD_MASK       (0x000000FFU)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_STIG_OPCODE_FLD_SHIFT      (0x00000000U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_STIG_OPCODE_FLD_MAX        (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_POLL_OPCODE_FLD_MASK       (0x0000FF00U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_POLL_OPCODE_FLD_SHIFT      (0x00000008U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_POLL_OPCODE_FLD_MAX        (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_WRITE_OPCODE_FLD_MASK      (0x00FF0000U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_WRITE_OPCODE_FLD_SHIFT     (0x00000010U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_WRITE_OPCODE_FLD_MAX       (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_READ_OPCODE_FLD_MASK       (0xFF000000U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_READ_OPCODE_FLD_SHIFT      (0x00000018U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_LOWER_REG_EXT_READ_OPCODE_FLD_MAX        (0x000000FFU)

/* OPCODE_EXT_UPPER_REG */

#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_OPCODE_EXT_UPPER_RESV1_FLD_MASK (0x0000FFFFU)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_OPCODE_EXT_UPPER_RESV1_FLD_SHIFT (0x00000000U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_OPCODE_EXT_UPPER_RESV1_FLD_MAX (0x0000FFFFU)

#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_EXT_WEL_OPCODE_FLD_MASK        (0x00FF0000U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_EXT_WEL_OPCODE_FLD_SHIFT       (0x00000010U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_EXT_WEL_OPCODE_FLD_MAX         (0x000000FFU)

#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_WEL_OPCODE_FLD_MASK            (0xFF000000U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_WEL_OPCODE_FLD_SHIFT           (0x00000018U)
#define CSL_OSPI_FLASH_CFG_OPCODE_EXT_UPPER_REG_WEL_OPCODE_FLD_MAX             (0x000000FFU)

/* MODULE_ID_REG */

#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_CONF_FLD_MASK                         (0x00000003U)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_CONF_FLD_SHIFT                        (0x00000000U)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_CONF_FLD_MAX                          (0x00000003U)

#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_MODULE_ID_RESV_FLD_MASK               (0x000000FCU)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_MODULE_ID_RESV_FLD_SHIFT              (0x00000002U)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_MODULE_ID_RESV_FLD_MAX                (0x0000003FU)

#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_MODULE_ID_FLD_MASK                    (0x00FFFF00U)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_MODULE_ID_FLD_SHIFT                   (0x00000008U)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_MODULE_ID_FLD_MAX                     (0x0000FFFFU)

#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_FIX_PATCH_FLD_MASK                    (0xFF000000U)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_FIX_PATCH_FLD_SHIFT                   (0x00000018U)
#define CSL_OSPI_FLASH_CFG_MODULE_ID_REG_FIX_PATCH_FLD_MAX                     (0x000000FFU)

/**************************************************************************
* Hardware Region  :
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t OSPI_CORE_MEM[1];   /* OSPI Core memory. */
} CSL_ospi_flash_mem1Regs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_OSPI_FLASH_MEM1_OSPI_CORE_MEM(OSPI_CORE_MEM)                       (0x00000000U+((OSPI_CORE_MEM)*0x4U))

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* OSPI_CORE_MEM */

#define CSL_OSPI_FLASH_MEM1_OSPI_CORE_MEM_OSPI_MEM_MASK                        (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_MEM1_OSPI_CORE_MEM_OSPI_MEM_SHIFT                       (0x00000000U)
#define CSL_OSPI_FLASH_MEM1_OSPI_CORE_MEM_OSPI_MEM_MAX                         (0xFFFFFFFFU)

/**************************************************************************
* Hardware Region  :
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t OSPI_CORE_MEM_LOW[16777216];   /* OSPI Core memory. */
    volatile uint32_t OSPI_CORE_MEM_HI[16777216];   /* OSPI Core memory. */
} CSL_ospi_flash_mem0Regs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_OSPI_FLASH_MEM0_OSPI_CORE_MEM_LOW(OSPI_CORE_MEM_LOW)               (0x00000000U+((OSPI_CORE_MEM_LOW)*0x4U))
#define CSL_OSPI_FLASH_MEM0_OSPI_CORE_MEM_HI(OSPI_CORE_MEM_HI)                 (0x04000000U+((OSPI_CORE_MEM_HI)*0x4U))

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* OSPI_CORE_MEM_LOW */

#define CSL_OSPI_FLASH_MEM0_OSPI_CORE_MEM_LOW_OSPI_MEM_MASK                    (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_MEM0_OSPI_CORE_MEM_LOW_OSPI_MEM_SHIFT                   (0x00000000U)
#define CSL_OSPI_FLASH_MEM0_OSPI_CORE_MEM_LOW_OSPI_MEM_MAX                     (0xFFFFFFFFU)

/* OSPI_CORE_MEM_HI */

#define CSL_OSPI_FLASH_MEM0_OSPI_CORE_MEM_HI_OSPI_MEM_MASK                     (0xFFFFFFFFU)
#define CSL_OSPI_FLASH_MEM0_OSPI_CORE_MEM_HI_OSPI_MEM_SHIFT                    (0x00000000U)
#define CSL_OSPI_FLASH_MEM0_OSPI_CORE_MEM_HI_OSPI_MEM_MAX                      (0xFFFFFFFFU)

#ifdef __cplusplus
}
#endif
#endif
