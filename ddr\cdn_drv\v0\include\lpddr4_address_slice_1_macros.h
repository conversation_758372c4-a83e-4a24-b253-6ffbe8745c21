/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_ADDRESS_SLICE_1_MACROS_H_
#define REG_LPDDR4_ADDRESS_SLICE_1_MACROS_H_

#define LPDDR4__DENALI_PHY_768_READ_MASK                             0x000107FFU
#define LPDDR4__DENALI_PHY_768_WRITE_MASK                            0x000107FFU
#define LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1_SHIFT     0U
#define LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1_WIDTH    11U
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1__REG DENALI_PHY_768
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_BYPASS_OVERRIDE_1_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_BYPASS_OVERRIDE_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_BYPASS_OVERRIDE_1_WIDTH           1U
#define LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_BYPASS_OVERRIDE_1_WOCLR           0U
#define LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_BYPASS_OVERRIDE_1_WOSET           0U
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_1__REG DENALI_PHY_768
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_1__FLD LPDDR4__DENALI_PHY_768__PHY_ADR_CLK_BYPASS_OVERRIDE_1

#define LPDDR4__DENALI_PHY_768__SC_PHY_ADR_MANUAL_CLEAR_1_MASK       0x07000000U
#define LPDDR4__DENALI_PHY_768__SC_PHY_ADR_MANUAL_CLEAR_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_768__SC_PHY_ADR_MANUAL_CLEAR_1_WIDTH               3U
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_1__REG DENALI_PHY_768
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_1__FLD LPDDR4__DENALI_PHY_768__SC_PHY_ADR_MANUAL_CLEAR_1

#define LPDDR4__DENALI_PHY_769_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_769_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_769__PHY_ADR_LPBK_RESULT_OBS_1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_769__PHY_ADR_LPBK_RESULT_OBS_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_769__PHY_ADR_LPBK_RESULT_OBS_1_WIDTH              32U
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_1__REG DENALI_PHY_769
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_1__FLD LPDDR4__DENALI_PHY_769__PHY_ADR_LPBK_RESULT_OBS_1

#define LPDDR4__DENALI_PHY_770_READ_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_770_WRITE_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_770__PHY_ADR_LPBK_ERROR_COUNT_OBS_1_MASK  0x0000FFFFU
#define LPDDR4__DENALI_PHY_770__PHY_ADR_LPBK_ERROR_COUNT_OBS_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_770__PHY_ADR_LPBK_ERROR_COUNT_OBS_1_WIDTH         16U
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_1__REG DENALI_PHY_770
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_1__FLD LPDDR4__DENALI_PHY_770__PHY_ADR_LPBK_ERROR_COUNT_OBS_1

#define LPDDR4__DENALI_PHY_770__PHY_ADR_MEAS_DLY_STEP_VALUE_1_MASK   0x00FF0000U
#define LPDDR4__DENALI_PHY_770__PHY_ADR_MEAS_DLY_STEP_VALUE_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_770__PHY_ADR_MEAS_DLY_STEP_VALUE_1_WIDTH           8U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_1__REG DENALI_PHY_770
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_1__FLD LPDDR4__DENALI_PHY_770__PHY_ADR_MEAS_DLY_STEP_VALUE_1

#define LPDDR4__DENALI_PHY_770__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1_MASK 0x0F000000U
#define LPDDR4__DENALI_PHY_770__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1_SHIFT   24U
#define LPDDR4__DENALI_PHY_770__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1_WIDTH    4U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1__REG DENALI_PHY_770
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_770__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_771_READ_MASK                             0xFF7F07FFU
#define LPDDR4__DENALI_PHY_771_WRITE_MASK                            0xFF7F07FFU
#define LPDDR4__DENALI_PHY_771__PHY_ADR_MASTER_DLY_LOCK_OBS_1_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_771__PHY_ADR_MASTER_DLY_LOCK_OBS_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_771__PHY_ADR_MASTER_DLY_LOCK_OBS_1_WIDTH          11U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_1__REG DENALI_PHY_771
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_1__FLD LPDDR4__DENALI_PHY_771__PHY_ADR_MASTER_DLY_LOCK_OBS_1

#define LPDDR4__DENALI_PHY_771__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1_MASK  0x007F0000U
#define LPDDR4__DENALI_PHY_771__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1_SHIFT         16U
#define LPDDR4__DENALI_PHY_771__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1_WIDTH          7U
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_771
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_771__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_771__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_771__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1_SHIFT        24U
#define LPDDR4__DENALI_PHY_771__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1_WIDTH         8U
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_771
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_771__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_772_READ_MASK                             0x01000707U
#define LPDDR4__DENALI_PHY_772_WRITE_MASK                            0x01000707U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1_MASK 0x00000007U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1_WIDTH         3U
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1__REG DENALI_PHY_772
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1__FLD LPDDR4__DENALI_PHY_772__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1

#define LPDDR4__DENALI_PHY_772__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1_MASK 0x00000700U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1_SHIFT        8U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1_WIDTH        3U
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1__REG DENALI_PHY_772
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_772__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_772__SC_PHY_ADR_SNAP_OBS_REGS_1_MASK      0x00010000U
#define LPDDR4__DENALI_PHY_772__SC_PHY_ADR_SNAP_OBS_REGS_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_772__SC_PHY_ADR_SNAP_OBS_REGS_1_WIDTH              1U
#define LPDDR4__DENALI_PHY_772__SC_PHY_ADR_SNAP_OBS_REGS_1_WOCLR              0U
#define LPDDR4__DENALI_PHY_772__SC_PHY_ADR_SNAP_OBS_REGS_1_WOSET              0U
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_1__REG DENALI_PHY_772
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_1__FLD LPDDR4__DENALI_PHY_772__SC_PHY_ADR_SNAP_OBS_REGS_1

#define LPDDR4__DENALI_PHY_772__PHY_ADR_TSEL_ENABLE_1_MASK           0x01000000U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_TSEL_ENABLE_1_SHIFT                  24U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_TSEL_ENABLE_1_WIDTH                   1U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_TSEL_ENABLE_1_WOCLR                   0U
#define LPDDR4__DENALI_PHY_772__PHY_ADR_TSEL_ENABLE_1_WOSET                   0U
#define LPDDR4__PHY_ADR_TSEL_ENABLE_1__REG DENALI_PHY_772
#define LPDDR4__PHY_ADR_TSEL_ENABLE_1__FLD LPDDR4__DENALI_PHY_772__PHY_ADR_TSEL_ENABLE_1

#define LPDDR4__DENALI_PHY_773_READ_MASK                             0x011F7F7FU
#define LPDDR4__DENALI_PHY_773_WRITE_MASK                            0x011F7F7FU
#define LPDDR4__DENALI_PHY_773__PHY_ADR_LPBK_CONTROL_1_MASK          0x0000007FU
#define LPDDR4__DENALI_PHY_773__PHY_ADR_LPBK_CONTROL_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_LPBK_CONTROL_1_WIDTH                  7U
#define LPDDR4__PHY_ADR_LPBK_CONTROL_1__REG DENALI_PHY_773
#define LPDDR4__PHY_ADR_LPBK_CONTROL_1__FLD LPDDR4__DENALI_PHY_773__PHY_ADR_LPBK_CONTROL_1

#define LPDDR4__DENALI_PHY_773__PHY_ADR_PRBS_PATTERN_START_1_MASK    0x00007F00U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_PRBS_PATTERN_START_1_SHIFT            8U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_PRBS_PATTERN_START_1_WIDTH            7U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_1__REG DENALI_PHY_773
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_1__FLD LPDDR4__DENALI_PHY_773__PHY_ADR_PRBS_PATTERN_START_1

#define LPDDR4__DENALI_PHY_773__PHY_ADR_PRBS_PATTERN_MASK_1_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_PRBS_PATTERN_MASK_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_PRBS_PATTERN_MASK_1_WIDTH             5U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_1__REG DENALI_PHY_773
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_1__FLD LPDDR4__DENALI_PHY_773__PHY_ADR_PRBS_PATTERN_MASK_1

#define LPDDR4__DENALI_PHY_773__PHY_ADR_PWR_RDC_DISABLE_1_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_PWR_RDC_DISABLE_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_PWR_RDC_DISABLE_1_WIDTH               1U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_PWR_RDC_DISABLE_1_WOCLR               0U
#define LPDDR4__DENALI_PHY_773__PHY_ADR_PWR_RDC_DISABLE_1_WOSET               0U
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_1__REG DENALI_PHY_773
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_1__FLD LPDDR4__DENALI_PHY_773__PHY_ADR_PWR_RDC_DISABLE_1

#define LPDDR4__DENALI_PHY_774_READ_MASK                             0x01070301U
#define LPDDR4__DENALI_PHY_774_WRITE_MASK                            0x01070301U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_SHIFT     0U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_WIDTH     1U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_WOCLR     0U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_WOSET     0U
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1__REG DENALI_PHY_774
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1__FLD LPDDR4__DENALI_PHY_774__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1

#define LPDDR4__DENALI_PHY_774__PHY_ADR_TYPE_1_MASK                  0x00000300U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_TYPE_1_SHIFT                          8U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_TYPE_1_WIDTH                          2U
#define LPDDR4__PHY_ADR_TYPE_1__REG DENALI_PHY_774
#define LPDDR4__PHY_ADR_TYPE_1__FLD LPDDR4__DENALI_PHY_774__PHY_ADR_TYPE_1

#define LPDDR4__DENALI_PHY_774__PHY_ADR_WRADDR_SHIFT_OBS_1_MASK      0x00070000U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_WRADDR_SHIFT_OBS_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_WRADDR_SHIFT_OBS_1_WIDTH              3U
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_1__REG DENALI_PHY_774
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_1__FLD LPDDR4__DENALI_PHY_774__PHY_ADR_WRADDR_SHIFT_OBS_1

#define LPDDR4__DENALI_PHY_774__PHY_ADR_IE_MODE_1_MASK               0x01000000U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_IE_MODE_1_SHIFT                      24U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_IE_MODE_1_WIDTH                       1U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_IE_MODE_1_WOCLR                       0U
#define LPDDR4__DENALI_PHY_774__PHY_ADR_IE_MODE_1_WOSET                       0U
#define LPDDR4__PHY_ADR_IE_MODE_1__REG DENALI_PHY_774
#define LPDDR4__PHY_ADR_IE_MODE_1__FLD LPDDR4__DENALI_PHY_774__PHY_ADR_IE_MODE_1

#define LPDDR4__DENALI_PHY_775_READ_MASK                             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_775_WRITE_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_775__PHY_ADR_DDL_MODE_1_MASK              0x07FFFFFFU
#define LPDDR4__DENALI_PHY_775__PHY_ADR_DDL_MODE_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_775__PHY_ADR_DDL_MODE_1_WIDTH                     27U
#define LPDDR4__PHY_ADR_DDL_MODE_1__REG DENALI_PHY_775
#define LPDDR4__PHY_ADR_DDL_MODE_1__FLD LPDDR4__DENALI_PHY_775__PHY_ADR_DDL_MODE_1

#define LPDDR4__DENALI_PHY_776_READ_MASK                             0x0000003FU
#define LPDDR4__DENALI_PHY_776_WRITE_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_776__PHY_ADR_DDL_MASK_1_MASK              0x0000003FU
#define LPDDR4__DENALI_PHY_776__PHY_ADR_DDL_MASK_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_776__PHY_ADR_DDL_MASK_1_WIDTH                      6U
#define LPDDR4__PHY_ADR_DDL_MASK_1__REG DENALI_PHY_776
#define LPDDR4__PHY_ADR_DDL_MASK_1__FLD LPDDR4__DENALI_PHY_776__PHY_ADR_DDL_MASK_1

#define LPDDR4__DENALI_PHY_777_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_777_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_777__PHY_ADR_DDL_TEST_OBS_1_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_777__PHY_ADR_DDL_TEST_OBS_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_777__PHY_ADR_DDL_TEST_OBS_1_WIDTH                 32U
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_1__REG DENALI_PHY_777
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_1__FLD LPDDR4__DENALI_PHY_777__PHY_ADR_DDL_TEST_OBS_1

#define LPDDR4__DENALI_PHY_778_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_778_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_778__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_778__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_778__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1_WIDTH        32U
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1__REG DENALI_PHY_778
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1__FLD LPDDR4__DENALI_PHY_778__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1

#define LPDDR4__DENALI_PHY_779_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_779_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_779__PHY_ADR_CALVL_START_1_MASK           0x000007FFU
#define LPDDR4__DENALI_PHY_779__PHY_ADR_CALVL_START_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_779__PHY_ADR_CALVL_START_1_WIDTH                  11U
#define LPDDR4__PHY_ADR_CALVL_START_1__REG DENALI_PHY_779
#define LPDDR4__PHY_ADR_CALVL_START_1__FLD LPDDR4__DENALI_PHY_779__PHY_ADR_CALVL_START_1

#define LPDDR4__DENALI_PHY_779__PHY_ADR_CALVL_COARSE_DLY_1_MASK      0x07FF0000U
#define LPDDR4__DENALI_PHY_779__PHY_ADR_CALVL_COARSE_DLY_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_779__PHY_ADR_CALVL_COARSE_DLY_1_WIDTH             11U
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_1__REG DENALI_PHY_779
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_1__FLD LPDDR4__DENALI_PHY_779__PHY_ADR_CALVL_COARSE_DLY_1

#define LPDDR4__DENALI_PHY_780_READ_MASK                             0x000007FFU
#define LPDDR4__DENALI_PHY_780_WRITE_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_780__PHY_ADR_CALVL_QTR_1_MASK             0x000007FFU
#define LPDDR4__DENALI_PHY_780__PHY_ADR_CALVL_QTR_1_SHIFT                     0U
#define LPDDR4__DENALI_PHY_780__PHY_ADR_CALVL_QTR_1_WIDTH                    11U
#define LPDDR4__PHY_ADR_CALVL_QTR_1__REG DENALI_PHY_780
#define LPDDR4__PHY_ADR_CALVL_QTR_1__FLD LPDDR4__DENALI_PHY_780__PHY_ADR_CALVL_QTR_1

#define LPDDR4__DENALI_PHY_781_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PHY_781_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_781__PHY_ADR_CALVL_SWIZZLE0_1_MASK        0x00FFFFFFU
#define LPDDR4__DENALI_PHY_781__PHY_ADR_CALVL_SWIZZLE0_1_SHIFT                0U
#define LPDDR4__DENALI_PHY_781__PHY_ADR_CALVL_SWIZZLE0_1_WIDTH               24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_1__REG DENALI_PHY_781
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_1__FLD LPDDR4__DENALI_PHY_781__PHY_ADR_CALVL_SWIZZLE0_1

#define LPDDR4__DENALI_PHY_782_READ_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PHY_782_WRITE_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_782__PHY_ADR_CALVL_SWIZZLE1_1_MASK        0x00FFFFFFU
#define LPDDR4__DENALI_PHY_782__PHY_ADR_CALVL_SWIZZLE1_1_SHIFT                0U
#define LPDDR4__DENALI_PHY_782__PHY_ADR_CALVL_SWIZZLE1_1_WIDTH               24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_1__REG DENALI_PHY_782
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_1__FLD LPDDR4__DENALI_PHY_782__PHY_ADR_CALVL_SWIZZLE1_1

#define LPDDR4__DENALI_PHY_782__PHY_ADR_CALVL_RANK_CTRL_1_MASK       0x03000000U
#define LPDDR4__DENALI_PHY_782__PHY_ADR_CALVL_RANK_CTRL_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_782__PHY_ADR_CALVL_RANK_CTRL_1_WIDTH               2U
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_1__REG DENALI_PHY_782
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_1__FLD LPDDR4__DENALI_PHY_782__PHY_ADR_CALVL_RANK_CTRL_1

#define LPDDR4__DENALI_PHY_783_READ_MASK                             0x01FF0F03U
#define LPDDR4__DENALI_PHY_783_WRITE_MASK                            0x01FF0F03U
#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_NUM_PATTERNS_1_MASK    0x00000003U
#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_NUM_PATTERNS_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_NUM_PATTERNS_1_WIDTH            2U
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_1__REG DENALI_PHY_783
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_1__FLD LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_NUM_PATTERNS_1

#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_RESP_WAIT_CNT_1_MASK   0x00000F00U
#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_RESP_WAIT_CNT_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_RESP_WAIT_CNT_1_WIDTH           4U
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_1__REG DENALI_PHY_783
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_1__FLD LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_RESP_WAIT_CNT_1

#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1_MASK 0x01FF0000U
#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1_SHIFT  16U
#define LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1_WIDTH   9U
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1__REG DENALI_PHY_783
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1__FLD LPDDR4__DENALI_PHY_783__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1

#define LPDDR4__DENALI_PHY_784_READ_MASK                             0x07000001U
#define LPDDR4__DENALI_PHY_784_WRITE_MASK                            0x07000001U
#define LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_DEBUG_MODE_1_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_DEBUG_MODE_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_DEBUG_MODE_1_WIDTH              1U
#define LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_DEBUG_MODE_1_WOCLR              0U
#define LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_DEBUG_MODE_1_WOSET              0U
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_1__REG DENALI_PHY_784
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_1__FLD LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_DEBUG_MODE_1

#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_DEBUG_CONT_1_MASK   0x00000100U
#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_DEBUG_CONT_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_DEBUG_CONT_1_WIDTH           1U
#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_DEBUG_CONT_1_WOCLR           0U
#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_DEBUG_CONT_1_WOSET           0U
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_1__REG DENALI_PHY_784
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_1__FLD LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_DEBUG_CONT_1

#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_ERROR_CLR_1_MASK    0x00010000U
#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_ERROR_CLR_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_ERROR_CLR_1_WIDTH            1U
#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_ERROR_CLR_1_WOCLR            0U
#define LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_ERROR_CLR_1_WOSET            0U
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_1__REG DENALI_PHY_784
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_1__FLD LPDDR4__DENALI_PHY_784__SC_PHY_ADR_CALVL_ERROR_CLR_1

#define LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_OBS_SELECT_1_MASK      0x07000000U
#define LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_OBS_SELECT_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_OBS_SELECT_1_WIDTH              3U
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_1__REG DENALI_PHY_784
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_784__PHY_ADR_CALVL_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_785_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_785_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_785__PHY_ADR_CALVL_OBS0_1_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_785__PHY_ADR_CALVL_OBS0_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_785__PHY_ADR_CALVL_OBS0_1_WIDTH                   32U
#define LPDDR4__PHY_ADR_CALVL_OBS0_1__REG DENALI_PHY_785
#define LPDDR4__PHY_ADR_CALVL_OBS0_1__FLD LPDDR4__DENALI_PHY_785__PHY_ADR_CALVL_OBS0_1

#define LPDDR4__DENALI_PHY_786_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_786_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_786__PHY_ADR_CALVL_OBS1_1_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_786__PHY_ADR_CALVL_OBS1_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_786__PHY_ADR_CALVL_OBS1_1_WIDTH                   32U
#define LPDDR4__PHY_ADR_CALVL_OBS1_1__REG DENALI_PHY_786
#define LPDDR4__PHY_ADR_CALVL_OBS1_1__FLD LPDDR4__DENALI_PHY_786__PHY_ADR_CALVL_OBS1_1

#define LPDDR4__DENALI_PHY_787_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_787_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_787__PHY_ADR_CALVL_OBS2_1_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_787__PHY_ADR_CALVL_OBS2_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_787__PHY_ADR_CALVL_OBS2_1_WIDTH                   32U
#define LPDDR4__PHY_ADR_CALVL_OBS2_1__REG DENALI_PHY_787
#define LPDDR4__PHY_ADR_CALVL_OBS2_1__FLD LPDDR4__DENALI_PHY_787__PHY_ADR_CALVL_OBS2_1

#define LPDDR4__DENALI_PHY_788_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_788_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_788__PHY_ADR_CALVL_FG_0_1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_788__PHY_ADR_CALVL_FG_0_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_788__PHY_ADR_CALVL_FG_0_1_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_FG_0_1__REG DENALI_PHY_788
#define LPDDR4__PHY_ADR_CALVL_FG_0_1__FLD LPDDR4__DENALI_PHY_788__PHY_ADR_CALVL_FG_0_1

#define LPDDR4__DENALI_PHY_789_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_789_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_789__PHY_ADR_CALVL_BG_0_1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_789__PHY_ADR_CALVL_BG_0_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_789__PHY_ADR_CALVL_BG_0_1_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_BG_0_1__REG DENALI_PHY_789
#define LPDDR4__PHY_ADR_CALVL_BG_0_1__FLD LPDDR4__DENALI_PHY_789__PHY_ADR_CALVL_BG_0_1

#define LPDDR4__DENALI_PHY_790_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_790_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_790__PHY_ADR_CALVL_FG_1_1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_790__PHY_ADR_CALVL_FG_1_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_790__PHY_ADR_CALVL_FG_1_1_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_FG_1_1__REG DENALI_PHY_790
#define LPDDR4__PHY_ADR_CALVL_FG_1_1__FLD LPDDR4__DENALI_PHY_790__PHY_ADR_CALVL_FG_1_1

#define LPDDR4__DENALI_PHY_791_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_791_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_791__PHY_ADR_CALVL_BG_1_1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_791__PHY_ADR_CALVL_BG_1_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_791__PHY_ADR_CALVL_BG_1_1_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_BG_1_1__REG DENALI_PHY_791
#define LPDDR4__PHY_ADR_CALVL_BG_1_1__FLD LPDDR4__DENALI_PHY_791__PHY_ADR_CALVL_BG_1_1

#define LPDDR4__DENALI_PHY_792_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_792_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_792__PHY_ADR_CALVL_FG_2_1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_792__PHY_ADR_CALVL_FG_2_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_792__PHY_ADR_CALVL_FG_2_1_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_FG_2_1__REG DENALI_PHY_792
#define LPDDR4__PHY_ADR_CALVL_FG_2_1__FLD LPDDR4__DENALI_PHY_792__PHY_ADR_CALVL_FG_2_1

#define LPDDR4__DENALI_PHY_793_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_793_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_793__PHY_ADR_CALVL_BG_2_1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_793__PHY_ADR_CALVL_BG_2_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_793__PHY_ADR_CALVL_BG_2_1_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_BG_2_1__REG DENALI_PHY_793
#define LPDDR4__PHY_ADR_CALVL_BG_2_1__FLD LPDDR4__DENALI_PHY_793__PHY_ADR_CALVL_BG_2_1

#define LPDDR4__DENALI_PHY_794_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_794_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_794__PHY_ADR_CALVL_FG_3_1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_794__PHY_ADR_CALVL_FG_3_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_794__PHY_ADR_CALVL_FG_3_1_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_FG_3_1__REG DENALI_PHY_794
#define LPDDR4__PHY_ADR_CALVL_FG_3_1__FLD LPDDR4__DENALI_PHY_794__PHY_ADR_CALVL_FG_3_1

#define LPDDR4__DENALI_PHY_795_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_795_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_795__PHY_ADR_CALVL_BG_3_1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_PHY_795__PHY_ADR_CALVL_BG_3_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_795__PHY_ADR_CALVL_BG_3_1_WIDTH                   20U
#define LPDDR4__PHY_ADR_CALVL_BG_3_1__REG DENALI_PHY_795
#define LPDDR4__PHY_ADR_CALVL_BG_3_1__FLD LPDDR4__DENALI_PHY_795__PHY_ADR_CALVL_BG_3_1

#define LPDDR4__DENALI_PHY_796_READ_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_796_WRITE_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_796__PHY_ADR_ADDR_SEL_1_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_796__PHY_ADR_ADDR_SEL_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_796__PHY_ADR_ADDR_SEL_1_WIDTH                     30U
#define LPDDR4__PHY_ADR_ADDR_SEL_1__REG DENALI_PHY_796
#define LPDDR4__PHY_ADR_ADDR_SEL_1__FLD LPDDR4__DENALI_PHY_796__PHY_ADR_ADDR_SEL_1

#define LPDDR4__DENALI_PHY_797_READ_MASK                             0x3F3F03FFU
#define LPDDR4__DENALI_PHY_797_WRITE_MASK                            0x3F3F03FFU
#define LPDDR4__DENALI_PHY_797__PHY_ADR_LP4_BOOT_SLV_DELAY_1_MASK    0x000003FFU
#define LPDDR4__DENALI_PHY_797__PHY_ADR_LP4_BOOT_SLV_DELAY_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_797__PHY_ADR_LP4_BOOT_SLV_DELAY_1_WIDTH           10U
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_1__REG DENALI_PHY_797
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_1__FLD LPDDR4__DENALI_PHY_797__PHY_ADR_LP4_BOOT_SLV_DELAY_1

#define LPDDR4__DENALI_PHY_797__PHY_ADR_BIT_MASK_1_MASK              0x003F0000U
#define LPDDR4__DENALI_PHY_797__PHY_ADR_BIT_MASK_1_SHIFT                     16U
#define LPDDR4__DENALI_PHY_797__PHY_ADR_BIT_MASK_1_WIDTH                      6U
#define LPDDR4__PHY_ADR_BIT_MASK_1__REG DENALI_PHY_797
#define LPDDR4__PHY_ADR_BIT_MASK_1__FLD LPDDR4__DENALI_PHY_797__PHY_ADR_BIT_MASK_1

#define LPDDR4__DENALI_PHY_797__PHY_ADR_SEG_MASK_1_MASK              0x3F000000U
#define LPDDR4__DENALI_PHY_797__PHY_ADR_SEG_MASK_1_SHIFT                     24U
#define LPDDR4__DENALI_PHY_797__PHY_ADR_SEG_MASK_1_WIDTH                      6U
#define LPDDR4__PHY_ADR_SEG_MASK_1__REG DENALI_PHY_797
#define LPDDR4__PHY_ADR_SEG_MASK_1__FLD LPDDR4__DENALI_PHY_797__PHY_ADR_SEG_MASK_1

#define LPDDR4__DENALI_PHY_798_READ_MASK                             0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_798_WRITE_MASK                            0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_798__PHY_ADR_CALVL_TRAIN_MASK_1_MASK      0x0000003FU
#define LPDDR4__DENALI_PHY_798__PHY_ADR_CALVL_TRAIN_MASK_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_798__PHY_ADR_CALVL_TRAIN_MASK_1_WIDTH              6U
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_1__REG DENALI_PHY_798
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_1__FLD LPDDR4__DENALI_PHY_798__PHY_ADR_CALVL_TRAIN_MASK_1

#define LPDDR4__DENALI_PHY_798__PHY_ADR_CSLVL_TRAIN_MASK_1_MASK      0x00003F00U
#define LPDDR4__DENALI_PHY_798__PHY_ADR_CSLVL_TRAIN_MASK_1_SHIFT              8U
#define LPDDR4__DENALI_PHY_798__PHY_ADR_CSLVL_TRAIN_MASK_1_WIDTH              6U
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_1__REG DENALI_PHY_798
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_1__FLD LPDDR4__DENALI_PHY_798__PHY_ADR_CSLVL_TRAIN_MASK_1

#define LPDDR4__DENALI_PHY_798__PHY_ADR_STATIC_TOG_DISABLE_1_MASK    0x000F0000U
#define LPDDR4__DENALI_PHY_798__PHY_ADR_STATIC_TOG_DISABLE_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_798__PHY_ADR_STATIC_TOG_DISABLE_1_WIDTH            4U
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_1__REG DENALI_PHY_798
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_1__FLD LPDDR4__DENALI_PHY_798__PHY_ADR_STATIC_TOG_DISABLE_1

#define LPDDR4__DENALI_PHY_798__PHY_ADR_SW_TXIO_CTRL_1_MASK          0x3F000000U
#define LPDDR4__DENALI_PHY_798__PHY_ADR_SW_TXIO_CTRL_1_SHIFT                 24U
#define LPDDR4__DENALI_PHY_798__PHY_ADR_SW_TXIO_CTRL_1_WIDTH                  6U
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_1__REG DENALI_PHY_798
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_1__FLD LPDDR4__DENALI_PHY_798__PHY_ADR_SW_TXIO_CTRL_1

#define LPDDR4__DENALI_PHY_799_READ_MASK                             0x0000003FU
#define LPDDR4__DENALI_PHY_799_WRITE_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_799__PHY_ADR_SW_TXPWR_CTRL_1_MASK         0x0000003FU
#define LPDDR4__DENALI_PHY_799__PHY_ADR_SW_TXPWR_CTRL_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_799__PHY_ADR_SW_TXPWR_CTRL_1_WIDTH                 6U
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_1__REG DENALI_PHY_799
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_1__FLD LPDDR4__DENALI_PHY_799__PHY_ADR_SW_TXPWR_CTRL_1

#define LPDDR4__DENALI_PHY_800_READ_MASK                             0x0707FFFFU
#define LPDDR4__DENALI_PHY_800_WRITE_MASK                            0x0707FFFFU
#define LPDDR4__DENALI_PHY_800__PHY_ADR_TSEL_SELECT_1_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_800__PHY_ADR_TSEL_SELECT_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_800__PHY_ADR_TSEL_SELECT_1_WIDTH                   8U
#define LPDDR4__PHY_ADR_TSEL_SELECT_1__REG DENALI_PHY_800
#define LPDDR4__PHY_ADR_TSEL_SELECT_1__FLD LPDDR4__DENALI_PHY_800__PHY_ADR_TSEL_SELECT_1

#define LPDDR4__DENALI_PHY_800__PHY_PAD_ADR_IO_CFG_1_MASK            0x0007FF00U
#define LPDDR4__DENALI_PHY_800__PHY_PAD_ADR_IO_CFG_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_800__PHY_PAD_ADR_IO_CFG_1_WIDTH                   11U
#define LPDDR4__PHY_PAD_ADR_IO_CFG_1__REG DENALI_PHY_800
#define LPDDR4__PHY_PAD_ADR_IO_CFG_1__FLD LPDDR4__DENALI_PHY_800__PHY_PAD_ADR_IO_CFG_1

#define LPDDR4__DENALI_PHY_800__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1_MASK   0x07000000U
#define LPDDR4__DENALI_PHY_800__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1_SHIFT          24U
#define LPDDR4__DENALI_PHY_800__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1_WIDTH           3U
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1__REG DENALI_PHY_800
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1__FLD LPDDR4__DENALI_PHY_800__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1

#define LPDDR4__DENALI_PHY_801_READ_MASK                             0x1F07FF1FU
#define LPDDR4__DENALI_PHY_801_WRITE_MASK                            0x1F07FF1FU
#define LPDDR4__DENALI_PHY_801__PHY_ADR0_SW_WRADDR_SHIFT_1_MASK      0x0000001FU
#define LPDDR4__DENALI_PHY_801__PHY_ADR0_SW_WRADDR_SHIFT_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_801__PHY_ADR0_SW_WRADDR_SHIFT_1_WIDTH              5U
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_1__REG DENALI_PHY_801
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_801__PHY_ADR0_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_801__PHY_ADR0_CLK_WR_SLAVE_DELAY_1_MASK   0x0007FF00U
#define LPDDR4__DENALI_PHY_801__PHY_ADR0_CLK_WR_SLAVE_DELAY_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_801__PHY_ADR0_CLK_WR_SLAVE_DELAY_1_WIDTH          11U
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_801
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_801__PHY_ADR0_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_801__PHY_ADR1_SW_WRADDR_SHIFT_1_MASK      0x1F000000U
#define LPDDR4__DENALI_PHY_801__PHY_ADR1_SW_WRADDR_SHIFT_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_801__PHY_ADR1_SW_WRADDR_SHIFT_1_WIDTH              5U
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_1__REG DENALI_PHY_801
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_801__PHY_ADR1_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_802_READ_MASK                             0x001F07FFU
#define LPDDR4__DENALI_PHY_802_WRITE_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_802__PHY_ADR1_CLK_WR_SLAVE_DELAY_1_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_802__PHY_ADR1_CLK_WR_SLAVE_DELAY_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_802__PHY_ADR1_CLK_WR_SLAVE_DELAY_1_WIDTH          11U
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_802
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_802__PHY_ADR1_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_802__PHY_ADR2_SW_WRADDR_SHIFT_1_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_802__PHY_ADR2_SW_WRADDR_SHIFT_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_802__PHY_ADR2_SW_WRADDR_SHIFT_1_WIDTH              5U
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_1__REG DENALI_PHY_802
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_802__PHY_ADR2_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_803_READ_MASK                             0x001F07FFU
#define LPDDR4__DENALI_PHY_803_WRITE_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_803__PHY_ADR2_CLK_WR_SLAVE_DELAY_1_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_803__PHY_ADR2_CLK_WR_SLAVE_DELAY_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_803__PHY_ADR2_CLK_WR_SLAVE_DELAY_1_WIDTH          11U
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_803
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_803__PHY_ADR2_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_803__PHY_ADR3_SW_WRADDR_SHIFT_1_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_803__PHY_ADR3_SW_WRADDR_SHIFT_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_803__PHY_ADR3_SW_WRADDR_SHIFT_1_WIDTH              5U
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_1__REG DENALI_PHY_803
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_803__PHY_ADR3_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_804_READ_MASK                             0x001F07FFU
#define LPDDR4__DENALI_PHY_804_WRITE_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_804__PHY_ADR3_CLK_WR_SLAVE_DELAY_1_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_804__PHY_ADR3_CLK_WR_SLAVE_DELAY_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_804__PHY_ADR3_CLK_WR_SLAVE_DELAY_1_WIDTH          11U
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_804
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_804__PHY_ADR3_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_804__PHY_ADR4_SW_WRADDR_SHIFT_1_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_804__PHY_ADR4_SW_WRADDR_SHIFT_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_804__PHY_ADR4_SW_WRADDR_SHIFT_1_WIDTH              5U
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_1__REG DENALI_PHY_804
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_804__PHY_ADR4_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_805_READ_MASK                             0x001F07FFU
#define LPDDR4__DENALI_PHY_805_WRITE_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_805__PHY_ADR4_CLK_WR_SLAVE_DELAY_1_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_805__PHY_ADR4_CLK_WR_SLAVE_DELAY_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_805__PHY_ADR4_CLK_WR_SLAVE_DELAY_1_WIDTH          11U
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_805
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_805__PHY_ADR4_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_805__PHY_ADR5_SW_WRADDR_SHIFT_1_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_805__PHY_ADR5_SW_WRADDR_SHIFT_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_805__PHY_ADR5_SW_WRADDR_SHIFT_1_WIDTH              5U
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_1__REG DENALI_PHY_805
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_805__PHY_ADR5_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_806_READ_MASK                             0x000F07FFU
#define LPDDR4__DENALI_PHY_806_WRITE_MASK                            0x000F07FFU
#define LPDDR4__DENALI_PHY_806__PHY_ADR5_CLK_WR_SLAVE_DELAY_1_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_806__PHY_ADR5_CLK_WR_SLAVE_DELAY_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_806__PHY_ADR5_CLK_WR_SLAVE_DELAY_1_WIDTH          11U
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_806
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_806__PHY_ADR5_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_806__PHY_ADR_SW_MASTER_MODE_1_MASK        0x000F0000U
#define LPDDR4__DENALI_PHY_806__PHY_ADR_SW_MASTER_MODE_1_SHIFT               16U
#define LPDDR4__DENALI_PHY_806__PHY_ADR_SW_MASTER_MODE_1_WIDTH                4U
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_1__REG DENALI_PHY_806
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_1__FLD LPDDR4__DENALI_PHY_806__PHY_ADR_SW_MASTER_MODE_1

#define LPDDR4__DENALI_PHY_807_READ_MASK                             0xFF3F07FFU
#define LPDDR4__DENALI_PHY_807_WRITE_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_START_1_MASK    0x000007FFU
#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_START_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_START_1_WIDTH           11U
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_1__REG DENALI_PHY_807
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_1__FLD LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_START_1

#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_STEP_1_MASK     0x003F0000U
#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_STEP_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_STEP_1_WIDTH             6U
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_1__REG DENALI_PHY_807
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_1__FLD LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_STEP_1

#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_WAIT_1_MASK     0xFF000000U
#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_WAIT_1_SHIFT            24U
#define LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_WAIT_1_WIDTH             8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_1__REG DENALI_PHY_807
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_1__FLD LPDDR4__DENALI_PHY_807__PHY_ADR_MASTER_DELAY_WAIT_1

#define LPDDR4__DENALI_PHY_808_READ_MASK                             0x0103FFFFU
#define LPDDR4__DENALI_PHY_808_WRITE_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_808__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_808__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1_SHIFT     0U
#define LPDDR4__DENALI_PHY_808__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1_WIDTH     8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1__REG DENALI_PHY_808
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1__FLD LPDDR4__DENALI_PHY_808__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1

#define LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_1_MASK      0x0003FF00U
#define LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_1_SHIFT              8U
#define LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_1_WIDTH             10U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_1__REG DENALI_PHY_808
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_1__FLD LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_1

#define LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_MASK   0x01000000U
#define LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_SHIFT          24U
#define LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_WIDTH           1U
#define LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_WOCLR           0U
#define LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_WOSET           0U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_1__REG DENALI_PHY_808
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_1__FLD LPDDR4__DENALI_PHY_808__PHY_ADR_SW_CALVL_DVW_MIN_EN_1

#define LPDDR4__DENALI_PHY_809_READ_MASK                             0x0000000FU
#define LPDDR4__DENALI_PHY_809_WRITE_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_809__PHY_ADR_CALVL_DLY_STEP_1_MASK        0x0000000FU
#define LPDDR4__DENALI_PHY_809__PHY_ADR_CALVL_DLY_STEP_1_SHIFT                0U
#define LPDDR4__DENALI_PHY_809__PHY_ADR_CALVL_DLY_STEP_1_WIDTH                4U
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_1__REG DENALI_PHY_809
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_1__FLD LPDDR4__DENALI_PHY_809__PHY_ADR_CALVL_DLY_STEP_1

#define LPDDR4__DENALI_PHY_810_READ_MASK                             0x0000010FU
#define LPDDR4__DENALI_PHY_810_WRITE_MASK                            0x0000010FU
#define LPDDR4__DENALI_PHY_810__PHY_ADR_CALVL_CAPTURE_CNT_1_MASK     0x0000000FU
#define LPDDR4__DENALI_PHY_810__PHY_ADR_CALVL_CAPTURE_CNT_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_810__PHY_ADR_CALVL_CAPTURE_CNT_1_WIDTH             4U
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_1__REG DENALI_PHY_810
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_1__FLD LPDDR4__DENALI_PHY_810__PHY_ADR_CALVL_CAPTURE_CNT_1

#define LPDDR4__DENALI_PHY_810__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_MASK  0x00000100U
#define LPDDR4__DENALI_PHY_810__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_SHIFT          8U
#define LPDDR4__DENALI_PHY_810__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_WIDTH          1U
#define LPDDR4__DENALI_PHY_810__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_WOCLR          0U
#define LPDDR4__DENALI_PHY_810__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_WOSET          0U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_1__REG DENALI_PHY_810
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_1__FLD LPDDR4__DENALI_PHY_810__PHY_ADR_MEAS_DLY_STEP_ENABLE_1

#endif /* REG_LPDDR4_ADDRESS_SLICE_1_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

