/* ****************************************************************
 *        CADENCE                    Copyright (c) 2001-2021      *
 *                                   Cadence Design Systems, Inc. *
 *                                   All rights reserved.         *
 ******************************************************************
 *  The values calculated from this script are meant to be        *
 *  representative programmings.   The values may not reflect the *
 *  actual required programming for production use.   Please      *
 *  closely review all programmed values for technical accuracy   *
 *  before use in production parts.                               *
 ******************************************************************
 *
 *   Module:         regconfig.h
 *   Documentation:  Register programming header file
 *
 ******************************************************************
 ******************************************************************
 * WARNING:  This file was automatically generated.  Manual
 * editing may result in undetermined behavior.
 ******************************************************************
 ******************************************************************/
/* REL: texas.dallas.am62a-AM62A_LPDDR4_DDR4_32b_EW__20211118 */



static uint32_t denali_ctl_data[] =
{
	0x00000B00,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00002710,
	0x000186A0,
	0x00000005,
	0x00000064,
	0x0005B18F,
	0x0038EF90,
	0x00000005,
	0x00000E94,
	0x0005B18F,
	0x0038EF90,
	0x00000005,
	0x00000E94,
	0x01010100,
	0x01010100,
	0x01000110,
	0x02010002,
	0x0000000A,
	0x000186A0,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00020200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x08000010,
	0x00004B4B,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0000040C,
	0x00000000,
	0x00001040,
	0x00000000,
	0x00001040,
	0x00000000,
	0x05000804,
	0x00000700,
	0x09090004,
	0x00000303,
	0x00720014,
	0x09140050,
	0x00004D22,
	0x00720014,
	0x09140050,
	0x09004D22,
	0x000A0A09,
	0x040006DB,
	0x090F2005,
	0x00001B13,
	0x0E00FFCD,
	0x090F200F,
	0x00001B13,
	0x0E00FFCD,
	0x0304200F,
	0x04050002,
	0x24232423,
	0x01010008,
	0x04464607,
	0x03282803,
	0x00002828,
	0x00000101,
	0x00000000,
	0x01000000,
	0x000E0803,
	0x000000BB,
	0x0000020B,
	0x00001C64,
	0x0000020B,
	0x00001C64,
	0x00000005,
	0x00000007,
	0x00000010,
	0x00000106,
	0x00000386,
	0x00000106,
	0x00000386,
	0x03004000,
	0x00001201,
	0x000E0005,
	0x2608000E,
	0x0A050526,
	0x1B0E0A03,
	0x1B0E0A04,
	0x04010104,
	0x00010401,
	0x000F000F,
	0x02190219,
	0x02190219,
	0x00000000,
	0x03030000,
	0x05050501,
	0x04041C04,
	0x0E0A0E0A,
	0x0A04041C,
	0x030E0A0E,
	0x00000404,
	0x00000301,
	0x00000001,
	0x00000000,
	0x40020100,
	0x00038010,
	0x00050004,
	0x00000004,
	0x00040003,
	0x00040005,
	0x00030000,
	0x00050004,
	0x00000004,
	0x00002EC0,
	0x00002EC0,
	0x00002EC0,
	0x00002EC0,
	0x00002EC0,
	0x00000000,
	0x0000051D,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00000000,
	0x0000C6BC,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00071900,
	0x00000000,
	0x0000C6BC,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x03050000,
	0x040A040A,
	0x00000000,
	0x08010000,
	0x000E0808,
	0x01000000,
	0x0E080808,
	0x00000000,
	0x08080801,
	0x0000080E,
	0x00040003,
	0x00000007,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000000,
	0x00000000,
	0x00001700,
	0x0000100E,
	0x00000002,
	0x00000000,
	0x00000001,
	0x00000002,
	0x00000C00,
	0x00008000,
	0x00000C00,
	0x00008000,
	0x00000C00,
	0x00008000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0005000A,
	0x0404000D,
	0x0000000D,
	0x00BB0176,
	0x0E0E01D3,
	0x000001D3,
	0x00BB0176,
	0x0E0E01D3,
	0x000001D3,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000064,
	0x00000036,
	0x00000000,
	0x00000000,
	0x00000031,
	0x000000B1,
	0x000000B1,
	0x00000031,
	0x000000B1,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x55005555,
	0x00002755,
	0x00000027,
	0x00000027,
	0x00000027,
	0x00000027,
	0x00000027,
	0x00000000,
	0x00000000,
	0x0000002B,
	0x0000002B,
	0x0000002B,
	0x0000002B,
	0x0000002B,
	0x0000002B,
	0x00000000,
	0x00000000,
	0x00000016,
	0x00000016,
	0x00000000,
	0x00000016,
	0x00000016,
	0x00000020,
	0x00010000,
	0x00000100,
	0x00000000,
	0x00000000,
	0x00000101,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0C181511,
	0x00000304,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00020000,
	0x00400100,
	0x00080032,
	0x01000200,
	0x074A0040,
	0x00020038,
	0x00400100,
	0x0038074A,
	0x00030000,
	0x005E005E,
	0x00000100,
	0x01010000,
	0x00000000,
	0x3FFF0000,
	0x000FFF00,
	0xFFFFFFFF,
	0x00FFFF00,
	0x0B000000,
	0x0001FFFF,
	0x01010101,
	0x01010101,
	0x00000118,
	0x00000C01,
	0x00040100,
	0x00040100,
	0x00000000,
	0x00000000,
	0x01030303,
	0x00000001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000101,
	0x01010001,
	0x00010101,
	0x01090903,
	0x05020201,
	0x0E081B1B,
	0x0008030E,
	0x0B12030E,
	0x0B120314,
	0x12120814,
	0x01000000,
	0x07030701,
	0x04000103,
	0x1B000004,
	0x00000176,
	0x00000200,
	0x00000200,
	0x00000200,
	0x00000200,
	0x00000693,
	0x00000E9C,
	0x03050202,
	0x37200201,
	0x000038C8,
	0x00000200,
	0x00000200,
	0x00000200,
	0x00000200,
	0x0000FF84,
	0x000237D0,
	0x111F0402,
	0x37200C0D,
	0x000038C8,
	0x00000200,
	0x00000200,
	0x00000200,
	0x00000200,
	0x0000FF84,
	0x000237D0,
	0x111F0402,
	0x00200C0D,
	0x00000000,
	0x02000A00,
	0x00050003,
	0x00010101,
	0x00010101,
	0x00010001,
	0x00000101,
	0x02000201,
	0x02010000,
	0x06000200,
	0x00002222
};
/* #pragma DATA_SECTION (denali_ctl_data, ".wkupram"); */



static uint32_t denali_pi_data[] =
{
	0x00000B00,
	0x00000000,
	0x00000000,
	0x01000000,
	0x00000001,
	0x00010064,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000002,
	0x00000005,
	0x00050001,
	0x08000000,
	0x00010300,
	0x00000005,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x0A000100,
	0x00000028,
	0x05000000,
	0x00320000,
	0x00000000,
	0x00000000,
	0x01010102,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000001,
	0x000000AA,
	0x00000055,
	0x000000B5,
	0x0000004A,
	0x00000056,
	0x000000A9,
	0x000000A9,
	0x000000B5,
	0x00000000,
	0x00000000,
	0x00050500,
	0x0000001A,
	0x000007D0,
	0x00000300,
	0x00000000,
	0x00000000,
	0x01000000,
	0x00010101,
	0x01000000,
	0x03000000,
	0x00000000,
	0x00001705,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0A0A140A,
	0x10020101,
	0x01000210,
	0x05000404,
	0x00010001,
	0x0001000E,
	0x01010500,
	0x00010000,
	0x00000034,
	0x00000000,
	0x00000000,
	0x0000FFFF,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000000,
	0x00010001,
	0x02000008,
	0x01000200,
	0x00000100,
	0x02000100,
	0x02000200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000400,
	0x0E0D0F10,
	0x080A1413,
	0x01000009,
	0x00000302,
	0x00000008,
	0x08000000,
	0x00000100,
	0x00000000,
	0x0000AA00,
	0x00000000,
	0x00000000,
	0x00010000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000008,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00010000,
	0x00000000,
	0x00000000,
	0x0000000A,
	0x000186A0,
	0x00000100,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01000000,
	0x00010003,
	0x02000101,
	0x01030001,
	0x00010400,
	0x06000105,
	0x01070001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00010001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00010000,
	0x00000004,
	0x00000000,
	0x00010000,
	0x00000000,
	0x00080000,
	0x01180118,
	0x00262601,
	0x00000034,
	0x0000005E,
	0x0002005E,
	0x02000200,
	0x00000004,
	0x0000100C,
	0x00104000,
	0x00400000,
	0x0000000E,
	0x000000BB,
	0x0000020B,
	0x00001C64,
	0x0000020B,
	0x04001C64,
	0x01010404,
	0x00001501,
	0x00270027,
	0x01000100,
	0x00000100,
	0x00000000,
	0x05090903,
	0x01011B1B,
	0x01010101,
	0x000C0C0A,
	0x00000000,
	0x00000000,
	0x04000000,
	0x0C021212,
	0x0404020C,
	0x00090031,
	0x001B0043,
	0x001B0043,
	0x01010101,
	0x0003000D,
	0x000301D3,
	0x010001D3,
	0x000E000E,
	0x01D40100,
	0x010001D4,
	0x01D401D4,
	0x32103200,
	0x01013210,
	0x0A070601,
	0x1C11090D,
	0x1C110913,
	0x000C0013,
	0x00001000,
	0x00000C00,
	0x00001000,
	0x00000C00,
	0x02001000,
	0x0021000D,
	0x002101D3,
	0x000001D3,
	0x00001900,
	0x32000056,
	0x06000101,
	0x00250204,
	0x3212005A,
	0x17000101,
	0x00250C12,
	0x3212005A,
	0x17000101,
	0x00000C12,
	0x05030900,
	0x00040900,
	0x0000062B,
	0x20010004,
	0x0A0A0A03,
	0x280F0000,
	0x24090023,
	0x0000E638,
	0x20070050,
	0x1B131B1C,
	0x280F0000,
	0x24090023,
	0x0000E638,
	0x20070050,
	0x1B131B1C,
	0x00000000,
	0x00000176,
	0x00000E9C,
	0x000038C8,
	0x000237D0,
	0x000038C8,
	0x000237D0,
	0x0219000F,
	0x03030219,
	0x00000003,
	0x00000000,
	0x0A040503,
	0x00000A04,
	0x00002710,
	0x000186A0,
	0x00000005,
	0x00000064,
	0x0000000F,
	0x0005B18F,
	0x000186A0,
	0x00000005,
	0x00000E94,
	0x00000219,
	0x0005B18F,
	0x000186A0,
	0x00000005,
	0x00000E94,
	0x01000219,
	0x00320040,
	0x00010008,
	0x074A0040,
	0x00010038,
	0x074A0040,
	0x00000338,
	0x0028005D,
	0x03040404,
	0x00000303,
	0x01010000,
	0x04040202,
	0x67670808,
	0x67676767,
	0x67676767,
	0x67676767,
	0x00006767,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x55000000,
	0x00000000,
	0x3C00005A,
	0x00005500,
	0x00005A00,
	0x0055003C,
	0x00000000,
	0x3C00005A,
	0x00005500,
	0x00005A00,
	0x1716153C,
	0x13121118,
	0x06050414,
	0x02010007,
	0x00000003,
	0x00000000,
	0x00000000,
	0x01000000,
	0x04020201,
	0x00080804,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00000000,
	0x20002B27,
	0x00000000,
	0x00000064,
	0x00000036,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x55000000,
	0x20162B27,
	0x00000000,
	0x00000064,
	0x00000036,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x55000000,
	0x20162B27,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00000000,
	0x20002B27,
	0x00000000,
	0x00000064,
	0x00000036,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x55000000,
	0x20162B27,
	0x00000000,
	0x00000064,
	0x00000036,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x55000000,
	0x20162B27,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00000000,
	0x20002B27,
	0x00000000,
	0x00000064,
	0x00000036,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x55000000,
	0x20162B27,
	0x00000000,
	0x00000064,
	0x00000036,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x55000000,
	0x20162B27,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000031,
	0x00000000,
	0x00000000,
	0x00000000,
	0x20002B27,
	0x00000000,
	0x00000064,
	0x00000036,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x55000000,
	0x20162B27,
	0x00000000,
	0x00000064,
	0x00000036,
	0x000000B1,
	0x00000000,
	0x00000000,
	0x55000000,
	0x20162B27
};
/* #pragma DATA_SECTION (denali_pi_data, ".wkupram"); */


static uint32_t denali_data_slice0[] =
{
	0x04F00000,
	0x00000000,
	0x00030200,
	0x00000000,
	0x00000000,
	0x01030000,
	0x00010000,
	0x01030004,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x00010000,
	0x00C00001,
	0x00CC0008,
	0x00660601,
	0x00000003,
	0x00000000,
	0x00000001,
	0x0000AAAA,
	0x00005555,
	0x0000B5B5,
	0x00004A4A,
	0x00005656,
	0x0000A9A9,
	0x0000B7B7,
	0x00004848,
	0x00000000,
	0x00000000,
	0x08000000,
	0x0F000008,
	0x00000F0F,
	0x00E4E400,
	0x00071020,
	0x000C0020,
	0x00062000,
	0x00000000,
	0x55555555,
	0xAAAAAAAA,
	0x55555555,
	0xAAAAAAAA,
	0x00005555,
	0x01000100,
	0x00800180,
	0x00000001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x081F07FF,
	0x10200080,
	0x00000008,
	0x00000401,
	0x00000000,
	0x01CC0C01,
	0x1003CC0C,
	0x20000140,
	0x07FF0200,
	0x0000DD01,
	0x00100303,
	0x00000000,
	0x00000000,
	0x00041000,
	0x00100010,
	0x00100010,
	0x00100010,
	0x00100010,
	0x02040010,
	0x00000005,
	0x51516042,
	0x31C06000,
	0x07AB0340,
	0x00C0C001,
	0x0D000000,
	0x000D0C0C,
	0x42100010,
	0x010C073E,
	0x000F0C32,
	0x01000140,
	0x011E0120,
	0x00000C00,
	0x000002DD,
	0x00030200,
	0x02800000,
	0x80800000,
	0x000D2010,
	0x76543210,
	0x00000008,
	0x045D045D,
	0x045D045D,
	0x045D045D,
	0x045D045D,
	0x0000045D,
	0x0000A000,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00B200A0,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00080200,
	0x00000000,
	0x20202020,
	0x20202020,
	0xF0F02020,
	0x00000000
};
/* #pragma DATA_SECTION (denali_data_slice0, ".wkupram"); */

static uint32_t denali_data_slice1[] =
{
	0x04F00000,
	0x00000000,
	0x00030200,
	0x00000000,
	0x00000000,
	0x01030000,
	0x00010000,
	0x01030004,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x00010000,
	0x00C00001,
	0x00CC0008,
	0x00660601,
	0x00000003,
	0x00000000,
	0x00000001,
	0x0000AAAA,
	0x00005555,
	0x0000B5B5,
	0x00004A4A,
	0x00005656,
	0x0000A9A9,
	0x0000B7B7,
	0x00004848,
	0x00000000,
	0x00000000,
	0x08000000,
	0x0F000008,
	0x00000F0F,
	0x00E4E400,
	0x00071020,
	0x000C0020,
	0x00062000,
	0x00000000,
	0x55555555,
	0xAAAAAAAA,
	0x55555555,
	0xAAAAAAAA,
	0x00005555,
	0x01000100,
	0x00800180,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x081F07FF,
	0x10200080,
	0x00000008,
	0x00000401,
	0x00000000,
	0x01CC0C01,
	0x1003CC0C,
	0x20000140,
	0x07FF0200,
	0x0000DD01,
	0x00100303,
	0x00000000,
	0x00000000,
	0x00041000,
	0x00100010,
	0x00100010,
	0x00100010,
	0x00100010,
	0x02040010,
	0x00000005,
	0x51516042,
	0x31C06000,
	0x07AB0340,
	0x00C0C001,
	0x0D000000,
	0x000D0C0C,
	0x42100010,
	0x010C073E,
	0x000F0C32,
	0x01000140,
	0x011E0120,
	0x00000C00,
	0x000002DD,
	0x00030200,
	0x02800000,
	0x80800000,
	0x000D2010,
	0x76543210,
	0x00000008,
	0x045D045D,
	0x045D045D,
	0x045D045D,
	0x045D045D,
	0x0000045D,
	0x0000A000,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00B200A0,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00080200,
	0x00000000,
	0x20202020,
	0x20202020,
	0xF0F02020,
	0x00000000
};
/* #pragma DATA_SECTION (denali_data_slice1, ".wkupram"); */

static uint32_t denali_data_slice2[] =
{
	0x04F00000,
	0x00000000,
	0x00030200,
	0x00000000,
	0x00000000,
	0x01030000,
	0x00010000,
	0x01030004,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x00010000,
	0x00C00001,
	0x00CC0008,
	0x00660601,
	0x00000003,
	0x00000000,
	0x00000001,
	0x0000AAAA,
	0x00005555,
	0x0000B5B5,
	0x00004A4A,
	0x00005656,
	0x0000A9A9,
	0x0000B7B7,
	0x00004848,
	0x00000000,
	0x00000000,
	0x08000000,
	0x0F000008,
	0x00000F0F,
	0x00E4E400,
	0x00071020,
	0x000C0020,
	0x00062000,
	0x00000000,
	0x55555555,
	0xAAAAAAAA,
	0x55555555,
	0xAAAAAAAA,
	0x00005555,
	0x01000100,
	0x00800180,
	0x00000001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x081F07FF,
	0x10200080,
	0x00000008,
	0x00000401,
	0x00000000,
	0x01CC0C01,
	0x1003CC0C,
	0x20000140,
	0x07FF0200,
	0x0000DD01,
	0x00100303,
	0x00000000,
	0x00000000,
	0x00041000,
	0x00100010,
	0x00100010,
	0x00100010,
	0x00100010,
	0x02040010,
	0x00000005,
	0x51516042,
	0x31C06000,
	0x07AB0340,
	0x00C0C001,
	0x0D000000,
	0x000D0C0C,
	0x42100010,
	0x010C073E,
	0x000F0C32,
	0x01000140,
	0x011E0120,
	0x00000C00,
	0x000002DD,
	0x00030200,
	0x02800000,
	0x80800000,
	0x000D2010,
	0x76543210,
	0x00000008,
	0x045D045D,
	0x045D045D,
	0x045D045D,
	0x045D045D,
	0x0000045D,
	0x0000A000,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00B200A0,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00080200,
	0x00000000,
	0x20202020,
	0x20202020,
	0xF0F02020,
	0x00000000
};
/* #pragma DATA_SECTION (denali_data_slice2, ".wkupram"); */

static uint32_t denali_data_slice3[] =
{
	0x04F00000,
	0x00000000,
	0x00030200,
	0x00000000,
	0x00000000,
	0x01030000,
	0x00010000,
	0x01030004,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x01010000,
	0x00010000,
	0x00C00001,
	0x00CC0008,
	0x00660601,
	0x00000003,
	0x00000000,
	0x00000001,
	0x0000AAAA,
	0x00005555,
	0x0000B5B5,
	0x00004A4A,
	0x00005656,
	0x0000A9A9,
	0x0000B7B7,
	0x00004848,
	0x00000000,
	0x00000000,
	0x08000000,
	0x0F000008,
	0x00000F0F,
	0x00E4E400,
	0x00071020,
	0x000C0020,
	0x00062000,
	0x00000000,
	0x55555555,
	0xAAAAAAAA,
	0x55555555,
	0xAAAAAAAA,
	0x00005555,
	0x01000100,
	0x00800180,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000004,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x081F07FF,
	0x10200080,
	0x00000008,
	0x00000401,
	0x00000000,
	0x01CC0C01,
	0x1003CC0C,
	0x20000140,
	0x07FF0200,
	0x0000DD01,
	0x00100303,
	0x00000000,
	0x00000000,
	0x00041000,
	0x00100010,
	0x00100010,
	0x00100010,
	0x00100010,
	0x02040010,
	0x00000005,
	0x51516042,
	0x31C06000,
	0x07AB0340,
	0x00C0C001,
	0x0D000000,
	0x000D0C0C,
	0x42100010,
	0x010C073E,
	0x000F0C32,
	0x01000140,
	0x011E0120,
	0x00000C00,
	0x000002DD,
	0x00030200,
	0x02800000,
	0x80800000,
	0x000D2010,
	0x76543210,
	0x00000008,
	0x045D045D,
	0x045D045D,
	0x045D045D,
	0x045D045D,
	0x0000045D,
	0x0000A000,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00A000A0,
	0x00B200A0,
	0x01000000,
	0x00000000,
	0x00000000,
	0x00080200,
	0x00000000,
	0x20202020,
	0x20202020,
	0xF0F02020,
	0x00000000
};
/* #pragma DATA_SECTION (denali_data_slice3, ".wkupram"); */

static uint32_t denali_addr_slice0[] =
{
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000100,
	0x00000200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00400000,
	0x00000080,
	0x00DCBA98,
	0x03000000,
	0x00200000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0000002A,
	0x00000015,
	0x00000015,
	0x0000002A,
	0x00000033,
	0x0000000C,
	0x0000000C,
	0x00000033,
	0x0A418820,
	0x003F0000,
	0x000F013F,
	0x20202003,
	0x00202020,
	0x20008008,
	0x00000810,
	0x00000F00,
	0x000405CC,
	0x03000004,
	0x00030000,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x42080010,
	0x0000803E,
	0x00000001,
	0x01000002,
	0x00008000
};
/* #pragma DATA_SECTION (denali_addr_slice0, ".wkupram"); */

static uint32_t denali_addr_slice1[] =
{
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000100,
	0x00000200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00400000,
	0x00000080,
	0x00DCBA98,
	0x03000000,
	0x00200000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0000002A,
	0x00000015,
	0x00000015,
	0x0000002A,
	0x00000033,
	0x0000000C,
	0x0000000C,
	0x00000033,
	0x0A418820,
	0x00000000,
	0x000F0000,
	0x20202003,
	0x00202020,
	0x20008008,
	0x00000810,
	0x00000F00,
	0x000405CC,
	0x03000004,
	0x00030000,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x42080010,
	0x0000803E,
	0x00000001,
	0x01000002,
	0x00008000
};
/* #pragma DATA_SECTION (denali_addr_slice1, ".wkupram"); */

static uint32_t denali_addr_slice2[] =
{
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000100,
	0x00000200,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00400000,
	0x00000080,
	0x00DCBA98,
	0x03000000,
	0x00200000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x0000002A,
	0x00000015,
	0x00000015,
	0x0000002A,
	0x00000033,
	0x0000000C,
	0x0000000C,
	0x00000033,
	0x0A418820,
	0x10000000,
	0x000F0000,
	0x20202003,
	0x00202020,
	0x20008008,
	0x00000810,
	0x00000F00,
	0x000405CC,
	0x03000004,
	0x00030000,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x42080010,
	0x0000803E,
	0x00000001,
	0x01000002,
	0x00008000
};
/* #pragma DATA_SECTION (denali_addr_slice2, ".wkupram"); */

static uint32_t denali_phy_data[] =
{
	0x00000000,
	0x00010100,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00050000,
	0x04000000,
	0x00000055,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00002001,
	0x00004003,
	0x50020028,
	0x01010000,
	0x80080001,
	0x10200000,
	0x00000008,
	0x00000000,
	0x06000000,
	0x010F0F0E,
	0x00040101,
	0x0000010F,
	0x00000000,
	0x00000064,
	0x00000000,
	0x00000000,
	0x0F0F0F01,
	0x0F0F0F02,
	0x0F0F0F0F,
	0x0F0F0804,
	0x00800120,
	0x00041B42,
	0x00004201,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x03010100,
	0x00540007,
	0x000040A2,
	0x00024410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00004410,
	0x00000000,
	0x00000076,
	0x00000400,
	0x00000008,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x03000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x04102006,
	0x00041020,
	0x01C98C98,
	0x3F400000,
	0x3F3F1F3F,
	0x0000001F,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000001,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x76543210,
	0x06010198,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00040700,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000000,
	0x00000002,
	0x00000000,
	0x00000000,
	0x0001F7C4,
	0x04000004,
	0x00000000,
	0x00001142,
	0x01020000,
	0x00000080,
	0x03900390,
	0x03900390,
	0x03900390,
	0x03900390,
	0x03000300,
	0x03000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000300,
	0x00000005,
	0x3183BF77,
	0x00000000,
	0x0C000DFF,
	0x30000DFF,
	0x3F0DFF11,
	0x00EF0000,
	0x780DFFCC,
	0x00000C11,
	0x00018011,
	0x0089FF00,
	0x000C3F11,
	0x01990000,
	0x000C3F11,
	0x01990000,
	0x3F0DFF11,
	0x00EF0000,
	0x00018011,
	0x0089FF00,
	0x20040006
};
/* #pragma DATA_SECTION (denali_phy_data, ".wkupram"); */
