/*
 * DM Stub Firmware
 *
 * DM Stub Memory Trace Buffer Layer
 *
 * Copyright (C) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 * its contributors may be used to endorse or promote products derived
 * from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include <types/short_types.h>

/* LPM trace buffer size. During a low power mode cycle ~600 bytes of
 * trace data is generated. Keeping *3 as the size of buffer so that trace
 * can be saved for the last 3 LPM cycles.( 3* 600 = 1800 ~ 2KB)
 */
#define LPM_TRACE_LOG_BUF_SIZE (2U * 1024U)

#ifdef CONFIG_LPM_DM_TRACE_BUFFER
/**
 * \brief Print the input buffer at memory buffer address with "0x" prepended to indicate
 *        hexadecimal representation
 * \param str Pointer to location where input debug buffer is placed
 * \param len Length of buffer to print
 */
void lpm_trace_debug_buffer(u8 *str, u8 len);
#else
static inline void lpm_trace_debug_buffer(u8 *str __attribute__((unused)), u8 len __attribute__((unused)))
{
	return;
}
#endif

