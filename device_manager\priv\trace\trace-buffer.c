/*
 *  Copyright (C) 2018-2019 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/**
 *  \file trace-buffer.c
 *
 *  \brief Trace to memory buffer
 *
 */

/* ========================================================================== */
/*                             Include Files                                  */
/* ========================================================================== */

#include <stdint.h>
#include <string.h>
#include <drivers/hw_include/tistdtypes.h>
#include <lib/itoa.h>
#include <lib/trace.h>
#include <trace_internal.h>

/* ========================================================================== */
/*                           Macros & Typedefs                                */
/* ========================================================================== */

/* Maximum number of bytes to cleanup at initialization */
#define TRACE_LOG_MAX_NUM_CLEAN_BYTES   (100U)
/* Log Buffer Size */
#define TRACE_LOG_BUF_SIZE              (20U * 1024U)

/* ========================================================================== */
/*                         Structures and Enums                               */
/* ========================================================================== */

/* None */

/* ========================================================================== */
/*                            Global Variables                                */
/* ========================================================================== */

uint8_t tracelog_rmpm[TRACE_LOG_BUF_SIZE];

static uint8_t *logbuf_pos;

/* ========================================================================== */
/*                          Function Definitions                              */
/* ========================================================================== */

int32_t trace_print_buffer_string(const uint8_t *str)
{
    int32_t i = 0, ret = CSL_PASS;

    for (i = 0; i < TRACE_PRINT_MAX_LENGTH; i++) {
        if (str[i] != 0U) {
            ret = trace_print_buffer(str[i]);
        }

        if ((ret != CSL_PASS) || (str[i] == 0U)) {
            break;
        }
    }

    return ret;
}

static void trace_internal_print_buffer_init(void)
{
    uint8_t *pos;
    uint32_t index;

    logbuf_pos = &tracelog_rmpm[0];

    pos = logbuf_pos;

    for (index = 0U; index < TRACE_LOG_MAX_NUM_CLEAN_BYTES; index++) {
        *pos = 0U;
        pos++;
    }
}

/**
 * \brief Raw buffer output function for trace over memory buffer
 *
 * \param ch Character to output into memory buffer.
 */
int32_t trace_print_buffer(uint8_t ch)
{
    if (logbuf_pos == NULL) {
        trace_internal_print_buffer_init();
    }

    *logbuf_pos = ch;
    logbuf_pos++;

    if (logbuf_pos == &tracelog_rmpm[TRACE_LOG_BUF_SIZE - 1U]) {
        logbuf_pos = &tracelog_rmpm[0];
    }

    return CSL_PASS;
}

/**
 * \brief Raw buffer output function for trace over memory buffer
 *
 * \param channel Channel to which one should output into memory buffer.
 * \param val Value to output into memory buffer.
 */
int32_t trace_debug_buffer(uint8_t channel, uint32_t val)
{
    uint8_t str[10];

    /* This is unused for buffer as we only have one channel */
    (void) channel;

    lib_itoa(val, str, 16);

    trace_print_buffer_string((uint8_t *)"0x");
    trace_print_buffer_string(str);
    trace_print_buffer((uint8_t) '\n');

    return CSL_PASS;
}

