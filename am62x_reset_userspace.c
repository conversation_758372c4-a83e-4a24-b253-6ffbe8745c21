/*
 * AM62x Reset Detection - 用户空间示例程序
 * 
 * 这个程序演示如何在Linux用户空间中检测AM62x平台的复位原因
 * 支持多种访问方法：proc文件系统、sysfs、直接内存映射
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <errno.h>

/* AM62x 复位源寄存器地址定义 */
#define WKUP_CTRL_MMR0_CFG0_BASE    0x43000000UL
#define CSL_WKUP_CTRL_MMR_CFG0_RST_SRC  0x00018178UL
#define RESET_SRC_PHYSICAL_ADDR     (WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_RST_SRC)

/* 内存映射大小 */
#define MAP_SIZE 4096UL
#define MAP_MASK (MAP_SIZE - 1)

/* 复位源寄存器位定义 */
#define RST_SRC_MCU_RESET_PIN_MASK          (0x00000001U)
#define RST_SRC_MAIN_RESET_REQ_MASK         (0x00000004U)
#define RST_SRC_THERMAL_RST_MASK            (0x00000010U)
#define RST_SRC_DEBUG_RST_MASK              (0x00000100U)
#define RST_SRC_COLD_OUT_RST_MASK           (0x00001000U)
#define RST_SRC_WARM_OUT_RST_MASK           (0x00002000U)
#define RST_SRC_SW_MCU_WARMRST_MASK         (0x00010000U)
#define RST_SRC_SW_MAIN_WARMRST_FROM_MCU_MASK   (0x00100000U)
#define RST_SRC_SW_MAIN_WARMRST_FROM_MAIN_MASK  (0x00200000U)
#define RST_SRC_DM_WDT_RST_MASK             (0x00400000U)
#define RST_SRC_DS_MAIN_PORZ_MASK           (0x00800000U)
#define RST_SRC_SW_MAIN_POR_FROM_MCU_MASK   (0x01000000U)
#define RST_SRC_SW_MAIN_POR_FROM_MAIN_MASK  (0x02000000U)
#define RST_SRC_MAIN_ESM_ERROR_MASK         (0x40000000U)
#define RST_SRC_MCU_ESM_ERROR_MASK          (0x80000000U)

/* 冷启动和热启动掩码 */
#define COLD_BOOT_MASK  (RST_SRC_MCU_RESET_PIN_MASK | RST_SRC_COLD_OUT_RST_MASK | \
                         RST_SRC_SW_MAIN_POR_FROM_MCU_MASK | RST_SRC_SW_MAIN_POR_FROM_MAIN_MASK | \
                         RST_SRC_DS_MAIN_PORZ_MASK)

#define WARM_BOOT_MASK  (RST_SRC_WARM_OUT_RST_MASK | RST_SRC_SW_MCU_WARMRST_MASK | \
                         RST_SRC_SW_MAIN_WARMRST_FROM_MCU_MASK | RST_SRC_SW_MAIN_WARMRST_FROM_MAIN_MASK)

/* 复位原因描述结构 */
struct reset_bit_info {
    uint32_t mask;
    const char *name;
    const char *description;
    const char *boot_type;
};

static const struct reset_bit_info reset_bits[] = {
    {RST_SRC_MCU_RESET_PIN_MASK, "MCU_RESET_PIN", "MCU复位引脚", "Cold"},
    {RST_SRC_MAIN_RESET_REQ_MASK, "MAIN_RESET_REQ", "Main域复位请求", "Neutral"},
    {RST_SRC_THERMAL_RST_MASK, "THERMAL_RST", "热复位", "Neutral"},
    {RST_SRC_DEBUG_RST_MASK, "DEBUG_RST", "调试复位", "Neutral"},
    {RST_SRC_COLD_OUT_RST_MASK, "COLD_OUT_RST", "冷复位输出", "Cold"},
    {RST_SRC_WARM_OUT_RST_MASK, "WARM_OUT_RST", "热复位输出", "Warm"},
    {RST_SRC_SW_MCU_WARMRST_MASK, "SW_MCU_WARMRST", "软件MCU热复位", "Warm"},
    {RST_SRC_SW_MAIN_WARMRST_FROM_MCU_MASK, "SW_MAIN_WARMRST_FROM_MCU", "从MCU的Main域软件热复位", "Warm"},
    {RST_SRC_SW_MAIN_WARMRST_FROM_MAIN_MASK, "SW_MAIN_WARMRST_FROM_MAIN", "从Main域的软件热复位", "Warm"},
    {RST_SRC_DM_WDT_RST_MASK, "DM_WDT_RST", "DM看门狗复位", "Neutral"},
    {RST_SRC_DS_MAIN_PORZ_MASK, "DS_MAIN_PORZ", "深度睡眠Main域POR", "Cold"},
    {RST_SRC_SW_MAIN_POR_FROM_MCU_MASK, "SW_MAIN_POR_FROM_MCU", "从MCU的Main域软件POR", "Cold"},
    {RST_SRC_SW_MAIN_POR_FROM_MAIN_MASK, "SW_MAIN_POR_FROM_MAIN", "从Main域的软件POR", "Cold"},
    {RST_SRC_MAIN_ESM_ERROR_MASK, "MAIN_ESM_ERROR", "Main域ESM错误", "Neutral"},
    {RST_SRC_MCU_ESM_ERROR_MASK, "MCU_ESM_ERROR", "MCU域ESM错误", "Neutral"},
    {0, NULL, NULL, NULL}
};

/**
 * 通过proc文件系统读取复位信息
 */
int read_reset_info_proc(void)
{
    FILE *fp;
    char buffer[1024];
    
    printf("=== 通过proc文件系统读取复位信息 ===\n");
    
    fp = fopen("/proc/am62x_reset_info", "r");
    if (!fp) {
        printf("错误：无法打开 /proc/am62x_reset_info\n");
        printf("请确保AM62x复位检测驱动已加载\n");
        return -1;
    }
    
    while (fgets(buffer, sizeof(buffer), fp)) {
        printf("%s", buffer);
    }
    
    fclose(fp);
    return 0;
}

/**
 * 通过直接内存映射读取复位源寄存器
 */
uint32_t read_reset_register_direct(void)
{
    int fd;
    void *map_base, *virt_addr;
    uint32_t reset_value = 0;
    off_t target = RESET_SRC_PHYSICAL_ADDR;
    
    printf("\n=== 通过直接内存映射读取复位寄存器 ===\n");
    
    /* 打开/dev/mem */
    fd = open("/dev/mem", O_RDONLY | O_SYNC);
    if (fd == -1) {
        printf("错误：无法打开 /dev/mem: %s\n", strerror(errno));
        printf("提示：需要root权限或者在内核中启用CONFIG_DEVMEM\n");
        return 0;
    }
    
    /* 内存映射 */
    map_base = mmap(0, MAP_SIZE, PROT_READ, MAP_SHARED, fd, target & ~MAP_MASK);
    if (map_base == MAP_FAILED) {
        printf("错误：内存映射失败: %s\n", strerror(errno));
        close(fd);
        return 0;
    }
    
    /* 计算虚拟地址 */
    virt_addr = (char*)map_base + (target & MAP_MASK);
    
    /* 读取寄存器值 */
    reset_value = *((volatile uint32_t*)virt_addr);
    
    printf("复位源寄存器地址: 0x%08lx\n", target);
    printf("复位源寄存器值: 0x%08x\n", reset_value);
    
    /* 清理 */
    munmap(map_base, MAP_SIZE);
    close(fd);
    
    return reset_value;
}

/**
 * 分析复位原因
 */
void analyze_reset_cause(uint32_t reset_value)
{
    int i;
    int cold_boot = 0, warm_boot = 0;
    
    printf("\n=== 复位原因分析 ===\n");
    printf("复位源寄存器值: 0x%08x\n\n", reset_value);
    
    printf("激活的复位源:\n");
    for (i = 0; reset_bits[i].mask != 0; i++) {
        if (reset_value & reset_bits[i].mask) {
            printf("  ✓ %s: %s (%s)\n", 
                   reset_bits[i].name, 
                   reset_bits[i].description,
                   reset_bits[i].boot_type);
            
            if (strcmp(reset_bits[i].boot_type, "Cold") == 0) {
                cold_boot = 1;
            } else if (strcmp(reset_bits[i].boot_type, "Warm") == 0) {
                warm_boot = 1;
            }
        }
    }
    
    printf("\n启动类型判断:\n");
    if (cold_boot && !warm_boot) {
        printf("  结论: 冷启动 (Cold Boot)\n");
        printf("  说明: 系统从完全断电状态启动，所有硬件都被重新初始化\n");
    } else if (warm_boot && !cold_boot) {
        printf("  结论: 热启动 (Warm Boot/Hot Restart)\n");
        printf("  说明: 系统从软件复位启动，部分硬件状态可能保持\n");
    } else if (cold_boot && warm_boot) {
        printf("  结论: 混合复位 (Mixed Reset)\n");
        printf("  说明: 同时检测到冷启动和热启动标志，可能是复杂的复位序列\n");
    } else {
        printf("  结论: 未知启动类型\n");
        printf("  说明: 没有检测到明确的冷启动或热启动标志\n");
    }
    
    /* 提供Linux内核/用户空间的使用建议 */
    printf("\n在Linux中的应用建议:\n");
    if (cold_boot && !warm_boot) {
        printf("  - 执行完整的硬件初始化\n");
        printf("  - 重新加载所有配置\n");
        printf("  - 清除所有缓存和临时数据\n");
    } else if (warm_boot && !cold_boot) {
        printf("  - 可以跳过某些硬件初始化步骤\n");
        printf("  - 尝试恢复之前的状态\n");
        printf("  - 保留某些配置和缓存数据\n");
    }
}

/**
 * 显示寄存器位映射
 */
void show_register_bit_map(void)
{
    int i;
    
    printf("\n=== AM62x复位源寄存器位映射 ===\n");
    printf("寄存器地址: 0x%08lx\n\n", RESET_SRC_PHYSICAL_ADDR);
    
    printf("位  | 掩码       | 名称                          | 描述                    | 启动类型\n");
    printf("----|------------|-------------------------------|-------------------------|----------\n");
    
    for (i = 0; reset_bits[i].mask != 0; i++) {
        int bit_pos = __builtin_ctz(reset_bits[i].mask);  /* 计算位位置 */
        printf("%2d  | 0x%08x | %-29s | %-23s | %s\n",
               bit_pos,
               reset_bits[i].mask,
               reset_bits[i].name,
               reset_bits[i].description,
               reset_bits[i].boot_type);
    }
}

/**
 * 主函数
 */
int main(int argc, char *argv[])
{
    uint32_t reset_value = 0;
    int use_direct_access = 0;
    
    printf("AM62x复位检测工具 - Linux用户空间版本\n");
    printf("=====================================\n");
    
    /* 解析命令行参数 */
    if (argc > 1 && strcmp(argv[1], "--direct") == 0) {
        use_direct_access = 1;
    }
    
    if (argc > 1 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("用法: %s [选项]\n", argv[0]);
        printf("选项:\n");
        printf("  --direct    使用直接内存访问而不是proc文件系统\n");
        printf("  --help, -h  显示此帮助信息\n");
        printf("  --map       显示寄存器位映射\n");
        return 0;
    }
    
    if (argc > 1 && strcmp(argv[1], "--map") == 0) {
        show_register_bit_map();
        return 0;
    }
    
    /* 首先尝试通过proc文件系统读取 */
    if (!use_direct_access) {
        if (read_reset_info_proc() == 0) {
            return 0;  /* 成功通过proc读取，退出 */
        }
    }
    
    /* 如果proc方法失败或者用户指定直接访问，使用直接内存映射 */
    reset_value = read_reset_register_direct();
    if (reset_value != 0) {
        analyze_reset_cause(reset_value);
    }
    
    printf("\n注意事项:\n");
    printf("1. 直接内存访问需要root权限\n");
    printf("2. 建议使用内核驱动提供的proc或sysfs接口\n");
    printf("3. 复位原因寄存器在某些情况下可能需要清除\n");
    printf("4. 不同的复位类型可能需要不同的初始化策略\n");
    
    return 0;
}
