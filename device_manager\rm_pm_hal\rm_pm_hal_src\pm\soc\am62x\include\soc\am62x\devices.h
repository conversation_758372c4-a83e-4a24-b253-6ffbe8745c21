/*
 * Data version: 230918_161319
 *
 * Copyright (C) 2017-2024 Texas Instruments Incorporated - http://www.ti.com/
 * ALL RIGHTS RESERVED
 */
#ifndef SOC_AM62X_DEVICES_H
#define SOC_AM62X_DEVICES_H

#define AM62X_DEV_CMP_EVENT_INTROUTER0 1U
#define AM62X_DEV_DBGSUSPENDROUTER0 2U
#define AM62X_DEV_MAIN_GPIOMUX_INTROUTER0 3U
#define AM62X_DEV_WKUP_MCU_GPIOMUX_INTROUTER0 5U
#define AM62X_DEV_TIMESYNC_EVENT_ROUTER0 6U
#define AM62X_DEV_MCU_M4FSS0 7U
#define AM62X_DEV_MCU_M4FSS0_CBASS_0 8U
#define AM62X_DEV_MCU_M4FSS0_CORE0 9U
#define AM62X_DEV_CPSW0 13U
#define AM62X_DEV_CPT2_AGGR0 14U
#define AM62X_DEV_STM0 15U
#define AM62X_DEV_DCC0 16U
#define AM62X_DEV_DCC1 17U
#define AM62X_DEV_DCC2 18U
#define AM62X_DEV_DCC3 19U
#define AM62X_DEV_DCC4 20U
#define AM62X_DEV_DCC5 21U
#define AM62X_DEV_SMS0 22U
#define AM62X_DEV_MCU_DCC0 23U
#define AM62X_DEV_DEBUGSS_WRAP0 24U
#define AM62X_DEV_DMASS0 25U
#define AM62X_DEV_DMASS0_BCDMA_0 26U
#define AM62X_DEV_DMASS0_CBASS_0 27U
#define AM62X_DEV_DMASS0_INTAGGR_0 28U
#define AM62X_DEV_DMASS0_IPCSS_0 29U
#define AM62X_DEV_DMASS0_PKTDMA_0 30U
#define AM62X_DEV_DMASS0_RINGACC_0 33U
#define AM62X_DEV_MCU_TIMER0 35U
#define AM62X_DEV_TIMER0 36U
#define AM62X_DEV_TIMER1 37U
#define AM62X_DEV_TIMER2 38U
#define AM62X_DEV_TIMER3 39U
#define AM62X_DEV_TIMER4 40U
#define AM62X_DEV_TIMER5 41U
#define AM62X_DEV_TIMER6 42U
#define AM62X_DEV_TIMER7 43U
#define AM62X_DEV_MCU_TIMER1 48U
#define AM62X_DEV_MCU_TIMER2 49U
#define AM62X_DEV_MCU_TIMER3 50U
#define AM62X_DEV_ECAP0 51U
#define AM62X_DEV_ECAP1 52U
#define AM62X_DEV_ECAP2 53U
#define AM62X_DEV_ELM0 54U
#define AM62X_DEV_EMIF_DATA_ISO_VD 55U
#define AM62X_DEV_MMCSD0 57U
#define AM62X_DEV_MMCSD1 58U
#define AM62X_DEV_EQEP0 59U
#define AM62X_DEV_EQEP1 60U
#define AM62X_DEV_WKUP_GTC0 61U
#define AM62X_DEV_EQEP2 62U
#define AM62X_DEV_ESM0 63U
#define AM62X_DEV_WKUP_ESM0 64U
#define AM62X_DEV_FSS0 73U
#define AM62X_DEV_FSS0_FSAS_0 74U
#define AM62X_DEV_FSS0_OSPI_0 75U
#define AM62X_DEV_GICSS0 76U
#define AM62X_DEV_GPIO0 77U
#define AM62X_DEV_GPIO1 78U
#define AM62X_DEV_MCU_GPIO0 79U
#define AM62X_DEV_GPMC0 80U
#define AM62X_DEV_ICSSM0 81U
#define AM62X_DEV_LED0 83U
#define AM62X_DEV_DDPA0 85U
#define AM62X_DEV_EPWM0 86U
#define AM62X_DEV_EPWM1 87U
#define AM62X_DEV_EPWM2 88U
#define AM62X_DEV_WKUP_VTM0 95U
#define AM62X_DEV_MAILBOX0 96U
#define AM62X_DEV_MAIN2MCU_VD 97U
#define AM62X_DEV_MCAN0 98U
#define AM62X_DEV_MCU_MCRC64_0 100U
#define AM62X_DEV_MCU2MAIN_VD 101U
#define AM62X_DEV_I2C0 102U
#define AM62X_DEV_I2C1 103U
#define AM62X_DEV_I2C2 104U
#define AM62X_DEV_I2C3 105U
#define AM62X_DEV_MCU_I2C0 106U
#define AM62X_DEV_WKUP_I2C0 107U
#define AM62X_DEV_WKUP_TIMER0 110U
#define AM62X_DEV_WKUP_TIMER1 111U
#define AM62X_DEV_WKUP_UART0 114U
#define AM62X_DEV_MCRC64_0 116U
#define AM62X_DEV_WKUP_RTCSS0 117U
#define AM62X_DEV_WKUP_R5FSS0_SS0 118U
#define AM62X_DEV_WKUP_R5FSS0 119U
#define AM62X_DEV_WKUP_R5FSS0_CORE0 121U
#define AM62X_DEV_RTI0 125U
#define AM62X_DEV_RTI1 126U
#define AM62X_DEV_RTI2 127U
#define AM62X_DEV_RTI3 128U
#define AM62X_DEV_RTI15 130U
#define AM62X_DEV_MCU_RTI0 131U
#define AM62X_DEV_WKUP_RTI0 132U
#define AM62X_DEV_COMPUTE_CLUSTER0 134U
#define AM62X_DEV_A53SS0_CORE_0 135U
#define AM62X_DEV_A53SS0_CORE_1 136U
#define AM62X_DEV_A53SS0_CORE_2 137U
#define AM62X_DEV_A53SS0_CORE_3 138U
#define AM62X_DEV_PSCSS0 139U
#define AM62X_DEV_WKUP_PSC0 140U
#define AM62X_DEV_MCSPI0 141U
#define AM62X_DEV_MCSPI1 142U
#define AM62X_DEV_MCSPI2 143U
#define AM62X_DEV_UART0 146U
#define AM62X_DEV_MCU_MCSPI0 147U
#define AM62X_DEV_MCU_MCSPI1 148U
#define AM62X_DEV_MCU_UART0 149U
#define AM62X_DEV_SPINLOCK0 150U
#define AM62X_DEV_UART1 152U
#define AM62X_DEV_UART2 153U
#define AM62X_DEV_UART3 154U
#define AM62X_DEV_UART4 155U
#define AM62X_DEV_UART5 156U
#define AM62X_DEV_BOARD0 157U
#define AM62X_DEV_UART6 158U
#define AM62X_DEV_USB0 161U
#define AM62X_DEV_USB1 162U
#define AM62X_DEV_PBIST0 163U
#define AM62X_DEV_PBIST1 164U
#define AM62X_DEV_WKUP_PBIST0 165U
#define AM62X_DEV_A53SS0 166U
#define AM62X_DEV_COMPUTE_CLUSTER0_PBIST_0 167U
#define AM62X_DEV_PSC0_FW_0 168U
#define AM62X_DEV_PSC0 169U
#define AM62X_DEV_DDR16SS0 170U
#define AM62X_DEV_DEBUGSS0 171U
#define AM62X_DEV_A53_RS_BW_LIMITER0 172U
#define AM62X_DEV_A53_WS_BW_LIMITER1 173U
#define AM62X_DEV_GPU_RS_BW_LIMITER2 174U
#define AM62X_DEV_GPU_WS_BW_LIMITER3 175U
#define AM62X_DEV_WKUP_DEEPSLEEP_SOURCES0 176U
#define AM62X_DEV_EMIF_CFG_ISO_VD 177U
#define AM62X_DEV_MAIN_USB0_ISO_VD 178U
#define AM62X_DEV_MAIN_USB1_ISO_VD 179U
#define AM62X_DEV_MCU_MCU_16FF0 180U
#define AM62X_DEV_CPT2_AGGR1 181U
#define AM62X_DEV_CSI_RX_IF0 182U
#define AM62X_DEV_DCC6 183U
#define AM62X_DEV_MMCSD2 184U
#define AM62X_DEV_DPHY_RX0 185U
#define AM62X_DEV_DSS0 186U
#define AM62X_DEV_GPU0 187U
#define AM62X_DEV_MCU_MCAN0 188U
#define AM62X_DEV_MCU_MCAN1 189U
#define AM62X_DEV_MCASP0 190U
#define AM62X_DEV_MCASP1 191U
#define AM62X_DEV_MCASP2 192U
#define AM62X_DEV_CLK_32K_RC_SEL_DEV_VD 193U
#define AM62X_DEV_HSM0 225U
#define AM62X_DEV_MCU_OBSCLK_MUX_SEL_DEV_VD 227U

#endif /* SOC_AM62X_DEVICES_H */
