# Build include file for configuration
#
# Copyright (C) 2017 Texas Instruments Incorporated - http://www.ti.com/
# ALL RIGHTS RESERVED
#

# Make sure .config is generated
SUBMAKE_STATUS:=$(shell $(MAKE) -s --no-print-directory -f $(srcroot)/build/Makefile.config \
	$(foreach v,DEFAULT_TARGET objtree srctree just_clean,$v=$($v)) \
	$(filter %defconfig,$(MAKECMDGOALS)) || echo 1)

# Fake rules, real ones are in Makefile.config
PHONY += $(filter %defconfig,$(MAKECMDGOALS)) _none menuconfig oldconfig
$(filter %defconfig,$(MAKECMDGOALS)) _none:
	@(exit $(SUBMAKE_STATUS))

-include $(objtree)/.config

# Generate a header for config file
git_valid := $(shell git rev-parse --is-inside-work-tree --prefix $(srctree) 2>/dev/null)
ifeq ($(git_valid),true)
   git_rev := $(shell git describe --abbrev=5 --dirty)
else
   git_rev := unknown
endif

menuconfig:
	@echo "WARNING: The output of this tool(.config) MUST be manually reviewed."
	@echo "This rule should *NOT* be used in production flow"
	menuconfig

# And let there be some GUI
xconfig:
	@echo "WARNING: The output of this tool(.config) MUST be manually reviewed."
	@echo "This rule should *NOT* be used in production flow"
	guiconfig

oldconfig:
	@echo "WARNING: The output of this tool(.config) MUST be manually reviewed."
	@echo "This rule should *NOT* be used in production flow"
	oldconfig
