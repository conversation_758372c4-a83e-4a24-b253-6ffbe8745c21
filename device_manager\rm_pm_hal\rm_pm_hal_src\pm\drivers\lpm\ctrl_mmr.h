/*
 * Device Manager - LPM GTC Driver
 *
 * Copyright (C) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 * its contributors may be used to endorse or promote products derived
 * from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON><PERSON>NT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UE<PERSON>IAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef __CTRL_MMR_H__
#define __CTRL_MMR_H__

#include <types/short_types.h>
#include <types/sbool.h>
#include <soc_ctrl_mmr.h>

/**
 * @brief Structure to store ctrl MMR lock status
 * \param base base address of MMR
 * \param partition MMR partition index
 * \param is_locked Whether MMR partition is locked or not
 */
struct ctrl_mmr {
	u32	base;
	u32	partition;
	sbool	is_locked;
};

extern struct ctrl_mmr ctrl_mmr_data[MAX_MMR_DATA];
/**
 *  \brief  Save ctrl mmr lock status
 *
 *  \return ret      SUCCESS
 */
s32 lpm_save_mmr_lock(void);

/**
 *  \brief  Restore ctrl mmr lock status
 *
 *  \return ret      SUCCESS
 */
s32 lpm_restore_mmr_lock(void);

#endif /* __CTRL_MMR_H__ */
