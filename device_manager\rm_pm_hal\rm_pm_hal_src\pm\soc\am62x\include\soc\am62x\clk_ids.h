/*
 * Data version: 230918_161319
 *
 * Copyright (C) 2017-2024, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 * its contributors may be used to endorse or promote products derived
 * from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
#ifndef SOC_AM62X_CLK_IDS_H
#define SOC_AM62X_CLK_IDS_H

#define CLK_AM62X_GLUELOGIC_HFOSC0_CLKOUT 1
#define CLK_AM62X_GLUELOGIC_LFOSC0_CLKOUT 2
#define CLK_AM62X_GLUELOGIC_RCOSC_CLKOUT 3
#define CLK_AM62X_GLUELOGIC_RCOSC_CLK_1P0V_97P65K 4
#define CLK_AM62X_K3_DPHY_RX_MAIN_0_PPI_RX_BYTE_CLK 5
#define CLK_AM62X_MAIN_OBSCLK0_MUX_SEL_DIV_CLKOUT 6
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT0_DIV_CLKOUT 7
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT1_DIV_CLKOUT 8
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT2_DIV_CLKOUT 9
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT3_DIV_CLKOUT 10
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT4_DIV_CLKOUT 11
#define CLK_AM62X_MCU_OBSCLK_MUX_SEL_DIV_CLKOUT 12
#define CLK_AM62X_RTC_CLK_SEL_DIV_CLKOUT 13
#define CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK0_OUT 14
#define CLK_AM62X_BOARD_0_AUDIO_EXT_REFCLK1_OUT 15
#define CLK_AM62X_BOARD_0_CP_GEMAC_CPTS0_RFT_CLK_OUT 16
#define CLK_AM62X_BOARD_0_CSI0_RXCLKN_OUT 17
#define CLK_AM62X_BOARD_0_CSI0_RXCLKP_OUT 18
#define CLK_AM62X_BOARD_0_DDR0_CK0_OUT 19
#define CLK_AM62X_BOARD_0_EXT_REFCLK1_OUT 20
#define CLK_AM62X_BOARD_0_GPMC0_CLKLB_OUT 21
#define CLK_AM62X_BOARD_0_I2C0_SCL_OUT 22
#define CLK_AM62X_BOARD_0_I2C1_SCL_OUT 23
#define CLK_AM62X_BOARD_0_I2C2_SCL_OUT 24
#define CLK_AM62X_BOARD_0_I2C3_SCL_OUT 25
#define CLK_AM62X_BOARD_0_MCASP0_ACLKR_OUT 26
#define CLK_AM62X_BOARD_0_MCASP0_ACLKX_OUT 27
#define CLK_AM62X_BOARD_0_MCASP1_ACLKR_OUT 28
#define CLK_AM62X_BOARD_0_MCASP1_ACLKX_OUT 29
#define CLK_AM62X_BOARD_0_MCASP2_ACLKR_OUT 30
#define CLK_AM62X_BOARD_0_MCASP2_ACLKX_OUT 31
#define CLK_AM62X_BOARD_0_MCU_EXT_REFCLK0_OUT 32
#define CLK_AM62X_BOARD_0_MCU_I2C0_SCL_OUT 33
#define CLK_AM62X_BOARD_0_MCU_SPI0_CLK_OUT 34
#define CLK_AM62X_BOARD_0_MCU_SPI1_CLK_OUT 35
#define CLK_AM62X_BOARD_0_MMC0_CLKLB_OUT 36
#define CLK_AM62X_BOARD_0_MMC0_CLK_OUT 37
#define CLK_AM62X_BOARD_0_MMC1_CLKLB_OUT 38
#define CLK_AM62X_BOARD_0_MMC1_CLK_OUT 39
#define CLK_AM62X_BOARD_0_MMC2_CLKLB_OUT 40
#define CLK_AM62X_BOARD_0_MMC2_CLK_OUT 41
#define CLK_AM62X_BOARD_0_OSPI0_DQS_OUT 42
#define CLK_AM62X_BOARD_0_OSPI0_LBCLKO_OUT 43
#define CLK_AM62X_BOARD_0_RGMII1_RXC_OUT 44
#define CLK_AM62X_BOARD_0_RGMII1_TXC_OUT 45
#define CLK_AM62X_BOARD_0_RGMII2_RXC_OUT 46
#define CLK_AM62X_BOARD_0_RGMII2_TXC_OUT 47
#define CLK_AM62X_BOARD_0_RMII1_REF_CLK_OUT 48
#define CLK_AM62X_BOARD_0_RMII2_REF_CLK_OUT 49
#define CLK_AM62X_BOARD_0_SPI0_CLK_OUT 50
#define CLK_AM62X_BOARD_0_SPI1_CLK_OUT 51
#define CLK_AM62X_BOARD_0_SPI2_CLK_OUT 52
#define CLK_AM62X_BOARD_0_TCK_OUT 53
#define CLK_AM62X_BOARD_0_VOUT0_EXTPCLKIN_OUT 54
#define CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF0 55
#define CLK_AM62X_CPSW_3GUSS_MAIN_0_CPTS_GENF1 56
#define CLK_AM62X_CPSW_3GUSS_MAIN_0_MDIO_MDCLK_O 57
#define CLK_AM62X_CPSW_3GUSS_MAIN_0_RGMII1_TXC_O 58
#define CLK_AM62X_CPSW_3GUSS_MAIN_0_RGMII2_TXC_O 59
#define CLK_AM62X_DEBUGSS_K3_WRAP_CV0_MAIN_0_CSTPIU_TRACECLK 60
#define CLK_AM62X_DMTIMER_DMC1MS_MAIN_0_TIMER_PWM 61
#define CLK_AM62X_DMTIMER_DMC1MS_MAIN_1_TIMER_PWM 62
#define CLK_AM62X_DMTIMER_DMC1MS_MAIN_2_TIMER_PWM 63
#define CLK_AM62X_DMTIMER_DMC1MS_MAIN_3_TIMER_PWM 64
#define CLK_AM62X_DMTIMER_DMC1MS_MAIN_4_TIMER_PWM 65
#define CLK_AM62X_DMTIMER_DMC1MS_MAIN_5_TIMER_PWM 66
#define CLK_AM62X_DMTIMER_DMC1MS_MAIN_6_TIMER_PWM 67
#define CLK_AM62X_DMTIMER_DMC1MS_MAIN_7_TIMER_PWM 68
#define CLK_AM62X_DMTIMER_DMC1MS_MCU_0_TIMER_PWM 69
#define CLK_AM62X_DMTIMER_DMC1MS_MCU_1_TIMER_PWM 70
#define CLK_AM62X_DMTIMER_DMC1MS_MCU_2_TIMER_PWM 71
#define CLK_AM62X_DMTIMER_DMC1MS_MCU_3_TIMER_PWM 72
#define CLK_AM62X_EMMCSD4SS_MAIN_0_EMMCSDSS_IO_CLK_O 73
#define CLK_AM62X_EMMCSD4SS_MAIN_1_EMMCSDSS_IO_CLK_O 74
#define CLK_AM62X_EMMCSD8SS_MAIN_0_EMMCSDSS_IO_CLK_O 75
#define CLK_AM62X_FSS_UL_MAIN_0_OSPI_0_OSPI_OCLK_CLK 76
#define CLK_AM62X_GPMC_MAIN_0_PO_GPMC_DEV_CLK 77
#define CLK_AM62X_HSDIV0_16FFT_MCU_32KHZ_GEN_0_HSDIVOUT0_CLK 78
#define CLK_AM62X_K3_DSS_UL_MAIN_0_DPI_1_OUT_CLK 79
#define CLK_AM62X_MCASP_MAIN_0_MCASP_ACLKR_POUT 80
#define CLK_AM62X_MCASP_MAIN_0_MCASP_ACLKX_POUT 81
#define CLK_AM62X_MCASP_MAIN_0_MCASP_AFSR_POUT 82
#define CLK_AM62X_MCASP_MAIN_0_MCASP_AFSX_POUT 83
#define CLK_AM62X_MCASP_MAIN_0_MCASP_AHCLKR_POUT 84
#define CLK_AM62X_MCASP_MAIN_0_MCASP_AHCLKX_POUT 85
#define CLK_AM62X_MCASP_MAIN_1_MCASP_ACLKR_POUT 86
#define CLK_AM62X_MCASP_MAIN_1_MCASP_ACLKX_POUT 87
#define CLK_AM62X_MCASP_MAIN_1_MCASP_AFSR_POUT 88
#define CLK_AM62X_MCASP_MAIN_1_MCASP_AFSX_POUT 89
#define CLK_AM62X_MCASP_MAIN_1_MCASP_AHCLKR_POUT 90
#define CLK_AM62X_MCASP_MAIN_1_MCASP_AHCLKX_POUT 91
#define CLK_AM62X_MCASP_MAIN_2_MCASP_ACLKR_POUT 92
#define CLK_AM62X_MCASP_MAIN_2_MCASP_ACLKX_POUT 93
#define CLK_AM62X_MCASP_MAIN_2_MCASP_AFSR_POUT 94
#define CLK_AM62X_MCASP_MAIN_2_MCASP_AFSX_POUT 95
#define CLK_AM62X_MCASP_MAIN_2_MCASP_AHCLKR_POUT 96
#define CLK_AM62X_MCASP_MAIN_2_MCASP_AHCLKX_POUT 97
#define CLK_AM62X_MSHSI2C_MAIN_0_PORSCL 98
#define CLK_AM62X_MSHSI2C_MAIN_1_PORSCL 99
#define CLK_AM62X_MSHSI2C_MAIN_2_PORSCL 100
#define CLK_AM62X_MSHSI2C_MAIN_3_PORSCL 101
#define CLK_AM62X_MSHSI2C_MCU_0_PORSCL 102
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_0_FOUTVCOP_CLK 103
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_0_FOUTPOSTDIV_CLK 104
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_1_FOUTVCOP_CLK 105
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_1_FOUTPOSTDIV_CLK 106
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_12_FOUTVCOP_CLK 107
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_15_FOUTVCOP_CLK 108
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_16_FOUTVCOP_CLK 109
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_17_FOUTVCOP_CLK 110
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_2_FOUTVCOP_CLK 111
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_2_FOUTPOSTDIV_CLK 112
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MAIN_8_FOUTVCOP_CLK 113
#define CLK_AM62X_PLLFRACF2_SSMOD_16FFT_MCU_0_FOUTVCOP_CLK 114
#define CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT5_CLK 115
#define CLK_AM62X_POSTDIV1_16FFT_MAIN_1_HSDIVOUT6_CLK 116
#define CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT5_CLK 117
#define CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT6_CLK 118
#define CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT7_CLK 119
#define CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT8_CLK 120
#define CLK_AM62X_POSTDIV4_16FF_MAIN_0_HSDIVOUT9_CLK 121
#define CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT5_CLK 122
#define CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT6_CLK 123
#define CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT7_CLK 124
#define CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT8_CLK 125
#define CLK_AM62X_POSTDIV4_16FF_MAIN_2_HSDIVOUT9_CLK 126
#define CLK_AM62X_SAM62_A53_512KB_WRAP_MAIN_0_ARM_COREPACK_0_A53_DIVH_CLK4_OBSCLK_OUT_CLK 127
#define CLK_AM62X_SPI_MAIN_0_IO_CLKSPIO_CLK 128
#define CLK_AM62X_SPI_MAIN_1_IO_CLKSPIO_CLK 129
#define CLK_AM62X_SPI_MAIN_2_IO_CLKSPIO_CLK 130
#define CLK_AM62X_SPI_MCU_0_IO_CLKSPIO_CLK 131
#define CLK_AM62X_SPI_MCU_1_IO_CLKSPIO_CLK 132
#define CLK_AM62X_AUDIO_REFCLKN_OUT0 133
#define CLK_AM62X_AUDIO_REFCLKN_OUT1 134
#define CLK_AM62X_CLK_32K_RC_SEL_DIV_CLKOUT 135
#define CLK_AM62X_MAIN_EMMCSD0_IO_CLKLB_SEL_OUT0 136
#define CLK_AM62X_MAIN_EMMCSD1_IO_CLKLB_SEL_OUT0 137
#define CLK_AM62X_MAIN_EMMCSD2_IO_CLKLB_SEL_OUT0 138
#define CLK_AM62X_MAIN_OSPI_LOOPBACK_CLK_SEL_OUT0 139
#define CLK_AM62X_MAIN_USB0_REFCLK_SEL_OUT0 140
#define CLK_AM62X_MAIN_USB1_REFCLK_SEL_OUT0 141
#define CLK_AM62X_MAIN_SPI0_MSTR_LP_CLKSEL_OUT0 142
#define CLK_AM62X_MAIN_SPI1_MSTR_LP_CLKSEL_OUT0 143
#define CLK_AM62X_MAIN_SPI2_MSTR_LP_CLKSEL_OUT0 144
#define CLK_AM62X_MCASPN_AHCLKSEL_AHCLKR_OUT0 145
#define CLK_AM62X_MCASPN_AHCLKSEL_AHCLKR_OUT1 146
#define CLK_AM62X_MCASPN_AHCLKSEL_AHCLKR_OUT2 147
#define CLK_AM62X_MCASPN_AHCLKSEL_AHCLKX_OUT0 148
#define CLK_AM62X_MCASPN_AHCLKSEL_AHCLKX_OUT1 149
#define CLK_AM62X_MCASPN_AHCLKSEL_AHCLKX_OUT2 150
#define CLK_AM62X_MCASPN_CLKSEL_AUXCLK_OUT0 151
#define CLK_AM62X_MCASPN_CLKSEL_AUXCLK_OUT1 152
#define CLK_AM62X_MCASPN_CLKSEL_AUXCLK_OUT2 153
#define CLK_AM62X_HSDIV0_16FFT_MAIN_12_HSDIVOUT0_CLK 154
#define CLK_AM62X_HSDIV0_16FFT_MAIN_16_HSDIVOUT0_CLK 155
#define CLK_AM62X_HSDIV0_16FFT_MAIN_17_HSDIVOUT0_CLK 156
#define CLK_AM62X_HSDIV0_16FFT_MAIN_8_HSDIVOUT0_CLK 157
#define CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT0_CLK 158
#define CLK_AM62X_HSDIV1_16FFT_MAIN_15_HSDIVOUT1_CLK 159
#define CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT0_CLK 160
#define CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT1_CLK 161
#define CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT2_CLK 162
#define CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT3_CLK 163
#define CLK_AM62X_HSDIV4_16FFT_MAIN_0_HSDIVOUT4_CLK 164
#define CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT0_CLK 165
#define CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT1_CLK 166
#define CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT2_CLK 167
#define CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT3_CLK 168
#define CLK_AM62X_HSDIV4_16FFT_MAIN_1_HSDIVOUT4_CLK 169
#define CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT0_CLK 170
#define CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT1_CLK 171
#define CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT2_CLK 172
#define CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT3_CLK 173
#define CLK_AM62X_HSDIV4_16FFT_MAIN_2_HSDIVOUT4_CLK 174
#define CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT0_CLK 175
#define CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT4_CLK 176
#define CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_SYSCLKOUT_CLK 177
#define CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV1_CLK_CLK 178
#define CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_SYSCLKOUT_CLK 179
#define CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV1_CLK_CLK 180
#define CLK_AM62X_CLKOUT0_CTRL_OUT0 181
#define CLK_AM62X_CLK_32K_RC_SEL_OUT0 182
#define CLK_AM62X_MAIN_CP_GEMAC_CPTS_CLK_SEL_OUT0 183
#define CLK_AM62X_MAIN_DSS_DPI1_OUT0 184
#define CLK_AM62X_MAIN_EMMCSD0_REFCLK_SEL_OUT0 185
#define CLK_AM62X_MAIN_EMMCSD1_REFCLK_SEL_OUT0 186
#define CLK_AM62X_MAIN_EMMCSD2_REFCLK_SEL_OUT0 187
#define CLK_AM62X_MAIN_GPMC_FCLK_SEL_OUT0 188
#define CLK_AM62X_MAIN_GTCCLK_SEL_OUT0 189
#define CLK_AM62X_MAIN_ICSSM_CORE_CLK_SEL_OUT0 190
#define CLK_AM62X_MAIN_ICSSM_IEPCLK_SEL_OUT0 191
#define CLK_AM62X_MAIN_MCANN_CLK_SEL_OUT0 192
#define CLK_AM62X_MAIN_OBSCLK0_MUX_SEL_OUT0 193
#define CLK_AM62X_MAIN_OSPI_REF_CLK_SEL_OUT0 194
#define CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT0 195
#define CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT1 196
#define CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT2 197
#define CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT3 198
#define CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT4 199
#define CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT5 200
#define CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT6 201
#define CLK_AM62X_MAIN_TIMERCLKN_SEL_OUT7 202
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT0 203
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT1 204
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT2 205
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT3 206
#define CLK_AM62X_MAIN_WWDTCLKN_SEL_OUT4 207
#define CLK_AM62X_MCU_M4FSS_CLKSEL_OUT0 208
#define CLK_AM62X_MCU_OBSCLK_MUX_SEL_OUT0 209
#define CLK_AM62X_RTC_CLK_SEL_OUT0 210
#define CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT0 211
#define CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT1 212
#define CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT2 213
#define CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT3 214
#define CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT4 215
#define CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT5 216
#define CLK_AM62X_USART_PROGRAMMABLE_CLOCK_DIVIDER_OUT6 217
#define CLK_AM62X_WKUP_CLKOUT_SEL_OUT0 218
#define CLK_AM62X_WKUP_CLKSEL_OUT0 219
#define CLK_AM62X_MCU_OBSCLK_DIV_OUT0 220
#define CLK_AM62X_OSBCLK0_DIV_OUT0 221
#define CLK_AM62X_MAIN_USART0_FCLK_SEL_OUT0 222
#define CLK_AM62X_MAIN_USART1_FCLK_SEL_OUT0 223
#define CLK_AM62X_MAIN_USART2_FCLK_SEL_OUT0 224
#define CLK_AM62X_MAIN_USART3_FCLK_SEL_OUT0 225
#define CLK_AM62X_MAIN_USART4_FCLK_SEL_OUT0 226
#define CLK_AM62X_MAIN_USART5_FCLK_SEL_OUT0 227
#define CLK_AM62X_MAIN_USART6_FCLK_SEL_OUT0 228
#define CLK_AM62X_MCU_OBSCLK_OUTMUX_SEL_OUT0 229
#define CLK_AM62X_MCU_GPIO0_CLKSEL_OUT0 230
#define CLK_AM62X_MCU_MCANN_CLK_SEL_OUT0 231
#define CLK_AM62X_MCU_MCANN_CLK_SEL_OUT1 232
#define CLK_AM62X_MCU_TIMERCLKN_SEL_OUT0_DIV_CLKOUT 233
#define CLK_AM62X_MCU_TIMERCLKN_SEL_OUT1_DIV_CLKOUT 234
#define CLK_AM62X_MCU_TIMERCLKN_SEL_OUT2_DIV_CLKOUT 235
#define CLK_AM62X_MCU_TIMERCLKN_SEL_OUT3_DIV_CLKOUT 236
#define CLK_AM62X_MCU_WWDTCLK_SEL_DIV_CLKOUT 237
#define CLK_AM62X_MCU_SPI0_MSTR_LP_CLKSEL_OUT0 238
#define CLK_AM62X_MCU_SPI1_MSTR_LP_CLKSEL_OUT0 239
#define CLK_AM62X_WKUP_TIMERCLKN_SEL_OUT0_DIV_CLKOUT 240
#define CLK_AM62X_WKUP_TIMERCLKN_SEL_OUT1_DIV_CLKOUT 241
#define CLK_AM62X_WKUP_WWDTCLK_SEL_DIV_CLKOUT 242
#define CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT1_CLK 243
#define CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT2_CLK 244
#define CLK_AM62X_HSDIV4_16FFT_MCU_0_HSDIVOUT3_CLK 245
#define CLK_AM62X_MSHSI2C_WKUP_0_PORSCL 246
#define CLK_AM62X_MCU_TIMERCLKN_SEL_OUT0 247
#define CLK_AM62X_MCU_TIMERCLKN_SEL_OUT1 248
#define CLK_AM62X_MCU_TIMERCLKN_SEL_OUT2 249
#define CLK_AM62X_MCU_TIMERCLKN_SEL_OUT3 250
#define CLK_AM62X_MCU_WWDTCLK_SEL_OUT0 251
#define CLK_AM62X_WKUP_TIMERCLKN_SEL_OUT0 252
#define CLK_AM62X_WKUP_TIMERCLKN_SEL_OUT1 253
#define CLK_AM62X_WKUP_WWDTCLK_SEL_OUT0 254
#define CLK_AM62X_SAM62_PLL_CTRL_WRAP_MAIN_0_CHIP_DIV24_CLK_CLK 255
#define CLK_AM62X_SAM62_PLL_CTRL_WRAP_MCU_0_CHIP_DIV24_CLK_CLK 256

#endif /* SOC_AM62X_CLK_IDS_H */
