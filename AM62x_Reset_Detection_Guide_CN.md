# AM62x平台冷启动与热启动检测技术指南

## 概述

本文档详细说明如何在TI AM62x平台上区分冷启动（Cold Boot）和热启动（Warm Boot/Hot Restart），包括硬件寄存器分析、Linux内核驱动实现和用户空间访问方法。

## 硬件寄存器分析

### 复位源寄存器

AM62x平台提供了专门的复位源寄存器来记录系统复位的原因：

#### 主要寄存器地址
- **WKUP_CTRL_MMR_CFG0_RST_SRC**: `0x43000000 + 0x18178 = 0x43018178`
- **MCU_CTRL_MMR_CFG0_RST_SRC**: `0x04500000 + 0x18178 = 0x04518178`

#### 寄存器位定义

| 位 | 掩码 | 名称 | 描述 | 启动类型 |
|----|------|------|------|----------|
| 0 | 0x00000001 | MCU_RESET_PIN | MCU复位引脚 | 冷启动 |
| 2 | 0x00000004 | MAIN_RESET_REQ | Main域复位请求 | 中性 |
| 4 | 0x00000010 | THERMAL_RST | 热复位 | 中性 |
| 8 | 0x00000100 | DEBUG_RST | 调试复位 | 中性 |
| 12 | 0x00001000 | COLD_OUT_RST | 冷复位输出 | 冷启动 |
| 13 | 0x00002000 | WARM_OUT_RST | 热复位输出 | 热启动 |
| 16 | 0x00010000 | SW_MCU_WARMRST | 软件MCU热复位 | 热启动 |
| 20 | 0x00100000 | SW_MAIN_WARMRST_FROM_MCU | 从MCU的Main域软件热复位 | 热启动 |
| 21 | 0x00200000 | SW_MAIN_WARMRST_FROM_MAIN | 从Main域的软件热复位 | 热启动 |
| 22 | 0x00400000 | DM_WDT_RST | DM看门狗复位 | 中性 |
| 23 | 0x00800000 | DS_MAIN_PORZ | 深度睡眠Main域POR | 冷启动 |
| 24 | 0x01000000 | SW_MAIN_POR_FROM_MCU | 从MCU的Main域软件POR | 冷启动 |
| 25 | 0x02000000 | SW_MAIN_POR_FROM_MAIN | 从Main域的软件POR | 冷启动 |
| 30 | 0x40000000 | MAIN_ESM_ERROR | Main域ESM错误 | 中性 |
| 31 | 0x80000000 | MCU_ESM_ERROR | MCU域ESM错误 | 中性 |

### 启动类型判断逻辑

#### 冷启动指示位
```c
#define COLD_BOOT_MASK  (0x00000001 | 0x00001000 | 0x00800000 | 0x01000000 | 0x02000000)
// MCU_RESET_PIN | COLD_OUT_RST | DS_MAIN_PORZ | SW_MAIN_POR_FROM_MCU | SW_MAIN_POR_FROM_MAIN
```

#### 热启动指示位
```c
#define WARM_BOOT_MASK  (0x00002000 | 0x00010000 | 0x00100000 | 0x00200000)
// WARM_OUT_RST | SW_MCU_WARMRST | SW_MAIN_WARMRST_FROM_MCU | SW_MAIN_WARMRST_FROM_MAIN
```

## Linux内核驱动实现

### 驱动功能特性
1. **寄存器访问**: 安全地映射和读取复位源寄存器
2. **启动类型检测**: 自动分析复位原因并判断启动类型
3. **proc接口**: 提供`/proc/am62x_reset_info`用户空间接口
4. **设备树支持**: 支持设备树配置和平台驱动模型

### 核心函数

#### 复位源读取
```c
static u32 am62x_read_reset_source(void)
{
    u32 reset_cause = 0;
    if (reset_data && reset_data->wkup_ctrl_base) {
        reset_cause = readl(reset_data->wkup_ctrl_base + CSL_WKUP_CTRL_MMR_CFG0_RST_SRC);
    }
    return reset_cause;
}
```

#### 启动类型判断
```c
static bool am62x_is_cold_boot(u32 reset_cause)
{
    return (reset_cause & COLD_BOOT_MASK) != 0;
}

static bool am62x_is_warm_boot(u32 reset_cause)
{
    return (reset_cause & WARM_BOOT_MASK) != 0;
}
```

### 设备树配置

```dts
am62x_reset_detection: reset-detection@43000000 {
    compatible = "ti,am62x-reset-detection";
    reg = <0x0 0x43000000 0x0 0x20000>;
    status = "okay";
};
```

## 用户空间访问方法

### 方法1: proc文件系统（推荐）
```bash
cat /proc/am62x_reset_info
```

### 方法2: 直接内存映射
```c
// 需要root权限，通过/dev/mem访问
fd = open("/dev/mem", O_RDONLY | O_SYNC);
map_base = mmap(0, MAP_SIZE, PROT_READ, MAP_SHARED, fd, target & ~MAP_MASK);
reset_value = *((volatile uint32_t*)virt_addr);
```

### 方法3: sysfs接口（可扩展）
```bash
# 如果驱动实现了sysfs接口
cat /sys/class/reset-detection/am62x/reset_cause
cat /sys/class/reset-detection/am62x/boot_type
```

## 实际应用场景

### Linux内核中的应用

#### 1. 驱动初始化策略
```c
static int my_driver_probe(struct platform_device *pdev)
{
    u32 reset_cause = am62x_read_reset_source();
    
    if (am62x_is_cold_boot(reset_cause)) {
        // 冷启动：执行完整初始化
        full_hardware_init();
        clear_all_caches();
        load_default_config();
    } else if (am62x_is_warm_boot(reset_cause)) {
        // 热启动：快速恢复
        quick_hardware_check();
        restore_previous_state();
    }
    
    return 0;
}
```

#### 2. 电源管理策略
```c
static int pm_resume_handler(struct device *dev)
{
    u32 reset_cause = am62x_read_reset_source();
    
    if (reset_cause & RST_SRC_DS_MAIN_PORZ_MASK) {
        // 从深度睡眠恢复
        restore_deep_sleep_context();
    }
    
    return 0;
}
```

### 用户空间应用

#### 1. 系统启动脚本
```bash
#!/bin/bash
# /etc/init.d/reset-detection

RESET_INFO=$(cat /proc/am62x_reset_info 2>/dev/null)
if echo "$RESET_INFO" | grep -q "Cold Boot"; then
    echo "检测到冷启动，执行完整初始化..."
    # 执行冷启动特定的初始化
    /usr/bin/cold-boot-init.sh
elif echo "$RESET_INFO" | grep -q "Warm Boot"; then
    echo "检测到热启动，执行快速恢复..."
    # 执行热启动特定的恢复
    /usr/bin/warm-boot-restore.sh
fi
```

#### 2. 应用程序状态恢复
```c
int main(int argc, char *argv[])
{
    uint32_t reset_cause = read_reset_register();
    
    if (reset_cause & WARM_BOOT_MASK) {
        // 尝试恢复之前的应用状态
        if (restore_application_state() == 0) {
            printf("成功恢复应用状态\n");
            return 0;
        }
    }
    
    // 冷启动或恢复失败，执行正常初始化
    initialize_application();
    return 0;
}
```

## 编译和使用说明

### 内核驱动编译
```makefile
obj-m += am62x_reset_detection.o

all:
	make -C /lib/modules/$(shell uname -r)/build M=$(PWD) modules

clean:
	make -C /lib/modules/$(shell uname -r)/build M=$(PWD) clean

install:
	make -C /lib/modules/$(shell uname -r)/build M=$(PWD) modules_install
	depmod -a
```

### 用户空间程序编译
```bash
gcc -o am62x_reset_userspace am62x_reset_userspace.c
```

### 使用示例
```bash
# 加载内核驱动
sudo insmod am62x_reset_detection.ko

# 查看复位信息
cat /proc/am62x_reset_info

# 使用用户空间工具
./am62x_reset_userspace
./am62x_reset_userspace --direct  # 直接内存访问
./am62x_reset_userspace --map     # 显示寄存器位映射
```

## 注意事项和最佳实践

### 1. 权限要求
- 内核驱动需要适当的设备树配置
- 直接内存访问需要root权限
- 建议使用内核驱动提供的接口而不是直接访问

### 2. 寄存器清除
- 某些复位原因位可能需要软件清除
- 使用`SOC_clearResetCauseMainMcuDomain()`函数清除

### 3. 多域考虑
- AM62x有多个电源域（Main、MCU、WKUP）
- 不同域的复位可能有不同的影响
- 需要考虑域间的复位传播

### 4. 时序考虑
- 复位检测应该在系统初始化的早期阶段进行
- 避免在复位检测完成前进行关键的硬件操作

### 5. 调试支持
- 启用内核调试选项以获得详细的复位信息
- 使用串口输出进行早期调试
- 考虑添加复位历史记录功能

## 总结

AM62x平台提供了完善的硬件机制来区分冷启动和热启动。通过正确使用复位源寄存器，可以实现：

1. **精确的启动类型检测**：基于硬件寄存器的可靠检测
2. **优化的初始化策略**：根据启动类型选择不同的初始化路径
3. **灵活的软件架构**：支持内核驱动和用户空间多种访问方式
4. **良好的系统性能**：通过跳过不必要的初始化步骤提高启动速度

这种机制对于需要快速恢复、状态保持或差异化初始化的嵌入式Linux系统特别有价值。
