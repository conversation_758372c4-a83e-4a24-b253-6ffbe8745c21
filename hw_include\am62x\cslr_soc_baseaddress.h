/********************************************************************
*
* SOC BASEADDRESS. header file
*
* Copyright (C) 2015-2019 Texas Instruments Incorporated.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*
*    Redistributions of source code must retain the above copyright
*    notice, this list of conditions and the following disclaimer.
*
*    Redistributions in binary form must reproduce the above copyright
*    notice, this list of conditions and the following disclaimer in the
*    documentation and/or other materials provided with the
*    distribution.
*
*    Neither the name of Texas Instruments Incorporated nor the names of
*    its contributors may be used to endorse or promote products derived
*    from this software without specific prior written permission.
*
*  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
*  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
*  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
*  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
*  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
*  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
*  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
*  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
*  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
*  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
*  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*
*/
#ifndef CSLR_SOC_BASEADDRESS_H_
#define CSLR_SOC_BASEADDRESS_H_

#include <drivers/hw_include/cslr.h>
#include <drivers/hw_include/tistdtypes.h>
#ifdef __cplusplus
extern "C"
{
#endif

/*
* Auto-generated SoC Base Address File:
*/

#define CSL_PSRAMECC0_RAM_BASE                                                                     (0x0UL)
#define CSL_PSRAMECC0_RAM_SIZE                                                                     (0x400UL)
#define CSL_PADCFG_CTRL0_CFG0_BASE                                                                 (0xf0000UL)
#define CSL_PADCFG_CTRL0_CFG0_SIZE                                                                 (0x8000UL)
#define CSL_CTRL_MMR0_CFG0_BASE                                                                    (0x100000UL)
#define CSL_CTRL_MMR0_CFG0_SIZE                                                                    (0x20000UL)
#define CSL_CBASS_DBG0_ERR_BASE                                                                    (0x200000UL)
#define CSL_CBASS_DBG0_ERR_SIZE                                                                    (0x400UL)
#define CSL_CBASS_INFRA1_ERR_BASE                                                                  (0x210000UL)
#define CSL_CBASS_INFRA1_ERR_SIZE                                                                  (0x400UL)
#define CSL_CBASS_FW0_ERR_BASE                                                                     (0x220000UL)
#define CSL_CBASS_FW0_ERR_SIZE                                                                     (0x400UL)
#define CSL_CBASS_IPCSS0_ERR_BASE                                                                  (0x230000UL)
#define CSL_CBASS_IPCSS0_ERR_SIZE                                                                  (0x400UL)
#define CSL_CBASS_MCASP0_ERR_BASE                                                                  (0x240000UL)
#define CSL_CBASS_MCASP0_ERR_SIZE                                                                  (0x400UL)
#define CSL_EFUSE0_BASE                                                                            (0x300000UL)
#define CSL_EFUSE0_SIZE                                                                            (0x100UL)
#define CSL_PBIST1_BASE                                                                            (0x310000UL)
#define CSL_PBIST1_SIZE                                                                            (0x400UL)
#define CSL_COMPUTE_CLUSTER0_PBIST_BASE                                                            (0x330000UL)
#define CSL_COMPUTE_CLUSTER0_PBIST_SIZE                                                            (0x400UL)
#define CSL_PSCSS0_BASE                                                                            (0x400000UL)
#define CSL_PSCSS0_SIZE                                                                            (0x1000UL)
#define CSL_PLLCTRL0_BASE                                                                          (0x410000UL)
#define CSL_PLLCTRL0_SIZE                                                                          (0x200UL)
#define CSL_ESM0_CFG_BASE                                                                          (0x420000UL)
#define CSL_ESM0_CFG_SIZE                                                                          (0x1000UL)
#define CSL_DFTSS0_BASE                                                                            (0x500000UL)
#define CSL_DFTSS0_SIZE                                                                            (0x400UL)
#define CSL_DDPA0_BASE                                                                             (0x580000UL)
#define CSL_DDPA0_SIZE                                                                             (0x400UL)
#define CSL_GPIO0_BASE                                                                             (0x600000UL)
#define CSL_GPIO0_SIZE                                                                             (0x100UL)
#define CSL_GPIO1_BASE                                                                             (0x601000UL)
#define CSL_GPIO1_SIZE                                                                             (0x100UL)
#define CSL_PLL0_CFG_BASE                                                                          (0x680000UL)
#define CSL_PLL0_CFG_SIZE                                                                          (0x20000UL)
#define CSL_PSRAMECC0_ECC_AGGR_BASE                                                                (0x700000UL)
#define CSL_PSRAMECC0_ECC_AGGR_SIZE                                                                (0x400UL)
#define CSL_PSCSS0_REGS_BASE                                                                       (0x700400UL)
#define CSL_PSCSS0_REGS_SIZE                                                                       (0x400UL)
#define CSL_USB0_DEBUG_TRACE_MMR_TRACE_VBUSP_USB2SS_DEBUG_TRACE_BASE                               (0x703000UL)
#define CSL_USB0_DEBUG_TRACE_MMR_TRACE_VBUSP_USB2SS_DEBUG_TRACE_SIZE                               (0x200UL)
#define CSL_CPSW0_ECC_BASE                                                                         (0x704000UL)
#define CSL_CPSW0_ECC_SIZE                                                                         (0x400UL)
#define CSL_MMCSD0_ECC_AGGR_RXMEM_BASE                                                             (0x706000UL)
#define CSL_MMCSD0_ECC_AGGR_RXMEM_SIZE                                                             (0x400UL)
#define CSL_MMCSD0_ECC_AGGR_TXMEM_BASE                                                             (0x707000UL)
#define CSL_MMCSD0_ECC_AGGR_TXMEM_SIZE                                                             (0x400UL)
#define CSL_MMCSD1_ECC_AGGR_RXMEM_BASE                                                             (0x708000UL)
#define CSL_MMCSD1_ECC_AGGR_RXMEM_SIZE                                                             (0x400UL)
#define CSL_MMCSD1_ECC_AGGR_TXMEM_BASE                                                             (0x709000UL)
#define CSL_MMCSD1_ECC_AGGR_TXMEM_SIZE                                                             (0x400UL)
#define CSL_MMCSD2_ECC_AGGR_TXMEM_BASE                                                             (0x70a000UL)
#define CSL_MMCSD2_ECC_AGGR_TXMEM_SIZE                                                             (0x400UL)
#define CSL_MMCSD2_ECC_AGGR_RXMEM_BASE                                                             (0x70b000UL)
#define CSL_MMCSD2_ECC_AGGR_RXMEM_SIZE                                                             (0x400UL)
#define CSL_USB1_DEBUG_TRACE_MMR_TRACE_VBUSP_USB2SS_DEBUG_TRACE_BASE                               (0x70c000UL)
#define CSL_USB1_DEBUG_TRACE_MMR_TRACE_VBUSP_USB2SS_DEBUG_TRACE_SIZE                               (0x200UL)
#define CSL_CSI_RX_IF0_ECC_AGGR_CFG_BASE                                                           (0x70e000UL)
#define CSL_CSI_RX_IF0_ECC_AGGR_CFG_SIZE                                                           (0x400UL)
#define CSL_SA3_SS0_ECC_AGGR_BASE                                                                  (0x712000UL)
#define CSL_SA3_SS0_ECC_AGGR_SIZE                                                                  (0x400UL)
#define CSL_FSS0_OSPI0_ECC_AGGR_BASE                                                               (0x716000UL)
#define CSL_FSS0_OSPI0_ECC_AGGR_SIZE                                                               (0x400UL)
#define CSL_COMPUTE_CLUSTER0_SS_ECC_AGGR_BASE                                                      (0x718000UL)
#define CSL_COMPUTE_CLUSTER0_SS_ECC_AGGR_SIZE                                                      (0x400UL)
#define CSL_COMPUTE_CLUSTER0_CORE0_ECC_AGGR_BASE                                                   (0x718400UL)
#define CSL_COMPUTE_CLUSTER0_CORE0_ECC_AGGR_SIZE                                                   (0x400UL)
#define CSL_COMPUTE_CLUSTER0_CORE1_ECC_AGGR_BASE                                                   (0x718800UL)
#define CSL_COMPUTE_CLUSTER0_CORE1_ECC_AGGR_SIZE                                                   (0x400UL)
#define CSL_COMPUTE_CLUSTER0_CORE2_ECC_AGGR_BASE                                                   (0x718c00UL)
#define CSL_COMPUTE_CLUSTER0_CORE2_ECC_AGGR_SIZE                                                   (0x400UL)
#define CSL_COMPUTE_CLUSTER0_CORE3_ECC_AGGR_BASE                                                   (0x719000UL)
#define CSL_COMPUTE_CLUSTER0_CORE3_ECC_AGGR_SIZE                                                   (0x400UL)
#define CSL_DCC0_BASE                                                                              (0x800000UL)
#define CSL_DCC0_SIZE                                                                              (0x40UL)
#define CSL_DCC1_BASE                                                                              (0x804000UL)
#define CSL_DCC1_SIZE                                                                              (0x40UL)
#define CSL_DCC2_BASE                                                                              (0x808000UL)
#define CSL_DCC2_SIZE                                                                              (0x40UL)
#define CSL_DCC3_BASE                                                                              (0x80c000UL)
#define CSL_DCC3_SIZE                                                                              (0x40UL)
#define CSL_DCC4_BASE                                                                              (0x810000UL)
#define CSL_DCC4_SIZE                                                                              (0x40UL)
#define CSL_DCC5_BASE                                                                              (0x814000UL)
#define CSL_DCC5_SIZE                                                                              (0x40UL)
#define CSL_DCC6_BASE                                                                              (0x818000UL)
#define CSL_DCC6_SIZE                                                                              (0x40UL)
#define CSL_MAIN_GPIOMUX_INTROUTER0_INTR_ROUTER_CFG_BASE                                           (0xa00000UL)
#define CSL_MAIN_GPIOMUX_INTROUTER0_INTR_ROUTER_CFG_SIZE                                           (0x800UL)
#define CSL_CMP_EVENT_INTROUTER0_INTR_ROUTER_CFG_BASE                                              (0xa30000UL)
#define CSL_CMP_EVENT_INTROUTER0_INTR_ROUTER_CFG_SIZE                                              (0x800UL)
#define CSL_TIMESYNC_EVENT_ROUTER0_INTR_ROUTER_CFG_BASE                                            (0xa40000UL)
#define CSL_TIMESYNC_EVENT_ROUTER0_INTR_ROUTER_CFG_SIZE                                            (0x400UL)
#define CSL_WKUP_GTC0_GTC_CFG0_BASE                                                                (0xa80000UL)
#define CSL_WKUP_GTC0_GTC_CFG0_SIZE                                                                (0x400UL)
#define CSL_WKUP_GTC0_GTC_CFG1_BASE                                                                (0xa90000UL)
#define CSL_WKUP_GTC0_GTC_CFG1_SIZE                                                                (0x4000UL)
#define CSL_WKUP_GTC0_GTC_CFG2_BASE                                                                (0xaa0000UL)
#define CSL_WKUP_GTC0_GTC_CFG2_SIZE                                                                (0x4000UL)
#define CSL_WKUP_GTC0_GTC_CFG3_BASE                                                                (0xab0000UL)
#define CSL_WKUP_GTC0_GTC_CFG3_SIZE                                                                (0x4000UL)
#define CSL_WKUP_VTM0_MMR_VBUSP_CFG1_BASE                                                          (0xb00000UL)
#define CSL_WKUP_VTM0_MMR_VBUSP_CFG1_SIZE                                                          (0x400UL)
#define CSL_WKUP_VTM0_MMR_VBUSP_CFG2_BASE                                                          (0xb01000UL)
#define CSL_WKUP_VTM0_MMR_VBUSP_CFG2_SIZE                                                          (0x400UL)
#define CSL_WKUP_VTM0_ECCAGGR_CFG_BASE                                                             (0xb02000UL)
#define CSL_WKUP_VTM0_ECCAGGR_CFG_SIZE                                                             (0x400UL)
#define CSL_PDMA0_BASE                                                                             (0xc00000UL)
#define CSL_PDMA0_SIZE                                                                             (0x400UL)
#define CSL_PDMA1_BASE                                                                             (0xc01000UL)
#define CSL_PDMA1_SIZE                                                                             (0x400UL)
#define CSL_GICSS0_GIC_TRANSLATER_BASE                                                             (0x1000000UL)
#define CSL_GICSS0_GIC_TRANSLATER_SIZE                                                             (0x400000UL)
#define CSL_GICSS0_GIC_BASE                                                                        (0x1800000UL)
#define CSL_GICSS0_GIC_SIZE                                                                        (0x100000UL)
#define CSL_TIMER0_CFG_BASE                                                                        (0x2400000UL)
#define CSL_TIMER0_CFG_SIZE                                                                        (0x400UL)
#define CSL_TIMER1_CFG_BASE                                                                        (0x2410000UL)
#define CSL_TIMER1_CFG_SIZE                                                                        (0x400UL)
#define CSL_TIMER2_CFG_BASE                                                                        (0x2420000UL)
#define CSL_TIMER2_CFG_SIZE                                                                        (0x400UL)
#define CSL_TIMER3_CFG_BASE                                                                        (0x2430000UL)
#define CSL_TIMER3_CFG_SIZE                                                                        (0x400UL)
#define CSL_TIMER4_CFG_BASE                                                                        (0x2440000UL)
#define CSL_TIMER4_CFG_SIZE                                                                        (0x400UL)
#define CSL_TIMER5_CFG_BASE                                                                        (0x2450000UL)
#define CSL_TIMER5_CFG_SIZE                                                                        (0x400UL)
#define CSL_TIMER6_CFG_BASE                                                                        (0x2460000UL)
#define CSL_TIMER6_CFG_SIZE                                                                        (0x400UL)
#define CSL_TIMER7_CFG_BASE                                                                        (0x2470000UL)
#define CSL_TIMER7_CFG_SIZE                                                                        (0x400UL)
#define CSL_UART0_BASE                                                                             (0x2800000UL)
#define CSL_UART0_SIZE                                                                             (0x200UL)
#define CSL_UART1_BASE                                                                             (0x2810000UL)
#define CSL_UART1_SIZE                                                                             (0x200UL)
#define CSL_UART2_BASE                                                                             (0x2820000UL)
#define CSL_UART2_SIZE                                                                             (0x200UL)
#define CSL_UART3_BASE                                                                             (0x2830000UL)
#define CSL_UART3_SIZE                                                                             (0x200UL)
#define CSL_UART4_BASE                                                                             (0x2840000UL)
#define CSL_UART4_SIZE                                                                             (0x200UL)
#define CSL_UART5_BASE                                                                             (0x2850000UL)
#define CSL_UART5_SIZE                                                                             (0x200UL)
#define CSL_UART6_BASE                                                                             (0x2860000UL)
#define CSL_UART6_SIZE                                                                             (0x200UL)
#define CSL_MCASP0_CFG_BASE                                                                        (0x2b00000UL)
#define CSL_MCASP0_CFG_SIZE                                                                        (0x2000UL)
#define CSL_MCASP0_DMA_BASE                                                                        (0x2b08000UL)
#define CSL_MCASP0_DMA_SIZE                                                                        (0x400UL)
#define CSL_MCASP1_CFG_BASE                                                                        (0x2b10000UL)
#define CSL_MCASP1_CFG_SIZE                                                                        (0x2000UL)
#define CSL_MCASP1_DMA_BASE                                                                        (0x2b18000UL)
#define CSL_MCASP1_DMA_SIZE                                                                        (0x400UL)
#define CSL_MCASP2_CFG_BASE                                                                        (0x2b20000UL)
#define CSL_MCASP2_CFG_SIZE                                                                        (0x2000UL)
#define CSL_MCASP2_DMA_BASE                                                                        (0x2b28000UL)
#define CSL_MCASP2_DMA_SIZE                                                                        (0x400UL)
#define CSL_WKUP_PSC0_BASE                                                                         (0x4000000UL)
#define CSL_WKUP_PSC0_SIZE                                                                         (0x1000UL)
#define CSL_MCU_PLLCTRL0_BASE                                                                      (0x4020000UL)
#define CSL_MCU_PLLCTRL0_SIZE                                                                      (0x200UL)
#define CSL_WKUP_SAFE_ECC_AGGR0_ECC_AGGR_BASE                                                      (0x4030000UL)
#define CSL_WKUP_SAFE_ECC_AGGR0_ECC_AGGR_SIZE                                                      (0x400UL)
#define CSL_WKUP_PLL0_CFG_BASE                                                                     (0x4040000UL)
#define CSL_WKUP_PLL0_CFG_SIZE                                                                     (0x1000UL)
#define CSL_MCU_PADCFG_CTRL0_CFG0_BASE                                                             (0x4080000UL)
#define CSL_MCU_PADCFG_CTRL0_CFG0_SIZE                                                             (0x8000UL)
#define CSL_WKUP_ESM0_CFG_BASE                                                                     (0x4100000UL)
#define CSL_WKUP_ESM0_CFG_SIZE                                                                     (0x1000UL)
#define CSL_MCU_GPIO0_BASE                                                                         (0x4201000UL)
#define CSL_MCU_GPIO0_SIZE                                                                         (0x100UL)
#define CSL_WKUP_MCU_GPIOMUX_INTROUTER0_INTR_ROUTER_CFG_BASE                                       (0x4210000UL)
#define CSL_WKUP_MCU_GPIOMUX_INTROUTER0_INTR_ROUTER_CFG_SIZE                                       (0x200UL)
#define CSL_MCU_TIMEOUT0_CFG_BASE                                                                  (0x4300000UL)
#define CSL_MCU_TIMEOUT0_CFG_SIZE                                                                  (0x400UL)
#define CSL_MCU_TIMEOUT1_CFG_BASE                                                                  (0x4301000UL)
#define CSL_MCU_TIMEOUT1_CFG_SIZE                                                                  (0x400UL)
#define CSL_MCU_CTRL_MMR0_CFG0_BASE                                                                (0x4500000UL)
#define CSL_MCU_CTRL_MMR0_CFG0_SIZE                                                                (0x20000UL)
#define CSL_WKUP_CBASS_SAFE1_ERR_BASE                                                              (0x4600000UL)
#define CSL_WKUP_CBASS_SAFE1_ERR_SIZE                                                              (0x400UL)
#define CSL_MCU_MCAN0_ECC_AGGR_BASE                                                                (0x4701000UL)
#define CSL_MCU_MCAN0_ECC_AGGR_SIZE                                                                (0x400UL)
#define CSL_MCU_MCAN1_ECC_AGGR_BASE                                                                (0x4702000UL)
#define CSL_MCU_MCAN1_ECC_AGGR_SIZE                                                                (0x400UL)
#define CSL_MCU_ECC_AGGR0_ECC_AGGR_BASE                                                            (0x4703000UL)
#define CSL_MCU_ECC_AGGR0_ECC_AGGR_SIZE                                                            (0x400UL)
#define CSL_MCU_CBASS0_ERR_BASE                                                                    (0x4720000UL)
#define CSL_MCU_CBASS0_ERR_SIZE                                                                    (0x400UL)
#define CSL_MCU_TIMER0_CFG_BASE                                                                    (0x4800000UL)
#define CSL_MCU_TIMER0_CFG_SIZE                                                                    (0x400UL)
#define CSL_MCU_TIMER1_CFG_BASE                                                                    (0x4810000UL)
#define CSL_MCU_TIMER1_CFG_SIZE                                                                    (0x400UL)
#define CSL_MCU_TIMER2_CFG_BASE                                                                    (0x4820000UL)
#define CSL_MCU_TIMER2_CFG_SIZE                                                                    (0x400UL)
#define CSL_MCU_TIMER3_CFG_BASE                                                                    (0x4830000UL)
#define CSL_MCU_TIMER3_CFG_SIZE                                                                    (0x400UL)
#define CSL_MCU_RTI0_CFG_BASE                                                                      (0x4880000UL)
#define CSL_MCU_RTI0_CFG_SIZE                                                                      (0x100UL)
#define CSL_MCU_I2C0_CFG_BASE                                                                      (0x4900000UL)
#define CSL_MCU_I2C0_CFG_SIZE                                                                      (0x100UL)
#define CSL_MCU_UART0_BASE                                                                         (0x4a00000UL)
#define CSL_MCU_UART0_SIZE                                                                         (0x200UL)
#define CSL_MCU_MCSPI0_CFG_BASE                                                                    (0x4b00000UL)
#define CSL_MCU_MCSPI0_CFG_SIZE                                                                    (0x400UL)
#define CSL_MCU_MCSPI1_CFG_BASE                                                                    (0x4b10000UL)
#define CSL_MCU_MCSPI1_CFG_SIZE                                                                    (0x400UL)
#define CSL_MCU_DCC0_BASE                                                                          (0x4c00000UL)
#define CSL_MCU_DCC0_SIZE                                                                          (0x40UL)
#define CSL_MCU_MCRC64_0_REGS_BASE                                                                 (0x4d00000UL)
#define CSL_MCU_MCRC64_0_REGS_SIZE                                                                 (0x1000UL)
#define CSL_MCU_MCAN0_MSGMEM_RAM_BASE                                                              (0x4e00000UL)
#define CSL_MCU_MCAN0_MSGMEM_RAM_SIZE                                                              (0x8000UL)
#define CSL_MCU_MCAN0_CFG_BASE                                                                     (0x4e08000UL)
#define CSL_MCU_MCAN0_CFG_SIZE                                                                     (0x200UL)
#define CSL_MCU_MCAN0_SS_BASE                                                                      (0x4e09000UL)
#define CSL_MCU_MCAN0_SS_SIZE                                                                      (0x100UL)
#define CSL_MCU_MCAN1_MSGMEM_RAM_BASE                                                              (0x4e10000UL)
#define CSL_MCU_MCAN1_MSGMEM_RAM_SIZE                                                              (0x8000UL)
#define CSL_MCU_MCAN1_CFG_BASE                                                                     (0x4e18000UL)
#define CSL_MCU_MCAN1_CFG_SIZE                                                                     (0x200UL)
#define CSL_MCU_MCAN1_SS_BASE                                                                      (0x4e19000UL)
#define CSL_MCU_MCAN1_SS_SIZE                                                                      (0x100UL)
#define CSL_MCU_M4FSS0_IRAM_BASE                                                                   (0x5000000UL)
#define CSL_MCU_M4FSS0_IRAM_SIZE                                                                   (0x30000UL)
#define CSL_MCU_M4FSS0_DRAM_BASE                                                                   (0x5040000UL)
#define CSL_MCU_M4FSS0_DRAM_SIZE                                                                   (0x10000UL)
#define CSL_MCU_M4FSS0_RAT_BASE                                                                    (0x5ff0000UL)
#define CSL_MCU_M4FSS0_RAT_SIZE                                                                    (0x1000UL)
#define CSL_MCU_M4FSS0_ECC_AGGR_BASE                                                               (0x5ff1000UL)
#define CSL_MCU_M4FSS0_ECC_AGGR_SIZE                                                               (0x400UL)
#define CSL_CPSW0_NUSS_BASE                                                                        (0x8000000UL)
#define CSL_CPSW0_NUSS_SIZE                                                                        (0x200000UL)
#define CSL_RTI0_CFG_BASE                                                                          (0xe000000UL)
#define CSL_RTI0_CFG_SIZE                                                                          (0x100UL)
#define CSL_RTI1_CFG_BASE                                                                          (0xe010000UL)
#define CSL_RTI1_CFG_SIZE                                                                          (0x100UL)
#define CSL_RTI2_CFG_BASE                                                                          (0xe020000UL)
#define CSL_RTI2_CFG_SIZE                                                                          (0x100UL)
#define CSL_RTI3_CFG_BASE                                                                          (0xe030000UL)
#define CSL_RTI3_CFG_SIZE                                                                          (0x100UL)
#define CSL_RTI15_CFG_BASE                                                                         (0xe0f0000UL)
#define CSL_RTI15_CFG_SIZE                                                                         (0x100UL)
#define CSL_DDR16SS0_REGS_SS_CFG_SSCFG_BASE                                                        (0xf300000UL)
#define CSL_DDR16SS0_REGS_SS_CFG_SSCFG_SIZE                                                        (0x200UL)
#define CSL_DDR16SS0_CTLPHY_WRAP_CTL_CFG_CTLCFG_BASE                                               (0xf308000UL)
#define CSL_DDR16SS0_CTLPHY_WRAP_CTL_CFG_CTLCFG_SIZE                                               (0x8000UL)
#define CSL_USB0_MMR_MMRVBP_USB2SS_CFG_BASE                                                        (0xf900000UL)
#define CSL_USB0_MMR_MMRVBP_USB2SS_CFG_SIZE                                                        (0x800UL)
#define CSL_USB0_PHY2_BASE                                                                         (0xf908000UL)
#define CSL_USB0_PHY2_SIZE                                                                         (0x400UL)
#define CSL_USB1_MMR_MMRVBP_USB2SS_CFG_BASE                                                        (0xf910000UL)
#define CSL_USB1_MMR_MMRVBP_USB2SS_CFG_SIZE                                                        (0x800UL)
#define CSL_USB1_PHY2_BASE                                                                         (0xf918000UL)
#define CSL_USB1_PHY2_SIZE                                                                         (0x400UL)
#define CSL_USB0_ECC_AGGR_BASE                                                                     (0xf980000UL)
#define CSL_USB0_ECC_AGGR_SIZE                                                                     (0x400UL)
#define CSL_USB1_ECC_AGGR_BASE                                                                     (0xf990000UL)
#define CSL_USB1_ECC_AGGR_SIZE                                                                     (0x400UL)
#define CSL_MMCSD1_CTL_CFG_BASE                                                                    (0xfa00000UL)
#define CSL_MMCSD1_CTL_CFG_SIZE                                                                    (0x1000UL)
#define CSL_MMCSD1_SS_CFG_BASE                                                                     (0xfa08000UL)
#define CSL_MMCSD1_SS_CFG_SIZE                                                                     (0x400UL)
#define CSL_MMCSD0_CTL_CFG_BASE                                                                    (0xfa10000UL)
#define CSL_MMCSD0_CTL_CFG_SIZE                                                                    (0x1000UL)
#define CSL_MMCSD0_SS_CFG_BASE                                                                     (0xfa18000UL)
#define CSL_MMCSD0_SS_CFG_SIZE                                                                     (0x400UL)
#define CSL_MMCSD2_CTL_CFG_BASE                                                                    (0xfa20000UL)
#define CSL_MMCSD2_CTL_CFG_SIZE                                                                    (0x1000UL)
#define CSL_MMCSD2_SS_CFG_BASE                                                                     (0xfa28000UL)
#define CSL_MMCSD2_SS_CFG_SIZE                                                                     (0x400UL)
#define CSL_FSS0_CFG_BASE                                                                          (0xfc00000UL)
#define CSL_FSS0_CFG_SIZE                                                                          (0x100UL)
#define CSL_FSS0_FSAS_CFG_BASE                                                                     (0xfc10000UL)
#define CSL_FSS0_FSAS_CFG_SIZE                                                                     (0x100UL)
#define CSL_FSS0_OTFA_CFG_BASE                                                                     (0xfc20000UL)
#define CSL_FSS0_OTFA_CFG_SIZE                                                                     (0x1000UL)
#define CSL_FSS0_OSPI0_CTRL_BASE                                                                   (0xfc40000UL)
#define CSL_FSS0_OSPI0_CTRL_SIZE                                                                   (0x100UL)
#define CSL_FSS0_OSPI0_SS_CFG_BASE                                                                 (0xfc44000UL)
#define CSL_FSS0_OSPI0_SS_CFG_SIZE                                                                 (0x200UL)
#define CSL_GPU0_RGX_CR_BASE                                                                       (0xfd00000UL)
#define CSL_GPU0_RGX_CR_SIZE                                                                       (0x20000UL)
#define CSL_I2C0_CFG_BASE                                                                          (0x20000000UL)
#define CSL_I2C0_CFG_SIZE                                                                          (0x100UL)
#define CSL_I2C1_CFG_BASE                                                                          (0x20010000UL)
#define CSL_I2C1_CFG_SIZE                                                                          (0x100UL)
#define CSL_I2C2_CFG_BASE                                                                          (0x20020000UL)
#define CSL_I2C2_CFG_SIZE                                                                          (0x100UL)
#define CSL_I2C3_CFG_BASE                                                                          (0x20030000UL)
#define CSL_I2C3_CFG_SIZE                                                                          (0x100UL)
#define CSL_MCSPI0_CFG_BASE                                                                        (0x20100000UL)
#define CSL_MCSPI0_CFG_SIZE                                                                        (0x400UL)
#define CSL_MCSPI1_CFG_BASE                                                                        (0x20110000UL)
#define CSL_MCSPI1_CFG_SIZE                                                                        (0x400UL)
#define CSL_MCSPI2_CFG_BASE                                                                        (0x20120000UL)
#define CSL_MCSPI2_CFG_SIZE                                                                        (0x400UL)
#define CSL_CBASS_MISC_PERI0_ERR_BASE                                                              (0x201f0000UL)
#define CSL_CBASS_MISC_PERI0_ERR_SIZE                                                              (0x400UL)
#define CSL_MCAN0_SS_BASE                                                                          (0x20700000UL)
#define CSL_MCAN0_SS_SIZE                                                                          (0x100UL)
#define CSL_MCAN0_CFG_BASE                                                                         (0x20701000UL)
#define CSL_MCAN0_CFG_SIZE                                                                         (0x200UL)
#define CSL_MCAN0_MSGMEM_RAM_BASE                                                                  (0x20708000UL)
#define CSL_MCAN0_MSGMEM_RAM_SIZE                                                                  (0x8000UL)
#define CSL_EPWM0_EPWM_BASE                                                                        (0x23000000UL)
#define CSL_EPWM0_EPWM_SIZE                                                                        (0x100UL)
#define CSL_EPWM1_EPWM_BASE                                                                        (0x23010000UL)
#define CSL_EPWM1_EPWM_SIZE                                                                        (0x100UL)
#define CSL_EPWM2_EPWM_BASE                                                                        (0x23020000UL)
#define CSL_EPWM2_EPWM_SIZE                                                                        (0x100UL)
#define CSL_ECAP0_CTL_STS_BASE                                                                     (0x23100000UL)
#define CSL_ECAP0_CTL_STS_SIZE                                                                     (0x100UL)
#define CSL_ECAP1_CTL_STS_BASE                                                                     (0x23110000UL)
#define CSL_ECAP1_CTL_STS_SIZE                                                                     (0x100UL)
#define CSL_ECAP2_CTL_STS_BASE                                                                     (0x23120000UL)
#define CSL_ECAP2_CTL_STS_SIZE                                                                     (0x100UL)
#define CSL_EQEP0_REG_BASE                                                                         (0x23200000UL)
#define CSL_EQEP0_REG_SIZE                                                                         (0x100UL)
#define CSL_EQEP1_REG_BASE                                                                         (0x23210000UL)
#define CSL_EQEP1_REG_SIZE                                                                         (0x100UL)
#define CSL_EQEP2_REG_BASE                                                                         (0x23220000UL)
#define CSL_EQEP2_REG_SIZE                                                                         (0x100UL)
#define CSL_MCAN0_ECC_AGGR_BASE                                                                    (0x24018000UL)
#define CSL_MCAN0_ECC_AGGR_SIZE                                                                    (0x400UL)
#define CSL_ELM0_BASE                                                                              (0x25010000UL)
#define CSL_ELM0_SIZE                                                                              (0x1000UL)
#define CSL_MAILBOX0_REGS0_BASE                                                                    (0x29000000UL)
#define CSL_MAILBOX0_REGS0_SIZE                                                                    (0x200UL)
#define CSL_SPINLOCK0_BASE                                                                         (0x2a000000UL)
#define CSL_SPINLOCK0_SIZE                                                                         (0x8000UL)
#define CSL_WKUP_RTI0_CFG_BASE                                                                     (0x2b000000UL)
#define CSL_WKUP_RTI0_CFG_SIZE                                                                     (0x100UL)
#define CSL_WKUP_TIMER0_CFG_BASE                                                                   (0x2b100000UL)
#define CSL_WKUP_TIMER0_CFG_SIZE                                                                   (0x400UL)
#define CSL_WKUP_TIMER1_CFG_BASE                                                                   (0x2b110000UL)
#define CSL_WKUP_TIMER1_CFG_SIZE                                                                   (0x400UL)
#define CSL_WKUP_RTCSS0_RTC_BASE                                                                   (0x2b1f0000UL)
#define CSL_WKUP_RTCSS0_RTC_SIZE                                                                   (0x80UL)
#define CSL_WKUP_I2C0_CFG_BASE                                                                     (0x2b200000UL)
#define CSL_WKUP_I2C0_CFG_SIZE                                                                     (0x100UL)
#define CSL_WKUP_UART0_BASE                                                                        (0x2b300000UL)
#define CSL_WKUP_UART0_SIZE                                                                        (0x200UL)
#define CSL_WKUP_CBASS0_ERR_BASE                                                                   (0x2b400000UL)
#define CSL_WKUP_CBASS0_ERR_SIZE                                                                   (0x400UL)
#define CSL_WKUP_PBIST0_BASE                                                                       (0x2b500000UL)
#define CSL_WKUP_PBIST0_SIZE                                                                       (0x400UL)
#define CSL_WKUP_ECC_AGGR0_ECC_AGGR_BASE                                                           (0x2b600000UL)
#define CSL_WKUP_ECC_AGGR0_ECC_AGGR_SIZE                                                           (0x400UL)
#define CSL_ICSSM0_DRAM0_SLV_RAM_BASE                                                              (0x30040000UL)
#define CSL_ICSSM0_DRAM0_SLV_RAM_SIZE                                                              (0x2000UL)
#define CSL_ICSSM0_DRAM1_SLV_RAM_BASE                                                              (0x30042000UL)
#define CSL_ICSSM0_DRAM1_SLV_RAM_SIZE                                                              (0x2000UL)
#define CSL_ICSSM0_RAT_SLICE0_CFG_BASE                                                             (0x30048000UL)
#define CSL_ICSSM0_RAT_SLICE0_CFG_SIZE                                                             (0x1000UL)
#define CSL_ICSSM0_RAT_SLICE1_CFG_BASE                                                             (0x30049000UL)
#define CSL_ICSSM0_RAT_SLICE1_CFG_SIZE                                                             (0x1000UL)
#define CSL_ICSSM0_RAM_SLV_RAM_BASE                                                                (0x30050000UL)
#define CSL_ICSSM0_RAM_SLV_RAM_SIZE                                                                (0x8000UL)
#define CSL_ICSSM0_PR1_ICSS_INTC_INTC_SLV_BASE                                                     (0x30060000UL)
#define CSL_ICSSM0_PR1_ICSS_INTC_INTC_SLV_SIZE                                                     (0x2000UL)
#define CSL_ICSSM0_PR1_PDSP0_IRAM_BASE                                                             (0x30062000UL)
#define CSL_ICSSM0_PR1_PDSP0_IRAM_SIZE                                                             (0x100UL)
#define CSL_ICSSM0_PR1_PDSP0_IRAM_DEBUG_BASE                                                       (0x30062400UL)
#define CSL_ICSSM0_PR1_PDSP0_IRAM_DEBUG_SIZE                                                       (0x100UL)
#define CSL_ICSSM0_PR1_PDSP1_IRAM_BASE                                                             (0x30064000UL)
#define CSL_ICSSM0_PR1_PDSP1_IRAM_SIZE                                                             (0x100UL)
#define CSL_ICSSM0_PR1_PDSP1_IRAM_DEBUG_BASE                                                       (0x30064400UL)
#define CSL_ICSSM0_PR1_PDSP1_IRAM_DEBUG_SIZE                                                       (0x100UL)
#define CSL_ICSSM0_PR1_PROT_SLV_BASE                                                               (0x30064c00UL)
#define CSL_ICSSM0_PR1_PROT_SLV_SIZE                                                               (0x100UL)
#define CSL_ICSSM0_PR1_CFG_SLV_BASE                                                                (0x30066000UL)
#define CSL_ICSSM0_PR1_CFG_SLV_SIZE                                                                (0x200UL)
#define CSL_ICSSM0_PR1_ICSS_UART_UART_SLV_BASE                                                     (0x30068000UL)
#define CSL_ICSSM0_PR1_ICSS_UART_UART_SLV_SIZE                                                     (0x40UL)
#define CSL_ICSSM0_IEP0_BASE                                                                       (0x3006e000UL)
#define CSL_ICSSM0_IEP0_SIZE                                                                       (0x1000UL)
#define CSL_ICSSM0_PR1_ICSS_ECAP0_ECAP_SLV_BASE                                                    (0x30070000UL)
#define CSL_ICSSM0_PR1_ICSS_ECAP0_ECAP_SLV_SIZE                                                    (0x100UL)
#define CSL_ICSSM0_PR1_MII_RT_PR1_MII_RT_CFG_BASE                                                  (0x30072000UL)
#define CSL_ICSSM0_PR1_MII_RT_PR1_MII_RT_CFG_SIZE                                                  (0x100UL)
#define CSL_ICSSM0_PR1_MDIO_V1P7_MDIO_BASE                                                         (0x30072400UL)
#define CSL_ICSSM0_PR1_MDIO_V1P7_MDIO_SIZE                                                         (0x100UL)
#define CSL_ICSSM0_PR1_MII_RT_PR1_MII_RT_G_CFG_REGS_G_BASE                                         (0x30073000UL)
#define CSL_ICSSM0_PR1_MII_RT_PR1_MII_RT_G_CFG_REGS_G_SIZE                                         (0x1000UL)
#define CSL_ICSSM0_PR1_PDSP0_IRAM_RAM_BASE                                                         (0x30074000UL)
#define CSL_ICSSM0_PR1_PDSP0_IRAM_RAM_SIZE                                                         (0x4000UL)
#define CSL_ICSSM0_PR1_PDSP1_IRAM_RAM_BASE                                                         (0x30078000UL)
#define CSL_ICSSM0_PR1_PDSP1_IRAM_RAM_SIZE                                                         (0x4000UL)
#define CSL_CSI_RX_IF0_CP_INTD_CFG_INTD_CFG_BASE                                                   (0x30100000UL)
#define CSL_CSI_RX_IF0_CP_INTD_CFG_INTD_CFG_SIZE                                                   (0x1000UL)
#define CSL_CSI_RX_IF0_VBUS2APB_WRAP_VBUSP_APB_CSI2RX_BASE                                         (0x30101000UL)
#define CSL_CSI_RX_IF0_VBUS2APB_WRAP_VBUSP_APB_CSI2RX_SIZE                                         (0x1000UL)
#define CSL_CSI_RX_IF0_RX_SHIM_VBUSP_MMR_CSI2RXIF_BASE                                             (0x30102000UL)
#define CSL_CSI_RX_IF0_RX_SHIM_VBUSP_MMR_CSI2RXIF_SIZE                                             (0x1000UL)
#define CSL_DPHY_RX0_VBUS2APB_WRAP_VBUSP_K3_DPHY_RX_BASE                                           (0x30110000UL)
#define CSL_DPHY_RX0_VBUS2APB_WRAP_VBUSP_K3_DPHY_RX_SIZE                                           (0x1000UL)
#define CSL_DPHY_RX0_MMR_SLV_K3_DPHY_WRAP_BASE                                                     (0x30111000UL)
#define CSL_DPHY_RX0_MMR_SLV_K3_DPHY_WRAP_SIZE                                                     (0x100UL)
#define CSL_DSS0_COMMON_BASE                                                                       (0x30200000UL)
#define CSL_DSS0_COMMON_SIZE                                                                       (0x1000UL)
#define CSL_DSS0_COMMON1_BASE                                                                      (0x30201000UL)
#define CSL_DSS0_COMMON1_SIZE                                                                      (0x1000UL)
#define CSL_DSS0_VIDL1_BASE                                                                        (0x30202000UL)
#define CSL_DSS0_VIDL1_SIZE                                                                        (0x1000UL)
#define CSL_DSS0_VID_BASE                                                                          (0x30206000UL)
#define CSL_DSS0_VID_SIZE                                                                          (0x1000UL)
#define CSL_DSS0_OVR1_BASE                                                                         (0x30207000UL)
#define CSL_DSS0_OVR1_SIZE                                                                         (0x1000UL)
#define CSL_DSS0_OVR2_BASE                                                                         (0x30208000UL)
#define CSL_DSS0_OVR2_SIZE                                                                         (0x1000UL)
#define CSL_DSS0_VP1_BASE                                                                          (0x3020a000UL)
#define CSL_DSS0_VP1_SIZE                                                                          (0x1000UL)
#define CSL_DSS0_VP2_BASE                                                                          (0x3020b000UL)
#define CSL_DSS0_VP2_SIZE                                                                          (0x1000UL)
#define CSL_MCRC64_0_REGS_BASE                                                                     (0x30300000UL)
#define CSL_MCRC64_0_REGS_SIZE                                                                     (0x1000UL)
#define CSL_GPU_WS_BW_LIMITER3_REGS_BASE                                                           (0x30400000UL)
#define CSL_GPU_WS_BW_LIMITER3_REGS_SIZE                                                           (0x1000UL)
#define CSL_GPU_RS_BW_LIMITER2_REGS_BASE                                                           (0x30401000UL)
#define CSL_GPU_RS_BW_LIMITER2_REGS_SIZE                                                           (0x1000UL)
#define CSL_A53_WS_BW_LIMITER1_REGS_BASE                                                           (0x30402000UL)
#define CSL_A53_WS_BW_LIMITER1_REGS_SIZE                                                           (0x1000UL)
#define CSL_A53_RS_BW_LIMITER0_REGS_BASE                                                           (0x30403000UL)
#define CSL_A53_RS_BW_LIMITER0_REGS_SIZE                                                           (0x1000UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_CAP_BASE                                    (0x31000000UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_CAP_SIZE                                    (0x20UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_OPER_BASE                                   (0x31000020UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_OPER_SIZE                                   (0x40UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_PORT_BASE                                   (0x31000420UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_PORT_SIZE                                   (0x20UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_RUNTIME_BASE                                (0x31000440UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_RUNTIME_SIZE                                (0x20UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_INTR_BASE                                   (0x31000460UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_INTR_SIZE                                   (0x40UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DB_BASE                                     (0x31000560UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DB_SIZE                                     (0x200UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_EXTCAP_BASE                                 (0x31000960UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_EXTCAP_SIZE                                 (0x10UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_SUPPRTCAP2_BASE                             (0x31000970UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_SUPPRTCAP2_SIZE                             (0x10UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_SUPPRTCAP3_BASE                             (0x31000980UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_SUPPRTCAP3_SIZE                             (0x20UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_GBL_BASE                                    (0x3100c100UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_GBL_SIZE                                    (0x800UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEV_BASE                                    (0x3100c700UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEV_SIZE                                    (0x800UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_LINK_BASE                                   (0x3100d000UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_LINK_SIZE                                   (0x80UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEBUG_BASE                                  (0x3100d800UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEBUG_SIZE                                  (0x200UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEBUG_RAM0_BASE                             (0x31040000UL)
#define CSL_USB0_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEBUG_RAM0_SIZE                             (0x10000UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_CAP_BASE                                    (0x31100000UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_CAP_SIZE                                    (0x20UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_OPER_BASE                                   (0x31100020UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_OPER_SIZE                                   (0x40UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_PORT_BASE                                   (0x31100420UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_PORT_SIZE                                   (0x20UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_RUNTIME_BASE                                (0x31100440UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_RUNTIME_SIZE                                (0x20UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_INTR_BASE                                   (0x31100460UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_INTR_SIZE                                   (0x40UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DB_BASE                                     (0x31100560UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DB_SIZE                                     (0x200UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_EXTCAP_BASE                                 (0x31100960UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_EXTCAP_SIZE                                 (0x10UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_SUPPRTCAP2_BASE                             (0x31100970UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_SUPPRTCAP2_SIZE                             (0x10UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_SUPPRTCAP3_BASE                             (0x31100980UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_SUPPRTCAP3_SIZE                             (0x20UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_GBL_BASE                                    (0x3110c100UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_GBL_SIZE                                    (0x800UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEV_BASE                                    (0x3110c700UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEV_SIZE                                    (0x800UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_LINK_BASE                                   (0x3110d000UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_LINK_SIZE                                   (0x80UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEBUG_BASE                                  (0x3110d800UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEBUG_SIZE                                  (0x200UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEBUG_RAM0_BASE                             (0x31140000UL)
#define CSL_USB1_VBP2AHB_WRAP_CONTROLLER_VBP_USB3_CORE_DEBUG_RAM0_SIZE                             (0x10000UL)
#define CSL_CBASS0_ERR_BASE                                                                        (0x3a000000UL)
#define CSL_CBASS0_ERR_SIZE                                                                        (0x400UL)
#define CSL_GPMC0_CFG_BASE                                                                         (0x3b000000UL)
#define CSL_GPMC0_CFG_SIZE                                                                         (0x400UL)
#define CSL_WKUP_R5FSS0_EVNT_BUS_VBUSP_MMRS_BASE                                                   (0x3c018000UL)
#define CSL_WKUP_R5FSS0_EVNT_BUS_VBUSP_MMRS_SIZE                                                   (0x100UL)
#define CSL_PSRAMECC_16K0_ECC_AGGR_BASE                                                            (0x3f001000UL)
#define CSL_PSRAMECC_16K0_ECC_AGGR_SIZE                                                            (0x400UL)
#define CSL_GICSS0_REGS_BASE                                                                       (0x3f004000UL)
#define CSL_GICSS0_REGS_SIZE                                                                       (0x400UL)
#define CSL_DMASS0_ECCAGGR_BASE                                                                    (0x3f005000UL)
#define CSL_DMASS0_ECCAGGR_SIZE                                                                    (0x400UL)
#define CSL_ICSSM0_ECC_AGGR_BASE                                                                   (0x3f00c000UL)
#define CSL_ICSSM0_ECC_AGGR_SIZE                                                                   (0x400UL)
#define CSL_WKUP_R5FSS0_CORE0_ECC_AGGR_BASE                                                        (0x3f00d000UL)
#define CSL_WKUP_R5FSS0_CORE0_ECC_AGGR_SIZE                                                        (0x400UL)
#define CSL_ECC_AGGR0_ECC_AGGR_BASE                                                                (0x3f00f000UL)
#define CSL_ECC_AGGR0_ECC_AGGR_SIZE                                                                (0x400UL)
#define CSL_CBASS_CENTRAL2_ERR_BASE                                                                (0x3f012000UL)
#define CSL_CBASS_CENTRAL2_ERR_SIZE                                                                (0x400UL)
#define CSL_PBIST0_BASE                                                                            (0x3f110000UL)
#define CSL_PBIST0_SIZE                                                                            (0x400UL)
#define CSL_SA3_SS0_REGS_BASE                                                                      (0x40900000UL)
#define CSL_SA3_SS0_REGS_SIZE                                                                      (0x1000UL)
#define CSL_SA3_SS0_MMRA_BASE                                                                      (0x40901000UL)
#define CSL_SA3_SS0_MMRA_SIZE                                                                      (0x200UL)
#define CSL_SA3_SS0_EIP_76_BASE                                                                    (0x40910000UL)
#define CSL_SA3_SS0_EIP_76_SIZE                                                                    (0x80UL)
#define CSL_SA3_SS0_EIP_29T2_BASE                                                                  (0x40920000UL)
#define CSL_SA3_SS0_EIP_29T2_SIZE                                                                  (0x10000UL)
#define CSL_DEBUGSS0_SYS_BASE                                                                      (0x41000000UL)
#define CSL_DEBUGSS0_SYS_SIZE                                                                      (0x1000UL)
#define CSL_WKUP_ROM0_BASE                                                                         (0x41800000UL)
#define CSL_WKUP_ROM0_SIZE                                                                         (0x40000UL)
#define CSL_STM0_STIMULUS_BASE                                                                     (0x42000000UL)
#define CSL_STM0_STIMULUS_SIZE                                                                     (0x1000000UL)
#define CSL_WKUP_CTRL_MMR0_CFG0_BASE                                                               (0x43000000UL)
#define CSL_WKUP_CTRL_MMR0_CFG0_SIZE                                                               (0x20000UL)
#define CSL_SA3_SS0_SEC_PROXY_SRC_TARGET_DATA_BASE                                                 (0x43600000UL)
#define CSL_SA3_SS0_SEC_PROXY_SRC_TARGET_DATA_SIZE                                                 (0x10000UL)
#define CSL_SMS0_ECC_AGGR_BASE                                                                     (0x43700000UL)
#define CSL_SMS0_ECC_AGGR_SIZE                                                                     (0x400UL)
#define CSL_SMS0_HSM_ECC_BASE                                                                      (0x43701000UL)
#define CSL_SMS0_HSM_ECC_SIZE                                                                      (0x400UL)
#define CSL_SA3_SS0_ECCAGGR_CFG_BASE                                                               (0x43702000UL)
#define CSL_SA3_SS0_ECCAGGR_CFG_SIZE                                                               (0x400UL)
#define CSL_SMS0_TIFS_DMSS_HSM_ECC_BASE                                                            (0x43702000UL)
#define CSL_SMS0_TIFS_DMSS_HSM_ECC_SIZE                                                            (0x400UL)
#define CSL_SMS0_HSM_WDT_RTI_BASE                                                                  (0x43935000UL)
#define CSL_SMS0_HSM_WDT_RTI_SIZE                                                                  (0x100UL)
#define CSL_SMS0_HSM_CTRL_MMR_BASE                                                                 (0x43936000UL)
#define CSL_SMS0_HSM_CTRL_MMR_SIZE                                                                 (0x1000UL)
#define CSL_SMS0_HSM_RAT_MMRS_BASE                                                                 (0x43a00000UL)
#define CSL_SMS0_HSM_RAT_MMRS_SIZE                                                                 (0x1000UL)
#define CSL_SMS0_HSM_SRAM0_0_BASE                                                                  (0x43c00000UL)
#define CSL_SMS0_HSM_SRAM0_0_SIZE                                                                  (0x20000UL)
#define CSL_SMS0_HSM_SRAM0_1_BASE                                                                  (0x43c20000UL)
#define CSL_SMS0_HSM_SRAM0_1_SIZE                                                                  (0x10000UL)
#define CSL_SMS0_HSM_SRAM1_BASE                                                                    (0x43c30000UL)
#define CSL_SMS0_HSM_SRAM1_SIZE                                                                    (0x10000UL)
#define CSL_SMS0_TIFS_SRAM0_BASE                                                                   (0x44040000UL)
#define CSL_SMS0_TIFS_SRAM0_SIZE                                                                   (0x20000UL)
#define CSL_SMS0_TIFS_SRAM1_0_BASE                                                                 (0x44060000UL)
#define CSL_SMS0_TIFS_SRAM1_0_SIZE                                                                 (0x8000UL)
#define CSL_SMS0_TIFS_SRAM1_1_BASE                                                                 (0x44068000UL)
#define CSL_SMS0_TIFS_SRAM1_1_SIZE                                                                 (0x4000UL)
#define CSL_SMS0_PWR_BASE                                                                          (0x44130000UL)
#define CSL_SMS0_PWR_SIZE                                                                          (0x800UL)
#define CSL_SMS0_DMTIMER0_BASE                                                                     (0x44133000UL)
#define CSL_SMS0_DMTIMER0_SIZE                                                                     (0x400UL)
#define CSL_SMS0_DMTIMER1_BASE                                                                     (0x44134000UL)
#define CSL_SMS0_DMTIMER1_SIZE                                                                     (0x400UL)
#define CSL_SMS0_WDT_RTI_BASE                                                                      (0x44135000UL)
#define CSL_SMS0_WDT_RTI_SIZE                                                                      (0x100UL)
#define CSL_SMS0_RTI_BASE                                                                          (0x44135100UL)
#define CSL_SMS0_RTI_SIZE                                                                          (0x100UL)
#define CSL_SMS0_RAT_BASE                                                                          (0x44200000UL)
#define CSL_SMS0_RAT_SIZE                                                                          (0x1000UL)
#define CSL_SMS0_SEC_BASE                                                                          (0x44230000UL)
#define CSL_SMS0_SEC_SIZE                                                                          (0x1000UL)
#define CSL_SMS0_SECMGR_BASE                                                                       (0x44234000UL)
#define CSL_SMS0_SECMGR_SIZE                                                                       (0x4000UL)
#define CSL_SMS0_DMTIMER2_BASE                                                                     (0x44238000UL)
#define CSL_SMS0_DMTIMER2_SIZE                                                                     (0x400UL)
#define CSL_SMS0_DMTIMER3_BASE                                                                     (0x44239000UL)
#define CSL_SMS0_DMTIMER3_SIZE                                                                     (0x400UL)
#define CSL_SMS0_AES_BASE                                                                          (0x4423c000UL)
#define CSL_SMS0_AES_SIZE                                                                          (0x2000UL)
#define CSL_SMS0_TIFS_DMSS_HSM_BASE                                                                (0x44800000UL)
#define CSL_SMS0_TIFS_DMSS_HSM_SIZE                                                                (0x800000UL)
#define CSL_SA3_SS0_PSILCFG_CFG_PROXY_BASE                                                         (0x44801000UL)
#define CSL_SA3_SS0_PSILCFG_CFG_PROXY_SIZE                                                         (0x200UL)
#define CSL_SA3_SS0_PSILSS_CFG_MMRS_BASE                                                           (0x44802000UL)
#define CSL_SA3_SS0_PSILSS_CFG_MMRS_SIZE                                                           (0x1000UL)
#define CSL_SA3_SS0_IPCSS_SEC_PROXY_CFG_MMRS_BASE                                                  (0x44804000UL)
#define CSL_SA3_SS0_IPCSS_SEC_PROXY_CFG_MMRS_SIZE                                                  (0x100UL)
#define CSL_SA3_SS0_IPCSS_RINGACC_CFG_GCFG_BASE                                                    (0x44805000UL)
#define CSL_SA3_SS0_IPCSS_RINGACC_CFG_GCFG_SIZE                                                    (0x400UL)
#define CSL_SA3_SS0_INTAGGR_CFG_BASE                                                               (0x44808000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_SIZE                                                               (0x20UL)
#define CSL_SA3_SS0_INTAGGR_CFG_IMAP_BASE                                                          (0x44809000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_IMAP_SIZE                                                          (0x400UL)
#define CSL_SA3_SS0_INTAGGR_CFG_MCAST_BASE                                                         (0x4480a000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_MCAST_SIZE                                                         (0x400UL)
#define CSL_SA3_SS0_INTAGGR_CFG_GCNTCFG_BASE                                                       (0x4480b000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_GCNTCFG_SIZE                                                       (0x400UL)
#define CSL_SA3_SS0_INTAGGR_CFG_INTR_BASE                                                          (0x44810000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_INTR_SIZE                                                          (0x8000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_GCNTRTI_BASE                                                       (0x44820000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_GCNTRTI_SIZE                                                       (0x20000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_UNMAP_BASE                                                         (0x44840000UL)
#define CSL_SA3_SS0_INTAGGR_CFG_UNMAP_SIZE                                                         (0x10000UL)
#define CSL_SA3_SS0_IPCSS_SEC_PROXY_CFG_SCFG_BASE                                                  (0x44860000UL)
#define CSL_SA3_SS0_IPCSS_SEC_PROXY_CFG_SCFG_SIZE                                                  (0x20000UL)
#define CSL_SA3_SS0_IPCSS_SEC_PROXY_CFG_RT_BASE                                                    (0x44880000UL)
#define CSL_SA3_SS0_IPCSS_SEC_PROXY_CFG_RT_SIZE                                                    (0x20000UL)
#define CSL_SA3_SS0_IPCSS_RINGACC_CFG_BASE                                                         (0x448c0000UL)
#define CSL_SA3_SS0_IPCSS_RINGACC_CFG_SIZE                                                         (0x40000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_GCFG_BASE                                                           (0x44910000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_GCFG_SIZE                                                           (0x100UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RFLOW_BASE                                                          (0x44911000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RFLOW_SIZE                                                          (0x400UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RCHAN_BASE                                                          (0x44912000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RCHAN_SIZE                                                          (0x400UL)
#define CSL_SA3_SS0_PKTDMA_CFG_TCHAN_BASE                                                          (0x44913000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_TCHAN_SIZE                                                          (0x200UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RCHANRT_BASE                                                        (0x44914000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RCHANRT_SIZE                                                        (0x4000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_TCHANRT_BASE                                                        (0x44918000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_TCHANRT_SIZE                                                        (0x2000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RING_BASE                                                           (0x4491a000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RING_SIZE                                                           (0x2000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RINGRT_BASE                                                         (0x44940000UL)
#define CSL_SA3_SS0_PKTDMA_CFG_RINGRT_SIZE                                                         (0x40000UL)
#define CSL_SA3_SS0_IPCSS_RINGACC_CFG_RT_BASE                                                      (0x44c00000UL)
#define CSL_SA3_SS0_IPCSS_RINGACC_CFG_RT_SIZE                                                      (0x400000UL)
#define CSL_CBASS0_FW_BASE                                                                         (0x45000000UL)
#define CSL_CBASS0_FW_SIZE                                                                         (0x4000UL)
#define CSL_SMS0_FW_BASE                                                                           (0x45000000UL)
#define CSL_SMS0_FW_SIZE                                                                           (0x1000000UL)
#define CSL_WKUP_CBASS0_FW_BASE                                                                    (0x45008000UL)
#define CSL_WKUP_CBASS0_FW_SIZE                                                                    (0x1000UL)
#define CSL_CBASS_CENTRAL2_FW_BASE                                                                 (0x45010000UL)
#define CSL_CBASS_CENTRAL2_FW_SIZE                                                                 (0x1000UL)
#define CSL_PSCSS0_FW_BASE                                                                         (0x45020000UL)
#define CSL_PSCSS0_FW_SIZE                                                                         (0x400UL)
#define CSL_CBASS_IPCSS0_FW_BASE                                                                   (0x45028000UL)
#define CSL_CBASS_IPCSS0_FW_SIZE                                                                   (0x800UL)
#define CSL_SMS0_CBASS_FW_BASE                                                                     (0x45080000UL)
#define CSL_SMS0_CBASS_FW_SIZE                                                                     (0x20000UL)
#define CSL_SMS0_HSM_CBASS_FW_BASE                                                                 (0x450a0000UL)
#define CSL_SMS0_HSM_CBASS_FW_SIZE                                                                 (0x10000UL)
#define CSL_CBASS_CENTRAL2_ISC_BASE                                                                (0x45804000UL)
#define CSL_CBASS_CENTRAL2_ISC_SIZE                                                                (0x800UL)
#define CSL_SMS0_CBASS_ISC_BASE                                                                    (0x45808000UL)
#define CSL_SMS0_CBASS_ISC_SIZE                                                                    (0x1000UL)
#define CSL_SMS0_HSM_CBASS_ISC_BASE                                                                (0x4580a000UL)
#define CSL_SMS0_HSM_CBASS_ISC_SIZE                                                                (0x1000UL)
#define CSL_SMS0_DMSS_HSM_FWMGR_CFG_BASE                                                           (0x4580b000UL)
#define CSL_SMS0_DMSS_HSM_FWMGR_CFG_SIZE                                                           (0x400UL)
#define CSL_DMASS0_PKTDMA_CRED_BASE                                                                (0x45810000UL)
#define CSL_DMASS0_PKTDMA_CRED_SIZE                                                                (0x1000UL)
#define CSL_DMASS0_BCDMA_CRED_BASE                                                                 (0x45812000UL)
#define CSL_DMASS0_BCDMA_CRED_SIZE                                                                 (0x800UL)
#define CSL_WKUP_CBASS0_ISC_BASE                                                                   (0x45814000UL)
#define CSL_WKUP_CBASS0_ISC_SIZE                                                                   (0x1000UL)
#define CSL_MCU_CBASS0_ISC_BASE                                                                    (0x45818000UL)
#define CSL_MCU_CBASS0_ISC_SIZE                                                                    (0x400UL)
#define CSL_CBASS0_ISC_BASE                                                                        (0x45820000UL)
#define CSL_CBASS0_ISC_SIZE                                                                        (0x8000UL)
#define CSL_MAIN_SEC_MMR0_CFG2_BASE                                                                (0x45900000UL)
#define CSL_MAIN_SEC_MMR0_CFG2_SIZE                                                                (0x20000UL)
#define CSL_WKUP_WKUP_SEC_MMR0_CFG2_BASE                                                           (0x45920000UL)
#define CSL_WKUP_WKUP_SEC_MMR0_CFG2_SIZE                                                           (0x20000UL)
#define CSL_MAIN_SEC_MMR0_CFG0_BASE                                                                (0x45a00000UL)
#define CSL_MAIN_SEC_MMR0_CFG0_SIZE                                                                (0x20000UL)
#define CSL_WKUP_WKUP_SEC_MMR0_CFG0_BASE                                                           (0x45a20000UL)
#define CSL_WKUP_WKUP_SEC_MMR0_CFG0_SIZE                                                           (0x20000UL)
#define CSL_SMS0_CBASS_GLB_BASE                                                                    (0x45b00000UL)
#define CSL_SMS0_CBASS_GLB_SIZE                                                                    (0x400UL)
#define CSL_SMS0_HSM_CBASS_GLB_BASE                                                                (0x45b00800UL)
#define CSL_SMS0_HSM_CBASS_GLB_SIZE                                                                (0x400UL)
#define CSL_CBASS_IPCSS0_GLB_BASE                                                                  (0x45b01000UL)
#define CSL_CBASS_IPCSS0_GLB_SIZE                                                                  (0x400UL)
#define CSL_MCU_CBASS0_GLB_BASE                                                                    (0x45b02000UL)
#define CSL_MCU_CBASS0_GLB_SIZE                                                                    (0x400UL)
#define CSL_WKUP_CBASS0_GLB_BASE                                                                   (0x45b03000UL)
#define CSL_WKUP_CBASS0_GLB_SIZE                                                                   (0x400UL)
#define CSL_CBASS_CENTRAL2_GLB_BASE                                                                (0x45b04000UL)
#define CSL_CBASS_CENTRAL2_GLB_SIZE                                                                (0x400UL)
#define CSL_CBASS0_GLB_BASE                                                                        (0x45b08000UL)
#define CSL_CBASS0_GLB_SIZE                                                                        (0x400UL)
#define CSL_PSCSS0_GLB_BASE                                                                        (0x45b09000UL)
#define CSL_PSCSS0_GLB_SIZE                                                                        (0x400UL)
#define CSL_CBASS_CENTRAL2_QOS_BASE                                                                (0x45d04000UL)
#define CSL_CBASS_CENTRAL2_QOS_SIZE                                                                (0x800UL)
#define CSL_WKUP_CBASS0_QOS_BASE                                                                   (0x45d14000UL)
#define CSL_WKUP_CBASS0_QOS_SIZE                                                                   (0x1000UL)
#define CSL_MCU_CBASS0_QOS_BASE                                                                    (0x45d18000UL)
#define CSL_MCU_CBASS0_QOS_SIZE                                                                    (0x400UL)
#define CSL_CBASS0_QOS_BASE                                                                        (0x45d20000UL)
#define CSL_CBASS0_QOS_SIZE                                                                        (0x8000UL)
#define CSL_DMASS0_INTAGGR_INTR_BASE                                                               (0x48000000UL)
#define CSL_DMASS0_INTAGGR_INTR_SIZE                                                               (0x100000UL)
#define CSL_DMASS0_INTAGGR_IMAP_BASE                                                               (0x48100000UL)
#define CSL_DMASS0_INTAGGR_IMAP_SIZE                                                               (0x4000UL)
#define CSL_DMASS0_INTAGGR_CFG_BASE                                                                (0x48110000UL)
#define CSL_DMASS0_INTAGGR_CFG_SIZE                                                                (0x20UL)
#define CSL_DMASS0_INTAGGR_L2G_BASE                                                                (0x48120000UL)
#define CSL_DMASS0_INTAGGR_L2G_SIZE                                                                (0x400UL)
#define CSL_DMASS0_PSILCFG_PROXY_BASE                                                              (0x48130000UL)
#define CSL_DMASS0_PSILCFG_PROXY_SIZE                                                              (0x200UL)
#define CSL_DMASS0_PSILSS_MMRS_BASE                                                                (0x48140000UL)
#define CSL_DMASS0_PSILSS_MMRS_SIZE                                                                (0x1000UL)
#define CSL_DMASS0_INTAGGR_UNMAP_BASE                                                              (0x48180000UL)
#define CSL_DMASS0_INTAGGR_UNMAP_SIZE                                                              (0x20000UL)
#define CSL_DMASS0_INTAGGR_MCAST_BASE                                                              (0x48210000UL)
#define CSL_DMASS0_INTAGGR_MCAST_SIZE                                                              (0x1000UL)
#define CSL_DMASS0_INTAGGR_GCNTCFG_BASE                                                            (0x48220000UL)
#define CSL_DMASS0_INTAGGR_GCNTCFG_SIZE                                                            (0x2000UL)
#define CSL_DMASS0_ETLSW_MMRS_BASE                                                                 (0x48230000UL)
#define CSL_DMASS0_ETLSW_MMRS_SIZE                                                                 (0x1000UL)
#define CSL_DMASS0_RINGACC_GCFG_BASE                                                               (0x48240000UL)
#define CSL_DMASS0_RINGACC_GCFG_SIZE                                                               (0x400UL)
#define CSL_DMASS0_SEC_PROXY_MMRS_BASE                                                             (0x48250000UL)
#define CSL_DMASS0_SEC_PROXY_MMRS_SIZE                                                             (0x100UL)
#define CSL_DMASS0_BCDMA_BCHAN_BASE                                                                (0x48420000UL)
#define CSL_DMASS0_BCDMA_BCHAN_SIZE                                                                (0x2000UL)
#define CSL_DMASS0_PKTDMA_RFLOW_BASE                                                               (0x48430000UL)
#define CSL_DMASS0_PKTDMA_RFLOW_SIZE                                                               (0x1000UL)
#define CSL_DMASS0_PKTDMA_TCHAN_BASE                                                               (0x484a0000UL)
#define CSL_DMASS0_PKTDMA_TCHAN_SIZE                                                               (0x2000UL)
#define CSL_DMASS0_BCDMA_TCHAN_BASE                                                                (0x484a4000UL)
#define CSL_DMASS0_BCDMA_TCHAN_SIZE                                                                (0x2000UL)
#define CSL_DMASS0_PKTDMA_RCHAN_BASE                                                               (0x484c0000UL)
#define CSL_DMASS0_PKTDMA_RCHAN_SIZE                                                               (0x2000UL)
#define CSL_DMASS0_BCDMA_RCHAN_BASE                                                                (0x484c2000UL)
#define CSL_DMASS0_BCDMA_RCHAN_SIZE                                                                (0x2000UL)
#define CSL_DMASS0_PKTDMA_GCFG_BASE                                                                (0x485c0000UL)
#define CSL_DMASS0_PKTDMA_GCFG_SIZE                                                                (0x100UL)
#define CSL_DMASS0_BCDMA_GCFG_BASE                                                                 (0x485c0100UL)
#define CSL_DMASS0_BCDMA_GCFG_SIZE                                                                 (0x100UL)
#define CSL_DMASS0_PKTDMA_RING_BASE                                                                (0x485e0000UL)
#define CSL_DMASS0_PKTDMA_RING_SIZE                                                                (0x10000UL)
#define CSL_DMASS0_BCDMA_RING_BASE                                                                 (0x48600000UL)
#define CSL_DMASS0_BCDMA_RING_SIZE                                                                 (0x8000UL)
#define CSL_DMASS0_RINGACC_RT_BASE                                                                 (0x49000000UL)
#define CSL_DMASS0_RINGACC_RT_SIZE                                                                 (0x400000UL)
#define CSL_DMASS0_RINGACC_CFG_BASE                                                                (0x49800000UL)
#define CSL_DMASS0_RINGACC_CFG_SIZE                                                                (0x40000UL)
#define CSL_DMASS0_INTAGGR_GCNTRTI_BASE                                                            (0x4a000000UL)
#define CSL_DMASS0_INTAGGR_GCNTRTI_SIZE                                                            (0x100000UL)
#define CSL_DMASS0_SEC_PROXY_SCFG_BASE                                                             (0x4a400000UL)
#define CSL_DMASS0_SEC_PROXY_SCFG_SIZE                                                             (0x80000UL)
#define CSL_DMASS0_SEC_PROXY_RT_BASE                                                               (0x4a600000UL)
#define CSL_DMASS0_SEC_PROXY_RT_SIZE                                                               (0x80000UL)
#define CSL_DMASS0_PKTDMA_RCHANRT_BASE                                                             (0x4a800000UL)
#define CSL_DMASS0_PKTDMA_RCHANRT_SIZE                                                             (0x20000UL)
#define CSL_DMASS0_BCDMA_RCHANRT_BASE                                                              (0x4a820000UL)
#define CSL_DMASS0_BCDMA_RCHANRT_SIZE                                                              (0x20000UL)
#define CSL_DMASS0_PKTDMA_TCHANRT_BASE                                                             (0x4aa00000UL)
#define CSL_DMASS0_PKTDMA_TCHANRT_SIZE                                                             (0x20000UL)
#define CSL_DMASS0_BCDMA_TCHANRT_BASE                                                              (0x4aa40000UL)
#define CSL_DMASS0_BCDMA_TCHANRT_SIZE                                                              (0x20000UL)
#define CSL_DMASS0_PKTDMA_RINGRT_BASE                                                              (0x4b800000UL)
#define CSL_DMASS0_PKTDMA_RINGRT_SIZE                                                              (0x200000UL)
#define CSL_DMASS0_BCDMA_RINGRT_BASE                                                               (0x4bc00000UL)
#define CSL_DMASS0_BCDMA_RINGRT_SIZE                                                               (0x100000UL)
#define CSL_DMASS0_BCDMA_BCHANRT_BASE                                                              (0x4c000000UL)
#define CSL_DMASS0_BCDMA_BCHANRT_SIZE                                                              (0x20000UL)
#define CSL_DMASS0_SEC_PROXY_SRC_TARGET_DATA_BASE                                                  (0x4d000000UL)
#define CSL_DMASS0_SEC_PROXY_SRC_TARGET_DATA_SIZE                                                  (0x80000UL)
#define CSL_GPMC0_DATA_BASE                                                                        (0x50000000UL)
#define CSL_GPMC0_DATA_SIZE                                                                        (0x8000000UL)
#define CSL_FSS0_DAT_REG1_BASE                                                                     (0x60000000UL)
#define CSL_FSS0_DAT_REG1_SIZE                                                                     (0x8000000UL)
#define CSL_PSRAMECC_16K0_RAM_BASE                                                                 (0x70000000UL)
#define CSL_PSRAMECC_16K0_RAM_SIZE                                                                 (0x10000UL)
#define CSL_WKUP_R5FSS0_CORE0_ICACHE_BASE                                                          (0x74000000UL)
#define CSL_WKUP_R5FSS0_CORE0_ICACHE_SIZE                                                          (0x800000UL)
#define CSL_WKUP_R5FSS0_CORE0_DCACHE_BASE                                                          (0x74800000UL)
#define CSL_WKUP_R5FSS0_CORE0_DCACHE_SIZE                                                          (0x800000UL)
#define CSL_WKUP_R5FSS0_CORE0_ATCM_BASE                                                            (0x78000000UL)
#define CSL_WKUP_R5FSS0_CORE0_ATCM_SIZE                                                            (0x8000UL)
#define CSL_WKUP_R5FSS0_CORE0_BTCM_BASE                                                            (0x78100000UL)
#define CSL_WKUP_R5FSS0_CORE0_BTCM_SIZE                                                            (0x8000UL)
#define CSL_DDR16SS0_SDRAM_BASE                                                                    (0x80000000UL)
#define CSL_DDR16SS0_SDRAM_SIZE                                                                    (0x80000000UL)
#define CSL_FSS0_DAT_REG0_BASE                                                                     (0x400000000ULL)
#define CSL_FSS0_DAT_REG0_SIZE                                                                     (0x100000000ULL)
#define CSL_FSS0_DAT_REG3_BASE                                                                     (0x500000000ULL)
#define CSL_FSS0_DAT_REG3_SIZE                                                                     (0x100000000ULL)
#define CSL_DEBUGSS_WRAP0_ROM_TABLE_0_0_BASE                                                       (0x700000000ULL)
#define CSL_DEBUGSS_WRAP0_ROM_TABLE_0_0_SIZE                                                       (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_RESV0_0_BASE                                                             (0x700001000ULL)
#define CSL_DEBUGSS_WRAP0_RESV0_0_SIZE                                                             (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_CFGAP0_BASE                                                              (0x700002000ULL)
#define CSL_DEBUGSS_WRAP0_CFGAP0_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_APBAP0_BASE                                                              (0x700002100ULL)
#define CSL_DEBUGSS_WRAP0_APBAP0_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_AXIAP0_BASE                                                              (0x700002200ULL)
#define CSL_DEBUGSS_WRAP0_AXIAP0_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_PWRAP0_BASE                                                              (0x700002300ULL)
#define CSL_DEBUGSS_WRAP0_PWRAP0_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_PVIEW0_BASE                                                              (0x700002400ULL)
#define CSL_DEBUGSS_WRAP0_PVIEW0_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_JTAGAP0_BASE                                                             (0x700002500ULL)
#define CSL_DEBUGSS_WRAP0_JTAGAP0_SIZE                                                             (0x100ULL)
#define CSL_DEBUGSS_WRAP0_SECAP0_BASE                                                              (0x700002600ULL)
#define CSL_DEBUGSS_WRAP0_SECAP0_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX0_CFG0_BASE                                                        (0x700002700ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX0_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX1_CFG0_BASE                                                        (0x700002800ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX1_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX2_CFG0_BASE                                                        (0x700002900ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX2_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX3_CFG0_BASE                                                        (0x700002a00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX3_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX4_CFG0_BASE                                                        (0x700002b00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX4_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX5_CFG0_BASE                                                        (0x700002c00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX5_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX6_CFG0_BASE                                                        (0x700002d00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX6_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX7_CFG0_BASE                                                        (0x700002e00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX7_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX8_CFG0_BASE                                                        (0x700002f00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX8_CFG0_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_RESV1_0_BASE                                                             (0x700003000ULL)
#define CSL_DEBUGSS_WRAP0_RESV1_0_SIZE                                                             (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_RESV2_0_BASE                                                             (0x700004000ULL)
#define CSL_DEBUGSS_WRAP0_RESV2_0_SIZE                                                             (0x2000000ULL)
#define CSL_DEBUGSS_WRAP0_ROM_TABLE_1_0_BASE                                                       (0x720000000ULL)
#define CSL_DEBUGSS_WRAP0_ROM_TABLE_1_0_SIZE                                                       (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_CSCTI0_BASE                                                              (0x720001000ULL)
#define CSL_DEBUGSS_WRAP0_CSCTI0_SIZE                                                              (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_DRM0_BASE                                                                (0x720002000ULL)
#define CSL_DEBUGSS_WRAP0_DRM0_SIZE                                                                (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_RESV3_0_BASE                                                             (0x720003000ULL)
#define CSL_DEBUGSS_WRAP0_RESV3_0_SIZE                                                             (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_CSTPIU0_BASE                                                             (0x720004000ULL)
#define CSL_DEBUGSS_WRAP0_CSTPIU0_SIZE                                                             (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_CTF0_BASE                                                                (0x720005000ULL)
#define CSL_DEBUGSS_WRAP0_CTF0_SIZE                                                                (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_RESV4_0_BASE                                                             (0x720006000ULL)
#define CSL_DEBUGSS_WRAP0_RESV4_0_SIZE                                                             (0x1000000ULL)
#define CSL_COMPUTE_CLUSTER0_SS_ROM_BASE                                                           (0x730000000ULL)
#define CSL_COMPUTE_CLUSTER0_SS_ROM_SIZE                                                           (0x10000ULL)
#define CSL_DEBUGSS_WRAP0_EXT_APB0_BASE                                                            (0x730000000ULL)
#define CSL_DEBUGSS_WRAP0_EXT_APB0_SIZE                                                            (0x10000000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE0_DBG_BASE                                                        (0x730010000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE0_DBG_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE0_CTI_BASE                                                        (0x730020000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE0_CTI_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE0_PMU_BASE                                                        (0x730030000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE0_PMU_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE0_ETM_BASE                                                        (0x730040000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE0_ETM_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE1_DBG_BASE                                                        (0x730110000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE1_DBG_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE1_PMU_BASE                                                        (0x730120000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE1_PMU_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE1_ETM_BASE                                                        (0x730130000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE1_ETM_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE1_CTI_BASE                                                        (0x730140000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE1_CTI_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE2_DBG_BASE                                                        (0x730210000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE2_DBG_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE2_PMU_BASE                                                        (0x730220000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE2_PMU_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE2_ETM_BASE                                                        (0x730230000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE2_ETM_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE2_CTI_BASE                                                        (0x730240000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE2_CTI_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE3_DBG_BASE                                                        (0x730310000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE3_DBG_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE3_PMU_BASE                                                        (0x730320000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE3_PMU_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE3_ETM_BASE                                                        (0x730330000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE3_ETM_SIZE                                                        (0x10000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE3_CTI_BASE                                                        (0x730340000ULL)
#define CSL_COMPUTE_CLUSTER0_CORE3_CTI_SIZE                                                        (0x10000ULL)
#define CSL_DEBUGSS0_ROM_BASE                                                                      (0x73c020000ULL)
#define CSL_DEBUGSS0_ROM_SIZE                                                                      (0x1000ULL)
#define CSL_DEBUGSS0_CTSET2_WRAP_CFG_CTSET2_CFG_BASE                                               (0x73c022000ULL)
#define CSL_DEBUGSS0_CTSET2_WRAP_CFG_CTSET2_CFG_SIZE                                               (0x2000ULL)
#define CSL_DEBUGSS0_ATB_REPLICATOR_CFG_CXATBREPLICATOR_CFG_BASE                                   (0x73c024000ULL)
#define CSL_DEBUGSS0_ATB_REPLICATOR_CFG_CXATBREPLICATOR_CFG_SIZE                                   (0x1000ULL)
#define CSL_DEBUGSS0_TBR_VBUSP_WRAP_TBR_CFG_TBR_CFG_BASE                                           (0x73c025000ULL)
#define CSL_DEBUGSS0_TBR_VBUSP_WRAP_TBR_CFG_TBR_CFG_SIZE                                           (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_0_CFG_CSCTI_CFG_BASE                                                  (0x73c026000ULL)
#define CSL_DEBUGSS0_ARM_CTI_0_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_1_CFG_CSCTI_CFG_BASE                                                  (0x73c028000ULL)
#define CSL_DEBUGSS0_ARM_CTI_1_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_2_CFG_CSCTI_CFG_BASE                                                  (0x73c029000ULL)
#define CSL_DEBUGSS0_ARM_CTI_2_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_3_CFG_CSCTI_CFG_BASE                                                  (0x73c02a000ULL)
#define CSL_DEBUGSS0_ARM_CTI_3_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_4_CFG_CSCTI_CFG_BASE                                                  (0x73c02b000ULL)
#define CSL_DEBUGSS0_ARM_CTI_4_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_5_CFG_CSCTI_CFG_BASE                                                  (0x73c02c000ULL)
#define CSL_DEBUGSS0_ARM_CTI_5_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_6_CFG_CSCTI_CFG_BASE                                                  (0x73c02d000ULL)
#define CSL_DEBUGSS0_ARM_CTI_6_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_7_CFG_CSCTI_CFG_BASE                                                  (0x73c02e000ULL)
#define CSL_DEBUGSS0_ARM_CTI_7_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_DEBUGSS0_ARM_CTI_8_CFG_CSCTI_CFG_BASE                                                  (0x73c02f000ULL)
#define CSL_DEBUGSS0_ARM_CTI_8_CFG_CSCTI_CFG_SIZE                                                  (0x1000ULL)
#define CSL_STM0_CXSTM_BASE                                                                        (0x73d200000ULL)
#define CSL_STM0_CXSTM_SIZE                                                                        (0x1000ULL)
#define CSL_STM0_CTI_CSCTI_BASE                                                                    (0x73d201000ULL)
#define CSL_STM0_CTI_CSCTI_SIZE                                                                    (0x1000ULL)
#define CSL_DBGSUSPENDROUTER0_INTR_ROUTER_CFG_BASE                                                 (0x73d300000ULL)
#define CSL_DBGSUSPENDROUTER0_INTR_ROUTER_CFG_SIZE                                                 (0x800ULL)
#define CSL_CPT2_AGGR0_MMR_BASE                                                                    (0x73e100000ULL)
#define CSL_CPT2_AGGR0_MMR_SIZE                                                                    (0x100ULL)
#define CSL_CPT2_AGGR0_STP2ATB_CFG_BASE                                                            (0x73e100100ULL)
#define CSL_CPT2_AGGR0_STP2ATB_CFG_SIZE                                                            (0x100ULL)
#define CSL_CPT2_AGGR0_MEM0_BASE                                                                   (0x73e120000ULL)
#define CSL_CPT2_AGGR0_MEM0_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM1_BASE                                                                   (0x73e121000ULL)
#define CSL_CPT2_AGGR0_MEM1_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM2_BASE                                                                   (0x73e122000ULL)
#define CSL_CPT2_AGGR0_MEM2_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM3_BASE                                                                   (0x73e123000ULL)
#define CSL_CPT2_AGGR0_MEM3_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM4_BASE                                                                   (0x73e124000ULL)
#define CSL_CPT2_AGGR0_MEM4_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM5_BASE                                                                   (0x73e125000ULL)
#define CSL_CPT2_AGGR0_MEM5_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM6_BASE                                                                   (0x73e126000ULL)
#define CSL_CPT2_AGGR0_MEM6_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM7_BASE                                                                   (0x73e127000ULL)
#define CSL_CPT2_AGGR0_MEM7_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM8_BASE                                                                   (0x73e128000ULL)
#define CSL_CPT2_AGGR0_MEM8_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM9_BASE                                                                   (0x73e129000ULL)
#define CSL_CPT2_AGGR0_MEM9_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM10_BASE                                                                  (0x73e12a000ULL)
#define CSL_CPT2_AGGR0_MEM10_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM11_BASE                                                                  (0x73e12b000ULL)
#define CSL_CPT2_AGGR0_MEM11_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM12_BASE                                                                  (0x73e12c000ULL)
#define CSL_CPT2_AGGR0_MEM12_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM13_BASE                                                                  (0x73e12d000ULL)
#define CSL_CPT2_AGGR0_MEM13_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM14_BASE                                                                  (0x73e12e000ULL)
#define CSL_CPT2_AGGR0_MEM14_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM15_BASE                                                                  (0x73e12f000ULL)
#define CSL_CPT2_AGGR0_MEM15_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM16_BASE                                                                  (0x73e130000ULL)
#define CSL_CPT2_AGGR0_MEM16_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM17_BASE                                                                  (0x73e131000ULL)
#define CSL_CPT2_AGGR0_MEM17_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM18_BASE                                                                  (0x73e132000ULL)
#define CSL_CPT2_AGGR0_MEM18_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM19_BASE                                                                  (0x73e133000ULL)
#define CSL_CPT2_AGGR0_MEM19_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM20_BASE                                                                  (0x73e134000ULL)
#define CSL_CPT2_AGGR0_MEM20_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM21_BASE                                                                  (0x73e135000ULL)
#define CSL_CPT2_AGGR0_MEM21_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM22_BASE                                                                  (0x73e136000ULL)
#define CSL_CPT2_AGGR0_MEM22_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM23_BASE                                                                  (0x73e137000ULL)
#define CSL_CPT2_AGGR0_MEM23_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM24_BASE                                                                  (0x73e138000ULL)
#define CSL_CPT2_AGGR0_MEM24_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM25_BASE                                                                  (0x73e139000ULL)
#define CSL_CPT2_AGGR0_MEM25_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM26_BASE                                                                  (0x73e13a000ULL)
#define CSL_CPT2_AGGR0_MEM26_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM27_BASE                                                                  (0x73e13b000ULL)
#define CSL_CPT2_AGGR0_MEM27_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM28_BASE                                                                  (0x73e13c000ULL)
#define CSL_CPT2_AGGR0_MEM28_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM29_BASE                                                                  (0x73e13d000ULL)
#define CSL_CPT2_AGGR0_MEM29_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM30_BASE                                                                  (0x73e13e000ULL)
#define CSL_CPT2_AGGR0_MEM30_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR0_MEM31_BASE                                                                  (0x73e13f000ULL)
#define CSL_CPT2_AGGR0_MEM31_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MMR_BASE                                                                    (0x73e140000ULL)
#define CSL_CPT2_AGGR1_MMR_SIZE                                                                    (0x100ULL)
#define CSL_CPT2_AGGR1_STP2ATB_CFG_BASE                                                            (0x73e140100ULL)
#define CSL_CPT2_AGGR1_STP2ATB_CFG_SIZE                                                            (0x100ULL)
#define CSL_CPT2_AGGR1_MEM0_BASE                                                                   (0x73e160000ULL)
#define CSL_CPT2_AGGR1_MEM0_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM1_BASE                                                                   (0x73e161000ULL)
#define CSL_CPT2_AGGR1_MEM1_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM2_BASE                                                                   (0x73e162000ULL)
#define CSL_CPT2_AGGR1_MEM2_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM3_BASE                                                                   (0x73e163000ULL)
#define CSL_CPT2_AGGR1_MEM3_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM4_BASE                                                                   (0x73e164000ULL)
#define CSL_CPT2_AGGR1_MEM4_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM5_BASE                                                                   (0x73e165000ULL)
#define CSL_CPT2_AGGR1_MEM5_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM6_BASE                                                                   (0x73e166000ULL)
#define CSL_CPT2_AGGR1_MEM6_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM7_BASE                                                                   (0x73e167000ULL)
#define CSL_CPT2_AGGR1_MEM7_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM8_BASE                                                                   (0x73e168000ULL)
#define CSL_CPT2_AGGR1_MEM8_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM9_BASE                                                                   (0x73e169000ULL)
#define CSL_CPT2_AGGR1_MEM9_SIZE                                                                   (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM10_BASE                                                                  (0x73e16a000ULL)
#define CSL_CPT2_AGGR1_MEM10_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM11_BASE                                                                  (0x73e16b000ULL)
#define CSL_CPT2_AGGR1_MEM11_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM12_BASE                                                                  (0x73e16c000ULL)
#define CSL_CPT2_AGGR1_MEM12_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM13_BASE                                                                  (0x73e16d000ULL)
#define CSL_CPT2_AGGR1_MEM13_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM14_BASE                                                                  (0x73e16e000ULL)
#define CSL_CPT2_AGGR1_MEM14_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM15_BASE                                                                  (0x73e16f000ULL)
#define CSL_CPT2_AGGR1_MEM15_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM16_BASE                                                                  (0x73e170000ULL)
#define CSL_CPT2_AGGR1_MEM16_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM17_BASE                                                                  (0x73e171000ULL)
#define CSL_CPT2_AGGR1_MEM17_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM18_BASE                                                                  (0x73e172000ULL)
#define CSL_CPT2_AGGR1_MEM18_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM19_BASE                                                                  (0x73e173000ULL)
#define CSL_CPT2_AGGR1_MEM19_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM20_BASE                                                                  (0x73e174000ULL)
#define CSL_CPT2_AGGR1_MEM20_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM21_BASE                                                                  (0x73e175000ULL)
#define CSL_CPT2_AGGR1_MEM21_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM22_BASE                                                                  (0x73e176000ULL)
#define CSL_CPT2_AGGR1_MEM22_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM23_BASE                                                                  (0x73e177000ULL)
#define CSL_CPT2_AGGR1_MEM23_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM24_BASE                                                                  (0x73e178000ULL)
#define CSL_CPT2_AGGR1_MEM24_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM25_BASE                                                                  (0x73e179000ULL)
#define CSL_CPT2_AGGR1_MEM25_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM26_BASE                                                                  (0x73e17a000ULL)
#define CSL_CPT2_AGGR1_MEM26_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM27_BASE                                                                  (0x73e17b000ULL)
#define CSL_CPT2_AGGR1_MEM27_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM28_BASE                                                                  (0x73e17c000ULL)
#define CSL_CPT2_AGGR1_MEM28_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM29_BASE                                                                  (0x73e17d000ULL)
#define CSL_CPT2_AGGR1_MEM29_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM30_BASE                                                                  (0x73e17e000ULL)
#define CSL_CPT2_AGGR1_MEM30_SIZE                                                                  (0x1000ULL)
#define CSL_CPT2_AGGR1_MEM31_BASE                                                                  (0x73e17f000ULL)
#define CSL_CPT2_AGGR1_MEM31_SIZE                                                                  (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_ROM_TABLE_0_1_BASE                                                       (0x740000000ULL)
#define CSL_DEBUGSS_WRAP0_ROM_TABLE_0_1_SIZE                                                       (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_RESV0_1_BASE                                                             (0x740001000ULL)
#define CSL_DEBUGSS_WRAP0_RESV0_1_SIZE                                                             (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_CFGAP1_BASE                                                              (0x740002000ULL)
#define CSL_DEBUGSS_WRAP0_CFGAP1_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_APBAP1_BASE                                                              (0x740002100ULL)
#define CSL_DEBUGSS_WRAP0_APBAP1_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_AXIAP1_BASE                                                              (0x740002200ULL)
#define CSL_DEBUGSS_WRAP0_AXIAP1_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_PWRAP1_BASE                                                              (0x740002300ULL)
#define CSL_DEBUGSS_WRAP0_PWRAP1_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_PVIEW1_BASE                                                              (0x740002400ULL)
#define CSL_DEBUGSS_WRAP0_PVIEW1_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_JTAGAP1_BASE                                                             (0x740002500ULL)
#define CSL_DEBUGSS_WRAP0_JTAGAP1_SIZE                                                             (0x100ULL)
#define CSL_DEBUGSS_WRAP0_SECAP1_BASE                                                              (0x740002600ULL)
#define CSL_DEBUGSS_WRAP0_SECAP1_SIZE                                                              (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX0_CFG1_BASE                                                        (0x740002700ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX0_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX1_CFG1_BASE                                                        (0x740002800ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX1_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX2_CFG1_BASE                                                        (0x740002900ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX2_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX3_CFG1_BASE                                                        (0x740002a00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX3_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX4_CFG1_BASE                                                        (0x740002b00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX4_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX5_CFG1_BASE                                                        (0x740002c00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX5_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX6_CFG1_BASE                                                        (0x740002d00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX6_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX7_CFG1_BASE                                                        (0x740002e00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX7_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX8_CFG1_BASE                                                        (0x740002f00ULL)
#define CSL_DEBUGSS_WRAP0_CORTEX8_CFG1_SIZE                                                        (0x100ULL)
#define CSL_DEBUGSS_WRAP0_RESV1_1_BASE                                                             (0x740003000ULL)
#define CSL_DEBUGSS_WRAP0_RESV1_1_SIZE                                                             (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_RESV2_1_BASE                                                             (0x740004000ULL)
#define CSL_DEBUGSS_WRAP0_RESV2_1_SIZE                                                             (0x2000000ULL)
#define CSL_DEBUGSS_WRAP0_ROM_TABLE_1_1_BASE                                                       (0x760000000ULL)
#define CSL_DEBUGSS_WRAP0_ROM_TABLE_1_1_SIZE                                                       (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_CSCTI1_BASE                                                              (0x760001000ULL)
#define CSL_DEBUGSS_WRAP0_CSCTI1_SIZE                                                              (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_DRM1_BASE                                                                (0x760002000ULL)
#define CSL_DEBUGSS_WRAP0_DRM1_SIZE                                                                (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_RESV3_1_BASE                                                             (0x760003000ULL)
#define CSL_DEBUGSS_WRAP0_RESV3_1_SIZE                                                             (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_CSTPIU1_BASE                                                             (0x760004000ULL)
#define CSL_DEBUGSS_WRAP0_CSTPIU1_SIZE                                                             (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_CTF1_BASE                                                                (0x760005000ULL)
#define CSL_DEBUGSS_WRAP0_CTF1_SIZE                                                                (0x1000ULL)
#define CSL_DEBUGSS_WRAP0_RESV4_1_BASE                                                             (0x760006000ULL)
#define CSL_DEBUGSS_WRAP0_RESV4_1_SIZE                                                             (0x1000000ULL)
#define CSL_DEBUGSS_WRAP0_EXT_APB1_BASE                                                            (0x770000000ULL)
#define CSL_DEBUGSS_WRAP0_EXT_APB1_SIZE                                                            (0x10000000ULL)

/*
* Auto-Generated Instance CNT List - Main Domain
*/

#define CSL_A53SS_MAIN_CNT                                                                         (1U)
#define CSL_A53SS_CORE_MAIN_CNT                                                                    (4U)
#define CSL_A53_RS_BW_LIMITER_MAIN_CNT                                                             (1U)
#define CSL_A53_WS_BW_LIMITER_MAIN_CNT                                                             (1U)
#define CSL_CBASS_MAIN_CNT                                                                         (1U)
#define CSL_CBASS_CENTRAL_MAIN_CNT                                                                 (1U)
#define CSL_CBASS_DBG_MAIN_CNT                                                                     (1U)
#define CSL_CBASS_FW_MAIN_CNT                                                                      (1U)
#define CSL_CBASS_INFRA_MAIN_CNT                                                                   (1U)
#define CSL_CBASS_IPCSS_MAIN_CNT                                                                   (1U)
#define CSL_CBASS_MCASP_MAIN_CNT                                                                   (1U)
#define CSL_CBASS_MISC_PERI_MAIN_CNT                                                               (1U)
#define CSL_CMP_EVENT_INTROUTER_MAIN_CNT                                                           (1U)
#define CSL_COMPUTE_CLUSTER_MAIN_CNT                                                               (1U)
#define CSL_COMPUTE_CLUSTER_PBIST_MAIN_CNT                                                         (1U)
#define CSL_CPSW_MAIN_CNT                                                                          (1U)
#define CSL_CSI_RX_IF_MAIN_CNT                                                                     (1U)
#define CSL_CTRL_MMR_MAIN_CNT                                                                      (1U)
#define CSL_DBGSUSPENDROUTER_MAIN_CNT                                                              (1U)
#define CSL_DCC_MAIN_CNT                                                                           (7U)
#define CSL_DDPA_MAIN_CNT                                                                          (1U)
#define CSL_DDR16SS_MAIN_CNT                                                                       (1U)
#define CSL_DEBUGSS_MAIN_CNT                                                                       (1U)
#define CSL_DEBUGSS_WRAP_MAIN_CNT                                                                  (1U)
#define CSL_DFTSS_MAIN_CNT                                                                         (1U)
#define CSL_DMASS_MAIN_CNT                                                                         (1U)
#define CSL_DMASS_BCDMA_MAIN_CNT                                                                   (1U)
#define CSL_DMASS_CBASS_MAIN_CNT                                                                   (1U)
#define CSL_DMASS_COMMON_MAIN_CNT                                                                  (1U)
#define CSL_DMASS_INTAGGR_MAIN_CNT                                                                 (1U)
#define CSL_DMASS_IPCSS_MAIN_CNT                                                                   (1U)
#define CSL_DMASS_PKTDMA_MAIN_CNT                                                                  (1U)
#define CSL_DMASS_PSILCFG_MAIN_CNT                                                                 (1U)
#define CSL_DMASS_PSILSS_MAIN_CNT                                                                  (1U)
#define CSL_DMASS_RINGACC_MAIN_CNT                                                                 (1U)
#define CSL_DMASS_SEC_PROXY_MAIN_CNT                                                               (1U)
#define CSL_DPHY_RX_MAIN_CNT                                                                       (1U)
#define CSL_DSS_MAIN_CNT                                                                           (1U)
#define CSL_ECAP_MAIN_CNT                                                                          (3U)
#define CSL_EFUSE_MAIN_CNT                                                                         (1U)
#define CSL_EFUSE_MAIN_CHAIN_MAIN_CNT                                                              (1U)
#define CSL_EFUSE_ODP_MAIN_CNT                                                                     (1U)
#define CSL_ELM_MAIN_CNT                                                                           (1U)
#define CSL_EPWM_MAIN_CNT                                                                          (3U)
#define CSL_EQEP_MAIN_CNT                                                                          (3U)
#define CSL_ESM_MAIN_CNT                                                                           (1U)
#define CSL_FSS_MAIN_CNT                                                                           (1U)
#define CSL_FSS_FSAS_MAIN_CNT                                                                      (1U)
#define CSL_FSS_OSPI_MAIN_CNT                                                                      (1U)
#define CSL_GICSS_MAIN_CNT                                                                         (1U)
#define CSL_GPIO_MAIN_CNT                                                                          (2U)
#define CSL_GPMC_MAIN_CNT                                                                          (1U)
#define CSL_GPU_MAIN_CNT                                                                           (1U)
#define CSL_GPU_RS_BW_LIMITER_MAIN_CNT                                                             (1U)
#define CSL_GPU_WS_BW_LIMITER_MAIN_CNT                                                             (1U)
#define CSL_HSDIV0_MAIN_CNT                                                                        (4U)
#define CSL_HSDIV1_MAIN_CNT                                                                        (1U)
#define CSL_HSDIV4_MAIN_CNT                                                                        (3U)
#define CSL_HSM_MAIN_CNT                                                                           (1U)
#define CSL_I2C_MAIN_CNT                                                                           (4U)
#define CSL_ICSSM_MAIN_CNT                                                                         (1U)
#define CSL_K3_ARM_ATB_FUNNEL_3_32_MAIN_CNT                                                        (1U)
#define CSL_LED_MAIN_CNT                                                                           (1U)
#define CSL_MAILBOX_MAIN_CNT                                                                       (1U)
#define CSL_MAILBOX_CLUSTER_MAIN_CNT                                                               (1U)
#define CSL_MAIN_GPIOMUX_INTROUTER_MAIN_CNT                                                        (1U)
#define CSL_MAIN_SEC_MMR_MAIN_CNT                                                                  (1U)
#define CSL_MCAN_MAIN_CNT                                                                          (1U)
#define CSL_MCASP_MAIN_CNT                                                                         (3U)
#define CSL_MCRC64_MAIN_CNT                                                                        (1U)
#define CSL_MCSPI_MAIN_CNT                                                                         (3U)
#define CSL_MMCSD_MAIN_CNT                                                                         (3U)
#define CSL_PADCFG_CTRL_MAIN_CNT                                                                   (1U)
#define CSL_PBIST_MAIN_CNT                                                                         (2U)
#define CSL_PDMA_MAIN_CNT                                                                          (3U)
#define CSL_PLL_MAIN_CNT                                                                           (1U)
#define CSL_PLLCTRL_MAIN_CNT                                                                       (1U)
#define CSL_POSTDIV1_MAIN_CNT                                                                      (1U)
#define CSL_POSTDIV4_MAIN_CNT                                                                      (2U)
#define CSL_PSC_MAIN_CNT                                                                           (1U)
#define CSL_PSCSS_MAIN_CNT                                                                         (1U)
#define CSL_PSC_FW_MAIN_CNT                                                                        (1U)
#define CSL_PSRAMECC_MAIN_CNT                                                                      (1U)
#define CSL_PSRAMECC_16K_MAIN_CNT                                                                  (1U)
#define CSL_RTI_MAIN_CNT                                                                           (5U)
#define CSL_SA3_SS_MAIN_CNT                                                                        (1U)
#define CSL_SA3_SS_DMSS_ECCAGGR_MAIN_CNT                                                           (1U)
#define CSL_SA3_SS_DMSS_HSM_MAIN_CNT                                                               (1U)
#define CSL_SA3_SS_INTAGGR_MAIN_CNT                                                                (1U)
#define CSL_SA3_SS_PKTDMA_MAIN_CNT                                                                 (1U)
#define CSL_SA3_SS_PSILCFG_MAIN_CNT                                                                (1U)
#define CSL_SA3_SS_PSILSS_MAIN_CNT                                                                 (1U)
#define CSL_SA3_SS_RINGACC_MAIN_CNT                                                                (1U)
#define CSL_SA3_SS_SA_UL_MAIN_CNT                                                                  (1U)
#define CSL_SA3_SS_SEC_PROXY_MAIN_CNT                                                              (1U)
#define CSL_SMS_MAIN_CNT                                                                           (1U)
#define CSL_SMS_AESEIP38T_MAIN_CNT                                                                 (1U)
#define CSL_SMS_COMMON_MAIN_CNT                                                                    (1U)
#define CSL_SMS_CORTEX_M4F_SS_MAIN_CNT                                                             (2U)
#define CSL_SMS_CTI_MAIN_CNT                                                                       (2U)
#define CSL_SMS_DBG_AUTH_MAIN_CNT                                                                  (1U)
#define CSL_SMS_DMTIMER_MAIN_CNT                                                                   (4U)
#define CSL_SMS_DWT_MAIN_CNT                                                                       (2U)
#define CSL_SMS_FBP_MAIN_CNT                                                                       (2U)
#define CSL_SMS_FWMGR_MAIN_CNT                                                                     (1U)
#define CSL_SMS_HSM_CBASS_MAIN_CNT                                                                 (1U)
#define CSL_SMS_HSM_SRAM_MAIN_CNT                                                                  (2U)
#define CSL_SMS_ITM_MAIN_CNT                                                                       (2U)
#define CSL_SMS_PWRCTRL_MAIN_CNT                                                                   (1U)
#define CSL_SMS_RAT_MAIN_CNT                                                                       (2U)
#define CSL_SMS_ROM_MAIN_CNT                                                                       (1U)
#define CSL_SMS_RTI_MAIN_CNT                                                                       (2U)
#define CSL_SMS_SCS_MAIN_CNT                                                                       (2U)
#define CSL_SMS_SECCTRL_MAIN_CNT                                                                   (1U)
#define CSL_SMS_SEC_MGR_MAIN_CNT                                                                   (1U)
#define CSL_SMS_TIFS_CBASS_MAIN_CNT                                                                (1U)
#define CSL_SMS_TIFS_SRAM_MAIN_CNT                                                                 (2U)
#define CSL_SMS_WDTCTRL_MAIN_CNT                                                                   (2U)
#define CSL_SPINLOCK_MAIN_CNT                                                                      (1U)
#define CSL_STM_MAIN_CNT                                                                           (1U)
#define CSL_TIFS_MAIN_CNT                                                                          (1U)
#define CSL_TIMER_MAIN_CNT                                                                         (8U)
#define CSL_TIMESYNC_EVENT_ROUTER_MAIN_CNT                                                         (1U)
#define CSL_UART_MAIN_CNT                                                                          (7U)
#define CSL_USB_MAIN_CNT                                                                           (2U)

/*
* Auto-Generated Instance CNT List - Mcu Domain
*/

#define CSL_CBASS_MCU_CNT                                                                          (1U)
#define CSL_DCC_MCU_CNT                                                                            (1U)
#define CSL_GPIO_MCU_CNT                                                                           (1U)
#define CSL_HSDIV0_MCU_CNT                                                                         (1U)
#define CSL_HSDIV4_MCU_CNT                                                                         (1U)
#define CSL_I2C_MCU_CNT                                                                            (1U)
#define CSL_M4FSS_MCU_CNT                                                                          (1U)
#define CSL_M4FSS_CBASS_MCU_CNT                                                                    (1U)
#define CSL_M4FSS_CORE0_MCU_CNT                                                                    (1U)
#define CSL_M4FSS_CORTEX_M4F_SS_MCU_CNT                                                            (1U)
#define CSL_M4FSS_DRAM_MCU_CNT                                                                     (1U)
#define CSL_M4FSS_IRAM_MCU_CNT                                                                     (1U)
#define CSL_M4FSS_RAT_MCU_CNT                                                                      (1U)
#define CSL_MCAN_MCU_CNT                                                                           (2U)
#define CSL_MCRC64_MCU_CNT                                                                         (1U)
#define CSL_MCSPI_MCU_CNT                                                                          (2U)
#define CSL_MCU_MCU_CNT                                                                            (1U)
#define CSL_PLLCTRL_MCU_CNT                                                                        (1U)
#define CSL_PRG_MCU_MCU_CNT                                                                        (1U)
#define CSL_PRG_MCU_5POKS_MCU_CNT                                                                  (1U)
#define CSL_RTI_MCU_CNT                                                                            (1U)
#define CSL_TIMEOUT_MCU_CNT                                                                        (3U)
#define CSL_TIMER_MCU_CNT                                                                          (4U)
#define CSL_UART_MCU_CNT                                                                           (1U)

/*
* Auto-Generated Instance CNT List - Wakeup Domain
*/

#define CSL_CBASS_WKUP_CNT                                                                         (1U)
#define CSL_CBASS_SAFE_WKUP_CNT                                                                    (1U)
#define CSL_CTRL_MMR_WKUP_CNT                                                                      (2U)
#define CSL_DEEPSLEEP_SOURCES_WKUP_CNT                                                             (1U)
#define CSL_ESM_WKUP_CNT                                                                           (1U)
#define CSL_GTC_WKUP_CNT                                                                           (1U)
#define CSL_I2C_WKUP_CNT                                                                           (1U)
#define CSL_ICEMELTER_WKUP_CNT                                                                     (1U)
#define CSL_MCU_GPIOMUX_INTROUTER_WKUP_CNT                                                         (1U)
#define CSL_PADCFG_CTRL_WKUP_CNT                                                                   (1U)
#define CSL_PBIST_WKUP_CNT                                                                         (1U)
#define CSL_PLL_WKUP_CNT                                                                           (1U)
#define CSL_PSC_WKUP_CNT                                                                           (1U)
#define CSL_R5FSS_WKUP_CNT                                                                         (1U)
#define CSL_R5FSS_COMMON0_WKUP_CNT                                                                 (1U)
#define CSL_R5FSS_CORE0_WKUP_CNT                                                                   (1U)
#define CSL_R5FSS_SS0_WKUP_CNT                                                                     (1U)
#define CSL_ROM_WKUP_CNT                                                                           (1U)
#define CSL_RTCSS_WKUP_CNT                                                                         (1U)
#define CSL_RTI_WKUP_CNT                                                                           (1U)
#define CSL_TIMEOUT_WKUP_CNT                                                                       (1U)
#define CSL_TIMER_WKUP_CNT                                                                         (2U)
#define CSL_UART_WKUP_CNT                                                                          (1U)
#define CSL_VTM_WKUP_CNT                                                                           (1U)
#define CSL_WKUP_SEC_MMR_WKUP_CNT                                                                  (1U)

#ifdef __cplusplus
}
#endif
#endif /* CSLR_SOC_BASEADDRESS_H_ */


