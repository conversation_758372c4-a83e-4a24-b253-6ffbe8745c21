/*
 * GPIO0_67引脚操作示例 - 修正后的实现
 * 
 * 展示如何正确设置GPIO0_67为输出模式并输出高电平
 * 根据AM62x技术手册修正了方向寄存器的位定义
 */

#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/io.h>
#include "am62x_gpio_linux.h"

/*
 * 修正后的GPIO0_67操作函数
 * 
 * 根据AM62x技术手册：
 * - DIR位=0表示输出模式(OUTPUT)
 * - DIR位=1表示输入模式(INPUT)
 */
void set_gpio67_output_high_corrected(void __iomem *gpio_base)
{
    uint32_t pin_num = 67;
    
    // 计算寄存器参数
    uint32_t reg_index = GPIO_GET_REG_INDEX(pin_num);  // 67 >> 5 = 2
    uint32_t bit_pos = GPIO_GET_BIT_POS(pin_num);      // 67 - (2 << 5) = 3
    uint32_t bit_mask = GPIO_GET_BIT_MASK(pin_num);    // 1 << 3 = 0x08
    
    // 寄存器偏移计算
    uint32_t dir_offset = GPIO_DIR(reg_index);         // 0x10 + (2 * 0x28) = 0x60
    uint32_t set_offset = GPIO_SET_DATA(reg_index);    // 0x18 + (2 * 0x28) = 0x68
    
    printk(KERN_INFO "GPIO67操作参数:\n");
    printk(KERN_INFO "  reg_index = %u\n", reg_index);
    printk(KERN_INFO "  bit_pos = %u\n", bit_pos);
    printk(KERN_INFO "  bit_mask = 0x%08X\n", bit_mask);
    printk(KERN_INFO "  dir_offset = 0x%08X\n", dir_offset);
    printk(KERN_INFO "  set_offset = 0x%08X\n", set_offset);
    
    // 步骤1：设置GPIO67为输出模式
    // 根据技术手册：DIR位=0表示输出模式
    uint32_t dir_val = readl(gpio_base + dir_offset);
    printk(KERN_INFO "DIR寄存器当前值: 0x%08X\n", dir_val);
    
    // 清除第3位，设置为输出模式(0)
    dir_val &= ~bit_mask;  // 清除bit[3]，设为0表示输出
    
    printk(KERN_INFO "DIR寄存器新值: 0x%08X\n", dir_val);
    writel(dir_val, gpio_base + dir_offset);
    
    // 验证写入
    uint32_t dir_readback = readl(gpio_base + dir_offset);
    printk(KERN_INFO "DIR寄存器回读值: 0x%08X\n", dir_readback);
    
    // 步骤2：设置GPIO67输出高电平
    printk(KERN_INFO "设置GPIO67输出高电平，写入SET寄存器: 0x%08X\n", bit_mask);
    writel(bit_mask, gpio_base + set_offset);
    
    // 验证输出状态（读取OUT_DATA寄存器）
    uint32_t out_offset = GPIO_OUT_DATA(reg_index);    // 0x14 + (2 * 0x28) = 0x64
    uint32_t out_val = readl(gpio_base + out_offset);
    printk(KERN_INFO "OUT_DATA寄存器值: 0x%08X\n", out_val);
    
    if (out_val & bit_mask) {
        printk(KERN_INFO "GPIO67成功设置为高电平\n");
    } else {
        printk(KERN_WARNING "GPIO67设置失败\n");
    }
}

/*
 * 对比：错误的实现方式（修正前）
 */
void set_gpio67_output_high_wrong(void __iomem *gpio_base)
{
    uint32_t pin_num = 67;
    uint32_t reg_index = GPIO_GET_REG_INDEX(pin_num);
    uint32_t bit_pos = GPIO_GET_BIT_POS(pin_num);
    uint32_t bit_mask = GPIO_GET_BIT_MASK(pin_num);
    uint32_t dir_offset = GPIO_DIR(reg_index);
    uint32_t set_offset = GPIO_SET_DATA(reg_index);
    
    printk(KERN_INFO "错误的实现方式（修正前）:\n");
    
    // 错误：设置DIR位为1，误以为1表示输出
    uint32_t dir_val = readl(gpio_base + dir_offset);
    dir_val |= bit_mask;  // 错误：设置bit[3]=1，实际上这是输入模式
    writel(dir_val, gpio_base + dir_offset);
    
    printk(KERN_WARNING "错误：DIR位设为1，实际配置为输入模式\n");
    
    // 即使设置SET寄存器，由于方向错误，GPIO不会输出
    writel(bit_mask, gpio_base + set_offset);
    
    printk(KERN_WARNING "由于方向设置错误，GPIO67无法输出高电平\n");
}

/*
 * 使用修正后的驱动函数进行操作
 */
void gpio67_operation_with_driver(struct am62x_gpio_chip *gpio_chip)
{
    int ret;
    
    printk(KERN_INFO "使用修正后的驱动函数操作GPIO67:\n");
    
    // 设置为输出模式（使用修正后的常量）
    ret = am62x_gpio_set_direction(gpio_chip, 67, GPIO_DIRECTION_OUTPUT);
    if (ret) {
        printk(KERN_ERR "设置GPIO67方向失败: %d\n", ret);
        return;
    }
    printk(KERN_INFO "GPIO67方向设置为输出模式成功\n");
    
    // 设置为高电平
    am62x_gpio_set_value(gpio_chip, 67, 1);
    printk(KERN_INFO "GPIO67设置为高电平\n");
    
    // 读取当前值进行验证
    int current_value = am62x_gpio_get_value(gpio_chip, 67);
    printk(KERN_INFO "GPIO67当前值: %d\n", current_value);
}

/*
 * 详细的寄存器操作说明
 */
void explain_gpio67_registers(void)
{
    printk(KERN_INFO "=== GPIO0_67寄存器操作详解 ===\n");
    printk(KERN_INFO "引脚编号: 67\n");
    printk(KERN_INFO "寄存器组: 第2组 (67 >> 5 = 2)\n");
    printk(KERN_INFO "位位置: 第3位 (67 %% 32 = 3)\n");
    printk(KERN_INFO "位掩码: 0x08 (1 << 3)\n");
    printk(KERN_INFO "\n");
    
    printk(KERN_INFO "相关寄存器偏移:\n");
    printk(KERN_INFO "  DIR寄存器:      0x60 (0x10 + 2*0x28)\n");
    printk(KERN_INFO "  OUT_DATA寄存器: 0x64 (0x14 + 2*0x28)\n");
    printk(KERN_INFO "  SET_DATA寄存器: 0x68 (0x18 + 2*0x28)\n");
    printk(KERN_INFO "  CLR_DATA寄存器: 0x6C (0x1C + 2*0x28)\n");
    printk(KERN_INFO "\n");
    
    printk(KERN_INFO "方向设置（修正后）:\n");
    printk(KERN_INFO "  输出模式: DIR[3] = 0\n");
    printk(KERN_INFO "  输入模式: DIR[3] = 1\n");
    printk(KERN_INFO "\n");
    
    printk(KERN_INFO "输出控制:\n");
    printk(KERN_INFO "  设置高电平: 向SET_DATA寄存器写入0x08\n");
    printk(KERN_INFO "  设置低电平: 向CLR_DATA寄存器写入0x08\n");
    printk(KERN_INFO "\n");
    
    printk(KERN_INFO "状态读取:\n");
    printk(KERN_INFO "  输入值: 读取IN_DATA寄存器bit[3]\n");
    printk(KERN_INFO "  输出值: 读取OUT_DATA寄存器bit[3]\n");
}

/*
 * 模块初始化函数（示例）
 */
static int __init gpio67_example_init(void)
{
    printk(KERN_INFO "GPIO67操作示例模块加载\n");
    explain_gpio67_registers();
    return 0;
}

/*
 * 模块清理函数（示例）
 */
static void __exit gpio67_example_exit(void)
{
    printk(KERN_INFO "GPIO67操作示例模块卸载\n");
}

module_init(gpio67_example_init);
module_exit(gpio67_example_exit);

MODULE_DESCRIPTION("GPIO67操作示例 - 修正后的实现");
MODULE_AUTHOR("AM62x GPIO Driver Example");
MODULE_LICENSE("GPL v2");
