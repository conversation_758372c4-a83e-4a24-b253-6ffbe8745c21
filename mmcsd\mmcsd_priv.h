/*
 *  Copyright (C) 2021-24 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
/**
 *  \file v0/mmcsd_priv.h
 *
 *  \brief MMCSD Driver private header file.
 */

#ifndef MMCSD_PRIV_H
#define MMCSD_PRIV_H

#ifdef __cplusplus
extern "C"
{
#endif


#include <stdint.h>
#include <drivers/mmcsd.h>

/* Macro to extract bit field */
#define MMCSD_GET_BITFIELD(val, start, end) (((val) >> (start)) & ((1U << ((end)-(start)+1))-1))

/* Make command from command index, type, data present or not, response length */
#define MMCSD_MAKE_CMD(idx, type, dp, rlen) (((idx) << 8) | ((type) << 6) | ((dp) << 5) | ((rlen) << 0))

/* Command types */
#define MMCSD_CMD_TYPE_NORMAL      (0U)
#define MMCSD_CMD_TYPE_SUSPEND     (1U)
#define MMCSD_CMD_TYPE_RESUME      (2U)
#define MMCSD_CMD_TYPE_ABORT       (3U)

/* Response types */
#define MMCSD_CMD_RESP_TYPE_NONE   (0U)
#define MMCSD_CMD_RESP_TYPE_L136   (1U)
#define MMCSD_CMD_RESP_TYPE_L48    (2U)
#define MMCSD_CMD_RESP_TYPE_L48_B  (3U)

/* MMC protocol commands */
#define MMCSD_MMC_CMD_0  (MMCSD_MAKE_CMD(0,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_NONE))
#define MMCSD_MMC_CMD_1  (MMCSD_MAKE_CMD(1,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_2  (MMCSD_MAKE_CMD(2,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L136))
#define MMCSD_MMC_CMD_3  (MMCSD_MAKE_CMD(3,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_4  (MMCSD_MAKE_CMD(4,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_NONE))
#define MMCSD_MMC_CMD_6  (MMCSD_MAKE_CMD(6,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48_B))
#define MMCSD_MMC_CMD_7  (MMCSD_MAKE_CMD(7,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48_B))
#define MMCSD_MMC_CMD_8  (MMCSD_MAKE_CMD(8,  MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_9  (MMCSD_MAKE_CMD(9,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L136))
#define MMCSD_MMC_CMD_12 (MMCSD_MAKE_CMD(12, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48_B))
#define MMCSD_MMC_CMD_13 (MMCSD_MAKE_CMD(13, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_17 (MMCSD_MAKE_CMD(17, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_18 (MMCSD_MAKE_CMD(18, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_21 (MMCSD_MAKE_CMD(21, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_23 (MMCSD_MAKE_CMD(23, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_24 (MMCSD_MAKE_CMD(24, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_MMC_CMD_25 (MMCSD_MAKE_CMD(25, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))

#define MMCSD_MMC_CMD(x) (MMCSD_MMC_CMD_##x)

/* SD protocol commands */
/* SD CMDs */
#define MMCSD_SD_CMD_0  (MMCSD_MAKE_CMD(0,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_NONE))
#define MMCSD_SD_CMD_2  (MMCSD_MAKE_CMD(2,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L136))
#define MMCSD_SD_CMD_3  (MMCSD_MAKE_CMD(3,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_6  (MMCSD_MAKE_CMD(6,  MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_7  (MMCSD_MAKE_CMD(7,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48_B))
#define MMCSD_SD_CMD_8  (MMCSD_MAKE_CMD(8,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_9  (MMCSD_MAKE_CMD(9,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L136))
#define MMCSD_SD_CMD_11 (MMCSD_MAKE_CMD(11, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_12 (MMCSD_MAKE_CMD(12, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48_B))
#define MMCSD_SD_CMD_13 (MMCSD_MAKE_CMD(13, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_16 (MMCSD_MAKE_CMD(16, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_17 (MMCSD_MAKE_CMD(17, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_18 (MMCSD_MAKE_CMD(18, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_19 (MMCSD_MAKE_CMD(19, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_21 (MMCSD_MAKE_CMD(21, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_23 (MMCSD_MAKE_CMD(23, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_24 (MMCSD_MAKE_CMD(24, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_25 (MMCSD_MAKE_CMD(25, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_41 (MMCSD_MAKE_CMD(41, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_51 (MMCSD_MAKE_CMD(51, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_CMD_55 (MMCSD_MAKE_CMD(55, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))

#define MMCSD_SD_CMD(x) (MMCSD_SD_CMD_##x)

/* SD ACMDs */
#define MMCSD_SD_ACMD_6  (MMCSD_MAKE_CMD(6,  MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_ACMD_13 (MMCSD_MAKE_CMD(13, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_ACMD_22 (MMCSD_MAKE_CMD(22, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_ACMD_23 (MMCSD_MAKE_CMD(23, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_ACMD_41 (MMCSD_MAKE_CMD(41, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_ACMD_42 (MMCSD_MAKE_CMD(42, MMCSD_CMD_TYPE_NORMAL, 0, MMCSD_CMD_RESP_TYPE_L48))
#define MMCSD_SD_ACMD_51 (MMCSD_MAKE_CMD(51, MMCSD_CMD_TYPE_NORMAL, 1, MMCSD_CMD_RESP_TYPE_L48))

#define MMCSD_SD_ACMD(x) (MMCSD_SD_ACMD_##x)

/* Device data parsers */
int32_t MMCSD_parseCIDEmmc(MMCSD_EmmcDeviceData *data, uint32_t resp[4]);
int32_t MMCSD_parseCSDEmmc(MMCSD_EmmcDeviceData *data, uint32_t resp[4]);
int32_t MMCSD_parseECSDEmmc(MMCSD_EmmcDeviceData *data, uint8_t ecsdData[512]);

int32_t MMCSD_parseCIDSd(MMCSD_SdDeviceData *data, uint32_t resp[4]);
int32_t MMCSD_parseCSDSd(MMCSD_SdDeviceData *data, uint32_t resp[4]);
int32_t MMCSD_parseSCRSd(MMCSD_SdDeviceData *data, uint8_t *scr);


#ifdef __cplusplus
}
#endif

#endif /* MMCSD_PRIV_H */