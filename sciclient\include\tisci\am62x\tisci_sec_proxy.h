/*
 *  Copyright (C) 2017-2024 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
/**
 * \ingroup TISCI
 * \defgroup tisci_sec_proxy tisci_sec_proxy
 *
 * DMSC controls the power management, security and resource management
 * of the device.
 *
 *
 * @{
 */
/**
 *
 *  \brief  This file contains:
 *
 *          WARNING!!: Autogenerated file from SYSFW. DO NOT MODIFY!!
 * System Firmware Source File
 *
 * Secure Proxy indices for AM62X device
 *
 * Data version: 230918_161319
 *
 */
#ifndef AM62X_TISCI_SEC_PROXY_H
#define AM62X_TISCI_SEC_PROXY_H

#ifdef __cplusplus
extern "C"
{
#endif


/*
 * Secure Proxy configurations for MAIN_0_R5_0 host
 */

/** Thread ID macro for MAIN_0_R5_0 response */
#define TISCI_SEC_PROXY_MAIN_0_R5_0_READ_RESPONSE_THREAD_ID (0U)
/** Num messages macro for MAIN_0_R5_0 response */
#define TISCI_SEC_PROXY_MAIN_0_R5_0_READ_RESPONSE_NUM_MESSAGES (11U)

/** Thread ID macro for MAIN_0_R5_0 low_priority */
#define TISCI_SEC_PROXY_MAIN_0_R5_0_WRITE_LOW_PRIORITY_THREAD_ID (1U)
/** Num messages macro for MAIN_0_R5_0 low_priority */
#define TISCI_SEC_PROXY_MAIN_0_R5_0_WRITE_LOW_PRIORITY_NUM_MESSAGES (10U)

/*
 * Secure Proxy configurations for MAIN_0_R5_1 host
 */

/** Thread ID macro for MAIN_0_R5_1 response */
#define TISCI_SEC_PROXY_MAIN_0_R5_1_READ_RESPONSE_THREAD_ID (2U)
/** Num messages macro for MAIN_0_R5_1 response */
#define TISCI_SEC_PROXY_MAIN_0_R5_1_READ_RESPONSE_NUM_MESSAGES (11U)

/** Thread ID macro for MAIN_0_R5_1 low_priority */
#define TISCI_SEC_PROXY_MAIN_0_R5_1_WRITE_LOW_PRIORITY_THREAD_ID (3U)
/** Num messages macro for MAIN_0_R5_1 low_priority */
#define TISCI_SEC_PROXY_MAIN_0_R5_1_WRITE_LOW_PRIORITY_NUM_MESSAGES (10U)

/*
 * Secure Proxy configurations for MAIN_0_R5_2 host
 */

/** Thread ID macro for MAIN_0_R5_2 response */
#define TISCI_SEC_PROXY_MAIN_0_R5_2_READ_RESPONSE_THREAD_ID (4U)
/** Num messages macro for MAIN_0_R5_2 response */
#define TISCI_SEC_PROXY_MAIN_0_R5_2_READ_RESPONSE_NUM_MESSAGES (2U)

/** Thread ID macro for MAIN_0_R5_2 low_priority */
#define TISCI_SEC_PROXY_MAIN_0_R5_2_WRITE_LOW_PRIORITY_THREAD_ID (5U)
/** Num messages macro for MAIN_0_R5_2 low_priority */
#define TISCI_SEC_PROXY_MAIN_0_R5_2_WRITE_LOW_PRIORITY_NUM_MESSAGES (1U)

/*
 * Secure Proxy configurations for MAIN_0_R5_3 host
 */

/** Thread ID macro for MAIN_0_R5_3 response */
#define TISCI_SEC_PROXY_MAIN_0_R5_3_READ_RESPONSE_THREAD_ID (6U)
/** Num messages macro for MAIN_0_R5_3 response */
#define TISCI_SEC_PROXY_MAIN_0_R5_3_READ_RESPONSE_NUM_MESSAGES (2U)

/** Thread ID macro for MAIN_0_R5_3 low_priority */
#define TISCI_SEC_PROXY_MAIN_0_R5_3_WRITE_LOW_PRIORITY_THREAD_ID (7U)
/** Num messages macro for MAIN_0_R5_3 low_priority */
#define TISCI_SEC_PROXY_MAIN_0_R5_3_WRITE_LOW_PRIORITY_NUM_MESSAGES (1U)

/*
 * Secure Proxy configurations for A53_0 host
 */

/** Thread ID macro for A53_0 response */
#define TISCI_SEC_PROXY_A53_0_READ_RESPONSE_THREAD_ID (8U)
/** Num messages macro for A53_0 response */
#define TISCI_SEC_PROXY_A53_0_READ_RESPONSE_NUM_MESSAGES (11U)

/** Thread ID macro for A53_0 low_priority */
#define TISCI_SEC_PROXY_A53_0_WRITE_LOW_PRIORITY_THREAD_ID (9U)
/** Num messages macro for A53_0 low_priority */
#define TISCI_SEC_PROXY_A53_0_WRITE_LOW_PRIORITY_NUM_MESSAGES (10U)

/*
 * Secure Proxy configurations for A53_1 host
 */

/** Thread ID macro for A53_1 response */
#define TISCI_SEC_PROXY_A53_1_READ_RESPONSE_THREAD_ID (10U)
/** Num messages macro for A53_1 response */
#define TISCI_SEC_PROXY_A53_1_READ_RESPONSE_NUM_MESSAGES (11U)

/** Thread ID macro for A53_1 low_priority */
#define TISCI_SEC_PROXY_A53_1_WRITE_LOW_PRIORITY_THREAD_ID (11U)
/** Num messages macro for A53_1 low_priority */
#define TISCI_SEC_PROXY_A53_1_WRITE_LOW_PRIORITY_NUM_MESSAGES (10U)

/*
 * Secure Proxy configurations for A53_2 host
 */

/** Thread ID macro for A53_2 response */
#define TISCI_SEC_PROXY_A53_2_READ_RESPONSE_THREAD_ID (12U)
/** Num messages macro for A53_2 response */
#define TISCI_SEC_PROXY_A53_2_READ_RESPONSE_NUM_MESSAGES (6U)

/** Thread ID macro for A53_2 low_priority */
#define TISCI_SEC_PROXY_A53_2_WRITE_LOW_PRIORITY_THREAD_ID (13U)
/** Num messages macro for A53_2 low_priority */
#define TISCI_SEC_PROXY_A53_2_WRITE_LOW_PRIORITY_NUM_MESSAGES (5U)

/*
 * Secure Proxy configurations for A53_3 host
 */

/** Thread ID macro for A53_3 response */
#define TISCI_SEC_PROXY_A53_3_READ_RESPONSE_THREAD_ID (14U)
/** Num messages macro for A53_3 response */
#define TISCI_SEC_PROXY_A53_3_READ_RESPONSE_NUM_MESSAGES (6U)

/** Thread ID macro for A53_3 low_priority */
#define TISCI_SEC_PROXY_A53_3_WRITE_LOW_PRIORITY_THREAD_ID (15U)
/** Num messages macro for A53_3 low_priority */
#define TISCI_SEC_PROXY_A53_3_WRITE_LOW_PRIORITY_NUM_MESSAGES (5U)

/*
 * Secure Proxy configurations for M4_0 host
 */

/** Thread ID macro for M4_0 response */
#define TISCI_SEC_PROXY_M4_0_READ_RESPONSE_THREAD_ID (16U)
/** Num messages macro for M4_0 response */
#define TISCI_SEC_PROXY_M4_0_READ_RESPONSE_NUM_MESSAGES (6U)

/** Thread ID macro for M4_0 low_priority */
#define TISCI_SEC_PROXY_M4_0_WRITE_LOW_PRIORITY_THREAD_ID (17U)
/** Num messages macro for M4_0 low_priority */
#define TISCI_SEC_PROXY_M4_0_WRITE_LOW_PRIORITY_NUM_MESSAGES (5U)

/*
 * Secure Proxy configurations for GPU host
 */

/** Thread ID macro for GPU response */
#define TISCI_SEC_PROXY_GPU_READ_RESPONSE_THREAD_ID (18U)
/** Num messages macro for GPU response */
#define TISCI_SEC_PROXY_GPU_READ_RESPONSE_NUM_MESSAGES (2U)

/** Thread ID macro for GPU low_priority */
#define TISCI_SEC_PROXY_GPU_WRITE_LOW_PRIORITY_THREAD_ID (19U)
/** Num messages macro for GPU low_priority */
#define TISCI_SEC_PROXY_GPU_WRITE_LOW_PRIORITY_NUM_MESSAGES (1U)

/*
 * Secure Proxy configurations for A53_4 host
 */

/** Thread ID macro for A53_4 response */
#define TISCI_SEC_PROXY_A53_4_READ_RESPONSE_THREAD_ID (20U)
/** Num messages macro for A53_4 response */
#define TISCI_SEC_PROXY_A53_4_READ_RESPONSE_NUM_MESSAGES (6U)

/** Thread ID macro for A53_4 low_priority */
#define TISCI_SEC_PROXY_A53_4_WRITE_LOW_PRIORITY_THREAD_ID (21U)
/** Num messages macro for A53_4 low_priority */
#define TISCI_SEC_PROXY_A53_4_WRITE_LOW_PRIORITY_NUM_MESSAGES (5U)

/*
 * Secure Proxy configurations for DM2TIFS host
 */

/** Thread ID macro for DM2TIFS response */
#define TISCI_SEC_PROXY_DM2TIFS_READ_RESPONSE_THREAD_ID (22U)
/** Num messages macro for DM2TIFS response */
#define TISCI_SEC_PROXY_DM2TIFS_READ_RESPONSE_NUM_MESSAGES (4U)

/** Thread ID macro for DM2TIFS low_priority */
#define TISCI_SEC_PROXY_DM2TIFS_WRITE_LOW_PRIORITY_THREAD_ID (23U)
/** Num messages macro for DM2TIFS low_priority */
#define TISCI_SEC_PROXY_DM2TIFS_WRITE_LOW_PRIORITY_NUM_MESSAGES (2U)

/*
 * Secure Proxy configurations for TIFS2DM host
 */

/** Thread ID macro for TIFS2DM response */
#define TISCI_SEC_PROXY_TIFS2DM_READ_RESPONSE_THREAD_ID (24U)
/** Num messages macro for TIFS2DM response */
#define TISCI_SEC_PROXY_TIFS2DM_READ_RESPONSE_NUM_MESSAGES (4U)

/** Thread ID macro for TIFS2DM low_priority */
#define TISCI_SEC_PROXY_TIFS2DM_WRITE_LOW_PRIORITY_THREAD_ID (25U)
/** Num messages macro for TIFS2DM low_priority */
#define TISCI_SEC_PROXY_TIFS2DM_WRITE_LOW_PRIORITY_NUM_MESSAGES (2U)

/*
 * Secure Proxy configurations for HSM host
 */

/** Thread ID macro for HSM response */
#define TISCI_SEC_PROXY_HSM_READ_RESPONSE_THREAD_ID (0U)
/** Num messages macro for HSM response */
#define TISCI_SEC_PROXY_HSM_READ_RESPONSE_NUM_MESSAGES (8U)

/** Thread ID macro for HSM low_priority */
#define TISCI_SEC_PROXY_HSM_WRITE_LOW_PRIORITY_THREAD_ID (1U)
/** Num messages macro for HSM low_priority */
#define TISCI_SEC_PROXY_HSM_WRITE_LOW_PRIORITY_NUM_MESSAGES (8U)



#ifdef __cplusplus
}
#endif

#endif /* AM62X_TISCI_SEC_PROXY_H */

/** @} */
