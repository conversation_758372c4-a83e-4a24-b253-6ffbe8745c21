/* parasoft-begin-suppress MISRA2012-RULE-1_1_a_c99-2 "C99 - limits, DRV-3919" parasoft-begin-suppress MISRA2012-RULE-1_1_a_c90-2 "C90 - limits, DRV-3919" */
/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef LPDDR4_RW_MASKS_H_
#define LPDDR4_RW_MASKS_H_

#include "../../common/include/cdn_stdint.h"

extern uint32_t g_lpddr4_ddr_controller_rw_mask[423];
extern uint32_t g_lpddr4_pi_rw_mask[345];
extern uint32_t g_lpddr4_data_slice_0_rw_mask[126];
extern uint32_t g_lpddr4_data_slice_1_rw_mask[126];
extern uint32_t g_lpddr4_address_slice_0_rw_mask[43];
extern uint32_t g_lpddr4_address_slice_1_rw_mask[43];
extern uint32_t g_lpddr4_address_slice_2_rw_mask[43];
extern uint32_t g_lpddr4_phy_core_rw_mask[126];

#endif /* LPDDR4_RW_MASKS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-1_1_a_c90-2 "C90 - limits, DRV-3919" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_a_c99-2 "C99 - limits, DRV-3919" */

