/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_ADDRESS_SLICE_1_MACROS_H_
#define REG_LPDDR4_ADDRESS_SLICE_1_MACROS_H_

#define LPDDR4__DENALI_PHY_1280_READ_MASK                            0x000107FFU
#define LPDDR4__DENALI_PHY_1280_WRITE_MASK                           0x000107FFU
#define LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1_SHIFT    0U
#define LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1_WIDTH   11U
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1__REG DENALI_PHY_1280
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_BYPASS_OVERRIDE_1_MASK  0x00010000U
#define LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_BYPASS_OVERRIDE_1_SHIFT         16U
#define LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_BYPASS_OVERRIDE_1_WIDTH          1U
#define LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_BYPASS_OVERRIDE_1_WOCLR          0U
#define LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_BYPASS_OVERRIDE_1_WOSET          0U
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_1__REG DENALI_PHY_1280
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_1__FLD LPDDR4__DENALI_PHY_1280__PHY_ADR_CLK_BYPASS_OVERRIDE_1

#define LPDDR4__DENALI_PHY_1280__SC_PHY_ADR_MANUAL_CLEAR_1_MASK      0x07000000U
#define LPDDR4__DENALI_PHY_1280__SC_PHY_ADR_MANUAL_CLEAR_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_1280__SC_PHY_ADR_MANUAL_CLEAR_1_WIDTH              3U
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_1__REG DENALI_PHY_1280
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_1__FLD LPDDR4__DENALI_PHY_1280__SC_PHY_ADR_MANUAL_CLEAR_1

#define LPDDR4__DENALI_PHY_1281_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1281_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1281__PHY_ADR_LPBK_RESULT_OBS_1_MASK      0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1281__PHY_ADR_LPBK_RESULT_OBS_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_1281__PHY_ADR_LPBK_RESULT_OBS_1_WIDTH             32U
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_1__REG DENALI_PHY_1281
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_1__FLD LPDDR4__DENALI_PHY_1281__PHY_ADR_LPBK_RESULT_OBS_1

#define LPDDR4__DENALI_PHY_1282_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1282_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1282__PHY_ADR_LPBK_ERROR_COUNT_OBS_1_MASK 0x0000FFFFU
#define LPDDR4__DENALI_PHY_1282__PHY_ADR_LPBK_ERROR_COUNT_OBS_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_1282__PHY_ADR_LPBK_ERROR_COUNT_OBS_1_WIDTH        16U
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_1__REG DENALI_PHY_1282
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_1__FLD LPDDR4__DENALI_PHY_1282__PHY_ADR_LPBK_ERROR_COUNT_OBS_1

#define LPDDR4__DENALI_PHY_1282__PHY_ADR_MEAS_DLY_STEP_VALUE_1_MASK  0x00FF0000U
#define LPDDR4__DENALI_PHY_1282__PHY_ADR_MEAS_DLY_STEP_VALUE_1_SHIFT         16U
#define LPDDR4__DENALI_PHY_1282__PHY_ADR_MEAS_DLY_STEP_VALUE_1_WIDTH          8U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_1__REG DENALI_PHY_1282
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_1__FLD LPDDR4__DENALI_PHY_1282__PHY_ADR_MEAS_DLY_STEP_VALUE_1

#define LPDDR4__DENALI_PHY_1282__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1_MASK 0x0F000000U
#define LPDDR4__DENALI_PHY_1282__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1_SHIFT  24U
#define LPDDR4__DENALI_PHY_1282__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1_WIDTH   4U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1__REG DENALI_PHY_1282
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_1282__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_1283_READ_MASK                            0xFF7F07FFU
#define LPDDR4__DENALI_PHY_1283_WRITE_MASK                           0xFF7F07FFU
#define LPDDR4__DENALI_PHY_1283__PHY_ADR_MASTER_DLY_LOCK_OBS_1_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1283__PHY_ADR_MASTER_DLY_LOCK_OBS_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_1283__PHY_ADR_MASTER_DLY_LOCK_OBS_1_WIDTH         11U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_1__REG DENALI_PHY_1283
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_1__FLD LPDDR4__DENALI_PHY_1283__PHY_ADR_MASTER_DLY_LOCK_OBS_1

#define LPDDR4__DENALI_PHY_1283__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1_MASK 0x007F0000U
#define LPDDR4__DENALI_PHY_1283__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_1283__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1_WIDTH         7U
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_1283
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_1283__PHY_ADR_BASE_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_1283__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_1283__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1_SHIFT       24U
#define LPDDR4__DENALI_PHY_1283__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1_WIDTH        8U
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_1283
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_1283__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_1284_READ_MASK                            0x01000707U
#define LPDDR4__DENALI_PHY_1284_WRITE_MASK                           0x01000707U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1_MASK 0x00000007U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1_WIDTH        3U
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1__REG DENALI_PHY_1284
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1__FLD LPDDR4__DENALI_PHY_1284__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_1

#define LPDDR4__DENALI_PHY_1284__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1_MASK 0x00000700U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1_SHIFT       8U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1_WIDTH       3U
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1__REG DENALI_PHY_1284
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_1284__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_1284__SC_PHY_ADR_SNAP_OBS_REGS_1_MASK     0x00010000U
#define LPDDR4__DENALI_PHY_1284__SC_PHY_ADR_SNAP_OBS_REGS_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_1284__SC_PHY_ADR_SNAP_OBS_REGS_1_WIDTH             1U
#define LPDDR4__DENALI_PHY_1284__SC_PHY_ADR_SNAP_OBS_REGS_1_WOCLR             0U
#define LPDDR4__DENALI_PHY_1284__SC_PHY_ADR_SNAP_OBS_REGS_1_WOSET             0U
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_1__REG DENALI_PHY_1284
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_1__FLD LPDDR4__DENALI_PHY_1284__SC_PHY_ADR_SNAP_OBS_REGS_1

#define LPDDR4__DENALI_PHY_1284__PHY_ADR_TSEL_ENABLE_1_MASK          0x01000000U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_TSEL_ENABLE_1_SHIFT                 24U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_TSEL_ENABLE_1_WIDTH                  1U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_TSEL_ENABLE_1_WOCLR                  0U
#define LPDDR4__DENALI_PHY_1284__PHY_ADR_TSEL_ENABLE_1_WOSET                  0U
#define LPDDR4__PHY_ADR_TSEL_ENABLE_1__REG DENALI_PHY_1284
#define LPDDR4__PHY_ADR_TSEL_ENABLE_1__FLD LPDDR4__DENALI_PHY_1284__PHY_ADR_TSEL_ENABLE_1

#define LPDDR4__DENALI_PHY_1285_READ_MASK                            0x011F7F7FU
#define LPDDR4__DENALI_PHY_1285_WRITE_MASK                           0x011F7F7FU
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_LPBK_CONTROL_1_MASK         0x0000007FU
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_LPBK_CONTROL_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_LPBK_CONTROL_1_WIDTH                 7U
#define LPDDR4__PHY_ADR_LPBK_CONTROL_1__REG DENALI_PHY_1285
#define LPDDR4__PHY_ADR_LPBK_CONTROL_1__FLD LPDDR4__DENALI_PHY_1285__PHY_ADR_LPBK_CONTROL_1

#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PRBS_PATTERN_START_1_MASK   0x00007F00U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PRBS_PATTERN_START_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PRBS_PATTERN_START_1_WIDTH           7U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_1__REG DENALI_PHY_1285
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_1__FLD LPDDR4__DENALI_PHY_1285__PHY_ADR_PRBS_PATTERN_START_1

#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PRBS_PATTERN_MASK_1_MASK    0x001F0000U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PRBS_PATTERN_MASK_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PRBS_PATTERN_MASK_1_WIDTH            5U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_1__REG DENALI_PHY_1285
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_1__FLD LPDDR4__DENALI_PHY_1285__PHY_ADR_PRBS_PATTERN_MASK_1

#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PWR_RDC_DISABLE_1_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PWR_RDC_DISABLE_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PWR_RDC_DISABLE_1_WIDTH              1U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PWR_RDC_DISABLE_1_WOCLR              0U
#define LPDDR4__DENALI_PHY_1285__PHY_ADR_PWR_RDC_DISABLE_1_WOSET              0U
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_1__REG DENALI_PHY_1285
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_1__FLD LPDDR4__DENALI_PHY_1285__PHY_ADR_PWR_RDC_DISABLE_1

#define LPDDR4__DENALI_PHY_1286_READ_MASK                            0x01070301U
#define LPDDR4__DENALI_PHY_1286_WRITE_MASK                           0x01070301U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_SHIFT    0U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_WIDTH    1U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_WOCLR    0U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1_WOSET    0U
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1__REG DENALI_PHY_1286
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1__FLD LPDDR4__DENALI_PHY_1286__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_1

#define LPDDR4__DENALI_PHY_1286__PHY_ADR_TYPE_1_MASK                 0x00000300U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_TYPE_1_SHIFT                         8U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_TYPE_1_WIDTH                         2U
#define LPDDR4__PHY_ADR_TYPE_1__REG DENALI_PHY_1286
#define LPDDR4__PHY_ADR_TYPE_1__FLD LPDDR4__DENALI_PHY_1286__PHY_ADR_TYPE_1

#define LPDDR4__DENALI_PHY_1286__PHY_ADR_WRADDR_SHIFT_OBS_1_MASK     0x00070000U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_WRADDR_SHIFT_OBS_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_WRADDR_SHIFT_OBS_1_WIDTH             3U
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_1__REG DENALI_PHY_1286
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_1__FLD LPDDR4__DENALI_PHY_1286__PHY_ADR_WRADDR_SHIFT_OBS_1

#define LPDDR4__DENALI_PHY_1286__PHY_ADR_IE_MODE_1_MASK              0x01000000U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_IE_MODE_1_SHIFT                     24U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_IE_MODE_1_WIDTH                      1U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_IE_MODE_1_WOCLR                      0U
#define LPDDR4__DENALI_PHY_1286__PHY_ADR_IE_MODE_1_WOSET                      0U
#define LPDDR4__PHY_ADR_IE_MODE_1__REG DENALI_PHY_1286
#define LPDDR4__PHY_ADR_IE_MODE_1__FLD LPDDR4__DENALI_PHY_1286__PHY_ADR_IE_MODE_1

#define LPDDR4__DENALI_PHY_1287_READ_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1287_WRITE_MASK                           0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1287__PHY_ADR_DDL_MODE_1_MASK             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1287__PHY_ADR_DDL_MODE_1_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1287__PHY_ADR_DDL_MODE_1_WIDTH                    27U
#define LPDDR4__PHY_ADR_DDL_MODE_1__REG DENALI_PHY_1287
#define LPDDR4__PHY_ADR_DDL_MODE_1__FLD LPDDR4__DENALI_PHY_1287__PHY_ADR_DDL_MODE_1

#define LPDDR4__DENALI_PHY_1288_READ_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_1288_WRITE_MASK                           0x0000003FU
#define LPDDR4__DENALI_PHY_1288__PHY_ADR_DDL_MASK_1_MASK             0x0000003FU
#define LPDDR4__DENALI_PHY_1288__PHY_ADR_DDL_MASK_1_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1288__PHY_ADR_DDL_MASK_1_WIDTH                     6U
#define LPDDR4__PHY_ADR_DDL_MASK_1__REG DENALI_PHY_1288
#define LPDDR4__PHY_ADR_DDL_MASK_1__FLD LPDDR4__DENALI_PHY_1288__PHY_ADR_DDL_MASK_1

#define LPDDR4__DENALI_PHY_1289_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1289_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1289__PHY_ADR_DDL_TEST_OBS_1_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1289__PHY_ADR_DDL_TEST_OBS_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1289__PHY_ADR_DDL_TEST_OBS_1_WIDTH                32U
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_1__REG DENALI_PHY_1289
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_1__FLD LPDDR4__DENALI_PHY_1289__PHY_ADR_DDL_TEST_OBS_1

#define LPDDR4__DENALI_PHY_1290_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1290_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1290__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1290__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_1290__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1_WIDTH       32U
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1__REG DENALI_PHY_1290
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1__FLD LPDDR4__DENALI_PHY_1290__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_1

#define LPDDR4__DENALI_PHY_1291_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1291_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1291__PHY_ADR_CALVL_START_1_MASK          0x000007FFU
#define LPDDR4__DENALI_PHY_1291__PHY_ADR_CALVL_START_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1291__PHY_ADR_CALVL_START_1_WIDTH                 11U
#define LPDDR4__PHY_ADR_CALVL_START_1__REG DENALI_PHY_1291
#define LPDDR4__PHY_ADR_CALVL_START_1__FLD LPDDR4__DENALI_PHY_1291__PHY_ADR_CALVL_START_1

#define LPDDR4__DENALI_PHY_1291__PHY_ADR_CALVL_COARSE_DLY_1_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_1291__PHY_ADR_CALVL_COARSE_DLY_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_1291__PHY_ADR_CALVL_COARSE_DLY_1_WIDTH            11U
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_1__REG DENALI_PHY_1291
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_1__FLD LPDDR4__DENALI_PHY_1291__PHY_ADR_CALVL_COARSE_DLY_1

#define LPDDR4__DENALI_PHY_1292_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1292_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1292__PHY_ADR_CALVL_QTR_1_MASK            0x000007FFU
#define LPDDR4__DENALI_PHY_1292__PHY_ADR_CALVL_QTR_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1292__PHY_ADR_CALVL_QTR_1_WIDTH                   11U
#define LPDDR4__PHY_ADR_CALVL_QTR_1__REG DENALI_PHY_1292
#define LPDDR4__PHY_ADR_CALVL_QTR_1__FLD LPDDR4__DENALI_PHY_1292__PHY_ADR_CALVL_QTR_1

#define LPDDR4__DENALI_PHY_1293_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1293_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1293__PHY_ADR_CALVL_SWIZZLE0_1_MASK       0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1293__PHY_ADR_CALVL_SWIZZLE0_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_1293__PHY_ADR_CALVL_SWIZZLE0_1_WIDTH              24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_1__REG DENALI_PHY_1293
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_1__FLD LPDDR4__DENALI_PHY_1293__PHY_ADR_CALVL_SWIZZLE0_1

#define LPDDR4__DENALI_PHY_1294_READ_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1294_WRITE_MASK                           0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1294__PHY_ADR_CALVL_SWIZZLE1_1_MASK       0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1294__PHY_ADR_CALVL_SWIZZLE1_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_1294__PHY_ADR_CALVL_SWIZZLE1_1_WIDTH              24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_1__REG DENALI_PHY_1294
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_1__FLD LPDDR4__DENALI_PHY_1294__PHY_ADR_CALVL_SWIZZLE1_1

#define LPDDR4__DENALI_PHY_1294__PHY_ADR_CALVL_RANK_CTRL_1_MASK      0x03000000U
#define LPDDR4__DENALI_PHY_1294__PHY_ADR_CALVL_RANK_CTRL_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_1294__PHY_ADR_CALVL_RANK_CTRL_1_WIDTH              2U
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_1__REG DENALI_PHY_1294
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_1__FLD LPDDR4__DENALI_PHY_1294__PHY_ADR_CALVL_RANK_CTRL_1

#define LPDDR4__DENALI_PHY_1295_READ_MASK                            0x01FF0F03U
#define LPDDR4__DENALI_PHY_1295_WRITE_MASK                           0x01FF0F03U
#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_NUM_PATTERNS_1_MASK   0x00000003U
#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_NUM_PATTERNS_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_NUM_PATTERNS_1_WIDTH           2U
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_1__REG DENALI_PHY_1295
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_1__FLD LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_NUM_PATTERNS_1

#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_RESP_WAIT_CNT_1_MASK  0x00000F00U
#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_RESP_WAIT_CNT_1_SHIFT          8U
#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_RESP_WAIT_CNT_1_WIDTH          4U
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_1__REG DENALI_PHY_1295
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_1__FLD LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_RESP_WAIT_CNT_1

#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1_MASK 0x01FF0000U
#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1_SHIFT 16U
#define LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1_WIDTH  9U
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1__REG DENALI_PHY_1295
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1__FLD LPDDR4__DENALI_PHY_1295__PHY_ADR_CALVL_PERIODIC_START_OFFSET_1

#define LPDDR4__DENALI_PHY_1296_READ_MASK                            0x07000001U
#define LPDDR4__DENALI_PHY_1296_WRITE_MASK                           0x07000001U
#define LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_DEBUG_MODE_1_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_DEBUG_MODE_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_DEBUG_MODE_1_WIDTH             1U
#define LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_DEBUG_MODE_1_WOCLR             0U
#define LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_DEBUG_MODE_1_WOSET             0U
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_1__REG DENALI_PHY_1296
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_1__FLD LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_DEBUG_MODE_1

#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_DEBUG_CONT_1_MASK  0x00000100U
#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_DEBUG_CONT_1_SHIFT          8U
#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_DEBUG_CONT_1_WIDTH          1U
#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_DEBUG_CONT_1_WOCLR          0U
#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_DEBUG_CONT_1_WOSET          0U
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_1__REG DENALI_PHY_1296
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_1__FLD LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_DEBUG_CONT_1

#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_ERROR_CLR_1_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_ERROR_CLR_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_ERROR_CLR_1_WIDTH           1U
#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_ERROR_CLR_1_WOCLR           0U
#define LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_ERROR_CLR_1_WOSET           0U
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_1__REG DENALI_PHY_1296
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_1__FLD LPDDR4__DENALI_PHY_1296__SC_PHY_ADR_CALVL_ERROR_CLR_1

#define LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_OBS_SELECT_1_MASK     0x07000000U
#define LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_OBS_SELECT_1_SHIFT            24U
#define LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_OBS_SELECT_1_WIDTH             3U
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_1__REG DENALI_PHY_1296
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_1296__PHY_ADR_CALVL_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_1297_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1297_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1297__PHY_ADR_CALVL_CH0_OBS0_1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1297__PHY_ADR_CALVL_CH0_OBS0_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_1297__PHY_ADR_CALVL_CH0_OBS0_1_WIDTH              32U
#define LPDDR4__PHY_ADR_CALVL_CH0_OBS0_1__REG DENALI_PHY_1297
#define LPDDR4__PHY_ADR_CALVL_CH0_OBS0_1__FLD LPDDR4__DENALI_PHY_1297__PHY_ADR_CALVL_CH0_OBS0_1

#define LPDDR4__DENALI_PHY_1298_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1298_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1298__PHY_ADR_CALVL_CH1_OBS0_1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1298__PHY_ADR_CALVL_CH1_OBS0_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_1298__PHY_ADR_CALVL_CH1_OBS0_1_WIDTH              32U
#define LPDDR4__PHY_ADR_CALVL_CH1_OBS0_1__REG DENALI_PHY_1298
#define LPDDR4__PHY_ADR_CALVL_CH1_OBS0_1__FLD LPDDR4__DENALI_PHY_1298__PHY_ADR_CALVL_CH1_OBS0_1

#define LPDDR4__DENALI_PHY_1299_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1299_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1299__PHY_ADR_CALVL_OBS1_1_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1299__PHY_ADR_CALVL_OBS1_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1299__PHY_ADR_CALVL_OBS1_1_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS1_1__REG DENALI_PHY_1299
#define LPDDR4__PHY_ADR_CALVL_OBS1_1__FLD LPDDR4__DENALI_PHY_1299__PHY_ADR_CALVL_OBS1_1

#define LPDDR4__DENALI_PHY_1300_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1300_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1300__PHY_ADR_CALVL_OBS2_1_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1300__PHY_ADR_CALVL_OBS2_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1300__PHY_ADR_CALVL_OBS2_1_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS2_1__REG DENALI_PHY_1300
#define LPDDR4__PHY_ADR_CALVL_OBS2_1__FLD LPDDR4__DENALI_PHY_1300__PHY_ADR_CALVL_OBS2_1

#define LPDDR4__DENALI_PHY_1301_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1301_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1301__PHY_ADR_CALVL_FG_0_1_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1301__PHY_ADR_CALVL_FG_0_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1301__PHY_ADR_CALVL_FG_0_1_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_0_1__REG DENALI_PHY_1301
#define LPDDR4__PHY_ADR_CALVL_FG_0_1__FLD LPDDR4__DENALI_PHY_1301__PHY_ADR_CALVL_FG_0_1

#define LPDDR4__DENALI_PHY_1302_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1302_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1302__PHY_ADR_CALVL_BG_0_1_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1302__PHY_ADR_CALVL_BG_0_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1302__PHY_ADR_CALVL_BG_0_1_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_0_1__REG DENALI_PHY_1302
#define LPDDR4__PHY_ADR_CALVL_BG_0_1__FLD LPDDR4__DENALI_PHY_1302__PHY_ADR_CALVL_BG_0_1

#define LPDDR4__DENALI_PHY_1303_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1303_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1303__PHY_ADR_CALVL_FG_1_1_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1303__PHY_ADR_CALVL_FG_1_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1303__PHY_ADR_CALVL_FG_1_1_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_1_1__REG DENALI_PHY_1303
#define LPDDR4__PHY_ADR_CALVL_FG_1_1__FLD LPDDR4__DENALI_PHY_1303__PHY_ADR_CALVL_FG_1_1

#define LPDDR4__DENALI_PHY_1304_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1304_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1304__PHY_ADR_CALVL_BG_1_1_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1304__PHY_ADR_CALVL_BG_1_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1304__PHY_ADR_CALVL_BG_1_1_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_1_1__REG DENALI_PHY_1304
#define LPDDR4__PHY_ADR_CALVL_BG_1_1__FLD LPDDR4__DENALI_PHY_1304__PHY_ADR_CALVL_BG_1_1

#define LPDDR4__DENALI_PHY_1305_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1305_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1305__PHY_ADR_CALVL_FG_2_1_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1305__PHY_ADR_CALVL_FG_2_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1305__PHY_ADR_CALVL_FG_2_1_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_2_1__REG DENALI_PHY_1305
#define LPDDR4__PHY_ADR_CALVL_FG_2_1__FLD LPDDR4__DENALI_PHY_1305__PHY_ADR_CALVL_FG_2_1

#define LPDDR4__DENALI_PHY_1306_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1306_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1306__PHY_ADR_CALVL_BG_2_1_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1306__PHY_ADR_CALVL_BG_2_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1306__PHY_ADR_CALVL_BG_2_1_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_2_1__REG DENALI_PHY_1306
#define LPDDR4__PHY_ADR_CALVL_BG_2_1__FLD LPDDR4__DENALI_PHY_1306__PHY_ADR_CALVL_BG_2_1

#define LPDDR4__DENALI_PHY_1307_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1307_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1307__PHY_ADR_CALVL_FG_3_1_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1307__PHY_ADR_CALVL_FG_3_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1307__PHY_ADR_CALVL_FG_3_1_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_3_1__REG DENALI_PHY_1307
#define LPDDR4__PHY_ADR_CALVL_FG_3_1__FLD LPDDR4__DENALI_PHY_1307__PHY_ADR_CALVL_FG_3_1

#define LPDDR4__DENALI_PHY_1308_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1308_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1308__PHY_ADR_CALVL_BG_3_1_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1308__PHY_ADR_CALVL_BG_3_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1308__PHY_ADR_CALVL_BG_3_1_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_3_1__REG DENALI_PHY_1308
#define LPDDR4__PHY_ADR_CALVL_BG_3_1__FLD LPDDR4__DENALI_PHY_1308__PHY_ADR_CALVL_BG_3_1

#define LPDDR4__DENALI_PHY_1309_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1309_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1309__PHY_ADR_ADDR_SEL_1_MASK             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1309__PHY_ADR_ADDR_SEL_1_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1309__PHY_ADR_ADDR_SEL_1_WIDTH                    30U
#define LPDDR4__PHY_ADR_ADDR_SEL_1__REG DENALI_PHY_1309
#define LPDDR4__PHY_ADR_ADDR_SEL_1__FLD LPDDR4__DENALI_PHY_1309__PHY_ADR_ADDR_SEL_1

#define LPDDR4__DENALI_PHY_1310_READ_MASK                            0x3F3F03FFU
#define LPDDR4__DENALI_PHY_1310_WRITE_MASK                           0x3F3F03FFU
#define LPDDR4__DENALI_PHY_1310__PHY_ADR_LP4_BOOT_SLV_DELAY_1_MASK   0x000003FFU
#define LPDDR4__DENALI_PHY_1310__PHY_ADR_LP4_BOOT_SLV_DELAY_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_1310__PHY_ADR_LP4_BOOT_SLV_DELAY_1_WIDTH          10U
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_1__REG DENALI_PHY_1310
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_1__FLD LPDDR4__DENALI_PHY_1310__PHY_ADR_LP4_BOOT_SLV_DELAY_1

#define LPDDR4__DENALI_PHY_1310__PHY_ADR_BIT_MASK_1_MASK             0x003F0000U
#define LPDDR4__DENALI_PHY_1310__PHY_ADR_BIT_MASK_1_SHIFT                    16U
#define LPDDR4__DENALI_PHY_1310__PHY_ADR_BIT_MASK_1_WIDTH                     6U
#define LPDDR4__PHY_ADR_BIT_MASK_1__REG DENALI_PHY_1310
#define LPDDR4__PHY_ADR_BIT_MASK_1__FLD LPDDR4__DENALI_PHY_1310__PHY_ADR_BIT_MASK_1

#define LPDDR4__DENALI_PHY_1310__PHY_ADR_SEG_MASK_1_MASK             0x3F000000U
#define LPDDR4__DENALI_PHY_1310__PHY_ADR_SEG_MASK_1_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1310__PHY_ADR_SEG_MASK_1_WIDTH                     6U
#define LPDDR4__PHY_ADR_SEG_MASK_1__REG DENALI_PHY_1310
#define LPDDR4__PHY_ADR_SEG_MASK_1__FLD LPDDR4__DENALI_PHY_1310__PHY_ADR_SEG_MASK_1

#define LPDDR4__DENALI_PHY_1311_READ_MASK                            0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_1311_WRITE_MASK                           0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_CALVL_TRAIN_MASK_1_MASK     0x0000003FU
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_CALVL_TRAIN_MASK_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_CALVL_TRAIN_MASK_1_WIDTH             6U
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_1__REG DENALI_PHY_1311
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_1__FLD LPDDR4__DENALI_PHY_1311__PHY_ADR_CALVL_TRAIN_MASK_1

#define LPDDR4__DENALI_PHY_1311__PHY_ADR_CSLVL_TRAIN_MASK_1_MASK     0x00003F00U
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_CSLVL_TRAIN_MASK_1_SHIFT             8U
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_CSLVL_TRAIN_MASK_1_WIDTH             6U
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_1__REG DENALI_PHY_1311
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_1__FLD LPDDR4__DENALI_PHY_1311__PHY_ADR_CSLVL_TRAIN_MASK_1

#define LPDDR4__DENALI_PHY_1311__PHY_ADR_STATIC_TOG_DISABLE_1_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_STATIC_TOG_DISABLE_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_STATIC_TOG_DISABLE_1_WIDTH           4U
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_1__REG DENALI_PHY_1311
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_1__FLD LPDDR4__DENALI_PHY_1311__PHY_ADR_STATIC_TOG_DISABLE_1

#define LPDDR4__DENALI_PHY_1311__PHY_ADR_SW_TXIO_CTRL_1_MASK         0x3F000000U
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_SW_TXIO_CTRL_1_SHIFT                24U
#define LPDDR4__DENALI_PHY_1311__PHY_ADR_SW_TXIO_CTRL_1_WIDTH                 6U
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_1__REG DENALI_PHY_1311
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_1__FLD LPDDR4__DENALI_PHY_1311__PHY_ADR_SW_TXIO_CTRL_1

#define LPDDR4__DENALI_PHY_1312_READ_MASK                            0xFFFFFF03U
#define LPDDR4__DENALI_PHY_1312_WRITE_MASK                           0xFFFFFF03U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_INIT_DISABLE_1_MASK      0x00000003U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_INIT_DISABLE_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_INIT_DISABLE_1_WIDTH              2U
#define LPDDR4__PHY_ADR_DC_INIT_DISABLE_1__REG DENALI_PHY_1312
#define LPDDR4__PHY_ADR_DC_INIT_DISABLE_1__FLD LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_INIT_DISABLE_1

#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR0_CLK_ADJUST_1_MASK   0x0000FF00U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR0_CLK_ADJUST_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR0_CLK_ADJUST_1_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR0_CLK_ADJUST_1__REG DENALI_PHY_1312
#define LPDDR4__PHY_ADR_DC_ADR0_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR0_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR1_CLK_ADJUST_1_MASK   0x00FF0000U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR1_CLK_ADJUST_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR1_CLK_ADJUST_1_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR1_CLK_ADJUST_1__REG DENALI_PHY_1312
#define LPDDR4__PHY_ADR_DC_ADR1_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR1_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR2_CLK_ADJUST_1_MASK   0xFF000000U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR2_CLK_ADJUST_1_SHIFT          24U
#define LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR2_CLK_ADJUST_1_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR2_CLK_ADJUST_1__REG DENALI_PHY_1312
#define LPDDR4__PHY_ADR_DC_ADR2_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_1312__PHY_ADR_DC_ADR2_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_1313_READ_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1313_WRITE_MASK                           0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR3_CLK_ADJUST_1_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR3_CLK_ADJUST_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR3_CLK_ADJUST_1_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR3_CLK_ADJUST_1__REG DENALI_PHY_1313
#define LPDDR4__PHY_ADR_DC_ADR3_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR3_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR4_CLK_ADJUST_1_MASK   0x0000FF00U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR4_CLK_ADJUST_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR4_CLK_ADJUST_1_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR4_CLK_ADJUST_1__REG DENALI_PHY_1313
#define LPDDR4__PHY_ADR_DC_ADR4_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR4_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR5_CLK_ADJUST_1_MASK   0x00FF0000U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR5_CLK_ADJUST_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR5_CLK_ADJUST_1_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR5_CLK_ADJUST_1__REG DENALI_PHY_1313
#define LPDDR4__PHY_ADR_DC_ADR5_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_1313__PHY_ADR_DC_ADR5_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_1_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_1_SHIFT 24U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_1_WIDTH  1U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_1_WOCLR  0U
#define LPDDR4__DENALI_PHY_1313__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_1_WOSET  0U
#define LPDDR4__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_1__REG DENALI_PHY_1313
#define LPDDR4__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_1__FLD LPDDR4__DENALI_PHY_1313__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_1

#define LPDDR4__DENALI_PHY_1314_READ_MASK                            0x3F03FFFFU
#define LPDDR4__DENALI_PHY_1314_WRITE_MASK                           0x3F03FFFFU
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_CAL_SAMPLE_WAIT_1_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_CAL_SAMPLE_WAIT_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_CAL_SAMPLE_WAIT_1_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_CAL_SAMPLE_WAIT_1__REG DENALI_PHY_1314
#define LPDDR4__PHY_ADR_DC_CAL_SAMPLE_WAIT_1__FLD LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_CAL_SAMPLE_WAIT_1

#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_CAL_TIMEOUT_1_MASK       0x0000FF00U
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_CAL_TIMEOUT_1_SHIFT               8U
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_CAL_TIMEOUT_1_WIDTH               8U
#define LPDDR4__PHY_ADR_DC_CAL_TIMEOUT_1__REG DENALI_PHY_1314
#define LPDDR4__PHY_ADR_DC_CAL_TIMEOUT_1__FLD LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_CAL_TIMEOUT_1

#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_WEIGHT_1_MASK            0x00030000U
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_WEIGHT_1_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_WEIGHT_1_WIDTH                    2U
#define LPDDR4__PHY_ADR_DC_WEIGHT_1__REG DENALI_PHY_1314
#define LPDDR4__PHY_ADR_DC_WEIGHT_1__FLD LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_WEIGHT_1

#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_ADJUST_START_1_MASK      0x3F000000U
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_ADJUST_START_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_ADJUST_START_1_WIDTH              6U
#define LPDDR4__PHY_ADR_DC_ADJUST_START_1__REG DENALI_PHY_1314
#define LPDDR4__PHY_ADR_DC_ADJUST_START_1__FLD LPDDR4__DENALI_PHY_1314__PHY_ADR_DC_ADJUST_START_1

#define LPDDR4__DENALI_PHY_1315_READ_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_PHY_1315_WRITE_MASK                           0x0101FFFFU
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_SAMPLE_CNT_1_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_SAMPLE_CNT_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_SAMPLE_CNT_1_WIDTH         8U
#define LPDDR4__PHY_ADR_DC_ADJUST_SAMPLE_CNT_1__REG DENALI_PHY_1315
#define LPDDR4__PHY_ADR_DC_ADJUST_SAMPLE_CNT_1__FLD LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_SAMPLE_CNT_1

#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_THRSHLD_1_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_THRSHLD_1_SHIFT            8U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_THRSHLD_1_WIDTH            8U
#define LPDDR4__PHY_ADR_DC_ADJUST_THRSHLD_1__REG DENALI_PHY_1315
#define LPDDR4__PHY_ADR_DC_ADJUST_THRSHLD_1__FLD LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_THRSHLD_1

#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_DIRECT_1_MASK     0x00010000U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_DIRECT_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_DIRECT_1_WIDTH             1U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_DIRECT_1_WOCLR             0U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_DIRECT_1_WOSET             0U
#define LPDDR4__PHY_ADR_DC_ADJUST_DIRECT_1__REG DENALI_PHY_1315
#define LPDDR4__PHY_ADR_DC_ADJUST_DIRECT_1__FLD LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_ADJUST_DIRECT_1

#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_CAL_POLARITY_1_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_CAL_POLARITY_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_CAL_POLARITY_1_WIDTH              1U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_CAL_POLARITY_1_WOCLR              0U
#define LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_CAL_POLARITY_1_WOSET              0U
#define LPDDR4__PHY_ADR_DC_CAL_POLARITY_1__REG DENALI_PHY_1315
#define LPDDR4__PHY_ADR_DC_CAL_POLARITY_1__FLD LPDDR4__DENALI_PHY_1315__PHY_ADR_DC_CAL_POLARITY_1

#define LPDDR4__DENALI_PHY_1316_READ_MASK                            0x00003F01U
#define LPDDR4__DENALI_PHY_1316_WRITE_MASK                           0x00003F01U
#define LPDDR4__DENALI_PHY_1316__PHY_ADR_DC_CAL_START_1_MASK         0x00000001U
#define LPDDR4__DENALI_PHY_1316__PHY_ADR_DC_CAL_START_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1316__PHY_ADR_DC_CAL_START_1_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1316__PHY_ADR_DC_CAL_START_1_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1316__PHY_ADR_DC_CAL_START_1_WOSET                 0U
#define LPDDR4__PHY_ADR_DC_CAL_START_1__REG DENALI_PHY_1316
#define LPDDR4__PHY_ADR_DC_CAL_START_1__FLD LPDDR4__DENALI_PHY_1316__PHY_ADR_DC_CAL_START_1

#define LPDDR4__DENALI_PHY_1316__PHY_ADR_SW_TXPWR_CTRL_1_MASK        0x00003F00U
#define LPDDR4__DENALI_PHY_1316__PHY_ADR_SW_TXPWR_CTRL_1_SHIFT                8U
#define LPDDR4__DENALI_PHY_1316__PHY_ADR_SW_TXPWR_CTRL_1_WIDTH                6U
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_1__REG DENALI_PHY_1316
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_1__FLD LPDDR4__DENALI_PHY_1316__PHY_ADR_SW_TXPWR_CTRL_1

#define LPDDR4__DENALI_PHY_1317_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1317_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1317__PHY_ADR_TSEL_SELECT_1_MASK          0x000000FFU
#define LPDDR4__DENALI_PHY_1317__PHY_ADR_TSEL_SELECT_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1317__PHY_ADR_TSEL_SELECT_1_WIDTH                  8U
#define LPDDR4__PHY_ADR_TSEL_SELECT_1__REG DENALI_PHY_1317
#define LPDDR4__PHY_ADR_TSEL_SELECT_1__FLD LPDDR4__DENALI_PHY_1317__PHY_ADR_TSEL_SELECT_1

#define LPDDR4__DENALI_PHY_1317__PHY_ADR_DC_CAL_CLK_SEL_1_MASK       0x00000700U
#define LPDDR4__DENALI_PHY_1317__PHY_ADR_DC_CAL_CLK_SEL_1_SHIFT               8U
#define LPDDR4__DENALI_PHY_1317__PHY_ADR_DC_CAL_CLK_SEL_1_WIDTH               3U
#define LPDDR4__PHY_ADR_DC_CAL_CLK_SEL_1__REG DENALI_PHY_1317
#define LPDDR4__PHY_ADR_DC_CAL_CLK_SEL_1__FLD LPDDR4__DENALI_PHY_1317__PHY_ADR_DC_CAL_CLK_SEL_1

#define LPDDR4__DENALI_PHY_1317__PHY_PAD_ADR_IO_CFG_1_MASK           0x07FF0000U
#define LPDDR4__DENALI_PHY_1317__PHY_PAD_ADR_IO_CFG_1_SHIFT                  16U
#define LPDDR4__DENALI_PHY_1317__PHY_PAD_ADR_IO_CFG_1_WIDTH                  11U
#define LPDDR4__PHY_PAD_ADR_IO_CFG_1__REG DENALI_PHY_1317
#define LPDDR4__PHY_PAD_ADR_IO_CFG_1__FLD LPDDR4__DENALI_PHY_1317__PHY_PAD_ADR_IO_CFG_1

#define LPDDR4__DENALI_PHY_1318_READ_MASK                            0x07FF1F07U
#define LPDDR4__DENALI_PHY_1318_WRITE_MASK                           0x07FF1F07U
#define LPDDR4__DENALI_PHY_1318__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1_MASK  0x00000007U
#define LPDDR4__DENALI_PHY_1318__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_1318__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1_WIDTH          3U
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1__REG DENALI_PHY_1318
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1__FLD LPDDR4__DENALI_PHY_1318__PHY_PAD_ADR_RX_PCLK_CLK_SEL_1

#define LPDDR4__DENALI_PHY_1318__PHY_ADR0_SW_WRADDR_SHIFT_1_MASK     0x00001F00U
#define LPDDR4__DENALI_PHY_1318__PHY_ADR0_SW_WRADDR_SHIFT_1_SHIFT             8U
#define LPDDR4__DENALI_PHY_1318__PHY_ADR0_SW_WRADDR_SHIFT_1_WIDTH             5U
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_1__REG DENALI_PHY_1318
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_1318__PHY_ADR0_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_1318__PHY_ADR0_CLK_WR_SLAVE_DELAY_1_MASK  0x07FF0000U
#define LPDDR4__DENALI_PHY_1318__PHY_ADR0_CLK_WR_SLAVE_DELAY_1_SHIFT         16U
#define LPDDR4__DENALI_PHY_1318__PHY_ADR0_CLK_WR_SLAVE_DELAY_1_WIDTH         11U
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_1318
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1318__PHY_ADR0_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1319_READ_MASK                            0x1F07FF1FU
#define LPDDR4__DENALI_PHY_1319_WRITE_MASK                           0x1F07FF1FU
#define LPDDR4__DENALI_PHY_1319__PHY_ADR1_SW_WRADDR_SHIFT_1_MASK     0x0000001FU
#define LPDDR4__DENALI_PHY_1319__PHY_ADR1_SW_WRADDR_SHIFT_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_1319__PHY_ADR1_SW_WRADDR_SHIFT_1_WIDTH             5U
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_1__REG DENALI_PHY_1319
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_1319__PHY_ADR1_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_1319__PHY_ADR1_CLK_WR_SLAVE_DELAY_1_MASK  0x0007FF00U
#define LPDDR4__DENALI_PHY_1319__PHY_ADR1_CLK_WR_SLAVE_DELAY_1_SHIFT          8U
#define LPDDR4__DENALI_PHY_1319__PHY_ADR1_CLK_WR_SLAVE_DELAY_1_WIDTH         11U
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_1319
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1319__PHY_ADR1_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1319__PHY_ADR2_SW_WRADDR_SHIFT_1_MASK     0x1F000000U
#define LPDDR4__DENALI_PHY_1319__PHY_ADR2_SW_WRADDR_SHIFT_1_SHIFT            24U
#define LPDDR4__DENALI_PHY_1319__PHY_ADR2_SW_WRADDR_SHIFT_1_WIDTH             5U
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_1__REG DENALI_PHY_1319
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_1319__PHY_ADR2_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_1320_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1320_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1320__PHY_ADR2_CLK_WR_SLAVE_DELAY_1_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1320__PHY_ADR2_CLK_WR_SLAVE_DELAY_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_1320__PHY_ADR2_CLK_WR_SLAVE_DELAY_1_WIDTH         11U
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_1320
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1320__PHY_ADR2_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1320__PHY_ADR3_SW_WRADDR_SHIFT_1_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1320__PHY_ADR3_SW_WRADDR_SHIFT_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_1320__PHY_ADR3_SW_WRADDR_SHIFT_1_WIDTH             5U
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_1__REG DENALI_PHY_1320
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_1320__PHY_ADR3_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_1321_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1321_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1321__PHY_ADR3_CLK_WR_SLAVE_DELAY_1_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1321__PHY_ADR3_CLK_WR_SLAVE_DELAY_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_1321__PHY_ADR3_CLK_WR_SLAVE_DELAY_1_WIDTH         11U
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_1321
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1321__PHY_ADR3_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1321__PHY_ADR4_SW_WRADDR_SHIFT_1_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1321__PHY_ADR4_SW_WRADDR_SHIFT_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_1321__PHY_ADR4_SW_WRADDR_SHIFT_1_WIDTH             5U
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_1__REG DENALI_PHY_1321
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_1321__PHY_ADR4_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_1322_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1322_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1322__PHY_ADR4_CLK_WR_SLAVE_DELAY_1_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1322__PHY_ADR4_CLK_WR_SLAVE_DELAY_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_1322__PHY_ADR4_CLK_WR_SLAVE_DELAY_1_WIDTH         11U
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_1322
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1322__PHY_ADR4_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1322__PHY_ADR5_SW_WRADDR_SHIFT_1_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1322__PHY_ADR5_SW_WRADDR_SHIFT_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_1322__PHY_ADR5_SW_WRADDR_SHIFT_1_WIDTH             5U
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_1__REG DENALI_PHY_1322
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_1__FLD LPDDR4__DENALI_PHY_1322__PHY_ADR5_SW_WRADDR_SHIFT_1

#define LPDDR4__DENALI_PHY_1323_READ_MASK                            0x000F07FFU
#define LPDDR4__DENALI_PHY_1323_WRITE_MASK                           0x000F07FFU
#define LPDDR4__DENALI_PHY_1323__PHY_ADR5_CLK_WR_SLAVE_DELAY_1_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1323__PHY_ADR5_CLK_WR_SLAVE_DELAY_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_1323__PHY_ADR5_CLK_WR_SLAVE_DELAY_1_WIDTH         11U
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_1__REG DENALI_PHY_1323
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1323__PHY_ADR5_CLK_WR_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1323__PHY_ADR_SW_MASTER_MODE_1_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_1323__PHY_ADR_SW_MASTER_MODE_1_SHIFT              16U
#define LPDDR4__DENALI_PHY_1323__PHY_ADR_SW_MASTER_MODE_1_WIDTH               4U
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_1__REG DENALI_PHY_1323
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_1__FLD LPDDR4__DENALI_PHY_1323__PHY_ADR_SW_MASTER_MODE_1

#define LPDDR4__DENALI_PHY_1324_READ_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_1324_WRITE_MASK                           0xFF3F07FFU
#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_START_1_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_START_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_START_1_WIDTH          11U
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_1__REG DENALI_PHY_1324
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_1__FLD LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_START_1

#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_STEP_1_MASK    0x003F0000U
#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_STEP_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_STEP_1_WIDTH            6U
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_1__REG DENALI_PHY_1324
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_1__FLD LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_STEP_1

#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_WAIT_1_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_WAIT_1_SHIFT           24U
#define LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_WAIT_1_WIDTH            8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_1__REG DENALI_PHY_1324
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_1__FLD LPDDR4__DENALI_PHY_1324__PHY_ADR_MASTER_DELAY_WAIT_1

#define LPDDR4__DENALI_PHY_1325_READ_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_1325_WRITE_MASK                           0x0103FFFFU
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1_SHIFT    0U
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1_WIDTH    8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1__REG DENALI_PHY_1325
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1__FLD LPDDR4__DENALI_PHY_1325__PHY_ADR_MASTER_DELAY_HALF_MEASURE_1

#define LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_1_MASK     0x0003FF00U
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_1_SHIFT             8U
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_1_WIDTH            10U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_1__REG DENALI_PHY_1325
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_1__FLD LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_1

#define LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_MASK  0x01000000U
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_SHIFT         24U
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_WIDTH          1U
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_WOCLR          0U
#define LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_EN_1_WOSET          0U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_1__REG DENALI_PHY_1325
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_1__FLD LPDDR4__DENALI_PHY_1325__PHY_ADR_SW_CALVL_DVW_MIN_EN_1

#define LPDDR4__DENALI_PHY_1326_READ_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_1326_WRITE_MASK                           0x0000000FU
#define LPDDR4__DENALI_PHY_1326__PHY_ADR_CALVL_DLY_STEP_1_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_1326__PHY_ADR_CALVL_DLY_STEP_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_1326__PHY_ADR_CALVL_DLY_STEP_1_WIDTH               4U
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_1__REG DENALI_PHY_1326
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_1__FLD LPDDR4__DENALI_PHY_1326__PHY_ADR_CALVL_DLY_STEP_1

#define LPDDR4__DENALI_PHY_1327_READ_MASK                            0x03FF010FU
#define LPDDR4__DENALI_PHY_1327_WRITE_MASK                           0x03FF010FU
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_CALVL_CAPTURE_CNT_1_MASK    0x0000000FU
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_CALVL_CAPTURE_CNT_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_CALVL_CAPTURE_CNT_1_WIDTH            4U
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_1__REG DENALI_PHY_1327
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_1__FLD LPDDR4__DENALI_PHY_1327__PHY_ADR_CALVL_CAPTURE_CNT_1

#define LPDDR4__DENALI_PHY_1327__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_MASK 0x00000100U
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_SHIFT         8U
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_WIDTH         1U
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_WOCLR         0U
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_MEAS_DLY_STEP_ENABLE_1_WOSET         0U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_1__REG DENALI_PHY_1327
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_1__FLD LPDDR4__DENALI_PHY_1327__PHY_ADR_MEAS_DLY_STEP_ENABLE_1

#define LPDDR4__DENALI_PHY_1327__PHY_ADR_DC_INIT_SLV_DELAY_1_MASK    0x03FF0000U
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_DC_INIT_SLV_DELAY_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_1327__PHY_ADR_DC_INIT_SLV_DELAY_1_WIDTH           10U
#define LPDDR4__PHY_ADR_DC_INIT_SLV_DELAY_1__REG DENALI_PHY_1327
#define LPDDR4__PHY_ADR_DC_INIT_SLV_DELAY_1__FLD LPDDR4__DENALI_PHY_1327__PHY_ADR_DC_INIT_SLV_DELAY_1

#define LPDDR4__DENALI_PHY_1328_READ_MASK                            0x0000FF01U
#define LPDDR4__DENALI_PHY_1328_WRITE_MASK                           0x0000FF01U
#define LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_CALVL_ENABLE_1_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_CALVL_ENABLE_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_CALVL_ENABLE_1_WIDTH              1U
#define LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_CALVL_ENABLE_1_WOCLR              0U
#define LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_CALVL_ENABLE_1_WOSET              0U
#define LPDDR4__PHY_ADR_DC_CALVL_ENABLE_1__REG DENALI_PHY_1328
#define LPDDR4__PHY_ADR_DC_CALVL_ENABLE_1__FLD LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_CALVL_ENABLE_1

#define LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_DM_CLK_THRSHLD_1_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_DM_CLK_THRSHLD_1_SHIFT            8U
#define LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_DM_CLK_THRSHLD_1_WIDTH            8U
#define LPDDR4__PHY_ADR_DC_DM_CLK_THRSHLD_1__REG DENALI_PHY_1328
#define LPDDR4__PHY_ADR_DC_DM_CLK_THRSHLD_1__FLD LPDDR4__DENALI_PHY_1328__PHY_ADR_DC_DM_CLK_THRSHLD_1

#endif /* REG_LPDDR4_ADDRESS_SLICE_1_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

