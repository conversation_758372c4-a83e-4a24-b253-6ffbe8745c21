/********************************************************************
*
* MAIN_GPIOMUX_INTROUTER0 INTERRUPT MAP. header file
*
* Copyright (C) 2015-2024 Texas Instruments Incorporated.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*
*    Redistributions of source code must retain the above copyright
*    notice, this list of conditions and the following disclaimer.
*
*    Redistributions in binary form must reproduce the above copyright
*    notice, this list of conditions and the following disclaimer in the
*    documentation and/or other materials provided with the
*    distribution.
*
*    Neither the name of Texas Instruments Incorporated nor the names of
*    its contributors may be used to endorse or promote products derived
*    from this software without specific prior written permission.
*
*  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
*  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
*  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
*  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
*  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
*  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
*  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
*  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
*  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
*  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
*  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*
*/
#ifndef CSLR_MAIN_GPIOMUX_INTROUTER0_INTERRUPT_MAP_H_
#define CSLR_MAIN_GPIOMUX_INTROUTER0_INTERRUPT_MAP_H_

#include <drivers/hw_include/cslr.h>
#include <drivers/hw_include/tistdtypes.h>
#ifdef __cplusplus
extern "C"
{
#endif

/*
* List of intr sources for receiver: MAIN_GPIOMUX_INTROUTER0
*/

#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_0                                               (0U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_1                                               (1U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_2                                               (2U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_3                                               (3U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_4                                               (4U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_5                                               (5U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_6                                               (6U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_7                                               (7U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_8                                               (8U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_9                                               (9U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_10                                              (10U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_11                                              (11U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_12                                              (12U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_13                                              (13U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_14                                              (14U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_15                                              (15U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_16                                              (16U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_17                                              (17U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_18                                              (18U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_19                                              (19U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_20                                              (20U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_21                                              (21U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_22                                              (22U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_23                                              (23U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_24                                              (24U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_25                                              (25U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_26                                              (26U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_27                                              (27U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_28                                              (28U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_29                                              (29U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_30                                              (30U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_31                                              (31U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_32                                              (32U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_33                                              (33U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_34                                              (34U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_35                                              (35U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_36                                              (36U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_37                                              (37U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_38                                              (38U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_39                                              (39U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_40                                              (40U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_41                                              (41U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_42                                              (42U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_43                                              (43U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_44                                              (44U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_45                                              (45U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_46                                              (46U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_47                                              (47U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_48                                              (48U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_49                                              (49U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_50                                              (50U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_51                                              (51U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_52                                              (52U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_53                                              (53U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_54                                              (54U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_55                                              (55U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_56                                              (56U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_57                                              (57U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_58                                              (58U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_59                                              (59U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_60                                              (60U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_61                                              (61U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_62                                              (62U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_63                                              (63U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_64                                              (64U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_65                                              (65U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_66                                              (66U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_67                                              (67U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_68                                              (68U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_69                                              (69U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_70                                              (70U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_71                                              (71U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_72                                              (72U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_73                                              (73U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_74                                              (74U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_75                                              (75U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_76                                              (76U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_77                                              (77U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_78                                              (78U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_79                                              (79U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_80                                              (80U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_81                                              (81U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_82                                              (82U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_83                                              (83U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_84                                              (84U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_85                                              (85U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_86                                              (86U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_87                                              (87U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_88                                              (88U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_89                                              (89U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_0                                               (90U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_1                                               (91U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_2                                               (92U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_3                                               (93U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_4                                               (94U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_5                                               (95U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_6                                               (96U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_7                                               (97U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_8                                               (98U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_9                                               (99U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_10                                              (100U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_11                                              (101U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_12                                              (102U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_13                                              (103U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_14                                              (104U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_15                                              (105U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_16                                              (106U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_17                                              (107U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_18                                              (108U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_19                                              (109U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_20                                              (110U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_21                                              (111U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_22                                              (112U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_23                                              (113U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_24                                              (114U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_25                                              (115U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_26                                              (116U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_27                                              (117U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_28                                              (118U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_29                                              (119U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_30                                              (120U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_31                                              (121U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_32                                              (122U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_33                                              (123U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_34                                              (124U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_35                                              (125U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_36                                              (126U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_37                                              (127U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_38                                              (128U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_39                                              (129U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_40                                              (130U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_41                                              (131U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_42                                              (132U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_43                                              (133U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_44                                              (134U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_45                                              (135U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_46                                              (136U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_47                                              (137U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_48                                              (138U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_49                                              (139U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_50                                              (140U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_51                                              (141U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_52                                              (142U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_53                                              (143U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_54                                              (144U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_55                                              (145U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_56                                              (146U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_57                                              (147U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_58                                              (148U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_59                                              (149U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_60                                              (150U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_61                                              (151U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_62                                              (152U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_63                                              (153U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_64                                              (154U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_65                                              (155U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_66                                              (156U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_67                                              (157U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_68                                              (158U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_69                                              (159U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_70                                              (160U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_71                                              (161U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_90                                              (176U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_91                                              (177U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_BANK_0                                          (180U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_BANK_1                                          (181U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_BANK_2                                          (182U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_BANK_3                                          (183U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO1_GPIO_BANK_4                                          (184U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_BANK_0                                          (190U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_BANK_1                                          (191U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_BANK_2                                          (192U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_BANK_3                                          (193U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_BANK_4                                          (194U)
#define CSLR_MAIN_GPIOMUX_INTROUTER0_IN_GPIO0_GPIO_BANK_5                                          (195U)

#ifdef __cplusplus
}
#endif
#endif /* CSLR_MAIN_GPIOMUX_INTROUTER0_INTERRUPT_MAP_H_ */

