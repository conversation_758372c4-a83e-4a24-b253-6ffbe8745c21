

/*
 *  Copyright (C) 2022 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */


#ifndef SCICLIENT_DEFAULTBOARDCFG_RM_HEX_H_
#define SCICLIENT_DEFAULTBOARDCFG_RM_HEX_H_

#ifdef __cplusplus
extern "C"
{
#endif

#define SCICLIENT_BOARDCFG_RM_SIZE_IN_BYTES (1342U)

#define SCICLIENT_BOARDCFG_RM { \
    0x00U, 0x01U, 0x41U, 0x4cU, 0x64U, 0x01U, 0x0cU, 0x2aU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU,  \
    0xaaU, 0x1eU, 0x2aU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0x24U, 0x2aU, 0xaaU, 0xaaU,  \
    0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0xaaU, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U, 0x25U, 0x7bU, 0x08U, 0x00U, 0xd0U, 0x03U, 0x00U, 0x00U, 0x00U, 0x00U,  \
    0x10U, 0x00U, 0x40U, 0x00U, 0x0cU, 0x00U, 0x10U, 0x00U, 0x04U, 0x00U, 0x40U, 0x00U, 0x23U, 0x00U, 0x10U, 0x00U,  \
    0x04U, 0x00U, 0x40U, 0x00U, 0x24U, 0x00U, 0x14U, 0x00U, 0x16U, 0x00U, 0x40U, 0x00U, 0x1eU, 0x00U, 0x00U, 0x00U,  \
    0x10U, 0x00U, 0xc0U, 0x00U, 0x0cU, 0x00U, 0x22U, 0x00U, 0x02U, 0x00U, 0xc0U, 0x00U, 0x1eU, 0x00U, 0x00U, 0x00U,  \
    0x02U, 0x00U, 0x40U, 0x01U, 0x0cU, 0x00U, 0x02U, 0x00U, 0x02U, 0x00U, 0x40U, 0x01U, 0x23U, 0x00U, 0x02U, 0x00U,  \
    0x02U, 0x00U, 0x40U, 0x01U, 0x24U, 0x00U, 0x04U, 0x00U, 0x04U, 0x00U, 0x40U, 0x01U, 0x1eU, 0x00U, 0x00U, 0x00U,  \
    0x1aU, 0x00U, 0x80U, 0x01U, 0x80U, 0x00U, 0x00U, 0xc4U, 0xa4U, 0x00U, 0x82U, 0x06U, 0x80U, 0x00U, 0x00U, 0x00U,  \
    0x01U, 0x00U, 0x83U, 0x06U, 0x80U, 0x00U, 0x00U, 0x00U, 0x12U, 0x00U, 0x8dU, 0x06U, 0x0cU, 0x00U, 0x12U, 0x00U,  \
    0x06U, 0x00U, 0x8dU, 0x06U, 0x23U, 0x00U, 0x12U, 0x00U, 0x06U, 0x00U, 0x8dU, 0x06U, 0x24U, 0x00U, 0x18U, 0x00U,  \
    0x02U, 0x00U, 0x8dU, 0x06U, 0x1eU, 0x00U, 0x1aU, 0x00U, 0x06U, 0x00U, 0x8dU, 0x06U, 0x80U, 0x00U, 0x36U, 0x00U,  \
    0x12U, 0x00U, 0x8eU, 0x06U, 0x0cU, 0x00U, 0x48U, 0x00U, 0x06U, 0x00U, 0x8eU, 0x06U, 0x23U, 0x00U, 0x48U, 0x00U,  \
    0x06U, 0x00U, 0x8eU, 0x06U, 0x24U, 0x00U, 0x4eU, 0x00U, 0x02U, 0x00U, 0x8eU, 0x06U, 0x1eU, 0x00U, 0x50U, 0x00U,  \
    0x02U, 0x00U, 0x8eU, 0x06U, 0x80U, 0x00U, 0x20U, 0x00U, 0x0cU, 0x00U, 0x8fU, 0x06U, 0x0cU, 0x00U, 0x2cU, 0x00U,  \
    0x06U, 0x00U, 0x8fU, 0x06U, 0x23U, 0x00U, 0x2cU, 0x00U, 0x06U, 0x00U, 0x8fU, 0x06U, 0x24U, 0x00U, 0x32U, 0x00U,  \
    0x02U, 0x00U, 0x8fU, 0x06U, 0x1eU, 0x00U, 0x34U, 0x00U, 0x02U, 0x00U, 0x8fU, 0x06U, 0x80U, 0x00U, 0x00U, 0x00U,  \
    0x12U, 0x00U, 0xa0U, 0x06U, 0x0cU, 0x00U, 0x12U, 0x00U, 0x06U, 0x00U, 0xa0U, 0x06U, 0x23U, 0x00U, 0x12U, 0x00U,  \
    0x06U, 0x00U, 0xa0U, 0x06U, 0x24U, 0x00U, 0x18U, 0x00U, 0x02U, 0x00U, 0xa0U, 0x06U, 0x1eU, 0x00U, 0x1aU, 0x00U,  \
    0x06U, 0x00U, 0xa0U, 0x06U, 0x80U, 0x00U, 0x00U, 0x00U, 0x12U, 0x00U, 0xa1U, 0x06U, 0x0cU, 0x00U, 0x12U, 0x00U,  \
    0x06U, 0x00U, 0xa1U, 0x06U, 0x23U, 0x00U, 0x12U, 0x00U, 0x06U, 0x00U, 0xa1U, 0x06U, 0x24U, 0x00U, 0x18U, 0x00U,  \
    0x02U, 0x00U, 0xa1U, 0x06U, 0x1eU, 0x00U, 0x1aU, 0x00U, 0x02U, 0x00U, 0xa1U, 0x06U, 0x80U, 0x00U, 0x00U, 0x00U,  \
    0x0cU, 0x00U, 0xa2U, 0x06U, 0x0cU, 0x00U, 0x0cU, 0x00U, 0x06U, 0x00U, 0xa2U, 0x06U, 0x23U, 0x00U, 0x0cU, 0x00U,  \
    0x06U, 0x00U, 0xa2U, 0x06U, 0x24U, 0x00U, 0x12U, 0x00U, 0x02U, 0x00U, 0xa2U, 0x06U, 0x1eU, 0x00U, 0x14U, 0x00U,  \
    0x02U, 0x00U, 0xa2U, 0x06U, 0x80U, 0x00U, 0x05U, 0x00U, 0x23U, 0x00U, 0x0aU, 0x07U, 0x0cU, 0x00U, 0x2cU, 0x00U,  \
    0x23U, 0x00U, 0x0aU, 0x07U, 0x23U, 0x00U, 0x2cU, 0x00U, 0x23U, 0x00U, 0x0aU, 0x07U, 0x24U, 0x00U, 0xa8U, 0x00U,  \
    0x08U, 0x00U, 0x0aU, 0x07U, 0x1eU, 0x00U, 0x0dU, 0x00U, 0x00U, 0x02U, 0x0dU, 0x07U, 0x0cU, 0x00U, 0x0dU, 0x02U,  \
    0x00U, 0x01U, 0x0dU, 0x07U, 0x23U, 0x00U, 0x0dU, 0x02U, 0x00U, 0x01U, 0x0dU, 0x07U, 0x24U, 0x00U, 0x0dU, 0x03U,  \
    0x80U, 0x00U, 0x0dU, 0x07U, 0x1eU, 0x00U, 0x8dU, 0x03U, 0x72U, 0x02U, 0x0dU, 0x07U, 0x80U, 0x00U, 0x00U, 0x00U,  \
    0x00U, 0x04U, 0x0fU, 0x07U, 0x80U, 0x00U, 0x00U, 0x10U, 0x1dU, 0x00U, 0x10U, 0x07U, 0x80U, 0x00U, 0x00U, 0x12U,  \
    0x63U, 0x00U, 0x11U, 0x07U, 0x80U, 0x00U, 0x00U, 0x14U, 0x18U, 0x00U, 0x12U, 0x07U, 0x80U, 0x00U, 0x00U, 0x16U,  \
    0x33U, 0x00U, 0x13U, 0x07U, 0x80U, 0x00U, 0x00U, 0x18U, 0x33U, 0x00U, 0x14U, 0x07U, 0x80U, 0x00U, 0x00U, 0x1aU,  \
    0x33U, 0x00U, 0x15U, 0x07U, 0x80U, 0x00U, 0x00U, 0x20U, 0x20U, 0x00U, 0x16U, 0x07U, 0x80U, 0x00U, 0x00U, 0x22U,  \
    0x20U, 0x00U, 0x17U, 0x07U, 0x80U, 0x00U, 0x00U, 0x24U, 0x20U, 0x00U, 0x18U, 0x07U, 0x80U, 0x00U, 0x00U, 0x26U,  \
    0x16U, 0x00U, 0x19U, 0x07U, 0x80U, 0x00U, 0x00U, 0x28U, 0x16U, 0x00U, 0x1aU, 0x07U, 0x80U, 0x00U, 0x00U, 0x2aU,  \
    0x16U, 0x00U, 0x1bU, 0x07U, 0x80U, 0x00U, 0x00U, 0x2cU, 0x1cU, 0x00U, 0x1cU, 0x07U, 0x80U, 0x00U, 0x00U, 0x2eU,  \
    0x1cU, 0x00U, 0x1dU, 0x07U, 0x80U, 0x00U, 0x00U, 0x30U, 0x1cU, 0x00U, 0x1eU, 0x07U, 0x80U, 0x00U, 0x00U, 0x00U,  \
    0x01U, 0x00U, 0x83U, 0x07U, 0x80U, 0x00U, 0x00U, 0x00U, 0x0aU, 0x00U, 0x90U, 0x07U, 0x0cU, 0x00U, 0x0aU, 0x00U,  \
    0x03U, 0x00U, 0x90U, 0x07U, 0x23U, 0x00U, 0x0aU, 0x00U, 0x03U, 0x00U, 0x90U, 0x07U, 0x24U, 0x00U, 0x0dU, 0x00U,  \
    0x03U, 0x00U, 0x90U, 0x07U, 0x1eU, 0x00U, 0x10U, 0x00U, 0x03U, 0x00U, 0x90U, 0x07U, 0x80U, 0x00U, 0x13U, 0x00U,  \
    0x40U, 0x00U, 0x91U, 0x07U, 0x0cU, 0x00U, 0x13U, 0x00U, 0x40U, 0x00U, 0x91U, 0x07U, 0x24U, 0x00U, 0x53U, 0x00U,  \
    0x08U, 0x00U, 0x92U, 0x07U, 0x0cU, 0x00U, 0x5bU, 0x00U, 0x08U, 0x00U, 0x93U, 0x07U, 0x0cU, 0x00U, 0x63U, 0x00U,  \
    0x0aU, 0x00U, 0x96U, 0x07U, 0x0cU, 0x00U, 0x6dU, 0x00U, 0x03U, 0x00U, 0x96U, 0x07U, 0x23U, 0x00U, 0x6dU, 0x00U,  \
    0x03U, 0x00U, 0x96U, 0x07U, 0x24U, 0x00U, 0x70U, 0x00U, 0x03U, 0x00U, 0x96U, 0x07U, 0x1eU, 0x00U, 0x73U, 0x00U,  \
    0x03U, 0x00U, 0x96U, 0x07U, 0x80U, 0x00U, 0x76U, 0x00U, 0x10U, 0x00U, 0x97U, 0x07U, 0x0cU, 0x00U, 0x76U, 0x00U,  \
    0x10U, 0x00U, 0x97U, 0x07U, 0x24U, 0x00U, 0x86U, 0x00U, 0x08U, 0x00U, 0x98U, 0x07U, 0x0cU, 0x00U, 0x86U, 0x00U,  \
    0x08U, 0x00U, 0x99U, 0x07U, 0x0cU, 0x00U, 0x8eU, 0x00U, 0x08U, 0x00U, 0x9aU, 0x07U, 0x0cU, 0x00U, 0x8eU, 0x00U,  \
    0x08U, 0x00U, 0x9bU, 0x07U, 0x0cU, 0x00U, 0x00U, 0x00U, 0x0aU, 0x00U, 0xa3U, 0x07U, 0x0cU, 0x00U, 0x0aU, 0x00U,  \
    0x03U, 0x00U, 0xa3U, 0x07U, 0x23U, 0x00U, 0x0aU, 0x00U, 0x03U, 0x00U, 0xa3U, 0x07U, 0x24U, 0x00U, 0x0dU, 0x00U,  \
    0x03U, 0x00U, 0xa3U, 0x07U, 0x1eU, 0x00U, 0x10U, 0x00U, 0x03U, 0x00U, 0xa3U, 0x07U, 0x80U, 0x00U, 0x13U, 0x00U,  \
    0x08U, 0x00U, 0xa4U, 0x07U, 0x0cU, 0x00U, 0x13U, 0x00U, 0x08U, 0x00U, 0xa4U, 0x07U, 0x24U, 0x00U, 0x1bU, 0x00U,  \
    0x01U, 0x00U, 0xa5U, 0x07U, 0x0cU, 0x00U, 0x1cU, 0x00U, 0x01U, 0x00U, 0xa6U, 0x07U, 0x0cU, 0x00U, 0x00U, 0x00U,  \
    0x0aU, 0x00U, 0xa9U, 0x07U, 0x0cU, 0x00U, 0x0aU, 0x00U, 0x03U, 0x00U, 0xa9U, 0x07U, 0x23U, 0x00U, 0x0aU, 0x00U,  \
    0x03U, 0x00U, 0xa9U, 0x07U, 0x24U, 0x00U, 0x0dU, 0x00U, 0x03U, 0x00U, 0xa9U, 0x07U, 0x1eU, 0x00U, 0x10U, 0x00U,  \
    0x03U, 0x00U, 0xa9U, 0x07U, 0x80U, 0x00U, 0x00U, 0x00U, 0x0aU, 0x00U, 0xaaU, 0x07U, 0x0cU, 0x00U, 0x0aU, 0x00U,  \
    0x03U, 0x00U, 0xaaU, 0x07U, 0x23U, 0x00U, 0x0aU, 0x00U, 0x03U, 0x00U, 0xaaU, 0x07U, 0x24U, 0x00U, 0x0dU, 0x00U,  \
    0x03U, 0x00U, 0xaaU, 0x07U, 0x1eU, 0x00U, 0x10U, 0x00U, 0x03U, 0x00U, 0xaaU, 0x07U, 0x80U, 0x00U, 0x13U, 0x00U,  \
    0x01U, 0x00U, 0xabU, 0x07U, 0x0cU, 0x00U, 0x13U, 0x00U, 0x01U, 0x00U, 0xabU, 0x07U, 0x24U, 0x00U, 0x13U, 0x00U,  \
    0x10U, 0x00U, 0xacU, 0x07U, 0x0cU, 0x00U, 0x13U, 0x00U, 0x10U, 0x00U, 0xacU, 0x07U, 0x24U, 0x00U, 0x14U, 0x00U,  \
    0x01U, 0x00U, 0xadU, 0x07U, 0x0cU, 0x00U, 0x23U, 0x00U, 0x08U, 0x00U, 0xaeU, 0x07U, 0x0cU, 0x00U, 0x15U, 0x00U,  \
    0x01U, 0x00U, 0xafU, 0x07U, 0x0cU, 0x00U, 0x23U, 0x00U, 0x08U, 0x00U, 0xb0U, 0x07U, 0x0cU, 0x00U, 0x16U, 0x00U,  \
    0x01U, 0x00U, 0xb1U, 0x07U, 0x0cU, 0x00U, 0x2bU, 0x00U, 0x08U, 0x00U, 0xb2U, 0x07U, 0x0cU, 0x00U, 0x17U, 0x00U,  \
    0x01U, 0x00U, 0xb3U, 0x07U, 0x0cU, 0x00U, 0x2bU, 0x00U, 0x08U, 0x00U, 0xb4U, 0x07U, 0x0cU, 0x00U, 0x00U, 0x00U,  \
    0x01U, 0x00U, 0x40U, 0x08U, 0x80U, 0x00U, 0x02U, 0x00U, 0x02U, 0x00U, 0x4aU, 0x08U, 0x0cU, 0x00U,  \
} /* 1342 bytes */

#ifdef __cplusplus
}
#endif

#endif /* SCICLIENT_DEFAULTBOARDCFG_RM_HEX_H_ */
