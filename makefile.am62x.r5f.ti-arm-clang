#
# Auto generated makefile
#

export MCU_PLUS_SDK_PATH?=$(abspath ../..)
include $(MCU_PLUS_SDK_PATH)/imports.mak

CG_TOOL_ROOT=$(CGT_TI_ARM_CLANG_PATH)

CC=$(CGT_TI_ARM_CLANG_PATH)/bin/tiarmclang
AR=$(CGT_TI_ARM_CLANG_PATH)/bin/tiarmar

PROFILE?=release
ConfigName:=$(PROFILE)

LIBNAME:=drivers.am62x.r5f.ti-arm-clang.$(PROFILE).lib

FILES_common := \
    i2c_v0.c \
    pinmux.c \
    soc.c \
    ipc_notify_v0.c \
    ipc_notify_v0_cfg.c \
    ipc_rpmsg.c \
    ipc_rpmsg_vring.c \
    csl_sec_proxy.c \
    sciclient_irq_rm.c \
    sciclient_fmwSecureProxyMap.c \
    gpio.c \
    bootloader.c \
    bootloader_dma.c \
    bootloader_flash.c \
    bootloader_mem.c \
    bootloader_mmcsd_raw.c \
    bootloader_soc.c \
    bootloader_xmodem.c \
    bootloader_uniflash.c \
    bootloader_profile.c \
    xmodem.c \
    csl_emif.c \
    crc16.c \
    lpddr4_obj_if.c \
    lpddr4.c \
    lpddr4_am6x.c \
    lpddr4_ctl_regs_rw_masks.c \
    ddr.c \
    ddr_soc.c \
    ddr_perf.c \
    mmcsd_v1.c \
    mmcsd_priv.c \
    ospi_v0.c \
    ospi_dma.c \
    ospi_dma_udma.c \
    ospi_nor_flash.c \
    ospi_phy.c \
    uart_v0.c \
    uart_dma.c \
    gtc.c \
    gtc_soc.c \
    csl_bcdma.c \
    csl_intaggr.c \
    csl_lcdma_ringacc.c \
    csl_pktdma.c \
    udma.c \
    udma_ch.c \
    udma_event.c \
    udma_flow.c \
    udma_ring_common.c \
    udma_ring_lcdma.c \
    udma_rm.c \
    udma_rmcfg_common.c \
    udma_utils.c \
    utils.c \
    soc.c \
    udma_rmcfg.c \
    udma_soc.c \
    elm_v0.c \
    gpmc_v0.c \
    gpmc_dma.c \
    gpmc_dma_udma.c \
    mcspi_v0.c \
    mcspi_dma.c \
    mcspi_dma_udma.c \
    mcan.c \
    dss_evtMgr.c \
    dss_init.c \
    dss_dctrlApi.c \
    dss_dctrlGraph.c \
    dss_dispApi.c \
    dss_dispPriv.c \
    csl_dssCommon.c \
    csl_dssOverlay.c \
    csl_dssVideoPipe.c \
    csl_dssVideoPort.c \
    dss_soc_graph.c \
    dss_soc.c \
    fvid2_drvMgr.c \
    fvid2_graph.c \
    fvid2_utils.c \
    fvid2_trace.c \

FILES_PATH_common = \
    i2c/v0 \
    pinmux/am62x \
    mcan/v0 \
    ipc_notify/v0 \
    epwm/v0 \
    gpio/v0 \
    mcspi/v0 \
    mcspi/v0/dma \
    mcspi/v0/dma/udma \
    ipc_notify/v0/soc/am62x \
    ipc_rpmsg/ \
    sciclient \
    sciclient/soc/am62x \
    soc/am62x \
    device_manager/sciclient_direct \
    gpio/v0 \
    gtc/v0 \
    gtc/v0/soc/am62x \
    bootloader \
    bootloader/soc/am62x \
    ddr/v0 \
    ddr/cdn_drv/ \
    ddr/cdn_drv/common/include \
    ddr/cdn_drv/v0/include \
    ddr/cdn_drv/common \
    ddr/cdn_drv/v0 \
    ddr \
    ddr/v0/soc/am62x \
    mmcsd \
    mmcsd/v1 \
    ospi \
    ospi/v0 \
    ospi/v0/dma \
    ospi/v0/dma/udma \
    uart/v0 \
    uart/v0/dma \
    udma \
    udma/soc \
    udma/hw_include \
    udma/soc/am62x \
    soc/am62x \
    elm/v0 \
    gpmc/v0 \
    gpmc/v0/dma \
    gpmc/v0/dma/udma \
    utils \
    dss \
    dss/v0 \
    dss/v0/common \
    dss/v0/dctrl \
    dss/v0/disp \
    dss/v0/hw_include \
    dss/v0/hw_include/V3 \
    dss/v0/include \
    dss/v0/soc \
    dss/v0/soc/am62x \
    fvid2/v0 \

INCLUDES_common := \
    -I${CG_TOOL_ROOT}/include/c \
    -I${MCU_PLUS_SDK_PATH}/source \

DEFINES_common := \
    -DSOC_AM62X \
    -DENABLE_SCICLIENT_DIRECT \
    -DFVID2_CFG_TRACE_ENABLE \
    -DFVID2_CFG_ASSERT_ENABLE \

CFLAGS_common := \
    -mcpu=cortex-r5 \
    -mfloat-abi=hard \
    -mfpu=vfpv3-d16 \
    -mthumb \
    -Wall \
    -Werror \
    -g \
    -Wno-gnu-variable-sized-type-not-at-end \
    -Wno-unused-function \

CFLAGS_cpp_common := \
    -Wno-c99-designator \
    -Wno-extern-c-compat \
    -Wno-c++11-narrowing \
    -Wno-reorder-init-list \
    -Wno-deprecated-register \
    -Wno-writable-strings \
    -Wno-enum-compare \
    -Wno-reserved-user-defined-literal \
    -Wno-unused-const-variable \
    -x c++ \

CFLAGS_debug := \
    -D_DEBUG_=1 \

CFLAGS_release := \
    -Os \

ARFLAGS_common := \
    rc \

FILES := $(FILES_common) $(FILES_$(PROFILE))
ASMFILES := $(ASMFILES_common) $(ASMFILES_$(PROFILE))
FILES_PATH := $(FILES_PATH_common) $(FILES_PATH_$(PROFILE))
CFLAGS := $(CFLAGS_common) $(CFLAGS_$(PROFILE))
ASMFLAGS := $(CFLAGS_common) $(CFLAGS_$(PROFILE))
ifeq ($(CPLUSPLUS_BUILD), yes)
CFLAGS += $(CFLAGS_cpp_common)
endif
DEFINES := $(DEFINES_common) $(DEFINES_$(PROFILE))
INCLUDES := $(INCLUDES_common) $(INCLUDE_$(PROFILE))
ARFLAGS := $(ARFLAGS_common) $(ARFLAGS_$(PROFILE))

LIBDIR := lib
OBJDIR := obj/am62x/ti-arm-clang/$(PROFILE)/r5f/drivers/
OBJS := $(FILES:%.c=%.obj)
OBJS += $(ASMFILES:%.S=%.obj)
DEPS := $(FILES:%.c=%.d)

vpath %.obj $(OBJDIR)
vpath %.c $(FILES_PATH)
vpath %.S $(FILES_PATH)

$(OBJDIR)/%.obj %.obj: %.c
	@echo  Compiling: $(LIBNAME): $<
	$(CC) -c $(CFLAGS) $(INCLUDES) $(DEFINES) -MMD -o $(OBJDIR)/$@ $<

$(OBJDIR)/%.obj %.obj: %.S
	@echo  Compiling: $(LIBNAME): $<
	$(CC) -c $(ASMFLAGS) -o $(OBJDIR)/$@ $<

all: $(LIBDIR)/$(LIBNAME)

$(LIBDIR)/$(LIBNAME): $(OBJS) | $(LIBDIR)
	@echo  .
	@echo  Archiving: $(LIBNAME) to $@ ...
	$(AR) $(ARFLAGS) $@ $(addprefix $(OBJDIR), $(OBJS))
	@echo  Archiving: $(LIBNAME) Done !!!
	@echo  .

clean:
	@echo  Cleaning: $(LIBNAME) ...
	$(RMDIR) $(OBJDIR)
	$(RM) $(LIBDIR)/$(LIBNAME)

scrub:
	@echo  Scrubing: $(LIBNAME) ...
	-$(RMDIR) obj/
	-$(RMDIR) lib/

$(OBJS): | $(OBJDIR)

$(LIBDIR) $(OBJDIR):
	$(MKDIR) $@

-include $(addprefix $(OBJDIR)/, $(DEPS))
