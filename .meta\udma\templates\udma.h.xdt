%%{
    let module = system.modules['/drivers/udma/udma'];
%%}
/*
 * UDMA
 */
#include <drivers/udma.h>

/* UDMA Instance Macros */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
#define `instance.$name.toUpperCase()` (`i`U)
% }
#define CONFIG_UDMA_NUM_INSTANCES (`module.$instances.length`U)

/* UDMA Driver Objects */
extern Udma_DrvObject   gUdmaDrvObj[CONFIG_UDMA_NUM_INSTANCES];

/* UDMA functions as specified in SYSCONFIG */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
/* For instance `instance.$name.toUpperCase()` */
extern uint64_t `config.virtToPhyFxn`(const void *virtAddr, uint32_t chNum, void *appData);
extern void *`config.phyToVirtFxn`(uint64_t phyAddr, uint32_t chNum, void *appData);
% }
