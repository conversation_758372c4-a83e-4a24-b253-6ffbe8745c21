/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_DATA_SLICE_1_MACROS_H_
#define REG_LPDDR4_DATA_SLICE_1_MACROS_H_

#define LPDDR4__DENALI_PHY_256_READ_MASK                             0x07FF7F07U
#define LPDDR4__DENALI_PHY_256_WRITE_MASK                            0x07FF7F07U
#define LPDDR4__DENALI_PHY_256__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_1_MASK  0x00000007U
#define LPDDR4__DENALI_PHY_256__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_256__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_1_WIDTH          3U
#define LPDDR4__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_1__REG DENALI_PHY_256
#define LPDDR4__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_1__FLD LPDDR4__DENALI_PHY_256__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_1

#define LPDDR4__DENALI_PHY_256__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_1_MASK 0x00007F00U
#define LPDDR4__DENALI_PHY_256__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_1_SHIFT        8U
#define LPDDR4__DENALI_PHY_256__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_1_WIDTH        7U
#define LPDDR4__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_1__REG DENALI_PHY_256
#define LPDDR4__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_1__FLD LPDDR4__DENALI_PHY_256__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_1

#define LPDDR4__DENALI_PHY_256__PHY_CLK_WR_BYPASS_SLAVE_DELAY_1_MASK 0x07FF0000U
#define LPDDR4__DENALI_PHY_256__PHY_CLK_WR_BYPASS_SLAVE_DELAY_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_256__PHY_CLK_WR_BYPASS_SLAVE_DELAY_1_WIDTH        11U
#define LPDDR4__PHY_CLK_WR_BYPASS_SLAVE_DELAY_1__REG DENALI_PHY_256
#define LPDDR4__PHY_CLK_WR_BYPASS_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_256__PHY_CLK_WR_BYPASS_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_257_READ_MASK                             0x0703FF0FU
#define LPDDR4__DENALI_PHY_257_WRITE_MASK                            0x0703FF0FU
#define LPDDR4__DENALI_PHY_257__PHY_IO_PAD_DELAY_TIMING_BYPASS_1_MASK 0x0000000FU
#define LPDDR4__DENALI_PHY_257__PHY_IO_PAD_DELAY_TIMING_BYPASS_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_257__PHY_IO_PAD_DELAY_TIMING_BYPASS_1_WIDTH        4U
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_BYPASS_1__REG DENALI_PHY_257
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_BYPASS_1__FLD LPDDR4__DENALI_PHY_257__PHY_IO_PAD_DELAY_TIMING_BYPASS_1

#define LPDDR4__DENALI_PHY_257__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_1_MASK 0x0003FF00U
#define LPDDR4__DENALI_PHY_257__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_1_SHIFT      8U
#define LPDDR4__DENALI_PHY_257__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_1_WIDTH     10U
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_1__REG DENALI_PHY_257
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_1__FLD LPDDR4__DENALI_PHY_257__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_1

#define LPDDR4__DENALI_PHY_257__PHY_WRITE_PATH_LAT_ADD_BYPASS_1_MASK 0x07000000U
#define LPDDR4__DENALI_PHY_257__PHY_WRITE_PATH_LAT_ADD_BYPASS_1_SHIFT        24U
#define LPDDR4__DENALI_PHY_257__PHY_WRITE_PATH_LAT_ADD_BYPASS_1_WIDTH         3U
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_BYPASS_1__REG DENALI_PHY_257
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_BYPASS_1__FLD LPDDR4__DENALI_PHY_257__PHY_WRITE_PATH_LAT_ADD_BYPASS_1

#define LPDDR4__DENALI_PHY_258_READ_MASK                             0x010303FFU
#define LPDDR4__DENALI_PHY_258_WRITE_MASK                            0x010303FFU
#define LPDDR4__DENALI_PHY_258__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_258__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_1_SHIFT     0U
#define LPDDR4__DENALI_PHY_258__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_1_WIDTH    10U
#define LPDDR4__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_1__REG DENALI_PHY_258
#define LPDDR4__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_258__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_258__PHY_BYPASS_TWO_CYC_PREAMBLE_1_MASK   0x00030000U
#define LPDDR4__DENALI_PHY_258__PHY_BYPASS_TWO_CYC_PREAMBLE_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_258__PHY_BYPASS_TWO_CYC_PREAMBLE_1_WIDTH           2U
#define LPDDR4__PHY_BYPASS_TWO_CYC_PREAMBLE_1__REG DENALI_PHY_258
#define LPDDR4__PHY_BYPASS_TWO_CYC_PREAMBLE_1__FLD LPDDR4__DENALI_PHY_258__PHY_BYPASS_TWO_CYC_PREAMBLE_1

#define LPDDR4__DENALI_PHY_258__PHY_CLK_BYPASS_OVERRIDE_1_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_258__PHY_CLK_BYPASS_OVERRIDE_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_258__PHY_CLK_BYPASS_OVERRIDE_1_WIDTH               1U
#define LPDDR4__DENALI_PHY_258__PHY_CLK_BYPASS_OVERRIDE_1_WOCLR               0U
#define LPDDR4__DENALI_PHY_258__PHY_CLK_BYPASS_OVERRIDE_1_WOSET               0U
#define LPDDR4__PHY_CLK_BYPASS_OVERRIDE_1__REG DENALI_PHY_258
#define LPDDR4__PHY_CLK_BYPASS_OVERRIDE_1__FLD LPDDR4__DENALI_PHY_258__PHY_CLK_BYPASS_OVERRIDE_1

#define LPDDR4__DENALI_PHY_259_READ_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_259_WRITE_MASK                            0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ0_SHIFT_1_MASK            0x0000003FU
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ0_SHIFT_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ0_SHIFT_1_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ0_SHIFT_1__REG DENALI_PHY_259
#define LPDDR4__PHY_SW_WRDQ0_SHIFT_1__FLD LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ0_SHIFT_1

#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ1_SHIFT_1_MASK            0x00003F00U
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ1_SHIFT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ1_SHIFT_1_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ1_SHIFT_1__REG DENALI_PHY_259
#define LPDDR4__PHY_SW_WRDQ1_SHIFT_1__FLD LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ1_SHIFT_1

#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ2_SHIFT_1_MASK            0x003F0000U
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ2_SHIFT_1_SHIFT                   16U
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ2_SHIFT_1_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ2_SHIFT_1__REG DENALI_PHY_259
#define LPDDR4__PHY_SW_WRDQ2_SHIFT_1__FLD LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ2_SHIFT_1

#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ3_SHIFT_1_MASK            0x3F000000U
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ3_SHIFT_1_SHIFT                   24U
#define LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ3_SHIFT_1_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ3_SHIFT_1__REG DENALI_PHY_259
#define LPDDR4__PHY_SW_WRDQ3_SHIFT_1__FLD LPDDR4__DENALI_PHY_259__PHY_SW_WRDQ3_SHIFT_1

#define LPDDR4__DENALI_PHY_260_READ_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_260_WRITE_MASK                            0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ4_SHIFT_1_MASK            0x0000003FU
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ4_SHIFT_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ4_SHIFT_1_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ4_SHIFT_1__REG DENALI_PHY_260
#define LPDDR4__PHY_SW_WRDQ4_SHIFT_1__FLD LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ4_SHIFT_1

#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ5_SHIFT_1_MASK            0x00003F00U
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ5_SHIFT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ5_SHIFT_1_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ5_SHIFT_1__REG DENALI_PHY_260
#define LPDDR4__PHY_SW_WRDQ5_SHIFT_1__FLD LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ5_SHIFT_1

#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ6_SHIFT_1_MASK            0x003F0000U
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ6_SHIFT_1_SHIFT                   16U
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ6_SHIFT_1_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ6_SHIFT_1__REG DENALI_PHY_260
#define LPDDR4__PHY_SW_WRDQ6_SHIFT_1__FLD LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ6_SHIFT_1

#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ7_SHIFT_1_MASK            0x3F000000U
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ7_SHIFT_1_SHIFT                   24U
#define LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ7_SHIFT_1_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ7_SHIFT_1__REG DENALI_PHY_260
#define LPDDR4__PHY_SW_WRDQ7_SHIFT_1__FLD LPDDR4__DENALI_PHY_260__PHY_SW_WRDQ7_SHIFT_1

#define LPDDR4__DENALI_PHY_261_READ_MASK                             0x01030F3FU
#define LPDDR4__DENALI_PHY_261_WRITE_MASK                            0x01030F3FU
#define LPDDR4__DENALI_PHY_261__PHY_SW_WRDM_SHIFT_1_MASK             0x0000003FU
#define LPDDR4__DENALI_PHY_261__PHY_SW_WRDM_SHIFT_1_SHIFT                     0U
#define LPDDR4__DENALI_PHY_261__PHY_SW_WRDM_SHIFT_1_WIDTH                     6U
#define LPDDR4__PHY_SW_WRDM_SHIFT_1__REG DENALI_PHY_261
#define LPDDR4__PHY_SW_WRDM_SHIFT_1__FLD LPDDR4__DENALI_PHY_261__PHY_SW_WRDM_SHIFT_1

#define LPDDR4__DENALI_PHY_261__PHY_SW_WRDQS_SHIFT_1_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_261__PHY_SW_WRDQS_SHIFT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_261__PHY_SW_WRDQS_SHIFT_1_WIDTH                    4U
#define LPDDR4__PHY_SW_WRDQS_SHIFT_1__REG DENALI_PHY_261
#define LPDDR4__PHY_SW_WRDQS_SHIFT_1__FLD LPDDR4__DENALI_PHY_261__PHY_SW_WRDQS_SHIFT_1

#define LPDDR4__DENALI_PHY_261__PHY_PER_RANK_CS_MAP_1_MASK           0x00030000U
#define LPDDR4__DENALI_PHY_261__PHY_PER_RANK_CS_MAP_1_SHIFT                  16U
#define LPDDR4__DENALI_PHY_261__PHY_PER_RANK_CS_MAP_1_WIDTH                   2U
#define LPDDR4__PHY_PER_RANK_CS_MAP_1__REG DENALI_PHY_261
#define LPDDR4__PHY_PER_RANK_CS_MAP_1__FLD LPDDR4__DENALI_PHY_261__PHY_PER_RANK_CS_MAP_1

#define LPDDR4__DENALI_PHY_261__PHY_PER_CS_TRAINING_MULTICAST_EN_1_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_261__PHY_PER_CS_TRAINING_MULTICAST_EN_1_SHIFT     24U
#define LPDDR4__DENALI_PHY_261__PHY_PER_CS_TRAINING_MULTICAST_EN_1_WIDTH      1U
#define LPDDR4__DENALI_PHY_261__PHY_PER_CS_TRAINING_MULTICAST_EN_1_WOCLR      0U
#define LPDDR4__DENALI_PHY_261__PHY_PER_CS_TRAINING_MULTICAST_EN_1_WOSET      0U
#define LPDDR4__PHY_PER_CS_TRAINING_MULTICAST_EN_1__REG DENALI_PHY_261
#define LPDDR4__PHY_PER_CS_TRAINING_MULTICAST_EN_1__FLD LPDDR4__DENALI_PHY_261__PHY_PER_CS_TRAINING_MULTICAST_EN_1

#define LPDDR4__DENALI_PHY_262_READ_MASK                             0x1F1F0301U
#define LPDDR4__DENALI_PHY_262_WRITE_MASK                            0x1F1F0301U
#define LPDDR4__DENALI_PHY_262__PHY_PER_CS_TRAINING_INDEX_1_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_262__PHY_PER_CS_TRAINING_INDEX_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_262__PHY_PER_CS_TRAINING_INDEX_1_WIDTH             1U
#define LPDDR4__DENALI_PHY_262__PHY_PER_CS_TRAINING_INDEX_1_WOCLR             0U
#define LPDDR4__DENALI_PHY_262__PHY_PER_CS_TRAINING_INDEX_1_WOSET             0U
#define LPDDR4__PHY_PER_CS_TRAINING_INDEX_1__REG DENALI_PHY_262
#define LPDDR4__PHY_PER_CS_TRAINING_INDEX_1__FLD LPDDR4__DENALI_PHY_262__PHY_PER_CS_TRAINING_INDEX_1

#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_1_MASK 0x00000300U
#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_1_SHIFT         8U
#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_1_WIDTH         2U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_1__REG DENALI_PHY_262
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_1__FLD LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_1

#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_DLY_1_MASK    0x001F0000U
#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_DLY_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_DLY_1_WIDTH            5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_DLY_1__REG DENALI_PHY_262
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_DLY_1__FLD LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_DLY_1

#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_1_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_1_SHIFT      24U
#define LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_1_WIDTH       5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_1__REG DENALI_PHY_262
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_1__FLD LPDDR4__DENALI_PHY_262__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_1

#define LPDDR4__DENALI_PHY_263_READ_MASK                             0x1F030F0FU
#define LPDDR4__DENALI_PHY_263_WRITE_MASK                            0x1F030F0FU
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RPTR_UPDATE_1_MASK      0x0000000FU
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RPTR_UPDATE_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RPTR_UPDATE_1_WIDTH              4U
#define LPDDR4__PHY_LP4_BOOT_RPTR_UPDATE_1__REG DENALI_PHY_263
#define LPDDR4__PHY_LP4_BOOT_RPTR_UPDATE_1__FLD LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RPTR_UPDATE_1

#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_1_MASK 0x00000F00U
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_1_SHIFT     8U
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_1_WIDTH     4U
#define LPDDR4__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_1__REG DENALI_PHY_263
#define LPDDR4__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_1__FLD LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_1

#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_1_MASK 0x00030000U
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_1_SHIFT     16U
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_1_WIDTH      2U
#define LPDDR4__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_1__REG DENALI_PHY_263
#define LPDDR4__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_1__FLD LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_1

#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_1_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_1_SHIFT        24U
#define LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_1_WIDTH         5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_1__REG DENALI_PHY_263
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_1__FLD LPDDR4__DENALI_PHY_263__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_1

#define LPDDR4__DENALI_PHY_264_READ_MASK                             0x0101FF03U
#define LPDDR4__DENALI_PHY_264_WRITE_MASK                            0x0101FF03U
#define LPDDR4__DENALI_PHY_264__PHY_CTRL_LPBK_EN_1_MASK              0x00000003U
#define LPDDR4__DENALI_PHY_264__PHY_CTRL_LPBK_EN_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_264__PHY_CTRL_LPBK_EN_1_WIDTH                      2U
#define LPDDR4__PHY_CTRL_LPBK_EN_1__REG DENALI_PHY_264
#define LPDDR4__PHY_CTRL_LPBK_EN_1__FLD LPDDR4__DENALI_PHY_264__PHY_CTRL_LPBK_EN_1

#define LPDDR4__DENALI_PHY_264__PHY_LPBK_CONTROL_1_MASK              0x0001FF00U
#define LPDDR4__DENALI_PHY_264__PHY_LPBK_CONTROL_1_SHIFT                      8U
#define LPDDR4__DENALI_PHY_264__PHY_LPBK_CONTROL_1_WIDTH                      9U
#define LPDDR4__PHY_LPBK_CONTROL_1__REG DENALI_PHY_264
#define LPDDR4__PHY_LPBK_CONTROL_1__FLD LPDDR4__DENALI_PHY_264__PHY_LPBK_CONTROL_1

#define LPDDR4__DENALI_PHY_264__PHY_LPBK_DFX_TIMEOUT_EN_1_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_264__PHY_LPBK_DFX_TIMEOUT_EN_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_264__PHY_LPBK_DFX_TIMEOUT_EN_1_WIDTH               1U
#define LPDDR4__DENALI_PHY_264__PHY_LPBK_DFX_TIMEOUT_EN_1_WOCLR               0U
#define LPDDR4__DENALI_PHY_264__PHY_LPBK_DFX_TIMEOUT_EN_1_WOSET               0U
#define LPDDR4__PHY_LPBK_DFX_TIMEOUT_EN_1__REG DENALI_PHY_264
#define LPDDR4__PHY_LPBK_DFX_TIMEOUT_EN_1__FLD LPDDR4__DENALI_PHY_264__PHY_LPBK_DFX_TIMEOUT_EN_1

#define LPDDR4__DENALI_PHY_265_READ_MASK                             0x00000001U
#define LPDDR4__DENALI_PHY_265_WRITE_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_265__PHY_GATE_DELAY_COMP_DISABLE_1_MASK   0x00000001U
#define LPDDR4__DENALI_PHY_265__PHY_GATE_DELAY_COMP_DISABLE_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_265__PHY_GATE_DELAY_COMP_DISABLE_1_WIDTH           1U
#define LPDDR4__DENALI_PHY_265__PHY_GATE_DELAY_COMP_DISABLE_1_WOCLR           0U
#define LPDDR4__DENALI_PHY_265__PHY_GATE_DELAY_COMP_DISABLE_1_WOSET           0U
#define LPDDR4__PHY_GATE_DELAY_COMP_DISABLE_1__REG DENALI_PHY_265
#define LPDDR4__PHY_GATE_DELAY_COMP_DISABLE_1__FLD LPDDR4__DENALI_PHY_265__PHY_GATE_DELAY_COMP_DISABLE_1

#define LPDDR4__DENALI_PHY_266_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_266_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_266__PHY_AUTO_TIMING_MARGIN_CONTROL_1_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_266__PHY_AUTO_TIMING_MARGIN_CONTROL_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_266__PHY_AUTO_TIMING_MARGIN_CONTROL_1_WIDTH       32U
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_CONTROL_1__REG DENALI_PHY_266
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_CONTROL_1__FLD LPDDR4__DENALI_PHY_266__PHY_AUTO_TIMING_MARGIN_CONTROL_1

#define LPDDR4__DENALI_PHY_267_READ_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_267_WRITE_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_267__PHY_AUTO_TIMING_MARGIN_OBS_1_MASK    0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_267__PHY_AUTO_TIMING_MARGIN_OBS_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_267__PHY_AUTO_TIMING_MARGIN_OBS_1_WIDTH           28U
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_OBS_1__REG DENALI_PHY_267
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_OBS_1__FLD LPDDR4__DENALI_PHY_267__PHY_AUTO_TIMING_MARGIN_OBS_1

#define LPDDR4__DENALI_PHY_268_READ_MASK                             0x7F0101FFU
#define LPDDR4__DENALI_PHY_268_WRITE_MASK                            0x7F0101FFU
#define LPDDR4__DENALI_PHY_268__PHY_DQ_IDLE_1_MASK                   0x000001FFU
#define LPDDR4__DENALI_PHY_268__PHY_DQ_IDLE_1_SHIFT                           0U
#define LPDDR4__DENALI_PHY_268__PHY_DQ_IDLE_1_WIDTH                           9U
#define LPDDR4__PHY_DQ_IDLE_1__REG DENALI_PHY_268
#define LPDDR4__PHY_DQ_IDLE_1__FLD LPDDR4__DENALI_PHY_268__PHY_DQ_IDLE_1

#define LPDDR4__DENALI_PHY_268__PHY_PDA_MODE_EN_1_MASK               0x00010000U
#define LPDDR4__DENALI_PHY_268__PHY_PDA_MODE_EN_1_SHIFT                      16U
#define LPDDR4__DENALI_PHY_268__PHY_PDA_MODE_EN_1_WIDTH                       1U
#define LPDDR4__DENALI_PHY_268__PHY_PDA_MODE_EN_1_WOCLR                       0U
#define LPDDR4__DENALI_PHY_268__PHY_PDA_MODE_EN_1_WOSET                       0U
#define LPDDR4__PHY_PDA_MODE_EN_1__REG DENALI_PHY_268
#define LPDDR4__PHY_PDA_MODE_EN_1__FLD LPDDR4__DENALI_PHY_268__PHY_PDA_MODE_EN_1

#define LPDDR4__DENALI_PHY_268__PHY_PRBS_PATTERN_START_1_MASK        0x7F000000U
#define LPDDR4__DENALI_PHY_268__PHY_PRBS_PATTERN_START_1_SHIFT               24U
#define LPDDR4__DENALI_PHY_268__PHY_PRBS_PATTERN_START_1_WIDTH                7U
#define LPDDR4__PHY_PRBS_PATTERN_START_1__REG DENALI_PHY_268
#define LPDDR4__PHY_PRBS_PATTERN_START_1__FLD LPDDR4__DENALI_PHY_268__PHY_PRBS_PATTERN_START_1

#define LPDDR4__DENALI_PHY_269_READ_MASK                             0x010101FFU
#define LPDDR4__DENALI_PHY_269_WRITE_MASK                            0x010101FFU
#define LPDDR4__DENALI_PHY_269__PHY_PRBS_PATTERN_MASK_1_MASK         0x000001FFU
#define LPDDR4__DENALI_PHY_269__PHY_PRBS_PATTERN_MASK_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_269__PHY_PRBS_PATTERN_MASK_1_WIDTH                 9U
#define LPDDR4__PHY_PRBS_PATTERN_MASK_1__REG DENALI_PHY_269
#define LPDDR4__PHY_PRBS_PATTERN_MASK_1__FLD LPDDR4__DENALI_PHY_269__PHY_PRBS_PATTERN_MASK_1

#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_ENABLE_1_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_ENABLE_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_ENABLE_1_WIDTH           1U
#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_ENABLE_1_WOCLR           0U
#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_ENABLE_1_WOSET           0U
#define LPDDR4__PHY_RDLVL_MULTI_PATT_ENABLE_1__REG DENALI_PHY_269
#define LPDDR4__PHY_RDLVL_MULTI_PATT_ENABLE_1__FLD LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_ENABLE_1

#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_RST_DISABLE_1_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_RST_DISABLE_1_SHIFT     24U
#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_RST_DISABLE_1_WIDTH      1U
#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_RST_DISABLE_1_WOCLR      0U
#define LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_RST_DISABLE_1_WOSET      0U
#define LPDDR4__PHY_RDLVL_MULTI_PATT_RST_DISABLE_1__REG DENALI_PHY_269
#define LPDDR4__PHY_RDLVL_MULTI_PATT_RST_DISABLE_1__FLD LPDDR4__DENALI_PHY_269__PHY_RDLVL_MULTI_PATT_RST_DISABLE_1

#define LPDDR4__DENALI_PHY_270_READ_MASK                             0x03FF7F3FU
#define LPDDR4__DENALI_PHY_270_WRITE_MASK                            0x03FF7F3FU
#define LPDDR4__DENALI_PHY_270__PHY_VREF_INITIAL_STEPSIZE_1_MASK     0x0000003FU
#define LPDDR4__DENALI_PHY_270__PHY_VREF_INITIAL_STEPSIZE_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_270__PHY_VREF_INITIAL_STEPSIZE_1_WIDTH             6U
#define LPDDR4__PHY_VREF_INITIAL_STEPSIZE_1__REG DENALI_PHY_270
#define LPDDR4__PHY_VREF_INITIAL_STEPSIZE_1__FLD LPDDR4__DENALI_PHY_270__PHY_VREF_INITIAL_STEPSIZE_1

#define LPDDR4__DENALI_PHY_270__PHY_VREF_TRAIN_OBS_1_MASK            0x00007F00U
#define LPDDR4__DENALI_PHY_270__PHY_VREF_TRAIN_OBS_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_270__PHY_VREF_TRAIN_OBS_1_WIDTH                    7U
#define LPDDR4__PHY_VREF_TRAIN_OBS_1__REG DENALI_PHY_270
#define LPDDR4__PHY_VREF_TRAIN_OBS_1__FLD LPDDR4__DENALI_PHY_270__PHY_VREF_TRAIN_OBS_1

#define LPDDR4__DENALI_PHY_270__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_270__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_1_SHIFT      16U
#define LPDDR4__DENALI_PHY_270__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_1_WIDTH      10U
#define LPDDR4__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_1__REG DENALI_PHY_270
#define LPDDR4__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_270__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_271_READ_MASK                             0x01FF000FU
#define LPDDR4__DENALI_PHY_271_WRITE_MASK                            0x01FF000FU
#define LPDDR4__DENALI_PHY_271__PHY_GATE_ERROR_DELAY_SELECT_1_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_271__PHY_GATE_ERROR_DELAY_SELECT_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_271__PHY_GATE_ERROR_DELAY_SELECT_1_WIDTH           4U
#define LPDDR4__PHY_GATE_ERROR_DELAY_SELECT_1__REG DENALI_PHY_271
#define LPDDR4__PHY_GATE_ERROR_DELAY_SELECT_1__FLD LPDDR4__DENALI_PHY_271__PHY_GATE_ERROR_DELAY_SELECT_1

#define LPDDR4__DENALI_PHY_271__SC_PHY_SNAP_OBS_REGS_1_MASK          0x00000100U
#define LPDDR4__DENALI_PHY_271__SC_PHY_SNAP_OBS_REGS_1_SHIFT                  8U
#define LPDDR4__DENALI_PHY_271__SC_PHY_SNAP_OBS_REGS_1_WIDTH                  1U
#define LPDDR4__DENALI_PHY_271__SC_PHY_SNAP_OBS_REGS_1_WOCLR                  0U
#define LPDDR4__DENALI_PHY_271__SC_PHY_SNAP_OBS_REGS_1_WOSET                  0U
#define LPDDR4__SC_PHY_SNAP_OBS_REGS_1__REG DENALI_PHY_271
#define LPDDR4__SC_PHY_SNAP_OBS_REGS_1__FLD LPDDR4__DENALI_PHY_271__SC_PHY_SNAP_OBS_REGS_1

#define LPDDR4__DENALI_PHY_271__PHY_GATE_SMPL1_SLAVE_DELAY_1_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_271__PHY_GATE_SMPL1_SLAVE_DELAY_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_271__PHY_GATE_SMPL1_SLAVE_DELAY_1_WIDTH            9U
#define LPDDR4__PHY_GATE_SMPL1_SLAVE_DELAY_1__REG DENALI_PHY_271
#define LPDDR4__PHY_GATE_SMPL1_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_271__PHY_GATE_SMPL1_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_272_READ_MASK                             0x01FF0701U
#define LPDDR4__DENALI_PHY_272_WRITE_MASK                            0x01FF0701U
#define LPDDR4__DENALI_PHY_272__PHY_LPDDR_1_MASK                     0x00000001U
#define LPDDR4__DENALI_PHY_272__PHY_LPDDR_1_SHIFT                             0U
#define LPDDR4__DENALI_PHY_272__PHY_LPDDR_1_WIDTH                             1U
#define LPDDR4__DENALI_PHY_272__PHY_LPDDR_1_WOCLR                             0U
#define LPDDR4__DENALI_PHY_272__PHY_LPDDR_1_WOSET                             0U
#define LPDDR4__PHY_LPDDR_1__REG DENALI_PHY_272
#define LPDDR4__PHY_LPDDR_1__FLD LPDDR4__DENALI_PHY_272__PHY_LPDDR_1

#define LPDDR4__DENALI_PHY_272__PHY_MEM_CLASS_1_MASK                 0x00000700U
#define LPDDR4__DENALI_PHY_272__PHY_MEM_CLASS_1_SHIFT                         8U
#define LPDDR4__DENALI_PHY_272__PHY_MEM_CLASS_1_WIDTH                         3U
#define LPDDR4__PHY_MEM_CLASS_1__REG DENALI_PHY_272
#define LPDDR4__PHY_MEM_CLASS_1__FLD LPDDR4__DENALI_PHY_272__PHY_MEM_CLASS_1

#define LPDDR4__DENALI_PHY_272__PHY_GATE_SMPL2_SLAVE_DELAY_1_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_272__PHY_GATE_SMPL2_SLAVE_DELAY_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_272__PHY_GATE_SMPL2_SLAVE_DELAY_1_WIDTH            9U
#define LPDDR4__PHY_GATE_SMPL2_SLAVE_DELAY_1__REG DENALI_PHY_272
#define LPDDR4__PHY_GATE_SMPL2_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_272__PHY_GATE_SMPL2_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_273_READ_MASK                             0x00000003U
#define LPDDR4__DENALI_PHY_273_WRITE_MASK                            0x00000003U
#define LPDDR4__DENALI_PHY_273__ON_FLY_GATE_ADJUST_EN_1_MASK         0x00000003U
#define LPDDR4__DENALI_PHY_273__ON_FLY_GATE_ADJUST_EN_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_273__ON_FLY_GATE_ADJUST_EN_1_WIDTH                 2U
#define LPDDR4__ON_FLY_GATE_ADJUST_EN_1__REG DENALI_PHY_273
#define LPDDR4__ON_FLY_GATE_ADJUST_EN_1__FLD LPDDR4__DENALI_PHY_273__ON_FLY_GATE_ADJUST_EN_1

#define LPDDR4__DENALI_PHY_274_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_274_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_274__PHY_GATE_TRACKING_OBS_1_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_274__PHY_GATE_TRACKING_OBS_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_274__PHY_GATE_TRACKING_OBS_1_WIDTH                32U
#define LPDDR4__PHY_GATE_TRACKING_OBS_1__REG DENALI_PHY_274
#define LPDDR4__PHY_GATE_TRACKING_OBS_1__FLD LPDDR4__DENALI_PHY_274__PHY_GATE_TRACKING_OBS_1

#define LPDDR4__DENALI_PHY_275_READ_MASK                             0x00000301U
#define LPDDR4__DENALI_PHY_275_WRITE_MASK                            0x00000301U
#define LPDDR4__DENALI_PHY_275__PHY_DFI40_POLARITY_1_MASK            0x00000001U
#define LPDDR4__DENALI_PHY_275__PHY_DFI40_POLARITY_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_275__PHY_DFI40_POLARITY_1_WIDTH                    1U
#define LPDDR4__DENALI_PHY_275__PHY_DFI40_POLARITY_1_WOCLR                    0U
#define LPDDR4__DENALI_PHY_275__PHY_DFI40_POLARITY_1_WOSET                    0U
#define LPDDR4__PHY_DFI40_POLARITY_1__REG DENALI_PHY_275
#define LPDDR4__PHY_DFI40_POLARITY_1__FLD LPDDR4__DENALI_PHY_275__PHY_DFI40_POLARITY_1

#define LPDDR4__DENALI_PHY_275__PHY_LP4_PST_AMBLE_1_MASK             0x00000300U
#define LPDDR4__DENALI_PHY_275__PHY_LP4_PST_AMBLE_1_SHIFT                     8U
#define LPDDR4__DENALI_PHY_275__PHY_LP4_PST_AMBLE_1_WIDTH                     2U
#define LPDDR4__PHY_LP4_PST_AMBLE_1__REG DENALI_PHY_275
#define LPDDR4__PHY_LP4_PST_AMBLE_1__FLD LPDDR4__DENALI_PHY_275__PHY_LP4_PST_AMBLE_1

#define LPDDR4__DENALI_PHY_276_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_276_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_276__PHY_RDLVL_PATT8_1_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_276__PHY_RDLVL_PATT8_1_SHIFT                       0U
#define LPDDR4__DENALI_PHY_276__PHY_RDLVL_PATT8_1_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT8_1__REG DENALI_PHY_276
#define LPDDR4__PHY_RDLVL_PATT8_1__FLD LPDDR4__DENALI_PHY_276__PHY_RDLVL_PATT8_1

#define LPDDR4__DENALI_PHY_277_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_277_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_277__PHY_RDLVL_PATT9_1_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_277__PHY_RDLVL_PATT9_1_SHIFT                       0U
#define LPDDR4__DENALI_PHY_277__PHY_RDLVL_PATT9_1_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT9_1__REG DENALI_PHY_277
#define LPDDR4__PHY_RDLVL_PATT9_1__FLD LPDDR4__DENALI_PHY_277__PHY_RDLVL_PATT9_1

#define LPDDR4__DENALI_PHY_278_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_278_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_278__PHY_RDLVL_PATT10_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_278__PHY_RDLVL_PATT10_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_278__PHY_RDLVL_PATT10_1_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT10_1__REG DENALI_PHY_278
#define LPDDR4__PHY_RDLVL_PATT10_1__FLD LPDDR4__DENALI_PHY_278__PHY_RDLVL_PATT10_1

#define LPDDR4__DENALI_PHY_279_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_279_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_279__PHY_RDLVL_PATT11_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_279__PHY_RDLVL_PATT11_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_279__PHY_RDLVL_PATT11_1_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT11_1__REG DENALI_PHY_279
#define LPDDR4__PHY_RDLVL_PATT11_1__FLD LPDDR4__DENALI_PHY_279__PHY_RDLVL_PATT11_1

#define LPDDR4__DENALI_PHY_280_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_280_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_280__PHY_RDLVL_PATT12_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_280__PHY_RDLVL_PATT12_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_280__PHY_RDLVL_PATT12_1_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT12_1__REG DENALI_PHY_280
#define LPDDR4__PHY_RDLVL_PATT12_1__FLD LPDDR4__DENALI_PHY_280__PHY_RDLVL_PATT12_1

#define LPDDR4__DENALI_PHY_281_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_281_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_281__PHY_RDLVL_PATT13_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_281__PHY_RDLVL_PATT13_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_281__PHY_RDLVL_PATT13_1_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT13_1__REG DENALI_PHY_281
#define LPDDR4__PHY_RDLVL_PATT13_1__FLD LPDDR4__DENALI_PHY_281__PHY_RDLVL_PATT13_1

#define LPDDR4__DENALI_PHY_282_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_282_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_282__PHY_RDLVL_PATT14_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_282__PHY_RDLVL_PATT14_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_282__PHY_RDLVL_PATT14_1_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT14_1__REG DENALI_PHY_282
#define LPDDR4__PHY_RDLVL_PATT14_1__FLD LPDDR4__DENALI_PHY_282__PHY_RDLVL_PATT14_1

#define LPDDR4__DENALI_PHY_283_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_283_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_283__PHY_RDLVL_PATT15_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_283__PHY_RDLVL_PATT15_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_283__PHY_RDLVL_PATT15_1_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT15_1__REG DENALI_PHY_283
#define LPDDR4__PHY_RDLVL_PATT15_1__FLD LPDDR4__DENALI_PHY_283__PHY_RDLVL_PATT15_1

#define LPDDR4__DENALI_PHY_284_READ_MASK                             0x070F0107U
#define LPDDR4__DENALI_PHY_284_WRITE_MASK                            0x070F0107U
#define LPDDR4__DENALI_PHY_284__PHY_SLAVE_LOOP_CNT_UPDATE_1_MASK     0x00000007U
#define LPDDR4__DENALI_PHY_284__PHY_SLAVE_LOOP_CNT_UPDATE_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_284__PHY_SLAVE_LOOP_CNT_UPDATE_1_WIDTH             3U
#define LPDDR4__PHY_SLAVE_LOOP_CNT_UPDATE_1__REG DENALI_PHY_284
#define LPDDR4__PHY_SLAVE_LOOP_CNT_UPDATE_1__FLD LPDDR4__DENALI_PHY_284__PHY_SLAVE_LOOP_CNT_UPDATE_1

#define LPDDR4__DENALI_PHY_284__PHY_SW_FIFO_PTR_RST_DISABLE_1_MASK   0x00000100U
#define LPDDR4__DENALI_PHY_284__PHY_SW_FIFO_PTR_RST_DISABLE_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_284__PHY_SW_FIFO_PTR_RST_DISABLE_1_WIDTH           1U
#define LPDDR4__DENALI_PHY_284__PHY_SW_FIFO_PTR_RST_DISABLE_1_WOCLR           0U
#define LPDDR4__DENALI_PHY_284__PHY_SW_FIFO_PTR_RST_DISABLE_1_WOSET           0U
#define LPDDR4__PHY_SW_FIFO_PTR_RST_DISABLE_1__REG DENALI_PHY_284
#define LPDDR4__PHY_SW_FIFO_PTR_RST_DISABLE_1__FLD LPDDR4__DENALI_PHY_284__PHY_SW_FIFO_PTR_RST_DISABLE_1

#define LPDDR4__DENALI_PHY_284__PHY_MASTER_DLY_LOCK_OBS_SELECT_1_MASK 0x000F0000U
#define LPDDR4__DENALI_PHY_284__PHY_MASTER_DLY_LOCK_OBS_SELECT_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_284__PHY_MASTER_DLY_LOCK_OBS_SELECT_1_WIDTH        4U
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_SELECT_1__REG DENALI_PHY_284
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_284__PHY_MASTER_DLY_LOCK_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_284__PHY_RDDQ_ENC_OBS_SELECT_1_MASK       0x07000000U
#define LPDDR4__DENALI_PHY_284__PHY_RDDQ_ENC_OBS_SELECT_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_284__PHY_RDDQ_ENC_OBS_SELECT_1_WIDTH               3U
#define LPDDR4__PHY_RDDQ_ENC_OBS_SELECT_1__REG DENALI_PHY_284
#define LPDDR4__PHY_RDDQ_ENC_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_284__PHY_RDDQ_ENC_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_285_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_285_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_285__PHY_RDDQS_DQ_ENC_OBS_SELECT_1_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_285__PHY_RDDQS_DQ_ENC_OBS_SELECT_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_285__PHY_RDDQS_DQ_ENC_OBS_SELECT_1_WIDTH           4U
#define LPDDR4__PHY_RDDQS_DQ_ENC_OBS_SELECT_1__REG DENALI_PHY_285
#define LPDDR4__PHY_RDDQS_DQ_ENC_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_285__PHY_RDDQS_DQ_ENC_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_285__PHY_WR_ENC_OBS_SELECT_1_MASK         0x00000F00U
#define LPDDR4__DENALI_PHY_285__PHY_WR_ENC_OBS_SELECT_1_SHIFT                 8U
#define LPDDR4__DENALI_PHY_285__PHY_WR_ENC_OBS_SELECT_1_WIDTH                 4U
#define LPDDR4__PHY_WR_ENC_OBS_SELECT_1__REG DENALI_PHY_285
#define LPDDR4__PHY_WR_ENC_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_285__PHY_WR_ENC_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_285__PHY_WR_SHIFT_OBS_SELECT_1_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_285__PHY_WR_SHIFT_OBS_SELECT_1_SHIFT              16U
#define LPDDR4__DENALI_PHY_285__PHY_WR_SHIFT_OBS_SELECT_1_WIDTH               4U
#define LPDDR4__PHY_WR_SHIFT_OBS_SELECT_1__REG DENALI_PHY_285
#define LPDDR4__PHY_WR_SHIFT_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_285__PHY_WR_SHIFT_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_285__PHY_FIFO_PTR_OBS_SELECT_1_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_285__PHY_FIFO_PTR_OBS_SELECT_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_285__PHY_FIFO_PTR_OBS_SELECT_1_WIDTH               4U
#define LPDDR4__PHY_FIFO_PTR_OBS_SELECT_1__REG DENALI_PHY_285
#define LPDDR4__PHY_FIFO_PTR_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_285__PHY_FIFO_PTR_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_286_READ_MASK                             0x3F030001U
#define LPDDR4__DENALI_PHY_286_WRITE_MASK                            0x3F030001U
#define LPDDR4__DENALI_PHY_286__PHY_LVL_DEBUG_MODE_1_MASK            0x00000001U
#define LPDDR4__DENALI_PHY_286__PHY_LVL_DEBUG_MODE_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_286__PHY_LVL_DEBUG_MODE_1_WIDTH                    1U
#define LPDDR4__DENALI_PHY_286__PHY_LVL_DEBUG_MODE_1_WOCLR                    0U
#define LPDDR4__DENALI_PHY_286__PHY_LVL_DEBUG_MODE_1_WOSET                    0U
#define LPDDR4__PHY_LVL_DEBUG_MODE_1__REG DENALI_PHY_286
#define LPDDR4__PHY_LVL_DEBUG_MODE_1__FLD LPDDR4__DENALI_PHY_286__PHY_LVL_DEBUG_MODE_1

#define LPDDR4__DENALI_PHY_286__SC_PHY_LVL_DEBUG_CONT_1_MASK         0x00000100U
#define LPDDR4__DENALI_PHY_286__SC_PHY_LVL_DEBUG_CONT_1_SHIFT                 8U
#define LPDDR4__DENALI_PHY_286__SC_PHY_LVL_DEBUG_CONT_1_WIDTH                 1U
#define LPDDR4__DENALI_PHY_286__SC_PHY_LVL_DEBUG_CONT_1_WOCLR                 0U
#define LPDDR4__DENALI_PHY_286__SC_PHY_LVL_DEBUG_CONT_1_WOSET                 0U
#define LPDDR4__SC_PHY_LVL_DEBUG_CONT_1__REG DENALI_PHY_286
#define LPDDR4__SC_PHY_LVL_DEBUG_CONT_1__FLD LPDDR4__DENALI_PHY_286__SC_PHY_LVL_DEBUG_CONT_1

#define LPDDR4__DENALI_PHY_286__PHY_WRLVL_ALGO_1_MASK                0x00030000U
#define LPDDR4__DENALI_PHY_286__PHY_WRLVL_ALGO_1_SHIFT                       16U
#define LPDDR4__DENALI_PHY_286__PHY_WRLVL_ALGO_1_WIDTH                        2U
#define LPDDR4__PHY_WRLVL_ALGO_1__REG DENALI_PHY_286
#define LPDDR4__PHY_WRLVL_ALGO_1__FLD LPDDR4__DENALI_PHY_286__PHY_WRLVL_ALGO_1

#define LPDDR4__DENALI_PHY_286__PHY_WRLVL_CAPTURE_CNT_1_MASK         0x3F000000U
#define LPDDR4__DENALI_PHY_286__PHY_WRLVL_CAPTURE_CNT_1_SHIFT                24U
#define LPDDR4__DENALI_PHY_286__PHY_WRLVL_CAPTURE_CNT_1_WIDTH                 6U
#define LPDDR4__PHY_WRLVL_CAPTURE_CNT_1__REG DENALI_PHY_286
#define LPDDR4__PHY_WRLVL_CAPTURE_CNT_1__FLD LPDDR4__DENALI_PHY_286__PHY_WRLVL_CAPTURE_CNT_1

#define LPDDR4__DENALI_PHY_287_READ_MASK                             0x0F3FFF0FU
#define LPDDR4__DENALI_PHY_287_WRITE_MASK                            0x0F3FFF0FU
#define LPDDR4__DENALI_PHY_287__PHY_WRLVL_UPDT_WAIT_CNT_1_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_287__PHY_WRLVL_UPDT_WAIT_CNT_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_287__PHY_WRLVL_UPDT_WAIT_CNT_1_WIDTH               4U
#define LPDDR4__PHY_WRLVL_UPDT_WAIT_CNT_1__REG DENALI_PHY_287
#define LPDDR4__PHY_WRLVL_UPDT_WAIT_CNT_1__FLD LPDDR4__DENALI_PHY_287__PHY_WRLVL_UPDT_WAIT_CNT_1

#define LPDDR4__DENALI_PHY_287__PHY_DQ_MASK_1_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PHY_287__PHY_DQ_MASK_1_SHIFT                           8U
#define LPDDR4__DENALI_PHY_287__PHY_DQ_MASK_1_WIDTH                           8U
#define LPDDR4__PHY_DQ_MASK_1__REG DENALI_PHY_287
#define LPDDR4__PHY_DQ_MASK_1__FLD LPDDR4__DENALI_PHY_287__PHY_DQ_MASK_1

#define LPDDR4__DENALI_PHY_287__PHY_GTLVL_CAPTURE_CNT_1_MASK         0x003F0000U
#define LPDDR4__DENALI_PHY_287__PHY_GTLVL_CAPTURE_CNT_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_287__PHY_GTLVL_CAPTURE_CNT_1_WIDTH                 6U
#define LPDDR4__PHY_GTLVL_CAPTURE_CNT_1__REG DENALI_PHY_287
#define LPDDR4__PHY_GTLVL_CAPTURE_CNT_1__FLD LPDDR4__DENALI_PHY_287__PHY_GTLVL_CAPTURE_CNT_1

#define LPDDR4__DENALI_PHY_287__PHY_GTLVL_UPDT_WAIT_CNT_1_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_287__PHY_GTLVL_UPDT_WAIT_CNT_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_287__PHY_GTLVL_UPDT_WAIT_CNT_1_WIDTH               4U
#define LPDDR4__PHY_GTLVL_UPDT_WAIT_CNT_1__REG DENALI_PHY_287
#define LPDDR4__PHY_GTLVL_UPDT_WAIT_CNT_1__FLD LPDDR4__DENALI_PHY_287__PHY_GTLVL_UPDT_WAIT_CNT_1

#define LPDDR4__DENALI_PHY_288_READ_MASK                             0x1F030F3FU
#define LPDDR4__DENALI_PHY_288_WRITE_MASK                            0x1F030F3FU
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_CAPTURE_CNT_1_MASK         0x0000003FU
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_CAPTURE_CNT_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_CAPTURE_CNT_1_WIDTH                 6U
#define LPDDR4__PHY_RDLVL_CAPTURE_CNT_1__REG DENALI_PHY_288
#define LPDDR4__PHY_RDLVL_CAPTURE_CNT_1__FLD LPDDR4__DENALI_PHY_288__PHY_RDLVL_CAPTURE_CNT_1

#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_UPDT_WAIT_CNT_1_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_UPDT_WAIT_CNT_1_SHIFT               8U
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_UPDT_WAIT_CNT_1_WIDTH               4U
#define LPDDR4__PHY_RDLVL_UPDT_WAIT_CNT_1__REG DENALI_PHY_288
#define LPDDR4__PHY_RDLVL_UPDT_WAIT_CNT_1__FLD LPDDR4__DENALI_PHY_288__PHY_RDLVL_UPDT_WAIT_CNT_1

#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_OP_MODE_1_MASK             0x00030000U
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_OP_MODE_1_SHIFT                    16U
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_OP_MODE_1_WIDTH                     2U
#define LPDDR4__PHY_RDLVL_OP_MODE_1__REG DENALI_PHY_288
#define LPDDR4__PHY_RDLVL_OP_MODE_1__FLD LPDDR4__DENALI_PHY_288__PHY_RDLVL_OP_MODE_1

#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_1_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_1_SHIFT        24U
#define LPDDR4__DENALI_PHY_288__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_1_WIDTH         5U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_1__REG DENALI_PHY_288
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_288__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_289_READ_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PHY_289_WRITE_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_289__PHY_RDLVL_DATA_MASK_1_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_289__PHY_RDLVL_DATA_MASK_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_289__PHY_RDLVL_DATA_MASK_1_WIDTH                   8U
#define LPDDR4__PHY_RDLVL_DATA_MASK_1__REG DENALI_PHY_289
#define LPDDR4__PHY_RDLVL_DATA_MASK_1__FLD LPDDR4__DENALI_PHY_289__PHY_RDLVL_DATA_MASK_1

#define LPDDR4__DENALI_PHY_289__PHY_RDLVL_DATA_SWIZZLE_1_MASK        0x03FFFF00U
#define LPDDR4__DENALI_PHY_289__PHY_RDLVL_DATA_SWIZZLE_1_SHIFT                8U
#define LPDDR4__DENALI_PHY_289__PHY_RDLVL_DATA_SWIZZLE_1_WIDTH               18U
#define LPDDR4__PHY_RDLVL_DATA_SWIZZLE_1__REG DENALI_PHY_289
#define LPDDR4__PHY_RDLVL_DATA_SWIZZLE_1__FLD LPDDR4__DENALI_PHY_289__PHY_RDLVL_DATA_SWIZZLE_1

#define LPDDR4__DENALI_PHY_290_READ_MASK                             0x00073FFFU
#define LPDDR4__DENALI_PHY_290_WRITE_MASK                            0x00073FFFU
#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_CLK_JITTER_TOLERANCE_1_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_CLK_JITTER_TOLERANCE_1_SHIFT       0U
#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_CLK_JITTER_TOLERANCE_1_WIDTH       8U
#define LPDDR4__PHY_WDQLVL_CLK_JITTER_TOLERANCE_1__REG DENALI_PHY_290
#define LPDDR4__PHY_WDQLVL_CLK_JITTER_TOLERANCE_1__FLD LPDDR4__DENALI_PHY_290__PHY_WDQLVL_CLK_JITTER_TOLERANCE_1

#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_BURST_CNT_1_MASK          0x00003F00U
#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_BURST_CNT_1_SHIFT                  8U
#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_BURST_CNT_1_WIDTH                  6U
#define LPDDR4__PHY_WDQLVL_BURST_CNT_1__REG DENALI_PHY_290
#define LPDDR4__PHY_WDQLVL_BURST_CNT_1__FLD LPDDR4__DENALI_PHY_290__PHY_WDQLVL_BURST_CNT_1

#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_PATT_1_MASK               0x00070000U
#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_PATT_1_SHIFT                      16U
#define LPDDR4__DENALI_PHY_290__PHY_WDQLVL_PATT_1_WIDTH                       3U
#define LPDDR4__PHY_WDQLVL_PATT_1__REG DENALI_PHY_290
#define LPDDR4__PHY_WDQLVL_PATT_1__FLD LPDDR4__DENALI_PHY_290__PHY_WDQLVL_PATT_1

#define LPDDR4__DENALI_PHY_291_READ_MASK                             0x0F0F07FFU
#define LPDDR4__DENALI_PHY_291_WRITE_MASK                            0x0F0F07FFU
#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_1_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_1_SHIFT   0U
#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_1_WIDTH  11U
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_1__REG DENALI_PHY_291
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_1__FLD LPDDR4__DENALI_PHY_291__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_1

#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_UPDT_WAIT_CNT_1_MASK      0x000F0000U
#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_UPDT_WAIT_CNT_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_UPDT_WAIT_CNT_1_WIDTH              4U
#define LPDDR4__PHY_WDQLVL_UPDT_WAIT_CNT_1__REG DENALI_PHY_291
#define LPDDR4__PHY_WDQLVL_UPDT_WAIT_CNT_1__FLD LPDDR4__DENALI_PHY_291__PHY_WDQLVL_UPDT_WAIT_CNT_1

#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_DQDM_OBS_SELECT_1_MASK    0x0F000000U
#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_DQDM_OBS_SELECT_1_SHIFT           24U
#define LPDDR4__DENALI_PHY_291__PHY_WDQLVL_DQDM_OBS_SELECT_1_WIDTH            4U
#define LPDDR4__PHY_WDQLVL_DQDM_OBS_SELECT_1__REG DENALI_PHY_291
#define LPDDR4__PHY_WDQLVL_DQDM_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_291__PHY_WDQLVL_DQDM_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_292_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_292_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_PERIODIC_OBS_SELECT_1_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_PERIODIC_OBS_SELECT_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_PERIODIC_OBS_SELECT_1_WIDTH        8U
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_SELECT_1__REG DENALI_PHY_292
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_SELECT_1__FLD LPDDR4__DENALI_PHY_292__PHY_WDQLVL_PERIODIC_OBS_SELECT_1

#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_DQ_SLV_DELTA_1_MASK       0x0000FF00U
#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_DQ_SLV_DELTA_1_SHIFT               8U
#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_DQ_SLV_DELTA_1_WIDTH               8U
#define LPDDR4__PHY_WDQLVL_DQ_SLV_DELTA_1__REG DENALI_PHY_292
#define LPDDR4__PHY_WDQLVL_DQ_SLV_DELTA_1__FLD LPDDR4__DENALI_PHY_292__PHY_WDQLVL_DQ_SLV_DELTA_1

#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_DM_DLY_STEP_1_MASK        0x000F0000U
#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_DM_DLY_STEP_1_SHIFT               16U
#define LPDDR4__DENALI_PHY_292__PHY_WDQLVL_DM_DLY_STEP_1_WIDTH                4U
#define LPDDR4__PHY_WDQLVL_DM_DLY_STEP_1__REG DENALI_PHY_292
#define LPDDR4__PHY_WDQLVL_DM_DLY_STEP_1__FLD LPDDR4__DENALI_PHY_292__PHY_WDQLVL_DM_DLY_STEP_1

#define LPDDR4__DENALI_PHY_292__SC_PHY_WDQLVL_CLR_PREV_RESULTS_1_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_292__SC_PHY_WDQLVL_CLR_PREV_RESULTS_1_SHIFT       24U
#define LPDDR4__DENALI_PHY_292__SC_PHY_WDQLVL_CLR_PREV_RESULTS_1_WIDTH        1U
#define LPDDR4__DENALI_PHY_292__SC_PHY_WDQLVL_CLR_PREV_RESULTS_1_WOCLR        0U
#define LPDDR4__DENALI_PHY_292__SC_PHY_WDQLVL_CLR_PREV_RESULTS_1_WOSET        0U
#define LPDDR4__SC_PHY_WDQLVL_CLR_PREV_RESULTS_1__REG DENALI_PHY_292
#define LPDDR4__SC_PHY_WDQLVL_CLR_PREV_RESULTS_1__FLD LPDDR4__DENALI_PHY_292__SC_PHY_WDQLVL_CLR_PREV_RESULTS_1

#define LPDDR4__DENALI_PHY_293_READ_MASK                             0x000001FFU
#define LPDDR4__DENALI_PHY_293_WRITE_MASK                            0x000001FFU
#define LPDDR4__DENALI_PHY_293__PHY_WDQLVL_DATADM_MASK_1_MASK        0x000001FFU
#define LPDDR4__DENALI_PHY_293__PHY_WDQLVL_DATADM_MASK_1_SHIFT                0U
#define LPDDR4__DENALI_PHY_293__PHY_WDQLVL_DATADM_MASK_1_WIDTH                9U
#define LPDDR4__PHY_WDQLVL_DATADM_MASK_1__REG DENALI_PHY_293
#define LPDDR4__PHY_WDQLVL_DATADM_MASK_1__FLD LPDDR4__DENALI_PHY_293__PHY_WDQLVL_DATADM_MASK_1

#define LPDDR4__DENALI_PHY_294_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_294_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_294__PHY_USER_PATT0_1_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_294__PHY_USER_PATT0_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_294__PHY_USER_PATT0_1_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT0_1__REG DENALI_PHY_294
#define LPDDR4__PHY_USER_PATT0_1__FLD LPDDR4__DENALI_PHY_294__PHY_USER_PATT0_1

#define LPDDR4__DENALI_PHY_295_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_295_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_295__PHY_USER_PATT1_1_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_295__PHY_USER_PATT1_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_295__PHY_USER_PATT1_1_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT1_1__REG DENALI_PHY_295
#define LPDDR4__PHY_USER_PATT1_1__FLD LPDDR4__DENALI_PHY_295__PHY_USER_PATT1_1

#define LPDDR4__DENALI_PHY_296_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_296_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_296__PHY_USER_PATT2_1_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_296__PHY_USER_PATT2_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_296__PHY_USER_PATT2_1_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT2_1__REG DENALI_PHY_296
#define LPDDR4__PHY_USER_PATT2_1__FLD LPDDR4__DENALI_PHY_296__PHY_USER_PATT2_1

#define LPDDR4__DENALI_PHY_297_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_297_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_297__PHY_USER_PATT3_1_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_297__PHY_USER_PATT3_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_297__PHY_USER_PATT3_1_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT3_1__REG DENALI_PHY_297
#define LPDDR4__PHY_USER_PATT3_1__FLD LPDDR4__DENALI_PHY_297__PHY_USER_PATT3_1

#define LPDDR4__DENALI_PHY_298_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PHY_298_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_298__PHY_USER_PATT4_1_MASK                0x0000FFFFU
#define LPDDR4__DENALI_PHY_298__PHY_USER_PATT4_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_298__PHY_USER_PATT4_1_WIDTH                       16U
#define LPDDR4__PHY_USER_PATT4_1__REG DENALI_PHY_298
#define LPDDR4__PHY_USER_PATT4_1__FLD LPDDR4__DENALI_PHY_298__PHY_USER_PATT4_1

#define LPDDR4__DENALI_PHY_298__PHY_NTP_MULT_TRAIN_1_MASK            0x00010000U
#define LPDDR4__DENALI_PHY_298__PHY_NTP_MULT_TRAIN_1_SHIFT                   16U
#define LPDDR4__DENALI_PHY_298__PHY_NTP_MULT_TRAIN_1_WIDTH                    1U
#define LPDDR4__DENALI_PHY_298__PHY_NTP_MULT_TRAIN_1_WOCLR                    0U
#define LPDDR4__DENALI_PHY_298__PHY_NTP_MULT_TRAIN_1_WOSET                    0U
#define LPDDR4__PHY_NTP_MULT_TRAIN_1__REG DENALI_PHY_298
#define LPDDR4__PHY_NTP_MULT_TRAIN_1__FLD LPDDR4__DENALI_PHY_298__PHY_NTP_MULT_TRAIN_1

#define LPDDR4__DENALI_PHY_299_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_299_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_299__PHY_NTP_EARLY_THRESHOLD_1_MASK       0x000003FFU
#define LPDDR4__DENALI_PHY_299__PHY_NTP_EARLY_THRESHOLD_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_299__PHY_NTP_EARLY_THRESHOLD_1_WIDTH              10U
#define LPDDR4__PHY_NTP_EARLY_THRESHOLD_1__REG DENALI_PHY_299
#define LPDDR4__PHY_NTP_EARLY_THRESHOLD_1__FLD LPDDR4__DENALI_PHY_299__PHY_NTP_EARLY_THRESHOLD_1

#define LPDDR4__DENALI_PHY_299__PHY_NTP_PERIOD_THRESHOLD_1_MASK      0x03FF0000U
#define LPDDR4__DENALI_PHY_299__PHY_NTP_PERIOD_THRESHOLD_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_299__PHY_NTP_PERIOD_THRESHOLD_1_WIDTH             10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_1__REG DENALI_PHY_299
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_1__FLD LPDDR4__DENALI_PHY_299__PHY_NTP_PERIOD_THRESHOLD_1

#define LPDDR4__DENALI_PHY_300_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_300_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_300__PHY_NTP_PERIOD_THRESHOLD_MIN_1_MASK  0x000003FFU
#define LPDDR4__DENALI_PHY_300__PHY_NTP_PERIOD_THRESHOLD_MIN_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_300__PHY_NTP_PERIOD_THRESHOLD_MIN_1_WIDTH         10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MIN_1__REG DENALI_PHY_300
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MIN_1__FLD LPDDR4__DENALI_PHY_300__PHY_NTP_PERIOD_THRESHOLD_MIN_1

#define LPDDR4__DENALI_PHY_300__PHY_NTP_PERIOD_THRESHOLD_MAX_1_MASK  0x03FF0000U
#define LPDDR4__DENALI_PHY_300__PHY_NTP_PERIOD_THRESHOLD_MAX_1_SHIFT         16U
#define LPDDR4__DENALI_PHY_300__PHY_NTP_PERIOD_THRESHOLD_MAX_1_WIDTH         10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MAX_1__REG DENALI_PHY_300
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MAX_1__FLD LPDDR4__DENALI_PHY_300__PHY_NTP_PERIOD_THRESHOLD_MAX_1

#define LPDDR4__DENALI_PHY_301_READ_MASK                             0x00FF0001U
#define LPDDR4__DENALI_PHY_301_WRITE_MASK                            0x00FF0001U
#define LPDDR4__DENALI_PHY_301__PHY_CALVL_VREF_DRIVING_SLICE_1_MASK  0x00000001U
#define LPDDR4__DENALI_PHY_301__PHY_CALVL_VREF_DRIVING_SLICE_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_301__PHY_CALVL_VREF_DRIVING_SLICE_1_WIDTH          1U
#define LPDDR4__DENALI_PHY_301__PHY_CALVL_VREF_DRIVING_SLICE_1_WOCLR          0U
#define LPDDR4__DENALI_PHY_301__PHY_CALVL_VREF_DRIVING_SLICE_1_WOSET          0U
#define LPDDR4__PHY_CALVL_VREF_DRIVING_SLICE_1__REG DENALI_PHY_301
#define LPDDR4__PHY_CALVL_VREF_DRIVING_SLICE_1__FLD LPDDR4__DENALI_PHY_301__PHY_CALVL_VREF_DRIVING_SLICE_1

#define LPDDR4__DENALI_PHY_301__SC_PHY_MANUAL_CLEAR_1_MASK           0x00003F00U
#define LPDDR4__DENALI_PHY_301__SC_PHY_MANUAL_CLEAR_1_SHIFT                   8U
#define LPDDR4__DENALI_PHY_301__SC_PHY_MANUAL_CLEAR_1_WIDTH                   6U
#define LPDDR4__SC_PHY_MANUAL_CLEAR_1__REG DENALI_PHY_301
#define LPDDR4__SC_PHY_MANUAL_CLEAR_1__FLD LPDDR4__DENALI_PHY_301__SC_PHY_MANUAL_CLEAR_1

#define LPDDR4__DENALI_PHY_301__PHY_FIFO_PTR_OBS_1_MASK              0x00FF0000U
#define LPDDR4__DENALI_PHY_301__PHY_FIFO_PTR_OBS_1_SHIFT                     16U
#define LPDDR4__DENALI_PHY_301__PHY_FIFO_PTR_OBS_1_WIDTH                      8U
#define LPDDR4__PHY_FIFO_PTR_OBS_1__REG DENALI_PHY_301
#define LPDDR4__PHY_FIFO_PTR_OBS_1__FLD LPDDR4__DENALI_PHY_301__PHY_FIFO_PTR_OBS_1

#define LPDDR4__DENALI_PHY_302_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_302_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_302__PHY_LPBK_RESULT_OBS_1_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_302__PHY_LPBK_RESULT_OBS_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_302__PHY_LPBK_RESULT_OBS_1_WIDTH                  32U
#define LPDDR4__PHY_LPBK_RESULT_OBS_1__REG DENALI_PHY_302
#define LPDDR4__PHY_LPBK_RESULT_OBS_1__FLD LPDDR4__DENALI_PHY_302__PHY_LPBK_RESULT_OBS_1

#define LPDDR4__DENALI_PHY_303_READ_MASK                             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_303_WRITE_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_303__PHY_LPBK_ERROR_COUNT_OBS_1_MASK      0x0000FFFFU
#define LPDDR4__DENALI_PHY_303__PHY_LPBK_ERROR_COUNT_OBS_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_303__PHY_LPBK_ERROR_COUNT_OBS_1_WIDTH             16U
#define LPDDR4__PHY_LPBK_ERROR_COUNT_OBS_1__REG DENALI_PHY_303
#define LPDDR4__PHY_LPBK_ERROR_COUNT_OBS_1__FLD LPDDR4__DENALI_PHY_303__PHY_LPBK_ERROR_COUNT_OBS_1

#define LPDDR4__DENALI_PHY_303__PHY_MASTER_DLY_LOCK_OBS_1_MASK       0x07FF0000U
#define LPDDR4__DENALI_PHY_303__PHY_MASTER_DLY_LOCK_OBS_1_SHIFT              16U
#define LPDDR4__DENALI_PHY_303__PHY_MASTER_DLY_LOCK_OBS_1_WIDTH              11U
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_1__REG DENALI_PHY_303
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_1__FLD LPDDR4__DENALI_PHY_303__PHY_MASTER_DLY_LOCK_OBS_1

#define LPDDR4__DENALI_PHY_304_READ_MASK                             0xFFFF7F7FU
#define LPDDR4__DENALI_PHY_304_WRITE_MASK                            0xFFFF7F7FU
#define LPDDR4__DENALI_PHY_304__PHY_RDDQ_SLV_DLY_ENC_OBS_1_MASK      0x0000007FU
#define LPDDR4__DENALI_PHY_304__PHY_RDDQ_SLV_DLY_ENC_OBS_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_304__PHY_RDDQ_SLV_DLY_ENC_OBS_1_WIDTH              7U
#define LPDDR4__PHY_RDDQ_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_304
#define LPDDR4__PHY_RDDQ_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_304__PHY_RDDQ_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_304__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_1_MASK 0x00007F00U
#define LPDDR4__DENALI_PHY_304__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_1_SHIFT        8U
#define LPDDR4__DENALI_PHY_304__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_1_WIDTH        7U
#define LPDDR4__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_304
#define LPDDR4__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_304__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_304__PHY_MEAS_DLY_STEP_VALUE_1_MASK       0x00FF0000U
#define LPDDR4__DENALI_PHY_304__PHY_MEAS_DLY_STEP_VALUE_1_SHIFT              16U
#define LPDDR4__DENALI_PHY_304__PHY_MEAS_DLY_STEP_VALUE_1_WIDTH               8U
#define LPDDR4__PHY_MEAS_DLY_STEP_VALUE_1__REG DENALI_PHY_304
#define LPDDR4__PHY_MEAS_DLY_STEP_VALUE_1__FLD LPDDR4__DENALI_PHY_304__PHY_MEAS_DLY_STEP_VALUE_1

#define LPDDR4__DENALI_PHY_304__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_1_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_304__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_1_SHIFT 24U
#define LPDDR4__DENALI_PHY_304__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_1_WIDTH 8U
#define LPDDR4__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_304
#define LPDDR4__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_304__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_305_READ_MASK                             0x7F07FFFFU
#define LPDDR4__DENALI_PHY_305_WRITE_MASK                            0x7F07FFFFU
#define LPDDR4__DENALI_PHY_305__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_1_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_305__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_1_SHIFT 0U
#define LPDDR4__DENALI_PHY_305__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_1_WIDTH 8U
#define LPDDR4__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_305
#define LPDDR4__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_305__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_305__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_1_MASK 0x0007FF00U
#define LPDDR4__DENALI_PHY_305__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_1_SHIFT        8U
#define LPDDR4__DENALI_PHY_305__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_1_WIDTH       11U
#define LPDDR4__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_305
#define LPDDR4__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_305__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_305__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_1_MASK 0x7F000000U
#define LPDDR4__DENALI_PHY_305__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_1_SHIFT       24U
#define LPDDR4__DENALI_PHY_305__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_1_WIDTH        7U
#define LPDDR4__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_305
#define LPDDR4__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_305__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_306_READ_MASK                             0x0007FFFFU
#define LPDDR4__DENALI_PHY_306_WRITE_MASK                            0x0007FFFFU
#define LPDDR4__DENALI_PHY_306__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_1_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_306__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_306__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_1_WIDTH         8U
#define LPDDR4__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_306
#define LPDDR4__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_306__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_306__PHY_WR_ADDER_SLV_DLY_ENC_OBS_1_MASK  0x0000FF00U
#define LPDDR4__DENALI_PHY_306__PHY_WR_ADDER_SLV_DLY_ENC_OBS_1_SHIFT          8U
#define LPDDR4__DENALI_PHY_306__PHY_WR_ADDER_SLV_DLY_ENC_OBS_1_WIDTH          8U
#define LPDDR4__PHY_WR_ADDER_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_306
#define LPDDR4__PHY_WR_ADDER_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_306__PHY_WR_ADDER_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_306__PHY_WR_SHIFT_OBS_1_MASK              0x00070000U
#define LPDDR4__DENALI_PHY_306__PHY_WR_SHIFT_OBS_1_SHIFT                     16U
#define LPDDR4__DENALI_PHY_306__PHY_WR_SHIFT_OBS_1_WIDTH                      3U
#define LPDDR4__PHY_WR_SHIFT_OBS_1__REG DENALI_PHY_306
#define LPDDR4__PHY_WR_SHIFT_OBS_1__FLD LPDDR4__DENALI_PHY_306__PHY_WR_SHIFT_OBS_1

#define LPDDR4__DENALI_PHY_307_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_307_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_307__PHY_WRLVL_HARD0_DELAY_OBS_1_MASK     0x000003FFU
#define LPDDR4__DENALI_PHY_307__PHY_WRLVL_HARD0_DELAY_OBS_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_307__PHY_WRLVL_HARD0_DELAY_OBS_1_WIDTH            10U
#define LPDDR4__PHY_WRLVL_HARD0_DELAY_OBS_1__REG DENALI_PHY_307
#define LPDDR4__PHY_WRLVL_HARD0_DELAY_OBS_1__FLD LPDDR4__DENALI_PHY_307__PHY_WRLVL_HARD0_DELAY_OBS_1

#define LPDDR4__DENALI_PHY_307__PHY_WRLVL_HARD1_DELAY_OBS_1_MASK     0x03FF0000U
#define LPDDR4__DENALI_PHY_307__PHY_WRLVL_HARD1_DELAY_OBS_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_307__PHY_WRLVL_HARD1_DELAY_OBS_1_WIDTH            10U
#define LPDDR4__PHY_WRLVL_HARD1_DELAY_OBS_1__REG DENALI_PHY_307
#define LPDDR4__PHY_WRLVL_HARD1_DELAY_OBS_1__FLD LPDDR4__DENALI_PHY_307__PHY_WRLVL_HARD1_DELAY_OBS_1

#define LPDDR4__DENALI_PHY_308_READ_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_PHY_308_WRITE_MASK                            0x001FFFFFU
#define LPDDR4__DENALI_PHY_308__PHY_WRLVL_STATUS_OBS_1_MASK          0x001FFFFFU
#define LPDDR4__DENALI_PHY_308__PHY_WRLVL_STATUS_OBS_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_308__PHY_WRLVL_STATUS_OBS_1_WIDTH                 21U
#define LPDDR4__PHY_WRLVL_STATUS_OBS_1__REG DENALI_PHY_308
#define LPDDR4__PHY_WRLVL_STATUS_OBS_1__FLD LPDDR4__DENALI_PHY_308__PHY_WRLVL_STATUS_OBS_1

#define LPDDR4__DENALI_PHY_309_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_309_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_309__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_309__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_309__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_1_WIDTH       10U
#define LPDDR4__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_309
#define LPDDR4__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_309__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_309__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_309__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_309__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_1_WIDTH       10U
#define LPDDR4__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_1__REG DENALI_PHY_309
#define LPDDR4__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_1__FLD LPDDR4__DENALI_PHY_309__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_1

#define LPDDR4__DENALI_PHY_310_READ_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_310_WRITE_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_310__PHY_WRLVL_ERROR_OBS_1_MASK           0x0000FFFFU
#define LPDDR4__DENALI_PHY_310__PHY_WRLVL_ERROR_OBS_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_310__PHY_WRLVL_ERROR_OBS_1_WIDTH                  16U
#define LPDDR4__PHY_WRLVL_ERROR_OBS_1__REG DENALI_PHY_310
#define LPDDR4__PHY_WRLVL_ERROR_OBS_1__FLD LPDDR4__DENALI_PHY_310__PHY_WRLVL_ERROR_OBS_1

#define LPDDR4__DENALI_PHY_310__PHY_GTLVL_HARD0_DELAY_OBS_1_MASK     0x3FFF0000U
#define LPDDR4__DENALI_PHY_310__PHY_GTLVL_HARD0_DELAY_OBS_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_310__PHY_GTLVL_HARD0_DELAY_OBS_1_WIDTH            14U
#define LPDDR4__PHY_GTLVL_HARD0_DELAY_OBS_1__REG DENALI_PHY_310
#define LPDDR4__PHY_GTLVL_HARD0_DELAY_OBS_1__FLD LPDDR4__DENALI_PHY_310__PHY_GTLVL_HARD0_DELAY_OBS_1

#define LPDDR4__DENALI_PHY_311_READ_MASK                             0x00003FFFU
#define LPDDR4__DENALI_PHY_311_WRITE_MASK                            0x00003FFFU
#define LPDDR4__DENALI_PHY_311__PHY_GTLVL_HARD1_DELAY_OBS_1_MASK     0x00003FFFU
#define LPDDR4__DENALI_PHY_311__PHY_GTLVL_HARD1_DELAY_OBS_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_311__PHY_GTLVL_HARD1_DELAY_OBS_1_WIDTH            14U
#define LPDDR4__PHY_GTLVL_HARD1_DELAY_OBS_1__REG DENALI_PHY_311
#define LPDDR4__PHY_GTLVL_HARD1_DELAY_OBS_1__FLD LPDDR4__DENALI_PHY_311__PHY_GTLVL_HARD1_DELAY_OBS_1

#define LPDDR4__DENALI_PHY_312_READ_MASK                             0x0003FFFFU
#define LPDDR4__DENALI_PHY_312_WRITE_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_312__PHY_GTLVL_STATUS_OBS_1_MASK          0x0003FFFFU
#define LPDDR4__DENALI_PHY_312__PHY_GTLVL_STATUS_OBS_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_312__PHY_GTLVL_STATUS_OBS_1_WIDTH                 18U
#define LPDDR4__PHY_GTLVL_STATUS_OBS_1__REG DENALI_PHY_312
#define LPDDR4__PHY_GTLVL_STATUS_OBS_1__FLD LPDDR4__DENALI_PHY_312__PHY_GTLVL_STATUS_OBS_1

#define LPDDR4__DENALI_PHY_313_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_313_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_313__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_313__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_313__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_1_WIDTH        10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_1__REG DENALI_PHY_313
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_1__FLD LPDDR4__DENALI_PHY_313__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_1

#define LPDDR4__DENALI_PHY_313__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_313__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_313__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_1_WIDTH        10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_1__REG DENALI_PHY_313
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_1__FLD LPDDR4__DENALI_PHY_313__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_1

#define LPDDR4__DENALI_PHY_314_READ_MASK                             0x00000003U
#define LPDDR4__DENALI_PHY_314_WRITE_MASK                            0x00000003U
#define LPDDR4__DENALI_PHY_314__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_1_MASK 0x00000003U
#define LPDDR4__DENALI_PHY_314__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_1_SHIFT    0U
#define LPDDR4__DENALI_PHY_314__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_1_WIDTH    2U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_1__REG DENALI_PHY_314
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_1__FLD LPDDR4__DENALI_PHY_314__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_1

#define LPDDR4__DENALI_PHY_315_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_315_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_315__PHY_RDLVL_STATUS_OBS_1_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_315__PHY_RDLVL_STATUS_OBS_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_315__PHY_RDLVL_STATUS_OBS_1_WIDTH                 32U
#define LPDDR4__PHY_RDLVL_STATUS_OBS_1__REG DENALI_PHY_315
#define LPDDR4__PHY_RDLVL_STATUS_OBS_1__FLD LPDDR4__DENALI_PHY_315__PHY_RDLVL_STATUS_OBS_1

#define LPDDR4__DENALI_PHY_316_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_316_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_316__PHY_WDQLVL_DQDM_LE_DLY_OBS_1_MASK    0x000007FFU
#define LPDDR4__DENALI_PHY_316__PHY_WDQLVL_DQDM_LE_DLY_OBS_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_316__PHY_WDQLVL_DQDM_LE_DLY_OBS_1_WIDTH           11U
#define LPDDR4__PHY_WDQLVL_DQDM_LE_DLY_OBS_1__REG DENALI_PHY_316
#define LPDDR4__PHY_WDQLVL_DQDM_LE_DLY_OBS_1__FLD LPDDR4__DENALI_PHY_316__PHY_WDQLVL_DQDM_LE_DLY_OBS_1

#define LPDDR4__DENALI_PHY_316__PHY_WDQLVL_DQDM_TE_DLY_OBS_1_MASK    0x07FF0000U
#define LPDDR4__DENALI_PHY_316__PHY_WDQLVL_DQDM_TE_DLY_OBS_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_316__PHY_WDQLVL_DQDM_TE_DLY_OBS_1_WIDTH           11U
#define LPDDR4__PHY_WDQLVL_DQDM_TE_DLY_OBS_1__REG DENALI_PHY_316
#define LPDDR4__PHY_WDQLVL_DQDM_TE_DLY_OBS_1__FLD LPDDR4__DENALI_PHY_316__PHY_WDQLVL_DQDM_TE_DLY_OBS_1

#define LPDDR4__DENALI_PHY_317_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_317_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_317__PHY_WDQLVL_STATUS_OBS_1_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_317__PHY_WDQLVL_STATUS_OBS_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_317__PHY_WDQLVL_STATUS_OBS_1_WIDTH                32U
#define LPDDR4__PHY_WDQLVL_STATUS_OBS_1__REG DENALI_PHY_317
#define LPDDR4__PHY_WDQLVL_STATUS_OBS_1__FLD LPDDR4__DENALI_PHY_317__PHY_WDQLVL_STATUS_OBS_1

#define LPDDR4__DENALI_PHY_318_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_318_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_318__PHY_WDQLVL_PERIODIC_OBS_1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_318__PHY_WDQLVL_PERIODIC_OBS_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_318__PHY_WDQLVL_PERIODIC_OBS_1_WIDTH              32U
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_1__REG DENALI_PHY_318
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_1__FLD LPDDR4__DENALI_PHY_318__PHY_WDQLVL_PERIODIC_OBS_1

#define LPDDR4__DENALI_PHY_319_READ_MASK                             0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_319_WRITE_MASK                            0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_319__PHY_DDL_MODE_1_MASK                  0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_319__PHY_DDL_MODE_1_SHIFT                          0U
#define LPDDR4__DENALI_PHY_319__PHY_DDL_MODE_1_WIDTH                         31U
#define LPDDR4__PHY_DDL_MODE_1__REG DENALI_PHY_319
#define LPDDR4__PHY_DDL_MODE_1__FLD LPDDR4__DENALI_PHY_319__PHY_DDL_MODE_1

#define LPDDR4__DENALI_PHY_320_READ_MASK                             0x0000003FU
#define LPDDR4__DENALI_PHY_320_WRITE_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_320__PHY_DDL_MASK_1_MASK                  0x0000003FU
#define LPDDR4__DENALI_PHY_320__PHY_DDL_MASK_1_SHIFT                          0U
#define LPDDR4__DENALI_PHY_320__PHY_DDL_MASK_1_WIDTH                          6U
#define LPDDR4__PHY_DDL_MASK_1__REG DENALI_PHY_320
#define LPDDR4__PHY_DDL_MASK_1__FLD LPDDR4__DENALI_PHY_320__PHY_DDL_MASK_1

#define LPDDR4__DENALI_PHY_321_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_321_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_321__PHY_DDL_TEST_OBS_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_321__PHY_DDL_TEST_OBS_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_321__PHY_DDL_TEST_OBS_1_WIDTH                     32U
#define LPDDR4__PHY_DDL_TEST_OBS_1__REG DENALI_PHY_321
#define LPDDR4__PHY_DDL_TEST_OBS_1__FLD LPDDR4__DENALI_PHY_321__PHY_DDL_TEST_OBS_1

#define LPDDR4__DENALI_PHY_322_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_322_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_322__PHY_DDL_TEST_MSTR_DLY_OBS_1_MASK     0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_322__PHY_DDL_TEST_MSTR_DLY_OBS_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_322__PHY_DDL_TEST_MSTR_DLY_OBS_1_WIDTH            32U
#define LPDDR4__PHY_DDL_TEST_MSTR_DLY_OBS_1__REG DENALI_PHY_322
#define LPDDR4__PHY_DDL_TEST_MSTR_DLY_OBS_1__FLD LPDDR4__DENALI_PHY_322__PHY_DDL_TEST_MSTR_DLY_OBS_1

#define LPDDR4__DENALI_PHY_323_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_323_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_323__PHY_DDL_TRACK_UPD_THRESHOLD_1_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_323__PHY_DDL_TRACK_UPD_THRESHOLD_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_323__PHY_DDL_TRACK_UPD_THRESHOLD_1_WIDTH           8U
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_1__REG DENALI_PHY_323
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_1__FLD LPDDR4__DENALI_PHY_323__PHY_DDL_TRACK_UPD_THRESHOLD_1

#define LPDDR4__DENALI_PHY_323__PHY_LP4_WDQS_OE_EXTEND_1_MASK        0x00000100U
#define LPDDR4__DENALI_PHY_323__PHY_LP4_WDQS_OE_EXTEND_1_SHIFT                8U
#define LPDDR4__DENALI_PHY_323__PHY_LP4_WDQS_OE_EXTEND_1_WIDTH                1U
#define LPDDR4__DENALI_PHY_323__PHY_LP4_WDQS_OE_EXTEND_1_WOCLR                0U
#define LPDDR4__DENALI_PHY_323__PHY_LP4_WDQS_OE_EXTEND_1_WOSET                0U
#define LPDDR4__PHY_LP4_WDQS_OE_EXTEND_1__REG DENALI_PHY_323
#define LPDDR4__PHY_LP4_WDQS_OE_EXTEND_1__FLD LPDDR4__DENALI_PHY_323__PHY_LP4_WDQS_OE_EXTEND_1

#define LPDDR4__DENALI_PHY_323__PHY_RX_CAL_DQ0_1_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_323__PHY_RX_CAL_DQ0_1_SHIFT                       16U
#define LPDDR4__DENALI_PHY_323__PHY_RX_CAL_DQ0_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ0_1__REG DENALI_PHY_323
#define LPDDR4__PHY_RX_CAL_DQ0_1__FLD LPDDR4__DENALI_PHY_323__PHY_RX_CAL_DQ0_1

#define LPDDR4__DENALI_PHY_324_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_324_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_324__PHY_RX_CAL_DQ1_1_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_324__PHY_RX_CAL_DQ1_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_324__PHY_RX_CAL_DQ1_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ1_1__REG DENALI_PHY_324
#define LPDDR4__PHY_RX_CAL_DQ1_1__FLD LPDDR4__DENALI_PHY_324__PHY_RX_CAL_DQ1_1

#define LPDDR4__DENALI_PHY_324__PHY_RX_CAL_DQ2_1_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_324__PHY_RX_CAL_DQ2_1_SHIFT                       16U
#define LPDDR4__DENALI_PHY_324__PHY_RX_CAL_DQ2_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ2_1__REG DENALI_PHY_324
#define LPDDR4__PHY_RX_CAL_DQ2_1__FLD LPDDR4__DENALI_PHY_324__PHY_RX_CAL_DQ2_1

#define LPDDR4__DENALI_PHY_325_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_325_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_325__PHY_RX_CAL_DQ3_1_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_325__PHY_RX_CAL_DQ3_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_325__PHY_RX_CAL_DQ3_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ3_1__REG DENALI_PHY_325
#define LPDDR4__PHY_RX_CAL_DQ3_1__FLD LPDDR4__DENALI_PHY_325__PHY_RX_CAL_DQ3_1

#define LPDDR4__DENALI_PHY_325__PHY_RX_CAL_DQ4_1_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_325__PHY_RX_CAL_DQ4_1_SHIFT                       16U
#define LPDDR4__DENALI_PHY_325__PHY_RX_CAL_DQ4_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ4_1__REG DENALI_PHY_325
#define LPDDR4__PHY_RX_CAL_DQ4_1__FLD LPDDR4__DENALI_PHY_325__PHY_RX_CAL_DQ4_1

#define LPDDR4__DENALI_PHY_326_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_326_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_326__PHY_RX_CAL_DQ5_1_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_326__PHY_RX_CAL_DQ5_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_326__PHY_RX_CAL_DQ5_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ5_1__REG DENALI_PHY_326
#define LPDDR4__PHY_RX_CAL_DQ5_1__FLD LPDDR4__DENALI_PHY_326__PHY_RX_CAL_DQ5_1

#define LPDDR4__DENALI_PHY_326__PHY_RX_CAL_DQ6_1_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_326__PHY_RX_CAL_DQ6_1_SHIFT                       16U
#define LPDDR4__DENALI_PHY_326__PHY_RX_CAL_DQ6_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ6_1__REG DENALI_PHY_326
#define LPDDR4__PHY_RX_CAL_DQ6_1__FLD LPDDR4__DENALI_PHY_326__PHY_RX_CAL_DQ6_1

#define LPDDR4__DENALI_PHY_327_READ_MASK                             0x000001FFU
#define LPDDR4__DENALI_PHY_327_WRITE_MASK                            0x000001FFU
#define LPDDR4__DENALI_PHY_327__PHY_RX_CAL_DQ7_1_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_327__PHY_RX_CAL_DQ7_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_327__PHY_RX_CAL_DQ7_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ7_1__REG DENALI_PHY_327
#define LPDDR4__PHY_RX_CAL_DQ7_1__FLD LPDDR4__DENALI_PHY_327__PHY_RX_CAL_DQ7_1

#define LPDDR4__DENALI_PHY_328_READ_MASK                             0x0003FFFFU
#define LPDDR4__DENALI_PHY_328_WRITE_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_328__PHY_RX_CAL_DM_1_MASK                 0x0003FFFFU
#define LPDDR4__DENALI_PHY_328__PHY_RX_CAL_DM_1_SHIFT                         0U
#define LPDDR4__DENALI_PHY_328__PHY_RX_CAL_DM_1_WIDTH                        18U
#define LPDDR4__PHY_RX_CAL_DM_1__REG DENALI_PHY_328
#define LPDDR4__PHY_RX_CAL_DM_1__FLD LPDDR4__DENALI_PHY_328__PHY_RX_CAL_DM_1

#define LPDDR4__DENALI_PHY_329_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_329_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_329__PHY_RX_CAL_DQS_1_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_329__PHY_RX_CAL_DQS_1_SHIFT                        0U
#define LPDDR4__DENALI_PHY_329__PHY_RX_CAL_DQS_1_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQS_1__REG DENALI_PHY_329
#define LPDDR4__PHY_RX_CAL_DQS_1__FLD LPDDR4__DENALI_PHY_329__PHY_RX_CAL_DQS_1

#define LPDDR4__DENALI_PHY_329__PHY_RX_CAL_FDBK_1_MASK               0x01FF0000U
#define LPDDR4__DENALI_PHY_329__PHY_RX_CAL_FDBK_1_SHIFT                      16U
#define LPDDR4__DENALI_PHY_329__PHY_RX_CAL_FDBK_1_WIDTH                       9U
#define LPDDR4__PHY_RX_CAL_FDBK_1__REG DENALI_PHY_329
#define LPDDR4__PHY_RX_CAL_FDBK_1__FLD LPDDR4__DENALI_PHY_329__PHY_RX_CAL_FDBK_1

#define LPDDR4__DENALI_PHY_330_READ_MASK                             0xFF1F07FFU
#define LPDDR4__DENALI_PHY_330_WRITE_MASK                            0xFF1F07FFU
#define LPDDR4__DENALI_PHY_330__PHY_PAD_RX_BIAS_EN_1_MASK            0x000007FFU
#define LPDDR4__DENALI_PHY_330__PHY_PAD_RX_BIAS_EN_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_330__PHY_PAD_RX_BIAS_EN_1_WIDTH                   11U
#define LPDDR4__PHY_PAD_RX_BIAS_EN_1__REG DENALI_PHY_330
#define LPDDR4__PHY_PAD_RX_BIAS_EN_1__FLD LPDDR4__DENALI_PHY_330__PHY_PAD_RX_BIAS_EN_1

#define LPDDR4__DENALI_PHY_330__PHY_STATIC_TOG_DISABLE_1_MASK        0x001F0000U
#define LPDDR4__DENALI_PHY_330__PHY_STATIC_TOG_DISABLE_1_SHIFT               16U
#define LPDDR4__DENALI_PHY_330__PHY_STATIC_TOG_DISABLE_1_WIDTH                5U
#define LPDDR4__PHY_STATIC_TOG_DISABLE_1__REG DENALI_PHY_330
#define LPDDR4__PHY_STATIC_TOG_DISABLE_1__FLD LPDDR4__DENALI_PHY_330__PHY_STATIC_TOG_DISABLE_1

#define LPDDR4__DENALI_PHY_330__PHY_DATA_DC_CAL_SAMPLE_WAIT_1_MASK   0xFF000000U
#define LPDDR4__DENALI_PHY_330__PHY_DATA_DC_CAL_SAMPLE_WAIT_1_SHIFT          24U
#define LPDDR4__DENALI_PHY_330__PHY_DATA_DC_CAL_SAMPLE_WAIT_1_WIDTH           8U
#define LPDDR4__PHY_DATA_DC_CAL_SAMPLE_WAIT_1__REG DENALI_PHY_330
#define LPDDR4__PHY_DATA_DC_CAL_SAMPLE_WAIT_1__FLD LPDDR4__DENALI_PHY_330__PHY_DATA_DC_CAL_SAMPLE_WAIT_1

#define LPDDR4__DENALI_PHY_331_READ_MASK                             0xFF3F03FFU
#define LPDDR4__DENALI_PHY_331_WRITE_MASK                            0xFF3F03FFU
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_CAL_TIMEOUT_1_MASK       0x000000FFU
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_CAL_TIMEOUT_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_CAL_TIMEOUT_1_WIDTH               8U
#define LPDDR4__PHY_DATA_DC_CAL_TIMEOUT_1__REG DENALI_PHY_331
#define LPDDR4__PHY_DATA_DC_CAL_TIMEOUT_1__FLD LPDDR4__DENALI_PHY_331__PHY_DATA_DC_CAL_TIMEOUT_1

#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_WEIGHT_1_MASK            0x00000300U
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_WEIGHT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_WEIGHT_1_WIDTH                    2U
#define LPDDR4__PHY_DATA_DC_WEIGHT_1__REG DENALI_PHY_331
#define LPDDR4__PHY_DATA_DC_WEIGHT_1__FLD LPDDR4__DENALI_PHY_331__PHY_DATA_DC_WEIGHT_1

#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_ADJUST_START_1_MASK      0x003F0000U
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_ADJUST_START_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_ADJUST_START_1_WIDTH              6U
#define LPDDR4__PHY_DATA_DC_ADJUST_START_1__REG DENALI_PHY_331
#define LPDDR4__PHY_DATA_DC_ADJUST_START_1__FLD LPDDR4__DENALI_PHY_331__PHY_DATA_DC_ADJUST_START_1

#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_ADJUST_SAMPLE_CNT_1_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_ADJUST_SAMPLE_CNT_1_SHIFT        24U
#define LPDDR4__DENALI_PHY_331__PHY_DATA_DC_ADJUST_SAMPLE_CNT_1_WIDTH         8U
#define LPDDR4__PHY_DATA_DC_ADJUST_SAMPLE_CNT_1__REG DENALI_PHY_331
#define LPDDR4__PHY_DATA_DC_ADJUST_SAMPLE_CNT_1__FLD LPDDR4__DENALI_PHY_331__PHY_DATA_DC_ADJUST_SAMPLE_CNT_1

#define LPDDR4__DENALI_PHY_332_READ_MASK                             0x010101FFU
#define LPDDR4__DENALI_PHY_332_WRITE_MASK                            0x010101FFU
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_THRSHLD_1_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_THRSHLD_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_THRSHLD_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_ADJUST_THRSHLD_1__REG DENALI_PHY_332
#define LPDDR4__PHY_DATA_DC_ADJUST_THRSHLD_1__FLD LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_THRSHLD_1

#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_DIRECT_1_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_DIRECT_1_SHIFT             8U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_DIRECT_1_WIDTH             1U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_DIRECT_1_WOCLR             0U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_DIRECT_1_WOSET             0U
#define LPDDR4__PHY_DATA_DC_ADJUST_DIRECT_1__REG DENALI_PHY_332
#define LPDDR4__PHY_DATA_DC_ADJUST_DIRECT_1__FLD LPDDR4__DENALI_PHY_332__PHY_DATA_DC_ADJUST_DIRECT_1

#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_POLARITY_1_MASK      0x00010000U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_POLARITY_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_POLARITY_1_WIDTH              1U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_POLARITY_1_WOCLR              0U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_POLARITY_1_WOSET              0U
#define LPDDR4__PHY_DATA_DC_CAL_POLARITY_1__REG DENALI_PHY_332
#define LPDDR4__PHY_DATA_DC_CAL_POLARITY_1__FLD LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_POLARITY_1

#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_START_1_MASK         0x01000000U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_START_1_SHIFT                24U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_START_1_WIDTH                 1U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_START_1_WOCLR                 0U
#define LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_START_1_WOSET                 0U
#define LPDDR4__PHY_DATA_DC_CAL_START_1__REG DENALI_PHY_332
#define LPDDR4__PHY_DATA_DC_CAL_START_1__FLD LPDDR4__DENALI_PHY_332__PHY_DATA_DC_CAL_START_1

#define LPDDR4__DENALI_PHY_333_READ_MASK                             0x01010703U
#define LPDDR4__DENALI_PHY_333_WRITE_MASK                            0x01010703U
#define LPDDR4__DENALI_PHY_333__PHY_DATA_DC_SW_RANK_1_MASK           0x00000003U
#define LPDDR4__DENALI_PHY_333__PHY_DATA_DC_SW_RANK_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_333__PHY_DATA_DC_SW_RANK_1_WIDTH                   2U
#define LPDDR4__PHY_DATA_DC_SW_RANK_1__REG DENALI_PHY_333
#define LPDDR4__PHY_DATA_DC_SW_RANK_1__FLD LPDDR4__DENALI_PHY_333__PHY_DATA_DC_SW_RANK_1

#define LPDDR4__DENALI_PHY_333__PHY_FDBK_PWR_CTRL_1_MASK             0x00000700U
#define LPDDR4__DENALI_PHY_333__PHY_FDBK_PWR_CTRL_1_SHIFT                     8U
#define LPDDR4__DENALI_PHY_333__PHY_FDBK_PWR_CTRL_1_WIDTH                     3U
#define LPDDR4__PHY_FDBK_PWR_CTRL_1__REG DENALI_PHY_333
#define LPDDR4__PHY_FDBK_PWR_CTRL_1__FLD LPDDR4__DENALI_PHY_333__PHY_FDBK_PWR_CTRL_1

#define LPDDR4__DENALI_PHY_333__PHY_SLV_DLY_CTRL_GATE_DISABLE_1_MASK 0x00010000U
#define LPDDR4__DENALI_PHY_333__PHY_SLV_DLY_CTRL_GATE_DISABLE_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_333__PHY_SLV_DLY_CTRL_GATE_DISABLE_1_WIDTH         1U
#define LPDDR4__DENALI_PHY_333__PHY_SLV_DLY_CTRL_GATE_DISABLE_1_WOCLR         0U
#define LPDDR4__DENALI_PHY_333__PHY_SLV_DLY_CTRL_GATE_DISABLE_1_WOSET         0U
#define LPDDR4__PHY_SLV_DLY_CTRL_GATE_DISABLE_1__REG DENALI_PHY_333
#define LPDDR4__PHY_SLV_DLY_CTRL_GATE_DISABLE_1__FLD LPDDR4__DENALI_PHY_333__PHY_SLV_DLY_CTRL_GATE_DISABLE_1

#define LPDDR4__DENALI_PHY_333__PHY_RDPATH_GATE_DISABLE_1_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_333__PHY_RDPATH_GATE_DISABLE_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_333__PHY_RDPATH_GATE_DISABLE_1_WIDTH               1U
#define LPDDR4__DENALI_PHY_333__PHY_RDPATH_GATE_DISABLE_1_WOCLR               0U
#define LPDDR4__DENALI_PHY_333__PHY_RDPATH_GATE_DISABLE_1_WOSET               0U
#define LPDDR4__PHY_RDPATH_GATE_DISABLE_1__REG DENALI_PHY_333
#define LPDDR4__PHY_RDPATH_GATE_DISABLE_1__FLD LPDDR4__DENALI_PHY_333__PHY_RDPATH_GATE_DISABLE_1

#define LPDDR4__DENALI_PHY_334_READ_MASK                             0x00000101U
#define LPDDR4__DENALI_PHY_334_WRITE_MASK                            0x00000101U
#define LPDDR4__DENALI_PHY_334__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_1_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_334__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_1_SHIFT       0U
#define LPDDR4__DENALI_PHY_334__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_1_WIDTH       1U
#define LPDDR4__DENALI_PHY_334__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_1_WOCLR       0U
#define LPDDR4__DENALI_PHY_334__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_1_WOSET       0U
#define LPDDR4__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_1__REG DENALI_PHY_334
#define LPDDR4__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_1__FLD LPDDR4__DENALI_PHY_334__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_1

#define LPDDR4__DENALI_PHY_334__PHY_SLICE_PWR_RDC_DISABLE_1_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_334__PHY_SLICE_PWR_RDC_DISABLE_1_SHIFT             8U
#define LPDDR4__DENALI_PHY_334__PHY_SLICE_PWR_RDC_DISABLE_1_WIDTH             1U
#define LPDDR4__DENALI_PHY_334__PHY_SLICE_PWR_RDC_DISABLE_1_WOCLR             0U
#define LPDDR4__DENALI_PHY_334__PHY_SLICE_PWR_RDC_DISABLE_1_WOSET             0U
#define LPDDR4__PHY_SLICE_PWR_RDC_DISABLE_1__REG DENALI_PHY_334
#define LPDDR4__PHY_SLICE_PWR_RDC_DISABLE_1__FLD LPDDR4__DENALI_PHY_334__PHY_SLICE_PWR_RDC_DISABLE_1

#define LPDDR4__DENALI_PHY_335_READ_MASK                             0x07FFFF07U
#define LPDDR4__DENALI_PHY_335_WRITE_MASK                            0x07FFFF07U
#define LPDDR4__DENALI_PHY_335__PHY_DQ_TSEL_ENABLE_1_MASK            0x00000007U
#define LPDDR4__DENALI_PHY_335__PHY_DQ_TSEL_ENABLE_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_335__PHY_DQ_TSEL_ENABLE_1_WIDTH                    3U
#define LPDDR4__PHY_DQ_TSEL_ENABLE_1__REG DENALI_PHY_335
#define LPDDR4__PHY_DQ_TSEL_ENABLE_1__FLD LPDDR4__DENALI_PHY_335__PHY_DQ_TSEL_ENABLE_1

#define LPDDR4__DENALI_PHY_335__PHY_DQ_TSEL_SELECT_1_MASK            0x00FFFF00U
#define LPDDR4__DENALI_PHY_335__PHY_DQ_TSEL_SELECT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_335__PHY_DQ_TSEL_SELECT_1_WIDTH                   16U
#define LPDDR4__PHY_DQ_TSEL_SELECT_1__REG DENALI_PHY_335
#define LPDDR4__PHY_DQ_TSEL_SELECT_1__FLD LPDDR4__DENALI_PHY_335__PHY_DQ_TSEL_SELECT_1

#define LPDDR4__DENALI_PHY_335__PHY_DQS_TSEL_ENABLE_1_MASK           0x07000000U
#define LPDDR4__DENALI_PHY_335__PHY_DQS_TSEL_ENABLE_1_SHIFT                  24U
#define LPDDR4__DENALI_PHY_335__PHY_DQS_TSEL_ENABLE_1_WIDTH                   3U
#define LPDDR4__PHY_DQS_TSEL_ENABLE_1__REG DENALI_PHY_335
#define LPDDR4__PHY_DQS_TSEL_ENABLE_1__FLD LPDDR4__DENALI_PHY_335__PHY_DQS_TSEL_ENABLE_1

#define LPDDR4__DENALI_PHY_336_READ_MASK                             0x7F03FFFFU
#define LPDDR4__DENALI_PHY_336_WRITE_MASK                            0x7F03FFFFU
#define LPDDR4__DENALI_PHY_336__PHY_DQS_TSEL_SELECT_1_MASK           0x0000FFFFU
#define LPDDR4__DENALI_PHY_336__PHY_DQS_TSEL_SELECT_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_336__PHY_DQS_TSEL_SELECT_1_WIDTH                  16U
#define LPDDR4__PHY_DQS_TSEL_SELECT_1__REG DENALI_PHY_336
#define LPDDR4__PHY_DQS_TSEL_SELECT_1__FLD LPDDR4__DENALI_PHY_336__PHY_DQS_TSEL_SELECT_1

#define LPDDR4__DENALI_PHY_336__PHY_TWO_CYC_PREAMBLE_1_MASK          0x00030000U
#define LPDDR4__DENALI_PHY_336__PHY_TWO_CYC_PREAMBLE_1_SHIFT                 16U
#define LPDDR4__DENALI_PHY_336__PHY_TWO_CYC_PREAMBLE_1_WIDTH                  2U
#define LPDDR4__PHY_TWO_CYC_PREAMBLE_1__REG DENALI_PHY_336
#define LPDDR4__PHY_TWO_CYC_PREAMBLE_1__FLD LPDDR4__DENALI_PHY_336__PHY_TWO_CYC_PREAMBLE_1

#define LPDDR4__DENALI_PHY_336__PHY_VREF_INITIAL_START_POINT_1_MASK  0x7F000000U
#define LPDDR4__DENALI_PHY_336__PHY_VREF_INITIAL_START_POINT_1_SHIFT         24U
#define LPDDR4__DENALI_PHY_336__PHY_VREF_INITIAL_START_POINT_1_WIDTH          7U
#define LPDDR4__PHY_VREF_INITIAL_START_POINT_1__REG DENALI_PHY_336
#define LPDDR4__PHY_VREF_INITIAL_START_POINT_1__FLD LPDDR4__DENALI_PHY_336__PHY_VREF_INITIAL_START_POINT_1

#define LPDDR4__DENALI_PHY_337_READ_MASK                             0xFF01037FU
#define LPDDR4__DENALI_PHY_337_WRITE_MASK                            0xFF01037FU
#define LPDDR4__DENALI_PHY_337__PHY_VREF_INITIAL_STOP_POINT_1_MASK   0x0000007FU
#define LPDDR4__DENALI_PHY_337__PHY_VREF_INITIAL_STOP_POINT_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_337__PHY_VREF_INITIAL_STOP_POINT_1_WIDTH           7U
#define LPDDR4__PHY_VREF_INITIAL_STOP_POINT_1__REG DENALI_PHY_337
#define LPDDR4__PHY_VREF_INITIAL_STOP_POINT_1__FLD LPDDR4__DENALI_PHY_337__PHY_VREF_INITIAL_STOP_POINT_1

#define LPDDR4__DENALI_PHY_337__PHY_VREF_TRAINING_CTRL_1_MASK        0x00000300U
#define LPDDR4__DENALI_PHY_337__PHY_VREF_TRAINING_CTRL_1_SHIFT                8U
#define LPDDR4__DENALI_PHY_337__PHY_VREF_TRAINING_CTRL_1_WIDTH                2U
#define LPDDR4__PHY_VREF_TRAINING_CTRL_1__REG DENALI_PHY_337
#define LPDDR4__PHY_VREF_TRAINING_CTRL_1__FLD LPDDR4__DENALI_PHY_337__PHY_VREF_TRAINING_CTRL_1

#define LPDDR4__DENALI_PHY_337__PHY_NTP_TRAIN_EN_1_MASK              0x00010000U
#define LPDDR4__DENALI_PHY_337__PHY_NTP_TRAIN_EN_1_SHIFT                     16U
#define LPDDR4__DENALI_PHY_337__PHY_NTP_TRAIN_EN_1_WIDTH                      1U
#define LPDDR4__DENALI_PHY_337__PHY_NTP_TRAIN_EN_1_WOCLR                      0U
#define LPDDR4__DENALI_PHY_337__PHY_NTP_TRAIN_EN_1_WOSET                      0U
#define LPDDR4__PHY_NTP_TRAIN_EN_1__REG DENALI_PHY_337
#define LPDDR4__PHY_NTP_TRAIN_EN_1__FLD LPDDR4__DENALI_PHY_337__PHY_NTP_TRAIN_EN_1

#define LPDDR4__DENALI_PHY_337__PHY_NTP_WDQ_STEP_SIZE_1_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_337__PHY_NTP_WDQ_STEP_SIZE_1_SHIFT                24U
#define LPDDR4__DENALI_PHY_337__PHY_NTP_WDQ_STEP_SIZE_1_WIDTH                 8U
#define LPDDR4__PHY_NTP_WDQ_STEP_SIZE_1__REG DENALI_PHY_337
#define LPDDR4__PHY_NTP_WDQ_STEP_SIZE_1__FLD LPDDR4__DENALI_PHY_337__PHY_NTP_WDQ_STEP_SIZE_1

#define LPDDR4__DENALI_PHY_338_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_338_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_338__PHY_NTP_WDQ_START_1_MASK             0x000007FFU
#define LPDDR4__DENALI_PHY_338__PHY_NTP_WDQ_START_1_SHIFT                     0U
#define LPDDR4__DENALI_PHY_338__PHY_NTP_WDQ_START_1_WIDTH                    11U
#define LPDDR4__PHY_NTP_WDQ_START_1__REG DENALI_PHY_338
#define LPDDR4__PHY_NTP_WDQ_START_1__FLD LPDDR4__DENALI_PHY_338__PHY_NTP_WDQ_START_1

#define LPDDR4__DENALI_PHY_338__PHY_NTP_WDQ_STOP_1_MASK              0x07FF0000U
#define LPDDR4__DENALI_PHY_338__PHY_NTP_WDQ_STOP_1_SHIFT                     16U
#define LPDDR4__DENALI_PHY_338__PHY_NTP_WDQ_STOP_1_WIDTH                     11U
#define LPDDR4__PHY_NTP_WDQ_STOP_1__REG DENALI_PHY_338
#define LPDDR4__PHY_NTP_WDQ_STOP_1__FLD LPDDR4__DENALI_PHY_338__PHY_NTP_WDQ_STOP_1

#define LPDDR4__DENALI_PHY_339_READ_MASK                             0x0103FFFFU
#define LPDDR4__DENALI_PHY_339_WRITE_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_339__PHY_NTP_WDQ_BIT_EN_1_MASK            0x000000FFU
#define LPDDR4__DENALI_PHY_339__PHY_NTP_WDQ_BIT_EN_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_339__PHY_NTP_WDQ_BIT_EN_1_WIDTH                    8U
#define LPDDR4__PHY_NTP_WDQ_BIT_EN_1__REG DENALI_PHY_339
#define LPDDR4__PHY_NTP_WDQ_BIT_EN_1__FLD LPDDR4__DENALI_PHY_339__PHY_NTP_WDQ_BIT_EN_1

#define LPDDR4__DENALI_PHY_339__PHY_WDQLVL_DVW_MIN_1_MASK            0x0003FF00U
#define LPDDR4__DENALI_PHY_339__PHY_WDQLVL_DVW_MIN_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_339__PHY_WDQLVL_DVW_MIN_1_WIDTH                   10U
#define LPDDR4__PHY_WDQLVL_DVW_MIN_1__REG DENALI_PHY_339
#define LPDDR4__PHY_WDQLVL_DVW_MIN_1__FLD LPDDR4__DENALI_PHY_339__PHY_WDQLVL_DVW_MIN_1

#define LPDDR4__DENALI_PHY_339__PHY_SW_WDQLVL_DVW_MIN_EN_1_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_339__PHY_SW_WDQLVL_DVW_MIN_EN_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_339__PHY_SW_WDQLVL_DVW_MIN_EN_1_WIDTH              1U
#define LPDDR4__DENALI_PHY_339__PHY_SW_WDQLVL_DVW_MIN_EN_1_WOCLR              0U
#define LPDDR4__DENALI_PHY_339__PHY_SW_WDQLVL_DVW_MIN_EN_1_WOSET              0U
#define LPDDR4__PHY_SW_WDQLVL_DVW_MIN_EN_1__REG DENALI_PHY_339
#define LPDDR4__PHY_SW_WDQLVL_DVW_MIN_EN_1__FLD LPDDR4__DENALI_PHY_339__PHY_SW_WDQLVL_DVW_MIN_EN_1

#define LPDDR4__DENALI_PHY_340_READ_MASK                             0x1F1F0F3FU
#define LPDDR4__DENALI_PHY_340_WRITE_MASK                            0x1F1F0F3FU
#define LPDDR4__DENALI_PHY_340__PHY_WDQLVL_PER_START_OFFSET_1_MASK   0x0000003FU
#define LPDDR4__DENALI_PHY_340__PHY_WDQLVL_PER_START_OFFSET_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_340__PHY_WDQLVL_PER_START_OFFSET_1_WIDTH           6U
#define LPDDR4__PHY_WDQLVL_PER_START_OFFSET_1__REG DENALI_PHY_340
#define LPDDR4__PHY_WDQLVL_PER_START_OFFSET_1__FLD LPDDR4__DENALI_PHY_340__PHY_WDQLVL_PER_START_OFFSET_1

#define LPDDR4__DENALI_PHY_340__PHY_FAST_LVL_EN_1_MASK               0x00000F00U
#define LPDDR4__DENALI_PHY_340__PHY_FAST_LVL_EN_1_SHIFT                       8U
#define LPDDR4__DENALI_PHY_340__PHY_FAST_LVL_EN_1_WIDTH                       4U
#define LPDDR4__PHY_FAST_LVL_EN_1__REG DENALI_PHY_340
#define LPDDR4__PHY_FAST_LVL_EN_1__FLD LPDDR4__DENALI_PHY_340__PHY_FAST_LVL_EN_1

#define LPDDR4__DENALI_PHY_340__PHY_PAD_TX_DCD_1_MASK                0x001F0000U
#define LPDDR4__DENALI_PHY_340__PHY_PAD_TX_DCD_1_SHIFT                       16U
#define LPDDR4__DENALI_PHY_340__PHY_PAD_TX_DCD_1_WIDTH                        5U
#define LPDDR4__PHY_PAD_TX_DCD_1__REG DENALI_PHY_340
#define LPDDR4__PHY_PAD_TX_DCD_1__FLD LPDDR4__DENALI_PHY_340__PHY_PAD_TX_DCD_1

#define LPDDR4__DENALI_PHY_340__PHY_PAD_RX_DCD_0_1_MASK              0x1F000000U
#define LPDDR4__DENALI_PHY_340__PHY_PAD_RX_DCD_0_1_SHIFT                     24U
#define LPDDR4__DENALI_PHY_340__PHY_PAD_RX_DCD_0_1_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_0_1__REG DENALI_PHY_340
#define LPDDR4__PHY_PAD_RX_DCD_0_1__FLD LPDDR4__DENALI_PHY_340__PHY_PAD_RX_DCD_0_1

#define LPDDR4__DENALI_PHY_341_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_341_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_1_1_MASK              0x0000001FU
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_1_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_1_1_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_1_1__REG DENALI_PHY_341
#define LPDDR4__PHY_PAD_RX_DCD_1_1__FLD LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_1_1

#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_2_1_MASK              0x00001F00U
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_2_1_SHIFT                      8U
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_2_1_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_2_1__REG DENALI_PHY_341
#define LPDDR4__PHY_PAD_RX_DCD_2_1__FLD LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_2_1

#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_3_1_MASK              0x001F0000U
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_3_1_SHIFT                     16U
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_3_1_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_3_1__REG DENALI_PHY_341
#define LPDDR4__PHY_PAD_RX_DCD_3_1__FLD LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_3_1

#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_4_1_MASK              0x1F000000U
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_4_1_SHIFT                     24U
#define LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_4_1_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_4_1__REG DENALI_PHY_341
#define LPDDR4__PHY_PAD_RX_DCD_4_1__FLD LPDDR4__DENALI_PHY_341__PHY_PAD_RX_DCD_4_1

#define LPDDR4__DENALI_PHY_342_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_342_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_5_1_MASK              0x0000001FU
#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_5_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_5_1_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_5_1__REG DENALI_PHY_342
#define LPDDR4__PHY_PAD_RX_DCD_5_1__FLD LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_5_1

#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_6_1_MASK              0x00001F00U
#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_6_1_SHIFT                      8U
#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_6_1_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_6_1__REG DENALI_PHY_342
#define LPDDR4__PHY_PAD_RX_DCD_6_1__FLD LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_6_1

#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_7_1_MASK              0x001F0000U
#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_7_1_SHIFT                     16U
#define LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_7_1_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_7_1__REG DENALI_PHY_342
#define LPDDR4__PHY_PAD_RX_DCD_7_1__FLD LPDDR4__DENALI_PHY_342__PHY_PAD_RX_DCD_7_1

#define LPDDR4__DENALI_PHY_342__PHY_PAD_DM_RX_DCD_1_MASK             0x1F000000U
#define LPDDR4__DENALI_PHY_342__PHY_PAD_DM_RX_DCD_1_SHIFT                    24U
#define LPDDR4__DENALI_PHY_342__PHY_PAD_DM_RX_DCD_1_WIDTH                     5U
#define LPDDR4__PHY_PAD_DM_RX_DCD_1__REG DENALI_PHY_342
#define LPDDR4__PHY_PAD_DM_RX_DCD_1__FLD LPDDR4__DENALI_PHY_342__PHY_PAD_DM_RX_DCD_1

#define LPDDR4__DENALI_PHY_343_READ_MASK                             0x007F1F1FU
#define LPDDR4__DENALI_PHY_343_WRITE_MASK                            0x007F1F1FU
#define LPDDR4__DENALI_PHY_343__PHY_PAD_DQS_RX_DCD_1_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_343__PHY_PAD_DQS_RX_DCD_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_343__PHY_PAD_DQS_RX_DCD_1_WIDTH                    5U
#define LPDDR4__PHY_PAD_DQS_RX_DCD_1__REG DENALI_PHY_343
#define LPDDR4__PHY_PAD_DQS_RX_DCD_1__FLD LPDDR4__DENALI_PHY_343__PHY_PAD_DQS_RX_DCD_1

#define LPDDR4__DENALI_PHY_343__PHY_PAD_FDBK_RX_DCD_1_MASK           0x00001F00U
#define LPDDR4__DENALI_PHY_343__PHY_PAD_FDBK_RX_DCD_1_SHIFT                   8U
#define LPDDR4__DENALI_PHY_343__PHY_PAD_FDBK_RX_DCD_1_WIDTH                   5U
#define LPDDR4__PHY_PAD_FDBK_RX_DCD_1__REG DENALI_PHY_343
#define LPDDR4__PHY_PAD_FDBK_RX_DCD_1__FLD LPDDR4__DENALI_PHY_343__PHY_PAD_FDBK_RX_DCD_1

#define LPDDR4__DENALI_PHY_343__PHY_PAD_DSLICE_IO_CFG_1_MASK         0x007F0000U
#define LPDDR4__DENALI_PHY_343__PHY_PAD_DSLICE_IO_CFG_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_343__PHY_PAD_DSLICE_IO_CFG_1_WIDTH                 7U
#define LPDDR4__PHY_PAD_DSLICE_IO_CFG_1__REG DENALI_PHY_343
#define LPDDR4__PHY_PAD_DSLICE_IO_CFG_1__FLD LPDDR4__DENALI_PHY_343__PHY_PAD_DSLICE_IO_CFG_1

#define LPDDR4__DENALI_PHY_344_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_344_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_344__PHY_RDDQ0_SLAVE_DELAY_1_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_344__PHY_RDDQ0_SLAVE_DELAY_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_344__PHY_RDDQ0_SLAVE_DELAY_1_WIDTH                10U
#define LPDDR4__PHY_RDDQ0_SLAVE_DELAY_1__REG DENALI_PHY_344
#define LPDDR4__PHY_RDDQ0_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_344__PHY_RDDQ0_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_344__PHY_RDDQ1_SLAVE_DELAY_1_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_344__PHY_RDDQ1_SLAVE_DELAY_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_344__PHY_RDDQ1_SLAVE_DELAY_1_WIDTH                10U
#define LPDDR4__PHY_RDDQ1_SLAVE_DELAY_1__REG DENALI_PHY_344
#define LPDDR4__PHY_RDDQ1_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_344__PHY_RDDQ1_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_345_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_345_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_345__PHY_RDDQ2_SLAVE_DELAY_1_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_345__PHY_RDDQ2_SLAVE_DELAY_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_345__PHY_RDDQ2_SLAVE_DELAY_1_WIDTH                10U
#define LPDDR4__PHY_RDDQ2_SLAVE_DELAY_1__REG DENALI_PHY_345
#define LPDDR4__PHY_RDDQ2_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_345__PHY_RDDQ2_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_345__PHY_RDDQ3_SLAVE_DELAY_1_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_345__PHY_RDDQ3_SLAVE_DELAY_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_345__PHY_RDDQ3_SLAVE_DELAY_1_WIDTH                10U
#define LPDDR4__PHY_RDDQ3_SLAVE_DELAY_1__REG DENALI_PHY_345
#define LPDDR4__PHY_RDDQ3_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_345__PHY_RDDQ3_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_346_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_346_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_346__PHY_RDDQ4_SLAVE_DELAY_1_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_346__PHY_RDDQ4_SLAVE_DELAY_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_346__PHY_RDDQ4_SLAVE_DELAY_1_WIDTH                10U
#define LPDDR4__PHY_RDDQ4_SLAVE_DELAY_1__REG DENALI_PHY_346
#define LPDDR4__PHY_RDDQ4_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_346__PHY_RDDQ4_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_346__PHY_RDDQ5_SLAVE_DELAY_1_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_346__PHY_RDDQ5_SLAVE_DELAY_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_346__PHY_RDDQ5_SLAVE_DELAY_1_WIDTH                10U
#define LPDDR4__PHY_RDDQ5_SLAVE_DELAY_1__REG DENALI_PHY_346
#define LPDDR4__PHY_RDDQ5_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_346__PHY_RDDQ5_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_347_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_347_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_347__PHY_RDDQ6_SLAVE_DELAY_1_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_347__PHY_RDDQ6_SLAVE_DELAY_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_347__PHY_RDDQ6_SLAVE_DELAY_1_WIDTH                10U
#define LPDDR4__PHY_RDDQ6_SLAVE_DELAY_1__REG DENALI_PHY_347
#define LPDDR4__PHY_RDDQ6_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_347__PHY_RDDQ6_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_347__PHY_RDDQ7_SLAVE_DELAY_1_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_347__PHY_RDDQ7_SLAVE_DELAY_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_347__PHY_RDDQ7_SLAVE_DELAY_1_WIDTH                10U
#define LPDDR4__PHY_RDDQ7_SLAVE_DELAY_1__REG DENALI_PHY_347
#define LPDDR4__PHY_RDDQ7_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_347__PHY_RDDQ7_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_348_READ_MASK                             0x1F0703FFU
#define LPDDR4__DENALI_PHY_348_WRITE_MASK                            0x1F0703FFU
#define LPDDR4__DENALI_PHY_348__PHY_RDDM_SLAVE_DELAY_1_MASK          0x000003FFU
#define LPDDR4__DENALI_PHY_348__PHY_RDDM_SLAVE_DELAY_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_348__PHY_RDDM_SLAVE_DELAY_1_WIDTH                 10U
#define LPDDR4__PHY_RDDM_SLAVE_DELAY_1__REG DENALI_PHY_348
#define LPDDR4__PHY_RDDM_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_348__PHY_RDDM_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_348__PHY_RX_PCLK_CLK_SEL_1_MASK           0x00070000U
#define LPDDR4__DENALI_PHY_348__PHY_RX_PCLK_CLK_SEL_1_SHIFT                  16U
#define LPDDR4__DENALI_PHY_348__PHY_RX_PCLK_CLK_SEL_1_WIDTH                   3U
#define LPDDR4__PHY_RX_PCLK_CLK_SEL_1__REG DENALI_PHY_348
#define LPDDR4__PHY_RX_PCLK_CLK_SEL_1__FLD LPDDR4__DENALI_PHY_348__PHY_RX_PCLK_CLK_SEL_1

#define LPDDR4__DENALI_PHY_348__PHY_RX_CAL_ALL_DLY_1_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_348__PHY_RX_CAL_ALL_DLY_1_SHIFT                   24U
#define LPDDR4__DENALI_PHY_348__PHY_RX_CAL_ALL_DLY_1_WIDTH                    5U
#define LPDDR4__PHY_RX_CAL_ALL_DLY_1__REG DENALI_PHY_348
#define LPDDR4__PHY_RX_CAL_ALL_DLY_1__FLD LPDDR4__DENALI_PHY_348__PHY_RX_CAL_ALL_DLY_1

#define LPDDR4__DENALI_PHY_349_READ_MASK                             0x00000007U
#define LPDDR4__DENALI_PHY_349_WRITE_MASK                            0x00000007U
#define LPDDR4__DENALI_PHY_349__PHY_DATA_DC_CAL_CLK_SEL_1_MASK       0x00000007U
#define LPDDR4__DENALI_PHY_349__PHY_DATA_DC_CAL_CLK_SEL_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_349__PHY_DATA_DC_CAL_CLK_SEL_1_WIDTH               3U
#define LPDDR4__PHY_DATA_DC_CAL_CLK_SEL_1__REG DENALI_PHY_349
#define LPDDR4__PHY_DATA_DC_CAL_CLK_SEL_1__FLD LPDDR4__DENALI_PHY_349__PHY_DATA_DC_CAL_CLK_SEL_1

#define LPDDR4__DENALI_PHY_350_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_350_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_350__PHY_DQ_OE_TIMING_1_MASK              0x000000FFU
#define LPDDR4__DENALI_PHY_350__PHY_DQ_OE_TIMING_1_SHIFT                      0U
#define LPDDR4__DENALI_PHY_350__PHY_DQ_OE_TIMING_1_WIDTH                      8U
#define LPDDR4__PHY_DQ_OE_TIMING_1__REG DENALI_PHY_350
#define LPDDR4__PHY_DQ_OE_TIMING_1__FLD LPDDR4__DENALI_PHY_350__PHY_DQ_OE_TIMING_1

#define LPDDR4__DENALI_PHY_350__PHY_DQ_TSEL_RD_TIMING_1_MASK         0x0000FF00U
#define LPDDR4__DENALI_PHY_350__PHY_DQ_TSEL_RD_TIMING_1_SHIFT                 8U
#define LPDDR4__DENALI_PHY_350__PHY_DQ_TSEL_RD_TIMING_1_WIDTH                 8U
#define LPDDR4__PHY_DQ_TSEL_RD_TIMING_1__REG DENALI_PHY_350
#define LPDDR4__PHY_DQ_TSEL_RD_TIMING_1__FLD LPDDR4__DENALI_PHY_350__PHY_DQ_TSEL_RD_TIMING_1

#define LPDDR4__DENALI_PHY_350__PHY_DQ_TSEL_WR_TIMING_1_MASK         0x00FF0000U
#define LPDDR4__DENALI_PHY_350__PHY_DQ_TSEL_WR_TIMING_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_350__PHY_DQ_TSEL_WR_TIMING_1_WIDTH                 8U
#define LPDDR4__PHY_DQ_TSEL_WR_TIMING_1__REG DENALI_PHY_350
#define LPDDR4__PHY_DQ_TSEL_WR_TIMING_1__FLD LPDDR4__DENALI_PHY_350__PHY_DQ_TSEL_WR_TIMING_1

#define LPDDR4__DENALI_PHY_350__PHY_DQS_OE_TIMING_1_MASK             0xFF000000U
#define LPDDR4__DENALI_PHY_350__PHY_DQS_OE_TIMING_1_SHIFT                    24U
#define LPDDR4__DENALI_PHY_350__PHY_DQS_OE_TIMING_1_WIDTH                     8U
#define LPDDR4__PHY_DQS_OE_TIMING_1__REG DENALI_PHY_350
#define LPDDR4__PHY_DQS_OE_TIMING_1__FLD LPDDR4__DENALI_PHY_350__PHY_DQS_OE_TIMING_1

#define LPDDR4__DENALI_PHY_351_READ_MASK                             0xFFFFFF0FU
#define LPDDR4__DENALI_PHY_351_WRITE_MASK                            0xFFFFFF0FU
#define LPDDR4__DENALI_PHY_351__PHY_IO_PAD_DELAY_TIMING_1_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_351__PHY_IO_PAD_DELAY_TIMING_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_351__PHY_IO_PAD_DELAY_TIMING_1_WIDTH               4U
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_1__REG DENALI_PHY_351
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_1__FLD LPDDR4__DENALI_PHY_351__PHY_IO_PAD_DELAY_TIMING_1

#define LPDDR4__DENALI_PHY_351__PHY_DQS_TSEL_RD_TIMING_1_MASK        0x0000FF00U
#define LPDDR4__DENALI_PHY_351__PHY_DQS_TSEL_RD_TIMING_1_SHIFT                8U
#define LPDDR4__DENALI_PHY_351__PHY_DQS_TSEL_RD_TIMING_1_WIDTH                8U
#define LPDDR4__PHY_DQS_TSEL_RD_TIMING_1__REG DENALI_PHY_351
#define LPDDR4__PHY_DQS_TSEL_RD_TIMING_1__FLD LPDDR4__DENALI_PHY_351__PHY_DQS_TSEL_RD_TIMING_1

#define LPDDR4__DENALI_PHY_351__PHY_DQS_OE_RD_TIMING_1_MASK          0x00FF0000U
#define LPDDR4__DENALI_PHY_351__PHY_DQS_OE_RD_TIMING_1_SHIFT                 16U
#define LPDDR4__DENALI_PHY_351__PHY_DQS_OE_RD_TIMING_1_WIDTH                  8U
#define LPDDR4__PHY_DQS_OE_RD_TIMING_1__REG DENALI_PHY_351
#define LPDDR4__PHY_DQS_OE_RD_TIMING_1__FLD LPDDR4__DENALI_PHY_351__PHY_DQS_OE_RD_TIMING_1

#define LPDDR4__DENALI_PHY_351__PHY_DQS_TSEL_WR_TIMING_1_MASK        0xFF000000U
#define LPDDR4__DENALI_PHY_351__PHY_DQS_TSEL_WR_TIMING_1_SHIFT               24U
#define LPDDR4__DENALI_PHY_351__PHY_DQS_TSEL_WR_TIMING_1_WIDTH                8U
#define LPDDR4__PHY_DQS_TSEL_WR_TIMING_1__REG DENALI_PHY_351
#define LPDDR4__PHY_DQS_TSEL_WR_TIMING_1__FLD LPDDR4__DENALI_PHY_351__PHY_DQS_TSEL_WR_TIMING_1

#define LPDDR4__DENALI_PHY_352_READ_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_352_WRITE_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_352__PHY_VREF_SETTING_TIME_1_MASK         0x0000FFFFU
#define LPDDR4__DENALI_PHY_352__PHY_VREF_SETTING_TIME_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_352__PHY_VREF_SETTING_TIME_1_WIDTH                16U
#define LPDDR4__PHY_VREF_SETTING_TIME_1__REG DENALI_PHY_352
#define LPDDR4__PHY_VREF_SETTING_TIME_1__FLD LPDDR4__DENALI_PHY_352__PHY_VREF_SETTING_TIME_1

#define LPDDR4__DENALI_PHY_352__PHY_PAD_VREF_CTRL_DQ_1_MASK          0x0FFF0000U
#define LPDDR4__DENALI_PHY_352__PHY_PAD_VREF_CTRL_DQ_1_SHIFT                 16U
#define LPDDR4__DENALI_PHY_352__PHY_PAD_VREF_CTRL_DQ_1_WIDTH                 12U
#define LPDDR4__PHY_PAD_VREF_CTRL_DQ_1__REG DENALI_PHY_352
#define LPDDR4__PHY_PAD_VREF_CTRL_DQ_1__FLD LPDDR4__DENALI_PHY_352__PHY_PAD_VREF_CTRL_DQ_1

#define LPDDR4__DENALI_PHY_353_READ_MASK                             0x03FFFF01U
#define LPDDR4__DENALI_PHY_353_WRITE_MASK                            0x03FFFF01U
#define LPDDR4__DENALI_PHY_353__PHY_PER_CS_TRAINING_EN_1_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_353__PHY_PER_CS_TRAINING_EN_1_SHIFT                0U
#define LPDDR4__DENALI_PHY_353__PHY_PER_CS_TRAINING_EN_1_WIDTH                1U
#define LPDDR4__DENALI_PHY_353__PHY_PER_CS_TRAINING_EN_1_WOCLR                0U
#define LPDDR4__DENALI_PHY_353__PHY_PER_CS_TRAINING_EN_1_WOSET                0U
#define LPDDR4__PHY_PER_CS_TRAINING_EN_1__REG DENALI_PHY_353
#define LPDDR4__PHY_PER_CS_TRAINING_EN_1__FLD LPDDR4__DENALI_PHY_353__PHY_PER_CS_TRAINING_EN_1

#define LPDDR4__DENALI_PHY_353__PHY_DQ_IE_TIMING_1_MASK              0x0000FF00U
#define LPDDR4__DENALI_PHY_353__PHY_DQ_IE_TIMING_1_SHIFT                      8U
#define LPDDR4__DENALI_PHY_353__PHY_DQ_IE_TIMING_1_WIDTH                      8U
#define LPDDR4__PHY_DQ_IE_TIMING_1__REG DENALI_PHY_353
#define LPDDR4__PHY_DQ_IE_TIMING_1__FLD LPDDR4__DENALI_PHY_353__PHY_DQ_IE_TIMING_1

#define LPDDR4__DENALI_PHY_353__PHY_DQS_IE_TIMING_1_MASK             0x00FF0000U
#define LPDDR4__DENALI_PHY_353__PHY_DQS_IE_TIMING_1_SHIFT                    16U
#define LPDDR4__DENALI_PHY_353__PHY_DQS_IE_TIMING_1_WIDTH                     8U
#define LPDDR4__PHY_DQS_IE_TIMING_1__REG DENALI_PHY_353
#define LPDDR4__PHY_DQS_IE_TIMING_1__FLD LPDDR4__DENALI_PHY_353__PHY_DQS_IE_TIMING_1

#define LPDDR4__DENALI_PHY_353__PHY_RDDATA_EN_IE_DLY_1_MASK          0x03000000U
#define LPDDR4__DENALI_PHY_353__PHY_RDDATA_EN_IE_DLY_1_SHIFT                 24U
#define LPDDR4__DENALI_PHY_353__PHY_RDDATA_EN_IE_DLY_1_WIDTH                  2U
#define LPDDR4__PHY_RDDATA_EN_IE_DLY_1__REG DENALI_PHY_353
#define LPDDR4__PHY_RDDATA_EN_IE_DLY_1__FLD LPDDR4__DENALI_PHY_353__PHY_RDDATA_EN_IE_DLY_1

#define LPDDR4__DENALI_PHY_354_READ_MASK                             0x1F010303U
#define LPDDR4__DENALI_PHY_354_WRITE_MASK                            0x1F010303U
#define LPDDR4__DENALI_PHY_354__PHY_IE_MODE_1_MASK                   0x00000003U
#define LPDDR4__DENALI_PHY_354__PHY_IE_MODE_1_SHIFT                           0U
#define LPDDR4__DENALI_PHY_354__PHY_IE_MODE_1_WIDTH                           2U
#define LPDDR4__PHY_IE_MODE_1__REG DENALI_PHY_354
#define LPDDR4__PHY_IE_MODE_1__FLD LPDDR4__DENALI_PHY_354__PHY_IE_MODE_1

#define LPDDR4__DENALI_PHY_354__PHY_DBI_MODE_1_MASK                  0x00000300U
#define LPDDR4__DENALI_PHY_354__PHY_DBI_MODE_1_SHIFT                          8U
#define LPDDR4__DENALI_PHY_354__PHY_DBI_MODE_1_WIDTH                          2U
#define LPDDR4__PHY_DBI_MODE_1__REG DENALI_PHY_354
#define LPDDR4__PHY_DBI_MODE_1__FLD LPDDR4__DENALI_PHY_354__PHY_DBI_MODE_1

#define LPDDR4__DENALI_PHY_354__PHY_WDQLVL_IE_ON_1_MASK              0x00010000U
#define LPDDR4__DENALI_PHY_354__PHY_WDQLVL_IE_ON_1_SHIFT                     16U
#define LPDDR4__DENALI_PHY_354__PHY_WDQLVL_IE_ON_1_WIDTH                      1U
#define LPDDR4__DENALI_PHY_354__PHY_WDQLVL_IE_ON_1_WOCLR                      0U
#define LPDDR4__DENALI_PHY_354__PHY_WDQLVL_IE_ON_1_WOSET                      0U
#define LPDDR4__PHY_WDQLVL_IE_ON_1__REG DENALI_PHY_354
#define LPDDR4__PHY_WDQLVL_IE_ON_1__FLD LPDDR4__DENALI_PHY_354__PHY_WDQLVL_IE_ON_1

#define LPDDR4__DENALI_PHY_354__PHY_WDQLVL_RDDATA_EN_DLY_1_MASK      0x1F000000U
#define LPDDR4__DENALI_PHY_354__PHY_WDQLVL_RDDATA_EN_DLY_1_SHIFT             24U
#define LPDDR4__DENALI_PHY_354__PHY_WDQLVL_RDDATA_EN_DLY_1_WIDTH              5U
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_DLY_1__REG DENALI_PHY_354
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_DLY_1__FLD LPDDR4__DENALI_PHY_354__PHY_WDQLVL_RDDATA_EN_DLY_1

#define LPDDR4__DENALI_PHY_355_READ_MASK                             0x0F1F1F1FU
#define LPDDR4__DENALI_PHY_355_WRITE_MASK                            0x0F1F1F1FU
#define LPDDR4__DENALI_PHY_355__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_1_MASK 0x0000001FU
#define LPDDR4__DENALI_PHY_355__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_355__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_1_WIDTH         5U
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_1__REG DENALI_PHY_355
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_1__FLD LPDDR4__DENALI_PHY_355__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_1

#define LPDDR4__DENALI_PHY_355__PHY_RDDATA_EN_TSEL_DLY_1_MASK        0x00001F00U
#define LPDDR4__DENALI_PHY_355__PHY_RDDATA_EN_TSEL_DLY_1_SHIFT                8U
#define LPDDR4__DENALI_PHY_355__PHY_RDDATA_EN_TSEL_DLY_1_WIDTH                5U
#define LPDDR4__PHY_RDDATA_EN_TSEL_DLY_1__REG DENALI_PHY_355
#define LPDDR4__PHY_RDDATA_EN_TSEL_DLY_1__FLD LPDDR4__DENALI_PHY_355__PHY_RDDATA_EN_TSEL_DLY_1

#define LPDDR4__DENALI_PHY_355__PHY_RDDATA_EN_OE_DLY_1_MASK          0x001F0000U
#define LPDDR4__DENALI_PHY_355__PHY_RDDATA_EN_OE_DLY_1_SHIFT                 16U
#define LPDDR4__DENALI_PHY_355__PHY_RDDATA_EN_OE_DLY_1_WIDTH                  5U
#define LPDDR4__PHY_RDDATA_EN_OE_DLY_1__REG DENALI_PHY_355
#define LPDDR4__PHY_RDDATA_EN_OE_DLY_1__FLD LPDDR4__DENALI_PHY_355__PHY_RDDATA_EN_OE_DLY_1

#define LPDDR4__DENALI_PHY_355__PHY_SW_MASTER_MODE_1_MASK            0x0F000000U
#define LPDDR4__DENALI_PHY_355__PHY_SW_MASTER_MODE_1_SHIFT                   24U
#define LPDDR4__DENALI_PHY_355__PHY_SW_MASTER_MODE_1_WIDTH                    4U
#define LPDDR4__PHY_SW_MASTER_MODE_1__REG DENALI_PHY_355
#define LPDDR4__PHY_SW_MASTER_MODE_1__FLD LPDDR4__DENALI_PHY_355__PHY_SW_MASTER_MODE_1

#define LPDDR4__DENALI_PHY_356_READ_MASK                             0xFF3F07FFU
#define LPDDR4__DENALI_PHY_356_WRITE_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_START_1_MASK        0x000007FFU
#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_START_1_SHIFT                0U
#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_START_1_WIDTH               11U
#define LPDDR4__PHY_MASTER_DELAY_START_1__REG DENALI_PHY_356
#define LPDDR4__PHY_MASTER_DELAY_START_1__FLD LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_START_1

#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_STEP_1_MASK         0x003F0000U
#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_STEP_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_STEP_1_WIDTH                 6U
#define LPDDR4__PHY_MASTER_DELAY_STEP_1__REG DENALI_PHY_356
#define LPDDR4__PHY_MASTER_DELAY_STEP_1__FLD LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_STEP_1

#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_WAIT_1_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_WAIT_1_SHIFT                24U
#define LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_WAIT_1_WIDTH                 8U
#define LPDDR4__PHY_MASTER_DELAY_WAIT_1__REG DENALI_PHY_356
#define LPDDR4__PHY_MASTER_DELAY_WAIT_1__FLD LPDDR4__DENALI_PHY_356__PHY_MASTER_DELAY_WAIT_1

#define LPDDR4__DENALI_PHY_357_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_357_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_357__PHY_MASTER_DELAY_HALF_MEASURE_1_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_357__PHY_MASTER_DELAY_HALF_MEASURE_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_357__PHY_MASTER_DELAY_HALF_MEASURE_1_WIDTH         8U
#define LPDDR4__PHY_MASTER_DELAY_HALF_MEASURE_1__REG DENALI_PHY_357
#define LPDDR4__PHY_MASTER_DELAY_HALF_MEASURE_1__FLD LPDDR4__DENALI_PHY_357__PHY_MASTER_DELAY_HALF_MEASURE_1

#define LPDDR4__DENALI_PHY_357__PHY_RPTR_UPDATE_1_MASK               0x00000F00U
#define LPDDR4__DENALI_PHY_357__PHY_RPTR_UPDATE_1_SHIFT                       8U
#define LPDDR4__DENALI_PHY_357__PHY_RPTR_UPDATE_1_WIDTH                       4U
#define LPDDR4__PHY_RPTR_UPDATE_1__REG DENALI_PHY_357
#define LPDDR4__PHY_RPTR_UPDATE_1__FLD LPDDR4__DENALI_PHY_357__PHY_RPTR_UPDATE_1

#define LPDDR4__DENALI_PHY_357__PHY_WRLVL_DLY_STEP_1_MASK            0x00FF0000U
#define LPDDR4__DENALI_PHY_357__PHY_WRLVL_DLY_STEP_1_SHIFT                   16U
#define LPDDR4__DENALI_PHY_357__PHY_WRLVL_DLY_STEP_1_WIDTH                    8U
#define LPDDR4__PHY_WRLVL_DLY_STEP_1__REG DENALI_PHY_357
#define LPDDR4__PHY_WRLVL_DLY_STEP_1__FLD LPDDR4__DENALI_PHY_357__PHY_WRLVL_DLY_STEP_1

#define LPDDR4__DENALI_PHY_357__PHY_WRLVL_DLY_FINE_STEP_1_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_357__PHY_WRLVL_DLY_FINE_STEP_1_SHIFT              24U
#define LPDDR4__DENALI_PHY_357__PHY_WRLVL_DLY_FINE_STEP_1_WIDTH               4U
#define LPDDR4__PHY_WRLVL_DLY_FINE_STEP_1__REG DENALI_PHY_357
#define LPDDR4__PHY_WRLVL_DLY_FINE_STEP_1__FLD LPDDR4__DENALI_PHY_357__PHY_WRLVL_DLY_FINE_STEP_1

#define LPDDR4__DENALI_PHY_358_READ_MASK                             0x001F0F3FU
#define LPDDR4__DENALI_PHY_358_WRITE_MASK                            0x001F0F3FU
#define LPDDR4__DENALI_PHY_358__PHY_WRLVL_RESP_WAIT_CNT_1_MASK       0x0000003FU
#define LPDDR4__DENALI_PHY_358__PHY_WRLVL_RESP_WAIT_CNT_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_358__PHY_WRLVL_RESP_WAIT_CNT_1_WIDTH               6U
#define LPDDR4__PHY_WRLVL_RESP_WAIT_CNT_1__REG DENALI_PHY_358
#define LPDDR4__PHY_WRLVL_RESP_WAIT_CNT_1__FLD LPDDR4__DENALI_PHY_358__PHY_WRLVL_RESP_WAIT_CNT_1

#define LPDDR4__DENALI_PHY_358__PHY_GTLVL_DLY_STEP_1_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_358__PHY_GTLVL_DLY_STEP_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_358__PHY_GTLVL_DLY_STEP_1_WIDTH                    4U
#define LPDDR4__PHY_GTLVL_DLY_STEP_1__REG DENALI_PHY_358
#define LPDDR4__PHY_GTLVL_DLY_STEP_1__FLD LPDDR4__DENALI_PHY_358__PHY_GTLVL_DLY_STEP_1

#define LPDDR4__DENALI_PHY_358__PHY_GTLVL_RESP_WAIT_CNT_1_MASK       0x001F0000U
#define LPDDR4__DENALI_PHY_358__PHY_GTLVL_RESP_WAIT_CNT_1_SHIFT              16U
#define LPDDR4__DENALI_PHY_358__PHY_GTLVL_RESP_WAIT_CNT_1_WIDTH               5U
#define LPDDR4__PHY_GTLVL_RESP_WAIT_CNT_1__REG DENALI_PHY_358
#define LPDDR4__PHY_GTLVL_RESP_WAIT_CNT_1__FLD LPDDR4__DENALI_PHY_358__PHY_GTLVL_RESP_WAIT_CNT_1

#define LPDDR4__DENALI_PHY_359_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_359_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_359__PHY_GTLVL_BACK_STEP_1_MASK           0x000003FFU
#define LPDDR4__DENALI_PHY_359__PHY_GTLVL_BACK_STEP_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_359__PHY_GTLVL_BACK_STEP_1_WIDTH                  10U
#define LPDDR4__PHY_GTLVL_BACK_STEP_1__REG DENALI_PHY_359
#define LPDDR4__PHY_GTLVL_BACK_STEP_1__FLD LPDDR4__DENALI_PHY_359__PHY_GTLVL_BACK_STEP_1

#define LPDDR4__DENALI_PHY_359__PHY_GTLVL_FINAL_STEP_1_MASK          0x03FF0000U
#define LPDDR4__DENALI_PHY_359__PHY_GTLVL_FINAL_STEP_1_SHIFT                 16U
#define LPDDR4__DENALI_PHY_359__PHY_GTLVL_FINAL_STEP_1_WIDTH                 10U
#define LPDDR4__PHY_GTLVL_FINAL_STEP_1__REG DENALI_PHY_359
#define LPDDR4__PHY_GTLVL_FINAL_STEP_1__FLD LPDDR4__DENALI_PHY_359__PHY_GTLVL_FINAL_STEP_1

#define LPDDR4__DENALI_PHY_360_READ_MASK                             0x01FF0FFFU
#define LPDDR4__DENALI_PHY_360_WRITE_MASK                            0x01FF0FFFU
#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_DLY_STEP_1_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_DLY_STEP_1_SHIFT                   0U
#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_DLY_STEP_1_WIDTH                   8U
#define LPDDR4__PHY_WDQLVL_DLY_STEP_1__REG DENALI_PHY_360
#define LPDDR4__PHY_WDQLVL_DLY_STEP_1__FLD LPDDR4__DENALI_PHY_360__PHY_WDQLVL_DLY_STEP_1

#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_QTR_DLY_STEP_1_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_QTR_DLY_STEP_1_SHIFT               8U
#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_QTR_DLY_STEP_1_WIDTH               4U
#define LPDDR4__PHY_WDQLVL_QTR_DLY_STEP_1__REG DENALI_PHY_360
#define LPDDR4__PHY_WDQLVL_QTR_DLY_STEP_1__FLD LPDDR4__DENALI_PHY_360__PHY_WDQLVL_QTR_DLY_STEP_1

#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_DM_SEARCH_RANGE_1_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_DM_SEARCH_RANGE_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_360__PHY_WDQLVL_DM_SEARCH_RANGE_1_WIDTH            9U
#define LPDDR4__PHY_WDQLVL_DM_SEARCH_RANGE_1__REG DENALI_PHY_360
#define LPDDR4__PHY_WDQLVL_DM_SEARCH_RANGE_1__FLD LPDDR4__DENALI_PHY_360__PHY_WDQLVL_DM_SEARCH_RANGE_1

#define LPDDR4__DENALI_PHY_361_READ_MASK                             0x00000F01U
#define LPDDR4__DENALI_PHY_361_WRITE_MASK                            0x00000F01U
#define LPDDR4__DENALI_PHY_361__PHY_TOGGLE_PRE_SUPPORT_1_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_361__PHY_TOGGLE_PRE_SUPPORT_1_SHIFT                0U
#define LPDDR4__DENALI_PHY_361__PHY_TOGGLE_PRE_SUPPORT_1_WIDTH                1U
#define LPDDR4__DENALI_PHY_361__PHY_TOGGLE_PRE_SUPPORT_1_WOCLR                0U
#define LPDDR4__DENALI_PHY_361__PHY_TOGGLE_PRE_SUPPORT_1_WOSET                0U
#define LPDDR4__PHY_TOGGLE_PRE_SUPPORT_1__REG DENALI_PHY_361
#define LPDDR4__PHY_TOGGLE_PRE_SUPPORT_1__FLD LPDDR4__DENALI_PHY_361__PHY_TOGGLE_PRE_SUPPORT_1

#define LPDDR4__DENALI_PHY_361__PHY_RDLVL_DLY_STEP_1_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_361__PHY_RDLVL_DLY_STEP_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_361__PHY_RDLVL_DLY_STEP_1_WIDTH                    4U
#define LPDDR4__PHY_RDLVL_DLY_STEP_1__REG DENALI_PHY_361
#define LPDDR4__PHY_RDLVL_DLY_STEP_1__FLD LPDDR4__DENALI_PHY_361__PHY_RDLVL_DLY_STEP_1

#define LPDDR4__DENALI_PHY_362_READ_MASK                             0x000003FFU
#define LPDDR4__DENALI_PHY_362_WRITE_MASK                            0x000003FFU
#define LPDDR4__DENALI_PHY_362__PHY_RDLVL_MAX_EDGE_1_MASK            0x000003FFU
#define LPDDR4__DENALI_PHY_362__PHY_RDLVL_MAX_EDGE_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_362__PHY_RDLVL_MAX_EDGE_1_WIDTH                   10U
#define LPDDR4__PHY_RDLVL_MAX_EDGE_1__REG DENALI_PHY_362
#define LPDDR4__PHY_RDLVL_MAX_EDGE_1__FLD LPDDR4__DENALI_PHY_362__PHY_RDLVL_MAX_EDGE_1

#define LPDDR4__DENALI_PHY_363_READ_MASK                             0x00030703U
#define LPDDR4__DENALI_PHY_363_WRITE_MASK                            0x00030703U
#define LPDDR4__DENALI_PHY_363__PHY_WRPATH_GATE_DISABLE_1_MASK       0x00000003U
#define LPDDR4__DENALI_PHY_363__PHY_WRPATH_GATE_DISABLE_1_SHIFT               0U
#define LPDDR4__DENALI_PHY_363__PHY_WRPATH_GATE_DISABLE_1_WIDTH               2U
#define LPDDR4__PHY_WRPATH_GATE_DISABLE_1__REG DENALI_PHY_363
#define LPDDR4__PHY_WRPATH_GATE_DISABLE_1__FLD LPDDR4__DENALI_PHY_363__PHY_WRPATH_GATE_DISABLE_1

#define LPDDR4__DENALI_PHY_363__PHY_WRPATH_GATE_TIMING_1_MASK        0x00000700U
#define LPDDR4__DENALI_PHY_363__PHY_WRPATH_GATE_TIMING_1_SHIFT                8U
#define LPDDR4__DENALI_PHY_363__PHY_WRPATH_GATE_TIMING_1_WIDTH                3U
#define LPDDR4__PHY_WRPATH_GATE_TIMING_1__REG DENALI_PHY_363
#define LPDDR4__PHY_WRPATH_GATE_TIMING_1__FLD LPDDR4__DENALI_PHY_363__PHY_WRPATH_GATE_TIMING_1

#define LPDDR4__DENALI_PHY_363__PHY_DATA_DC_INIT_DISABLE_1_MASK      0x00030000U
#define LPDDR4__DENALI_PHY_363__PHY_DATA_DC_INIT_DISABLE_1_SHIFT             16U
#define LPDDR4__DENALI_PHY_363__PHY_DATA_DC_INIT_DISABLE_1_WIDTH              2U
#define LPDDR4__PHY_DATA_DC_INIT_DISABLE_1__REG DENALI_PHY_363
#define LPDDR4__PHY_DATA_DC_INIT_DISABLE_1__FLD LPDDR4__DENALI_PHY_363__PHY_DATA_DC_INIT_DISABLE_1

#define LPDDR4__DENALI_PHY_364_READ_MASK                             0x07FF03FFU
#define LPDDR4__DENALI_PHY_364_WRITE_MASK                            0x07FF03FFU
#define LPDDR4__DENALI_PHY_364__PHY_DATA_DC_DQS_INIT_SLV_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_364__PHY_DATA_DC_DQS_INIT_SLV_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_364__PHY_DATA_DC_DQS_INIT_SLV_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_DATA_DC_DQS_INIT_SLV_DELAY_1__REG DENALI_PHY_364
#define LPDDR4__PHY_DATA_DC_DQS_INIT_SLV_DELAY_1__FLD LPDDR4__DENALI_PHY_364__PHY_DATA_DC_DQS_INIT_SLV_DELAY_1

#define LPDDR4__DENALI_PHY_364__PHY_DATA_DC_DQ_INIT_SLV_DELAY_1_MASK 0x07FF0000U
#define LPDDR4__DENALI_PHY_364__PHY_DATA_DC_DQ_INIT_SLV_DELAY_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_364__PHY_DATA_DC_DQ_INIT_SLV_DELAY_1_WIDTH        11U
#define LPDDR4__PHY_DATA_DC_DQ_INIT_SLV_DELAY_1__REG DENALI_PHY_364
#define LPDDR4__PHY_DATA_DC_DQ_INIT_SLV_DELAY_1__FLD LPDDR4__DENALI_PHY_364__PHY_DATA_DC_DQ_INIT_SLV_DELAY_1

#define LPDDR4__DENALI_PHY_365_READ_MASK                             0xFFFF0101U
#define LPDDR4__DENALI_PHY_365_WRITE_MASK                            0xFFFF0101U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WRLVL_ENABLE_1_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WRLVL_ENABLE_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WRLVL_ENABLE_1_WIDTH              1U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WRLVL_ENABLE_1_WOCLR              0U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WRLVL_ENABLE_1_WOSET              0U
#define LPDDR4__PHY_DATA_DC_WRLVL_ENABLE_1__REG DENALI_PHY_365
#define LPDDR4__PHY_DATA_DC_WRLVL_ENABLE_1__FLD LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WRLVL_ENABLE_1

#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WDQLVL_ENABLE_1_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WDQLVL_ENABLE_1_SHIFT             8U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WDQLVL_ENABLE_1_WIDTH             1U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WDQLVL_ENABLE_1_WOCLR             0U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WDQLVL_ENABLE_1_WOSET             0U
#define LPDDR4__PHY_DATA_DC_WDQLVL_ENABLE_1__REG DENALI_PHY_365
#define LPDDR4__PHY_DATA_DC_WDQLVL_ENABLE_1__FLD LPDDR4__DENALI_PHY_365__PHY_DATA_DC_WDQLVL_ENABLE_1

#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_DM_CLK_SE_THRSHLD_1_MASK 0x00FF0000U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_DM_CLK_SE_THRSHLD_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_DM_CLK_SE_THRSHLD_1_WIDTH         8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_SE_THRSHLD_1__REG DENALI_PHY_365
#define LPDDR4__PHY_DATA_DC_DM_CLK_SE_THRSHLD_1__FLD LPDDR4__DENALI_PHY_365__PHY_DATA_DC_DM_CLK_SE_THRSHLD_1

#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_1_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_1_SHIFT      24U
#define LPDDR4__DENALI_PHY_365__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_1_WIDTH       8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_1__REG DENALI_PHY_365
#define LPDDR4__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_1__FLD LPDDR4__DENALI_PHY_365__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_1

#define LPDDR4__DENALI_PHY_366_READ_MASK                             0x001F7F7FU
#define LPDDR4__DENALI_PHY_366_WRITE_MASK                            0x001F7F7FU
#define LPDDR4__DENALI_PHY_366__PHY_WDQ_OSC_DELTA_1_MASK             0x0000007FU
#define LPDDR4__DENALI_PHY_366__PHY_WDQ_OSC_DELTA_1_SHIFT                     0U
#define LPDDR4__DENALI_PHY_366__PHY_WDQ_OSC_DELTA_1_WIDTH                     7U
#define LPDDR4__PHY_WDQ_OSC_DELTA_1__REG DENALI_PHY_366
#define LPDDR4__PHY_WDQ_OSC_DELTA_1__FLD LPDDR4__DENALI_PHY_366__PHY_WDQ_OSC_DELTA_1

#define LPDDR4__DENALI_PHY_366__PHY_MEAS_DLY_STEP_ENABLE_1_MASK      0x00007F00U
#define LPDDR4__DENALI_PHY_366__PHY_MEAS_DLY_STEP_ENABLE_1_SHIFT              8U
#define LPDDR4__DENALI_PHY_366__PHY_MEAS_DLY_STEP_ENABLE_1_WIDTH              7U
#define LPDDR4__PHY_MEAS_DLY_STEP_ENABLE_1__REG DENALI_PHY_366
#define LPDDR4__PHY_MEAS_DLY_STEP_ENABLE_1__FLD LPDDR4__DENALI_PHY_366__PHY_MEAS_DLY_STEP_ENABLE_1

#define LPDDR4__DENALI_PHY_366__PHY_RDDATA_EN_DLY_1_MASK             0x001F0000U
#define LPDDR4__DENALI_PHY_366__PHY_RDDATA_EN_DLY_1_SHIFT                    16U
#define LPDDR4__DENALI_PHY_366__PHY_RDDATA_EN_DLY_1_WIDTH                     5U
#define LPDDR4__PHY_RDDATA_EN_DLY_1__REG DENALI_PHY_366
#define LPDDR4__PHY_RDDATA_EN_DLY_1__FLD LPDDR4__DENALI_PHY_366__PHY_RDDATA_EN_DLY_1

#define LPDDR4__DENALI_PHY_367_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_367_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_367__PHY_DQ_DM_SWIZZLE0_1_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_367__PHY_DQ_DM_SWIZZLE0_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_367__PHY_DQ_DM_SWIZZLE0_1_WIDTH                   32U
#define LPDDR4__PHY_DQ_DM_SWIZZLE0_1__REG DENALI_PHY_367
#define LPDDR4__PHY_DQ_DM_SWIZZLE0_1__FLD LPDDR4__DENALI_PHY_367__PHY_DQ_DM_SWIZZLE0_1

#define LPDDR4__DENALI_PHY_368_READ_MASK                             0x0000000FU
#define LPDDR4__DENALI_PHY_368_WRITE_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_368__PHY_DQ_DM_SWIZZLE1_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PHY_368__PHY_DQ_DM_SWIZZLE1_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_368__PHY_DQ_DM_SWIZZLE1_1_WIDTH                    4U
#define LPDDR4__PHY_DQ_DM_SWIZZLE1_1__REG DENALI_PHY_368
#define LPDDR4__PHY_DQ_DM_SWIZZLE1_1__FLD LPDDR4__DENALI_PHY_368__PHY_DQ_DM_SWIZZLE1_1

#define LPDDR4__DENALI_PHY_369_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_369_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_369__PHY_CLK_WRDQ0_SLAVE_DELAY_1_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_369__PHY_CLK_WRDQ0_SLAVE_DELAY_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_369__PHY_CLK_WRDQ0_SLAVE_DELAY_1_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ0_SLAVE_DELAY_1__REG DENALI_PHY_369
#define LPDDR4__PHY_CLK_WRDQ0_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_369__PHY_CLK_WRDQ0_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_369__PHY_CLK_WRDQ1_SLAVE_DELAY_1_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_369__PHY_CLK_WRDQ1_SLAVE_DELAY_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_369__PHY_CLK_WRDQ1_SLAVE_DELAY_1_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ1_SLAVE_DELAY_1__REG DENALI_PHY_369
#define LPDDR4__PHY_CLK_WRDQ1_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_369__PHY_CLK_WRDQ1_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_370_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_370_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_370__PHY_CLK_WRDQ2_SLAVE_DELAY_1_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_370__PHY_CLK_WRDQ2_SLAVE_DELAY_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_370__PHY_CLK_WRDQ2_SLAVE_DELAY_1_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ2_SLAVE_DELAY_1__REG DENALI_PHY_370
#define LPDDR4__PHY_CLK_WRDQ2_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_370__PHY_CLK_WRDQ2_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_370__PHY_CLK_WRDQ3_SLAVE_DELAY_1_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_370__PHY_CLK_WRDQ3_SLAVE_DELAY_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_370__PHY_CLK_WRDQ3_SLAVE_DELAY_1_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ3_SLAVE_DELAY_1__REG DENALI_PHY_370
#define LPDDR4__PHY_CLK_WRDQ3_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_370__PHY_CLK_WRDQ3_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_371_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_371_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_371__PHY_CLK_WRDQ4_SLAVE_DELAY_1_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_371__PHY_CLK_WRDQ4_SLAVE_DELAY_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_371__PHY_CLK_WRDQ4_SLAVE_DELAY_1_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ4_SLAVE_DELAY_1__REG DENALI_PHY_371
#define LPDDR4__PHY_CLK_WRDQ4_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_371__PHY_CLK_WRDQ4_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_371__PHY_CLK_WRDQ5_SLAVE_DELAY_1_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_371__PHY_CLK_WRDQ5_SLAVE_DELAY_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_371__PHY_CLK_WRDQ5_SLAVE_DELAY_1_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ5_SLAVE_DELAY_1__REG DENALI_PHY_371
#define LPDDR4__PHY_CLK_WRDQ5_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_371__PHY_CLK_WRDQ5_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_372_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_372_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_372__PHY_CLK_WRDQ6_SLAVE_DELAY_1_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_372__PHY_CLK_WRDQ6_SLAVE_DELAY_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_372__PHY_CLK_WRDQ6_SLAVE_DELAY_1_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ6_SLAVE_DELAY_1__REG DENALI_PHY_372
#define LPDDR4__PHY_CLK_WRDQ6_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_372__PHY_CLK_WRDQ6_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_372__PHY_CLK_WRDQ7_SLAVE_DELAY_1_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_372__PHY_CLK_WRDQ7_SLAVE_DELAY_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_372__PHY_CLK_WRDQ7_SLAVE_DELAY_1_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ7_SLAVE_DELAY_1__REG DENALI_PHY_372
#define LPDDR4__PHY_CLK_WRDQ7_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_372__PHY_CLK_WRDQ7_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_373_READ_MASK                             0x03FF07FFU
#define LPDDR4__DENALI_PHY_373_WRITE_MASK                            0x03FF07FFU
#define LPDDR4__DENALI_PHY_373__PHY_CLK_WRDM_SLAVE_DELAY_1_MASK      0x000007FFU
#define LPDDR4__DENALI_PHY_373__PHY_CLK_WRDM_SLAVE_DELAY_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_373__PHY_CLK_WRDM_SLAVE_DELAY_1_WIDTH             11U
#define LPDDR4__PHY_CLK_WRDM_SLAVE_DELAY_1__REG DENALI_PHY_373
#define LPDDR4__PHY_CLK_WRDM_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_373__PHY_CLK_WRDM_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_373__PHY_CLK_WRDQS_SLAVE_DELAY_1_MASK     0x03FF0000U
#define LPDDR4__DENALI_PHY_373__PHY_CLK_WRDQS_SLAVE_DELAY_1_SHIFT            16U
#define LPDDR4__DENALI_PHY_373__PHY_CLK_WRDQS_SLAVE_DELAY_1_WIDTH            10U
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_1__REG DENALI_PHY_373
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_373__PHY_CLK_WRDQS_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_374_READ_MASK                             0x0003FF03U
#define LPDDR4__DENALI_PHY_374_WRITE_MASK                            0x0003FF03U
#define LPDDR4__DENALI_PHY_374__PHY_WRLVL_THRESHOLD_ADJUST_1_MASK    0x00000003U
#define LPDDR4__DENALI_PHY_374__PHY_WRLVL_THRESHOLD_ADJUST_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_374__PHY_WRLVL_THRESHOLD_ADJUST_1_WIDTH            2U
#define LPDDR4__PHY_WRLVL_THRESHOLD_ADJUST_1__REG DENALI_PHY_374
#define LPDDR4__PHY_WRLVL_THRESHOLD_ADJUST_1__FLD LPDDR4__DENALI_PHY_374__PHY_WRLVL_THRESHOLD_ADJUST_1

#define LPDDR4__DENALI_PHY_374__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_1_MASK 0x0003FF00U
#define LPDDR4__DENALI_PHY_374__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_1_SHIFT        8U
#define LPDDR4__DENALI_PHY_374__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_1__REG DENALI_PHY_374
#define LPDDR4__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_374__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_375_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_375_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_375__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_375__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_375__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_1__REG DENALI_PHY_375
#define LPDDR4__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_375__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_375__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_375__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_375__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_1__REG DENALI_PHY_375
#define LPDDR4__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_375__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_376_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_376_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_376__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_376__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_376__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_1__REG DENALI_PHY_376
#define LPDDR4__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_376__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_376__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_376__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_376__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_1__REG DENALI_PHY_376
#define LPDDR4__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_376__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_377_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_377_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_377__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_377__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_377__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_1__REG DENALI_PHY_377
#define LPDDR4__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_377__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_377__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_377__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_377__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_1__REG DENALI_PHY_377
#define LPDDR4__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_377__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_378_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_378_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_378__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_378__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_378__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_1__REG DENALI_PHY_378
#define LPDDR4__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_378__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_378__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_378__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_378__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_1__REG DENALI_PHY_378
#define LPDDR4__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_378__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_379_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_379_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_379__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_379__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_379__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_1__REG DENALI_PHY_379
#define LPDDR4__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_379__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_379__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_379__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_379__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_1__REG DENALI_PHY_379
#define LPDDR4__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_379__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_380_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_380_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_380__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_380__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_380__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_1__REG DENALI_PHY_380
#define LPDDR4__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_380__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_380__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_380__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_380__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_1__REG DENALI_PHY_380
#define LPDDR4__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_380__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_381_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_381_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_381__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_381__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_381__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_1__REG DENALI_PHY_381
#define LPDDR4__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_381__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_381__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_381__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_381__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_1__REG DENALI_PHY_381
#define LPDDR4__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_381__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_382_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_382_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_382__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_382__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_382__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_1_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_1__REG DENALI_PHY_382
#define LPDDR4__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_382__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_382__PHY_RDDQS_DM_RISE_SLAVE_DELAY_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_382__PHY_RDDQS_DM_RISE_SLAVE_DELAY_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_382__PHY_RDDQS_DM_RISE_SLAVE_DELAY_1_WIDTH        10U
#define LPDDR4__PHY_RDDQS_DM_RISE_SLAVE_DELAY_1__REG DENALI_PHY_382
#define LPDDR4__PHY_RDDQS_DM_RISE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_382__PHY_RDDQS_DM_RISE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_383_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_383_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_383__PHY_RDDQS_DM_FALL_SLAVE_DELAY_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_383__PHY_RDDQS_DM_FALL_SLAVE_DELAY_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_383__PHY_RDDQS_DM_FALL_SLAVE_DELAY_1_WIDTH        10U
#define LPDDR4__PHY_RDDQS_DM_FALL_SLAVE_DELAY_1__REG DENALI_PHY_383
#define LPDDR4__PHY_RDDQS_DM_FALL_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_383__PHY_RDDQS_DM_FALL_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_383__PHY_RDDQS_GATE_SLAVE_DELAY_1_MASK    0x03FF0000U
#define LPDDR4__DENALI_PHY_383__PHY_RDDQS_GATE_SLAVE_DELAY_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_383__PHY_RDDQS_GATE_SLAVE_DELAY_1_WIDTH           10U
#define LPDDR4__PHY_RDDQS_GATE_SLAVE_DELAY_1__REG DENALI_PHY_383
#define LPDDR4__PHY_RDDQS_GATE_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_383__PHY_RDDQS_GATE_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_384_READ_MASK                             0x03FF070FU
#define LPDDR4__DENALI_PHY_384_WRITE_MASK                            0x03FF070FU
#define LPDDR4__DENALI_PHY_384__PHY_RDDQS_LATENCY_ADJUST_1_MASK      0x0000000FU
#define LPDDR4__DENALI_PHY_384__PHY_RDDQS_LATENCY_ADJUST_1_SHIFT              0U
#define LPDDR4__DENALI_PHY_384__PHY_RDDQS_LATENCY_ADJUST_1_WIDTH              4U
#define LPDDR4__PHY_RDDQS_LATENCY_ADJUST_1__REG DENALI_PHY_384
#define LPDDR4__PHY_RDDQS_LATENCY_ADJUST_1__FLD LPDDR4__DENALI_PHY_384__PHY_RDDQS_LATENCY_ADJUST_1

#define LPDDR4__DENALI_PHY_384__PHY_WRITE_PATH_LAT_ADD_1_MASK        0x00000700U
#define LPDDR4__DENALI_PHY_384__PHY_WRITE_PATH_LAT_ADD_1_SHIFT                8U
#define LPDDR4__DENALI_PHY_384__PHY_WRITE_PATH_LAT_ADD_1_WIDTH                3U
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_1__REG DENALI_PHY_384
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_1__FLD LPDDR4__DENALI_PHY_384__PHY_WRITE_PATH_LAT_ADD_1

#define LPDDR4__DENALI_PHY_384__PHY_WRLVL_DELAY_EARLY_THRESHOLD_1_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_384__PHY_WRLVL_DELAY_EARLY_THRESHOLD_1_SHIFT      16U
#define LPDDR4__DENALI_PHY_384__PHY_WRLVL_DELAY_EARLY_THRESHOLD_1_WIDTH      10U
#define LPDDR4__PHY_WRLVL_DELAY_EARLY_THRESHOLD_1__REG DENALI_PHY_384
#define LPDDR4__PHY_WRLVL_DELAY_EARLY_THRESHOLD_1__FLD LPDDR4__DENALI_PHY_384__PHY_WRLVL_DELAY_EARLY_THRESHOLD_1

#define LPDDR4__DENALI_PHY_385_READ_MASK                             0x000103FFU
#define LPDDR4__DENALI_PHY_385_WRITE_MASK                            0x000103FFU
#define LPDDR4__DENALI_PHY_385__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_385__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_1_SHIFT      0U
#define LPDDR4__DENALI_PHY_385__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_1_WIDTH     10U
#define LPDDR4__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_1__REG DENALI_PHY_385
#define LPDDR4__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_1__FLD LPDDR4__DENALI_PHY_385__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_1

#define LPDDR4__DENALI_PHY_385__PHY_WRLVL_EARLY_FORCE_ZERO_1_MASK    0x00010000U
#define LPDDR4__DENALI_PHY_385__PHY_WRLVL_EARLY_FORCE_ZERO_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_385__PHY_WRLVL_EARLY_FORCE_ZERO_1_WIDTH            1U
#define LPDDR4__DENALI_PHY_385__PHY_WRLVL_EARLY_FORCE_ZERO_1_WOCLR            0U
#define LPDDR4__DENALI_PHY_385__PHY_WRLVL_EARLY_FORCE_ZERO_1_WOSET            0U
#define LPDDR4__PHY_WRLVL_EARLY_FORCE_ZERO_1__REG DENALI_PHY_385
#define LPDDR4__PHY_WRLVL_EARLY_FORCE_ZERO_1__FLD LPDDR4__DENALI_PHY_385__PHY_WRLVL_EARLY_FORCE_ZERO_1

#define LPDDR4__DENALI_PHY_386_READ_MASK                             0x000F03FFU
#define LPDDR4__DENALI_PHY_386_WRITE_MASK                            0x000F03FFU
#define LPDDR4__DENALI_PHY_386__PHY_GTLVL_RDDQS_SLV_DLY_START_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_386__PHY_GTLVL_RDDQS_SLV_DLY_START_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_386__PHY_GTLVL_RDDQS_SLV_DLY_START_1_WIDTH        10U
#define LPDDR4__PHY_GTLVL_RDDQS_SLV_DLY_START_1__REG DENALI_PHY_386
#define LPDDR4__PHY_GTLVL_RDDQS_SLV_DLY_START_1__FLD LPDDR4__DENALI_PHY_386__PHY_GTLVL_RDDQS_SLV_DLY_START_1

#define LPDDR4__DENALI_PHY_386__PHY_GTLVL_LAT_ADJ_START_1_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_386__PHY_GTLVL_LAT_ADJ_START_1_SHIFT              16U
#define LPDDR4__DENALI_PHY_386__PHY_GTLVL_LAT_ADJ_START_1_WIDTH               4U
#define LPDDR4__PHY_GTLVL_LAT_ADJ_START_1__REG DENALI_PHY_386
#define LPDDR4__PHY_GTLVL_LAT_ADJ_START_1__FLD LPDDR4__DENALI_PHY_386__PHY_GTLVL_LAT_ADJ_START_1

#define LPDDR4__DENALI_PHY_387_READ_MASK                             0x010F07FFU
#define LPDDR4__DENALI_PHY_387_WRITE_MASK                            0x010F07FFU
#define LPDDR4__DENALI_PHY_387__PHY_WDQLVL_DQDM_SLV_DLY_START_1_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_387__PHY_WDQLVL_DQDM_SLV_DLY_START_1_SHIFT         0U
#define LPDDR4__DENALI_PHY_387__PHY_WDQLVL_DQDM_SLV_DLY_START_1_WIDTH        11U
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_START_1__REG DENALI_PHY_387
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_START_1__FLD LPDDR4__DENALI_PHY_387__PHY_WDQLVL_DQDM_SLV_DLY_START_1

#define LPDDR4__DENALI_PHY_387__PHY_NTP_WRLAT_START_1_MASK           0x000F0000U
#define LPDDR4__DENALI_PHY_387__PHY_NTP_WRLAT_START_1_SHIFT                  16U
#define LPDDR4__DENALI_PHY_387__PHY_NTP_WRLAT_START_1_WIDTH                   4U
#define LPDDR4__PHY_NTP_WRLAT_START_1__REG DENALI_PHY_387
#define LPDDR4__PHY_NTP_WRLAT_START_1__FLD LPDDR4__DENALI_PHY_387__PHY_NTP_WRLAT_START_1

#define LPDDR4__DENALI_PHY_387__PHY_NTP_PASS_1_MASK                  0x01000000U
#define LPDDR4__DENALI_PHY_387__PHY_NTP_PASS_1_SHIFT                         24U
#define LPDDR4__DENALI_PHY_387__PHY_NTP_PASS_1_WIDTH                          1U
#define LPDDR4__DENALI_PHY_387__PHY_NTP_PASS_1_WOCLR                          0U
#define LPDDR4__DENALI_PHY_387__PHY_NTP_PASS_1_WOSET                          0U
#define LPDDR4__PHY_NTP_PASS_1__REG DENALI_PHY_387
#define LPDDR4__PHY_NTP_PASS_1__FLD LPDDR4__DENALI_PHY_387__PHY_NTP_PASS_1

#define LPDDR4__DENALI_PHY_388_READ_MASK                             0x000003FFU
#define LPDDR4__DENALI_PHY_388_WRITE_MASK                            0x000003FFU
#define LPDDR4__DENALI_PHY_388__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_1_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_388__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_1_SHIFT      0U
#define LPDDR4__DENALI_PHY_388__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_1_WIDTH     10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_1__REG DENALI_PHY_388
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_1__FLD LPDDR4__DENALI_PHY_388__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_1

#define LPDDR4__DENALI_PHY_389_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_389_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQS_CLK_ADJUST_1_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQS_CLK_ADJUST_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQS_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQS_CLK_ADJUST_1__REG DENALI_PHY_389
#define LPDDR4__PHY_DATA_DC_DQS_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQS_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ0_CLK_ADJUST_1_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ0_CLK_ADJUST_1_SHIFT            8U
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ0_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ0_CLK_ADJUST_1__REG DENALI_PHY_389
#define LPDDR4__PHY_DATA_DC_DQ0_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ0_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ1_CLK_ADJUST_1_MASK    0x00FF0000U
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ1_CLK_ADJUST_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ1_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ1_CLK_ADJUST_1__REG DENALI_PHY_389
#define LPDDR4__PHY_DATA_DC_DQ1_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ1_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ2_CLK_ADJUST_1_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ2_CLK_ADJUST_1_SHIFT           24U
#define LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ2_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ2_CLK_ADJUST_1__REG DENALI_PHY_389
#define LPDDR4__PHY_DATA_DC_DQ2_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_389__PHY_DATA_DC_DQ2_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_390_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_390_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ3_CLK_ADJUST_1_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ3_CLK_ADJUST_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ3_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ3_CLK_ADJUST_1__REG DENALI_PHY_390
#define LPDDR4__PHY_DATA_DC_DQ3_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ3_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ4_CLK_ADJUST_1_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ4_CLK_ADJUST_1_SHIFT            8U
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ4_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ4_CLK_ADJUST_1__REG DENALI_PHY_390
#define LPDDR4__PHY_DATA_DC_DQ4_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ4_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ5_CLK_ADJUST_1_MASK    0x00FF0000U
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ5_CLK_ADJUST_1_SHIFT           16U
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ5_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ5_CLK_ADJUST_1__REG DENALI_PHY_390
#define LPDDR4__PHY_DATA_DC_DQ5_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ5_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ6_CLK_ADJUST_1_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ6_CLK_ADJUST_1_SHIFT           24U
#define LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ6_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ6_CLK_ADJUST_1__REG DENALI_PHY_390
#define LPDDR4__PHY_DATA_DC_DQ6_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_390__PHY_DATA_DC_DQ6_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_391_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_391_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_391__PHY_DATA_DC_DQ7_CLK_ADJUST_1_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_391__PHY_DATA_DC_DQ7_CLK_ADJUST_1_SHIFT            0U
#define LPDDR4__DENALI_PHY_391__PHY_DATA_DC_DQ7_CLK_ADJUST_1_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ7_CLK_ADJUST_1__REG DENALI_PHY_391
#define LPDDR4__PHY_DATA_DC_DQ7_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_391__PHY_DATA_DC_DQ7_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_391__PHY_DATA_DC_DM_CLK_ADJUST_1_MASK     0x0000FF00U
#define LPDDR4__DENALI_PHY_391__PHY_DATA_DC_DM_CLK_ADJUST_1_SHIFT             8U
#define LPDDR4__DENALI_PHY_391__PHY_DATA_DC_DM_CLK_ADJUST_1_WIDTH             8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_ADJUST_1__REG DENALI_PHY_391
#define LPDDR4__PHY_DATA_DC_DM_CLK_ADJUST_1__FLD LPDDR4__DENALI_PHY_391__PHY_DATA_DC_DM_CLK_ADJUST_1

#define LPDDR4__DENALI_PHY_391__PHY_DSLICE_PAD_BOOSTPN_SETTING_1_MASK 0xFFFF0000U
#define LPDDR4__DENALI_PHY_391__PHY_DSLICE_PAD_BOOSTPN_SETTING_1_SHIFT       16U
#define LPDDR4__DENALI_PHY_391__PHY_DSLICE_PAD_BOOSTPN_SETTING_1_WIDTH       16U
#define LPDDR4__PHY_DSLICE_PAD_BOOSTPN_SETTING_1__REG DENALI_PHY_391
#define LPDDR4__PHY_DSLICE_PAD_BOOSTPN_SETTING_1__FLD LPDDR4__DENALI_PHY_391__PHY_DSLICE_PAD_BOOSTPN_SETTING_1

#define LPDDR4__DENALI_PHY_392_READ_MASK                             0x0003033FU
#define LPDDR4__DENALI_PHY_392_WRITE_MASK                            0x0003033FU
#define LPDDR4__DENALI_PHY_392__PHY_DSLICE_PAD_RX_CTLE_SETTING_1_MASK 0x0000003FU
#define LPDDR4__DENALI_PHY_392__PHY_DSLICE_PAD_RX_CTLE_SETTING_1_SHIFT        0U
#define LPDDR4__DENALI_PHY_392__PHY_DSLICE_PAD_RX_CTLE_SETTING_1_WIDTH        6U
#define LPDDR4__PHY_DSLICE_PAD_RX_CTLE_SETTING_1__REG DENALI_PHY_392
#define LPDDR4__PHY_DSLICE_PAD_RX_CTLE_SETTING_1__FLD LPDDR4__DENALI_PHY_392__PHY_DSLICE_PAD_RX_CTLE_SETTING_1

#define LPDDR4__DENALI_PHY_392__PHY_DQ_FFE_1_MASK                    0x00000300U
#define LPDDR4__DENALI_PHY_392__PHY_DQ_FFE_1_SHIFT                            8U
#define LPDDR4__DENALI_PHY_392__PHY_DQ_FFE_1_WIDTH                            2U
#define LPDDR4__PHY_DQ_FFE_1__REG DENALI_PHY_392
#define LPDDR4__PHY_DQ_FFE_1__FLD LPDDR4__DENALI_PHY_392__PHY_DQ_FFE_1

#define LPDDR4__DENALI_PHY_392__PHY_DQS_FFE_1_MASK                   0x00030000U
#define LPDDR4__DENALI_PHY_392__PHY_DQS_FFE_1_SHIFT                          16U
#define LPDDR4__DENALI_PHY_392__PHY_DQS_FFE_1_WIDTH                           2U
#define LPDDR4__PHY_DQS_FFE_1__REG DENALI_PHY_392
#define LPDDR4__PHY_DQS_FFE_1__FLD LPDDR4__DENALI_PHY_392__PHY_DQS_FFE_1

#endif /* REG_LPDDR4_DATA_SLICE_1_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

