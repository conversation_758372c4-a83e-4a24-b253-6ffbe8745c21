/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_DATA_SLICE_0_MACROS_H_
#define REG_LPDDR4_DATA_SLICE_0_MACROS_H_

#define LPDDR4__DENALI_PHY_0_READ_MASK                               0x07FF7F07U
#define LPDDR4__DENALI_PHY_0_WRITE_MASK                              0x07FF7F07U
#define LPDDR4__DENALI_PHY_0__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_0_MASK    0x00000007U
#define LPDDR4__DENALI_PHY_0__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_0__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_0_WIDTH            3U
#define LPDDR4__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_0__REG DENALI_PHY_0
#define LPDDR4__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_0__FLD LPDDR4__DENALI_PHY_0__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_0

#define LPDDR4__DENALI_PHY_0__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_0_MASK  0x00007F00U
#define LPDDR4__DENALI_PHY_0__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_0_SHIFT          8U
#define LPDDR4__DENALI_PHY_0__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_0_WIDTH          7U
#define LPDDR4__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_0__REG DENALI_PHY_0
#define LPDDR4__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_0__FLD LPDDR4__DENALI_PHY_0__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_0

#define LPDDR4__DENALI_PHY_0__PHY_CLK_WR_BYPASS_SLAVE_DELAY_0_MASK   0x07FF0000U
#define LPDDR4__DENALI_PHY_0__PHY_CLK_WR_BYPASS_SLAVE_DELAY_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_0__PHY_CLK_WR_BYPASS_SLAVE_DELAY_0_WIDTH          11U
#define LPDDR4__PHY_CLK_WR_BYPASS_SLAVE_DELAY_0__REG DENALI_PHY_0
#define LPDDR4__PHY_CLK_WR_BYPASS_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_0__PHY_CLK_WR_BYPASS_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1_READ_MASK                               0x0703FF0FU
#define LPDDR4__DENALI_PHY_1_WRITE_MASK                              0x0703FF0FU
#define LPDDR4__DENALI_PHY_1__PHY_IO_PAD_DELAY_TIMING_BYPASS_0_MASK  0x0000000FU
#define LPDDR4__DENALI_PHY_1__PHY_IO_PAD_DELAY_TIMING_BYPASS_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_1__PHY_IO_PAD_DELAY_TIMING_BYPASS_0_WIDTH          4U
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_BYPASS_0__REG DENALI_PHY_1
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_BYPASS_0__FLD LPDDR4__DENALI_PHY_1__PHY_IO_PAD_DELAY_TIMING_BYPASS_0

#define LPDDR4__DENALI_PHY_1__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_0_MASK 0x0003FF00U
#define LPDDR4__DENALI_PHY_1__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_0_SHIFT        8U
#define LPDDR4__DENALI_PHY_1__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_0_WIDTH       10U
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_0__REG DENALI_PHY_1
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_0__FLD LPDDR4__DENALI_PHY_1__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_0

#define LPDDR4__DENALI_PHY_1__PHY_WRITE_PATH_LAT_ADD_BYPASS_0_MASK   0x07000000U
#define LPDDR4__DENALI_PHY_1__PHY_WRITE_PATH_LAT_ADD_BYPASS_0_SHIFT          24U
#define LPDDR4__DENALI_PHY_1__PHY_WRITE_PATH_LAT_ADD_BYPASS_0_WIDTH           3U
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_BYPASS_0__REG DENALI_PHY_1
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_BYPASS_0__FLD LPDDR4__DENALI_PHY_1__PHY_WRITE_PATH_LAT_ADD_BYPASS_0

#define LPDDR4__DENALI_PHY_2_READ_MASK                               0x010303FFU
#define LPDDR4__DENALI_PHY_2_WRITE_MASK                              0x010303FFU
#define LPDDR4__DENALI_PHY_2__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_2__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_0_SHIFT       0U
#define LPDDR4__DENALI_PHY_2__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_0_WIDTH      10U
#define LPDDR4__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_0__REG DENALI_PHY_2
#define LPDDR4__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_2__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_2__PHY_BYPASS_TWO_CYC_PREAMBLE_0_MASK     0x00030000U
#define LPDDR4__DENALI_PHY_2__PHY_BYPASS_TWO_CYC_PREAMBLE_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_2__PHY_BYPASS_TWO_CYC_PREAMBLE_0_WIDTH             2U
#define LPDDR4__PHY_BYPASS_TWO_CYC_PREAMBLE_0__REG DENALI_PHY_2
#define LPDDR4__PHY_BYPASS_TWO_CYC_PREAMBLE_0__FLD LPDDR4__DENALI_PHY_2__PHY_BYPASS_TWO_CYC_PREAMBLE_0

#define LPDDR4__DENALI_PHY_2__PHY_CLK_BYPASS_OVERRIDE_0_MASK         0x01000000U
#define LPDDR4__DENALI_PHY_2__PHY_CLK_BYPASS_OVERRIDE_0_SHIFT                24U
#define LPDDR4__DENALI_PHY_2__PHY_CLK_BYPASS_OVERRIDE_0_WIDTH                 1U
#define LPDDR4__DENALI_PHY_2__PHY_CLK_BYPASS_OVERRIDE_0_WOCLR                 0U
#define LPDDR4__DENALI_PHY_2__PHY_CLK_BYPASS_OVERRIDE_0_WOSET                 0U
#define LPDDR4__PHY_CLK_BYPASS_OVERRIDE_0__REG DENALI_PHY_2
#define LPDDR4__PHY_CLK_BYPASS_OVERRIDE_0__FLD LPDDR4__DENALI_PHY_2__PHY_CLK_BYPASS_OVERRIDE_0

#define LPDDR4__DENALI_PHY_3_READ_MASK                               0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_3_WRITE_MASK                              0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ0_SHIFT_0_MASK              0x0000003FU
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ0_SHIFT_0_SHIFT                      0U
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ0_SHIFT_0_WIDTH                      6U
#define LPDDR4__PHY_SW_WRDQ0_SHIFT_0__REG DENALI_PHY_3
#define LPDDR4__PHY_SW_WRDQ0_SHIFT_0__FLD LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ0_SHIFT_0

#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ1_SHIFT_0_MASK              0x00003F00U
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ1_SHIFT_0_SHIFT                      8U
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ1_SHIFT_0_WIDTH                      6U
#define LPDDR4__PHY_SW_WRDQ1_SHIFT_0__REG DENALI_PHY_3
#define LPDDR4__PHY_SW_WRDQ1_SHIFT_0__FLD LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ1_SHIFT_0

#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ2_SHIFT_0_MASK              0x003F0000U
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ2_SHIFT_0_SHIFT                     16U
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ2_SHIFT_0_WIDTH                      6U
#define LPDDR4__PHY_SW_WRDQ2_SHIFT_0__REG DENALI_PHY_3
#define LPDDR4__PHY_SW_WRDQ2_SHIFT_0__FLD LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ2_SHIFT_0

#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ3_SHIFT_0_MASK              0x3F000000U
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ3_SHIFT_0_SHIFT                     24U
#define LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ3_SHIFT_0_WIDTH                      6U
#define LPDDR4__PHY_SW_WRDQ3_SHIFT_0__REG DENALI_PHY_3
#define LPDDR4__PHY_SW_WRDQ3_SHIFT_0__FLD LPDDR4__DENALI_PHY_3__PHY_SW_WRDQ3_SHIFT_0

#define LPDDR4__DENALI_PHY_4_READ_MASK                               0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_4_WRITE_MASK                              0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ4_SHIFT_0_MASK              0x0000003FU
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ4_SHIFT_0_SHIFT                      0U
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ4_SHIFT_0_WIDTH                      6U
#define LPDDR4__PHY_SW_WRDQ4_SHIFT_0__REG DENALI_PHY_4
#define LPDDR4__PHY_SW_WRDQ4_SHIFT_0__FLD LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ4_SHIFT_0

#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ5_SHIFT_0_MASK              0x00003F00U
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ5_SHIFT_0_SHIFT                      8U
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ5_SHIFT_0_WIDTH                      6U
#define LPDDR4__PHY_SW_WRDQ5_SHIFT_0__REG DENALI_PHY_4
#define LPDDR4__PHY_SW_WRDQ5_SHIFT_0__FLD LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ5_SHIFT_0

#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ6_SHIFT_0_MASK              0x003F0000U
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ6_SHIFT_0_SHIFT                     16U
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ6_SHIFT_0_WIDTH                      6U
#define LPDDR4__PHY_SW_WRDQ6_SHIFT_0__REG DENALI_PHY_4
#define LPDDR4__PHY_SW_WRDQ6_SHIFT_0__FLD LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ6_SHIFT_0

#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ7_SHIFT_0_MASK              0x3F000000U
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ7_SHIFT_0_SHIFT                     24U
#define LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ7_SHIFT_0_WIDTH                      6U
#define LPDDR4__PHY_SW_WRDQ7_SHIFT_0__REG DENALI_PHY_4
#define LPDDR4__PHY_SW_WRDQ7_SHIFT_0__FLD LPDDR4__DENALI_PHY_4__PHY_SW_WRDQ7_SHIFT_0

#define LPDDR4__DENALI_PHY_5_READ_MASK                               0x01030F3FU
#define LPDDR4__DENALI_PHY_5_WRITE_MASK                              0x01030F3FU
#define LPDDR4__DENALI_PHY_5__PHY_SW_WRDM_SHIFT_0_MASK               0x0000003FU
#define LPDDR4__DENALI_PHY_5__PHY_SW_WRDM_SHIFT_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_5__PHY_SW_WRDM_SHIFT_0_WIDTH                       6U
#define LPDDR4__PHY_SW_WRDM_SHIFT_0__REG DENALI_PHY_5
#define LPDDR4__PHY_SW_WRDM_SHIFT_0__FLD LPDDR4__DENALI_PHY_5__PHY_SW_WRDM_SHIFT_0

#define LPDDR4__DENALI_PHY_5__PHY_SW_WRDQS_SHIFT_0_MASK              0x00000F00U
#define LPDDR4__DENALI_PHY_5__PHY_SW_WRDQS_SHIFT_0_SHIFT                      8U
#define LPDDR4__DENALI_PHY_5__PHY_SW_WRDQS_SHIFT_0_WIDTH                      4U
#define LPDDR4__PHY_SW_WRDQS_SHIFT_0__REG DENALI_PHY_5
#define LPDDR4__PHY_SW_WRDQS_SHIFT_0__FLD LPDDR4__DENALI_PHY_5__PHY_SW_WRDQS_SHIFT_0

#define LPDDR4__DENALI_PHY_5__PHY_PER_RANK_CS_MAP_0_MASK             0x00030000U
#define LPDDR4__DENALI_PHY_5__PHY_PER_RANK_CS_MAP_0_SHIFT                    16U
#define LPDDR4__DENALI_PHY_5__PHY_PER_RANK_CS_MAP_0_WIDTH                     2U
#define LPDDR4__PHY_PER_RANK_CS_MAP_0__REG DENALI_PHY_5
#define LPDDR4__PHY_PER_RANK_CS_MAP_0__FLD LPDDR4__DENALI_PHY_5__PHY_PER_RANK_CS_MAP_0

#define LPDDR4__DENALI_PHY_5__PHY_PER_CS_TRAINING_MULTICAST_EN_0_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_5__PHY_PER_CS_TRAINING_MULTICAST_EN_0_SHIFT       24U
#define LPDDR4__DENALI_PHY_5__PHY_PER_CS_TRAINING_MULTICAST_EN_0_WIDTH        1U
#define LPDDR4__DENALI_PHY_5__PHY_PER_CS_TRAINING_MULTICAST_EN_0_WOCLR        0U
#define LPDDR4__DENALI_PHY_5__PHY_PER_CS_TRAINING_MULTICAST_EN_0_WOSET        0U
#define LPDDR4__PHY_PER_CS_TRAINING_MULTICAST_EN_0__REG DENALI_PHY_5
#define LPDDR4__PHY_PER_CS_TRAINING_MULTICAST_EN_0__FLD LPDDR4__DENALI_PHY_5__PHY_PER_CS_TRAINING_MULTICAST_EN_0

#define LPDDR4__DENALI_PHY_6_READ_MASK                               0x1F1F0301U
#define LPDDR4__DENALI_PHY_6_WRITE_MASK                              0x1F1F0301U
#define LPDDR4__DENALI_PHY_6__PHY_PER_CS_TRAINING_INDEX_0_MASK       0x00000001U
#define LPDDR4__DENALI_PHY_6__PHY_PER_CS_TRAINING_INDEX_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_6__PHY_PER_CS_TRAINING_INDEX_0_WIDTH               1U
#define LPDDR4__DENALI_PHY_6__PHY_PER_CS_TRAINING_INDEX_0_WOCLR               0U
#define LPDDR4__DENALI_PHY_6__PHY_PER_CS_TRAINING_INDEX_0_WOSET               0U
#define LPDDR4__PHY_PER_CS_TRAINING_INDEX_0__REG DENALI_PHY_6
#define LPDDR4__PHY_PER_CS_TRAINING_INDEX_0__FLD LPDDR4__DENALI_PHY_6__PHY_PER_CS_TRAINING_INDEX_0

#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_0_MASK   0x00000300U
#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_0_WIDTH           2U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_0__REG DENALI_PHY_6
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_0__FLD LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_0

#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_DLY_0_MASK      0x001F0000U
#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_DLY_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_DLY_0_WIDTH              5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_DLY_0__REG DENALI_PHY_6
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_DLY_0__FLD LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_DLY_0

#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_0_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_0_SHIFT        24U
#define LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_0_WIDTH         5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_0__REG DENALI_PHY_6
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_0__FLD LPDDR4__DENALI_PHY_6__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_0

#define LPDDR4__DENALI_PHY_7_READ_MASK                               0x1F030F0FU
#define LPDDR4__DENALI_PHY_7_WRITE_MASK                              0x1F030F0FU
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RPTR_UPDATE_0_MASK        0x0000000FU
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RPTR_UPDATE_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RPTR_UPDATE_0_WIDTH                4U
#define LPDDR4__PHY_LP4_BOOT_RPTR_UPDATE_0__REG DENALI_PHY_7
#define LPDDR4__PHY_LP4_BOOT_RPTR_UPDATE_0__FLD LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RPTR_UPDATE_0

#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_0_MASK 0x00000F00U
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_0_SHIFT       8U
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_0_WIDTH       4U
#define LPDDR4__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_0__REG DENALI_PHY_7
#define LPDDR4__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_0__FLD LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_0

#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_0_MASK 0x00030000U
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_0_WIDTH        2U
#define LPDDR4__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_0__REG DENALI_PHY_7
#define LPDDR4__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_0__FLD LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_0

#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_0_MASK   0x1F000000U
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_0_SHIFT          24U
#define LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_0_WIDTH           5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_0__REG DENALI_PHY_7
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_0__FLD LPDDR4__DENALI_PHY_7__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_0

#define LPDDR4__DENALI_PHY_8_READ_MASK                               0x0101FF03U
#define LPDDR4__DENALI_PHY_8_WRITE_MASK                              0x0101FF03U
#define LPDDR4__DENALI_PHY_8__PHY_CTRL_LPBK_EN_0_MASK                0x00000003U
#define LPDDR4__DENALI_PHY_8__PHY_CTRL_LPBK_EN_0_SHIFT                        0U
#define LPDDR4__DENALI_PHY_8__PHY_CTRL_LPBK_EN_0_WIDTH                        2U
#define LPDDR4__PHY_CTRL_LPBK_EN_0__REG DENALI_PHY_8
#define LPDDR4__PHY_CTRL_LPBK_EN_0__FLD LPDDR4__DENALI_PHY_8__PHY_CTRL_LPBK_EN_0

#define LPDDR4__DENALI_PHY_8__PHY_LPBK_CONTROL_0_MASK                0x0001FF00U
#define LPDDR4__DENALI_PHY_8__PHY_LPBK_CONTROL_0_SHIFT                        8U
#define LPDDR4__DENALI_PHY_8__PHY_LPBK_CONTROL_0_WIDTH                        9U
#define LPDDR4__PHY_LPBK_CONTROL_0__REG DENALI_PHY_8
#define LPDDR4__PHY_LPBK_CONTROL_0__FLD LPDDR4__DENALI_PHY_8__PHY_LPBK_CONTROL_0

#define LPDDR4__DENALI_PHY_8__PHY_LPBK_DFX_TIMEOUT_EN_0_MASK         0x01000000U
#define LPDDR4__DENALI_PHY_8__PHY_LPBK_DFX_TIMEOUT_EN_0_SHIFT                24U
#define LPDDR4__DENALI_PHY_8__PHY_LPBK_DFX_TIMEOUT_EN_0_WIDTH                 1U
#define LPDDR4__DENALI_PHY_8__PHY_LPBK_DFX_TIMEOUT_EN_0_WOCLR                 0U
#define LPDDR4__DENALI_PHY_8__PHY_LPBK_DFX_TIMEOUT_EN_0_WOSET                 0U
#define LPDDR4__PHY_LPBK_DFX_TIMEOUT_EN_0__REG DENALI_PHY_8
#define LPDDR4__PHY_LPBK_DFX_TIMEOUT_EN_0__FLD LPDDR4__DENALI_PHY_8__PHY_LPBK_DFX_TIMEOUT_EN_0

#define LPDDR4__DENALI_PHY_9_READ_MASK                               0x00000001U
#define LPDDR4__DENALI_PHY_9_WRITE_MASK                              0x00000001U
#define LPDDR4__DENALI_PHY_9__PHY_GATE_DELAY_COMP_DISABLE_0_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_9__PHY_GATE_DELAY_COMP_DISABLE_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_9__PHY_GATE_DELAY_COMP_DISABLE_0_WIDTH             1U
#define LPDDR4__DENALI_PHY_9__PHY_GATE_DELAY_COMP_DISABLE_0_WOCLR             0U
#define LPDDR4__DENALI_PHY_9__PHY_GATE_DELAY_COMP_DISABLE_0_WOSET             0U
#define LPDDR4__PHY_GATE_DELAY_COMP_DISABLE_0__REG DENALI_PHY_9
#define LPDDR4__PHY_GATE_DELAY_COMP_DISABLE_0__FLD LPDDR4__DENALI_PHY_9__PHY_GATE_DELAY_COMP_DISABLE_0

#define LPDDR4__DENALI_PHY_10_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_10_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_10__PHY_AUTO_TIMING_MARGIN_CONTROL_0_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_10__PHY_AUTO_TIMING_MARGIN_CONTROL_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_10__PHY_AUTO_TIMING_MARGIN_CONTROL_0_WIDTH        32U
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_CONTROL_0__REG DENALI_PHY_10
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_CONTROL_0__FLD LPDDR4__DENALI_PHY_10__PHY_AUTO_TIMING_MARGIN_CONTROL_0

#define LPDDR4__DENALI_PHY_11_READ_MASK                              0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_11_WRITE_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_11__PHY_AUTO_TIMING_MARGIN_OBS_0_MASK     0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_11__PHY_AUTO_TIMING_MARGIN_OBS_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_11__PHY_AUTO_TIMING_MARGIN_OBS_0_WIDTH            28U
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_OBS_0__REG DENALI_PHY_11
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_OBS_0__FLD LPDDR4__DENALI_PHY_11__PHY_AUTO_TIMING_MARGIN_OBS_0

#define LPDDR4__DENALI_PHY_12_READ_MASK                              0x7F0101FFU
#define LPDDR4__DENALI_PHY_12_WRITE_MASK                             0x7F0101FFU
#define LPDDR4__DENALI_PHY_12__PHY_DQ_IDLE_0_MASK                    0x000001FFU
#define LPDDR4__DENALI_PHY_12__PHY_DQ_IDLE_0_SHIFT                            0U
#define LPDDR4__DENALI_PHY_12__PHY_DQ_IDLE_0_WIDTH                            9U
#define LPDDR4__PHY_DQ_IDLE_0__REG DENALI_PHY_12
#define LPDDR4__PHY_DQ_IDLE_0__FLD LPDDR4__DENALI_PHY_12__PHY_DQ_IDLE_0

#define LPDDR4__DENALI_PHY_12__PHY_PDA_MODE_EN_0_MASK                0x00010000U
#define LPDDR4__DENALI_PHY_12__PHY_PDA_MODE_EN_0_SHIFT                       16U
#define LPDDR4__DENALI_PHY_12__PHY_PDA_MODE_EN_0_WIDTH                        1U
#define LPDDR4__DENALI_PHY_12__PHY_PDA_MODE_EN_0_WOCLR                        0U
#define LPDDR4__DENALI_PHY_12__PHY_PDA_MODE_EN_0_WOSET                        0U
#define LPDDR4__PHY_PDA_MODE_EN_0__REG DENALI_PHY_12
#define LPDDR4__PHY_PDA_MODE_EN_0__FLD LPDDR4__DENALI_PHY_12__PHY_PDA_MODE_EN_0

#define LPDDR4__DENALI_PHY_12__PHY_PRBS_PATTERN_START_0_MASK         0x7F000000U
#define LPDDR4__DENALI_PHY_12__PHY_PRBS_PATTERN_START_0_SHIFT                24U
#define LPDDR4__DENALI_PHY_12__PHY_PRBS_PATTERN_START_0_WIDTH                 7U
#define LPDDR4__PHY_PRBS_PATTERN_START_0__REG DENALI_PHY_12
#define LPDDR4__PHY_PRBS_PATTERN_START_0__FLD LPDDR4__DENALI_PHY_12__PHY_PRBS_PATTERN_START_0

#define LPDDR4__DENALI_PHY_13_READ_MASK                              0x010101FFU
#define LPDDR4__DENALI_PHY_13_WRITE_MASK                             0x010101FFU
#define LPDDR4__DENALI_PHY_13__PHY_PRBS_PATTERN_MASK_0_MASK          0x000001FFU
#define LPDDR4__DENALI_PHY_13__PHY_PRBS_PATTERN_MASK_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_13__PHY_PRBS_PATTERN_MASK_0_WIDTH                  9U
#define LPDDR4__PHY_PRBS_PATTERN_MASK_0__REG DENALI_PHY_13
#define LPDDR4__PHY_PRBS_PATTERN_MASK_0__FLD LPDDR4__DENALI_PHY_13__PHY_PRBS_PATTERN_MASK_0

#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_ENABLE_0_MASK    0x00010000U
#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_ENABLE_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_ENABLE_0_WIDTH            1U
#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_ENABLE_0_WOCLR            0U
#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_ENABLE_0_WOSET            0U
#define LPDDR4__PHY_RDLVL_MULTI_PATT_ENABLE_0__REG DENALI_PHY_13
#define LPDDR4__PHY_RDLVL_MULTI_PATT_ENABLE_0__FLD LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_ENABLE_0

#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_RST_DISABLE_0_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_RST_DISABLE_0_SHIFT      24U
#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_RST_DISABLE_0_WIDTH       1U
#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_RST_DISABLE_0_WOCLR       0U
#define LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_RST_DISABLE_0_WOSET       0U
#define LPDDR4__PHY_RDLVL_MULTI_PATT_RST_DISABLE_0__REG DENALI_PHY_13
#define LPDDR4__PHY_RDLVL_MULTI_PATT_RST_DISABLE_0__FLD LPDDR4__DENALI_PHY_13__PHY_RDLVL_MULTI_PATT_RST_DISABLE_0

#define LPDDR4__DENALI_PHY_14_READ_MASK                              0x03FF7F3FU
#define LPDDR4__DENALI_PHY_14_WRITE_MASK                             0x03FF7F3FU
#define LPDDR4__DENALI_PHY_14__PHY_VREF_INITIAL_STEPSIZE_0_MASK      0x0000003FU
#define LPDDR4__DENALI_PHY_14__PHY_VREF_INITIAL_STEPSIZE_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_14__PHY_VREF_INITIAL_STEPSIZE_0_WIDTH              6U
#define LPDDR4__PHY_VREF_INITIAL_STEPSIZE_0__REG DENALI_PHY_14
#define LPDDR4__PHY_VREF_INITIAL_STEPSIZE_0__FLD LPDDR4__DENALI_PHY_14__PHY_VREF_INITIAL_STEPSIZE_0

#define LPDDR4__DENALI_PHY_14__PHY_VREF_TRAIN_OBS_0_MASK             0x00007F00U
#define LPDDR4__DENALI_PHY_14__PHY_VREF_TRAIN_OBS_0_SHIFT                     8U
#define LPDDR4__DENALI_PHY_14__PHY_VREF_TRAIN_OBS_0_WIDTH                     7U
#define LPDDR4__PHY_VREF_TRAIN_OBS_0__REG DENALI_PHY_14
#define LPDDR4__PHY_VREF_TRAIN_OBS_0__FLD LPDDR4__DENALI_PHY_14__PHY_VREF_TRAIN_OBS_0

#define LPDDR4__DENALI_PHY_14__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_14__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_14__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_0__REG DENALI_PHY_14
#define LPDDR4__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_14__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_15_READ_MASK                              0x01FF000FU
#define LPDDR4__DENALI_PHY_15_WRITE_MASK                             0x01FF000FU
#define LPDDR4__DENALI_PHY_15__PHY_GATE_ERROR_DELAY_SELECT_0_MASK    0x0000000FU
#define LPDDR4__DENALI_PHY_15__PHY_GATE_ERROR_DELAY_SELECT_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_15__PHY_GATE_ERROR_DELAY_SELECT_0_WIDTH            4U
#define LPDDR4__PHY_GATE_ERROR_DELAY_SELECT_0__REG DENALI_PHY_15
#define LPDDR4__PHY_GATE_ERROR_DELAY_SELECT_0__FLD LPDDR4__DENALI_PHY_15__PHY_GATE_ERROR_DELAY_SELECT_0

#define LPDDR4__DENALI_PHY_15__SC_PHY_SNAP_OBS_REGS_0_MASK           0x00000100U
#define LPDDR4__DENALI_PHY_15__SC_PHY_SNAP_OBS_REGS_0_SHIFT                   8U
#define LPDDR4__DENALI_PHY_15__SC_PHY_SNAP_OBS_REGS_0_WIDTH                   1U
#define LPDDR4__DENALI_PHY_15__SC_PHY_SNAP_OBS_REGS_0_WOCLR                   0U
#define LPDDR4__DENALI_PHY_15__SC_PHY_SNAP_OBS_REGS_0_WOSET                   0U
#define LPDDR4__SC_PHY_SNAP_OBS_REGS_0__REG DENALI_PHY_15
#define LPDDR4__SC_PHY_SNAP_OBS_REGS_0__FLD LPDDR4__DENALI_PHY_15__SC_PHY_SNAP_OBS_REGS_0

#define LPDDR4__DENALI_PHY_15__PHY_GATE_SMPL1_SLAVE_DELAY_0_MASK     0x01FF0000U
#define LPDDR4__DENALI_PHY_15__PHY_GATE_SMPL1_SLAVE_DELAY_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_15__PHY_GATE_SMPL1_SLAVE_DELAY_0_WIDTH             9U
#define LPDDR4__PHY_GATE_SMPL1_SLAVE_DELAY_0__REG DENALI_PHY_15
#define LPDDR4__PHY_GATE_SMPL1_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_15__PHY_GATE_SMPL1_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_16_READ_MASK                              0x01FF0701U
#define LPDDR4__DENALI_PHY_16_WRITE_MASK                             0x01FF0701U
#define LPDDR4__DENALI_PHY_16__PHY_LPDDR_0_MASK                      0x00000001U
#define LPDDR4__DENALI_PHY_16__PHY_LPDDR_0_SHIFT                              0U
#define LPDDR4__DENALI_PHY_16__PHY_LPDDR_0_WIDTH                              1U
#define LPDDR4__DENALI_PHY_16__PHY_LPDDR_0_WOCLR                              0U
#define LPDDR4__DENALI_PHY_16__PHY_LPDDR_0_WOSET                              0U
#define LPDDR4__PHY_LPDDR_0__REG DENALI_PHY_16
#define LPDDR4__PHY_LPDDR_0__FLD LPDDR4__DENALI_PHY_16__PHY_LPDDR_0

#define LPDDR4__DENALI_PHY_16__PHY_MEM_CLASS_0_MASK                  0x00000700U
#define LPDDR4__DENALI_PHY_16__PHY_MEM_CLASS_0_SHIFT                          8U
#define LPDDR4__DENALI_PHY_16__PHY_MEM_CLASS_0_WIDTH                          3U
#define LPDDR4__PHY_MEM_CLASS_0__REG DENALI_PHY_16
#define LPDDR4__PHY_MEM_CLASS_0__FLD LPDDR4__DENALI_PHY_16__PHY_MEM_CLASS_0

#define LPDDR4__DENALI_PHY_16__PHY_GATE_SMPL2_SLAVE_DELAY_0_MASK     0x01FF0000U
#define LPDDR4__DENALI_PHY_16__PHY_GATE_SMPL2_SLAVE_DELAY_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_16__PHY_GATE_SMPL2_SLAVE_DELAY_0_WIDTH             9U
#define LPDDR4__PHY_GATE_SMPL2_SLAVE_DELAY_0__REG DENALI_PHY_16
#define LPDDR4__PHY_GATE_SMPL2_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_16__PHY_GATE_SMPL2_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_17_READ_MASK                              0x00000003U
#define LPDDR4__DENALI_PHY_17_WRITE_MASK                             0x00000003U
#define LPDDR4__DENALI_PHY_17__ON_FLY_GATE_ADJUST_EN_0_MASK          0x00000003U
#define LPDDR4__DENALI_PHY_17__ON_FLY_GATE_ADJUST_EN_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_17__ON_FLY_GATE_ADJUST_EN_0_WIDTH                  2U
#define LPDDR4__ON_FLY_GATE_ADJUST_EN_0__REG DENALI_PHY_17
#define LPDDR4__ON_FLY_GATE_ADJUST_EN_0__FLD LPDDR4__DENALI_PHY_17__ON_FLY_GATE_ADJUST_EN_0

#define LPDDR4__DENALI_PHY_18_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_18_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_18__PHY_GATE_TRACKING_OBS_0_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_18__PHY_GATE_TRACKING_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_18__PHY_GATE_TRACKING_OBS_0_WIDTH                 32U
#define LPDDR4__PHY_GATE_TRACKING_OBS_0__REG DENALI_PHY_18
#define LPDDR4__PHY_GATE_TRACKING_OBS_0__FLD LPDDR4__DENALI_PHY_18__PHY_GATE_TRACKING_OBS_0

#define LPDDR4__DENALI_PHY_19_READ_MASK                              0x00000301U
#define LPDDR4__DENALI_PHY_19_WRITE_MASK                             0x00000301U
#define LPDDR4__DENALI_PHY_19__PHY_DFI40_POLARITY_0_MASK             0x00000001U
#define LPDDR4__DENALI_PHY_19__PHY_DFI40_POLARITY_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_19__PHY_DFI40_POLARITY_0_WIDTH                     1U
#define LPDDR4__DENALI_PHY_19__PHY_DFI40_POLARITY_0_WOCLR                     0U
#define LPDDR4__DENALI_PHY_19__PHY_DFI40_POLARITY_0_WOSET                     0U
#define LPDDR4__PHY_DFI40_POLARITY_0__REG DENALI_PHY_19
#define LPDDR4__PHY_DFI40_POLARITY_0__FLD LPDDR4__DENALI_PHY_19__PHY_DFI40_POLARITY_0

#define LPDDR4__DENALI_PHY_19__PHY_LP4_PST_AMBLE_0_MASK              0x00000300U
#define LPDDR4__DENALI_PHY_19__PHY_LP4_PST_AMBLE_0_SHIFT                      8U
#define LPDDR4__DENALI_PHY_19__PHY_LP4_PST_AMBLE_0_WIDTH                      2U
#define LPDDR4__PHY_LP4_PST_AMBLE_0__REG DENALI_PHY_19
#define LPDDR4__PHY_LP4_PST_AMBLE_0__FLD LPDDR4__DENALI_PHY_19__PHY_LP4_PST_AMBLE_0

#define LPDDR4__DENALI_PHY_20_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_20_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_20__PHY_RDLVL_PATT8_0_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_20__PHY_RDLVL_PATT8_0_SHIFT                        0U
#define LPDDR4__DENALI_PHY_20__PHY_RDLVL_PATT8_0_WIDTH                       32U
#define LPDDR4__PHY_RDLVL_PATT8_0__REG DENALI_PHY_20
#define LPDDR4__PHY_RDLVL_PATT8_0__FLD LPDDR4__DENALI_PHY_20__PHY_RDLVL_PATT8_0

#define LPDDR4__DENALI_PHY_21_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_21_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_21__PHY_RDLVL_PATT9_0_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_21__PHY_RDLVL_PATT9_0_SHIFT                        0U
#define LPDDR4__DENALI_PHY_21__PHY_RDLVL_PATT9_0_WIDTH                       32U
#define LPDDR4__PHY_RDLVL_PATT9_0__REG DENALI_PHY_21
#define LPDDR4__PHY_RDLVL_PATT9_0__FLD LPDDR4__DENALI_PHY_21__PHY_RDLVL_PATT9_0

#define LPDDR4__DENALI_PHY_22_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_22_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_22__PHY_RDLVL_PATT10_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_22__PHY_RDLVL_PATT10_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_22__PHY_RDLVL_PATT10_0_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT10_0__REG DENALI_PHY_22
#define LPDDR4__PHY_RDLVL_PATT10_0__FLD LPDDR4__DENALI_PHY_22__PHY_RDLVL_PATT10_0

#define LPDDR4__DENALI_PHY_23_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_23_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_23__PHY_RDLVL_PATT11_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_23__PHY_RDLVL_PATT11_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_23__PHY_RDLVL_PATT11_0_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT11_0__REG DENALI_PHY_23
#define LPDDR4__PHY_RDLVL_PATT11_0__FLD LPDDR4__DENALI_PHY_23__PHY_RDLVL_PATT11_0

#define LPDDR4__DENALI_PHY_24_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_24_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_24__PHY_RDLVL_PATT12_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_24__PHY_RDLVL_PATT12_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_24__PHY_RDLVL_PATT12_0_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT12_0__REG DENALI_PHY_24
#define LPDDR4__PHY_RDLVL_PATT12_0__FLD LPDDR4__DENALI_PHY_24__PHY_RDLVL_PATT12_0

#define LPDDR4__DENALI_PHY_25_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_25_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_25__PHY_RDLVL_PATT13_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_25__PHY_RDLVL_PATT13_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_25__PHY_RDLVL_PATT13_0_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT13_0__REG DENALI_PHY_25
#define LPDDR4__PHY_RDLVL_PATT13_0__FLD LPDDR4__DENALI_PHY_25__PHY_RDLVL_PATT13_0

#define LPDDR4__DENALI_PHY_26_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_26_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_26__PHY_RDLVL_PATT14_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_26__PHY_RDLVL_PATT14_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_26__PHY_RDLVL_PATT14_0_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT14_0__REG DENALI_PHY_26
#define LPDDR4__PHY_RDLVL_PATT14_0__FLD LPDDR4__DENALI_PHY_26__PHY_RDLVL_PATT14_0

#define LPDDR4__DENALI_PHY_27_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_27_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_27__PHY_RDLVL_PATT15_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_27__PHY_RDLVL_PATT15_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_27__PHY_RDLVL_PATT15_0_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT15_0__REG DENALI_PHY_27
#define LPDDR4__PHY_RDLVL_PATT15_0__FLD LPDDR4__DENALI_PHY_27__PHY_RDLVL_PATT15_0

#define LPDDR4__DENALI_PHY_28_READ_MASK                              0x070F0107U
#define LPDDR4__DENALI_PHY_28_WRITE_MASK                             0x070F0107U
#define LPDDR4__DENALI_PHY_28__PHY_SLAVE_LOOP_CNT_UPDATE_0_MASK      0x00000007U
#define LPDDR4__DENALI_PHY_28__PHY_SLAVE_LOOP_CNT_UPDATE_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_28__PHY_SLAVE_LOOP_CNT_UPDATE_0_WIDTH              3U
#define LPDDR4__PHY_SLAVE_LOOP_CNT_UPDATE_0__REG DENALI_PHY_28
#define LPDDR4__PHY_SLAVE_LOOP_CNT_UPDATE_0__FLD LPDDR4__DENALI_PHY_28__PHY_SLAVE_LOOP_CNT_UPDATE_0

#define LPDDR4__DENALI_PHY_28__PHY_SW_FIFO_PTR_RST_DISABLE_0_MASK    0x00000100U
#define LPDDR4__DENALI_PHY_28__PHY_SW_FIFO_PTR_RST_DISABLE_0_SHIFT            8U
#define LPDDR4__DENALI_PHY_28__PHY_SW_FIFO_PTR_RST_DISABLE_0_WIDTH            1U
#define LPDDR4__DENALI_PHY_28__PHY_SW_FIFO_PTR_RST_DISABLE_0_WOCLR            0U
#define LPDDR4__DENALI_PHY_28__PHY_SW_FIFO_PTR_RST_DISABLE_0_WOSET            0U
#define LPDDR4__PHY_SW_FIFO_PTR_RST_DISABLE_0__REG DENALI_PHY_28
#define LPDDR4__PHY_SW_FIFO_PTR_RST_DISABLE_0__FLD LPDDR4__DENALI_PHY_28__PHY_SW_FIFO_PTR_RST_DISABLE_0

#define LPDDR4__DENALI_PHY_28__PHY_MASTER_DLY_LOCK_OBS_SELECT_0_MASK 0x000F0000U
#define LPDDR4__DENALI_PHY_28__PHY_MASTER_DLY_LOCK_OBS_SELECT_0_SHIFT        16U
#define LPDDR4__DENALI_PHY_28__PHY_MASTER_DLY_LOCK_OBS_SELECT_0_WIDTH         4U
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_SELECT_0__REG DENALI_PHY_28
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_28__PHY_MASTER_DLY_LOCK_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_28__PHY_RDDQ_ENC_OBS_SELECT_0_MASK        0x07000000U
#define LPDDR4__DENALI_PHY_28__PHY_RDDQ_ENC_OBS_SELECT_0_SHIFT               24U
#define LPDDR4__DENALI_PHY_28__PHY_RDDQ_ENC_OBS_SELECT_0_WIDTH                3U
#define LPDDR4__PHY_RDDQ_ENC_OBS_SELECT_0__REG DENALI_PHY_28
#define LPDDR4__PHY_RDDQ_ENC_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_28__PHY_RDDQ_ENC_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_29_READ_MASK                              0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_29_WRITE_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_29__PHY_RDDQS_DQ_ENC_OBS_SELECT_0_MASK    0x0000000FU
#define LPDDR4__DENALI_PHY_29__PHY_RDDQS_DQ_ENC_OBS_SELECT_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_29__PHY_RDDQS_DQ_ENC_OBS_SELECT_0_WIDTH            4U
#define LPDDR4__PHY_RDDQS_DQ_ENC_OBS_SELECT_0__REG DENALI_PHY_29
#define LPDDR4__PHY_RDDQS_DQ_ENC_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_29__PHY_RDDQS_DQ_ENC_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_29__PHY_WR_ENC_OBS_SELECT_0_MASK          0x00000F00U
#define LPDDR4__DENALI_PHY_29__PHY_WR_ENC_OBS_SELECT_0_SHIFT                  8U
#define LPDDR4__DENALI_PHY_29__PHY_WR_ENC_OBS_SELECT_0_WIDTH                  4U
#define LPDDR4__PHY_WR_ENC_OBS_SELECT_0__REG DENALI_PHY_29
#define LPDDR4__PHY_WR_ENC_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_29__PHY_WR_ENC_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_29__PHY_WR_SHIFT_OBS_SELECT_0_MASK        0x000F0000U
#define LPDDR4__DENALI_PHY_29__PHY_WR_SHIFT_OBS_SELECT_0_SHIFT               16U
#define LPDDR4__DENALI_PHY_29__PHY_WR_SHIFT_OBS_SELECT_0_WIDTH                4U
#define LPDDR4__PHY_WR_SHIFT_OBS_SELECT_0__REG DENALI_PHY_29
#define LPDDR4__PHY_WR_SHIFT_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_29__PHY_WR_SHIFT_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_29__PHY_FIFO_PTR_OBS_SELECT_0_MASK        0x0F000000U
#define LPDDR4__DENALI_PHY_29__PHY_FIFO_PTR_OBS_SELECT_0_SHIFT               24U
#define LPDDR4__DENALI_PHY_29__PHY_FIFO_PTR_OBS_SELECT_0_WIDTH                4U
#define LPDDR4__PHY_FIFO_PTR_OBS_SELECT_0__REG DENALI_PHY_29
#define LPDDR4__PHY_FIFO_PTR_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_29__PHY_FIFO_PTR_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_30_READ_MASK                              0x3F030001U
#define LPDDR4__DENALI_PHY_30_WRITE_MASK                             0x3F030001U
#define LPDDR4__DENALI_PHY_30__PHY_LVL_DEBUG_MODE_0_MASK             0x00000001U
#define LPDDR4__DENALI_PHY_30__PHY_LVL_DEBUG_MODE_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_30__PHY_LVL_DEBUG_MODE_0_WIDTH                     1U
#define LPDDR4__DENALI_PHY_30__PHY_LVL_DEBUG_MODE_0_WOCLR                     0U
#define LPDDR4__DENALI_PHY_30__PHY_LVL_DEBUG_MODE_0_WOSET                     0U
#define LPDDR4__PHY_LVL_DEBUG_MODE_0__REG DENALI_PHY_30
#define LPDDR4__PHY_LVL_DEBUG_MODE_0__FLD LPDDR4__DENALI_PHY_30__PHY_LVL_DEBUG_MODE_0

#define LPDDR4__DENALI_PHY_30__SC_PHY_LVL_DEBUG_CONT_0_MASK          0x00000100U
#define LPDDR4__DENALI_PHY_30__SC_PHY_LVL_DEBUG_CONT_0_SHIFT                  8U
#define LPDDR4__DENALI_PHY_30__SC_PHY_LVL_DEBUG_CONT_0_WIDTH                  1U
#define LPDDR4__DENALI_PHY_30__SC_PHY_LVL_DEBUG_CONT_0_WOCLR                  0U
#define LPDDR4__DENALI_PHY_30__SC_PHY_LVL_DEBUG_CONT_0_WOSET                  0U
#define LPDDR4__SC_PHY_LVL_DEBUG_CONT_0__REG DENALI_PHY_30
#define LPDDR4__SC_PHY_LVL_DEBUG_CONT_0__FLD LPDDR4__DENALI_PHY_30__SC_PHY_LVL_DEBUG_CONT_0

#define LPDDR4__DENALI_PHY_30__PHY_WRLVL_ALGO_0_MASK                 0x00030000U
#define LPDDR4__DENALI_PHY_30__PHY_WRLVL_ALGO_0_SHIFT                        16U
#define LPDDR4__DENALI_PHY_30__PHY_WRLVL_ALGO_0_WIDTH                         2U
#define LPDDR4__PHY_WRLVL_ALGO_0__REG DENALI_PHY_30
#define LPDDR4__PHY_WRLVL_ALGO_0__FLD LPDDR4__DENALI_PHY_30__PHY_WRLVL_ALGO_0

#define LPDDR4__DENALI_PHY_30__PHY_WRLVL_CAPTURE_CNT_0_MASK          0x3F000000U
#define LPDDR4__DENALI_PHY_30__PHY_WRLVL_CAPTURE_CNT_0_SHIFT                 24U
#define LPDDR4__DENALI_PHY_30__PHY_WRLVL_CAPTURE_CNT_0_WIDTH                  6U
#define LPDDR4__PHY_WRLVL_CAPTURE_CNT_0__REG DENALI_PHY_30
#define LPDDR4__PHY_WRLVL_CAPTURE_CNT_0__FLD LPDDR4__DENALI_PHY_30__PHY_WRLVL_CAPTURE_CNT_0

#define LPDDR4__DENALI_PHY_31_READ_MASK                              0x0F3FFF0FU
#define LPDDR4__DENALI_PHY_31_WRITE_MASK                             0x0F3FFF0FU
#define LPDDR4__DENALI_PHY_31__PHY_WRLVL_UPDT_WAIT_CNT_0_MASK        0x0000000FU
#define LPDDR4__DENALI_PHY_31__PHY_WRLVL_UPDT_WAIT_CNT_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_31__PHY_WRLVL_UPDT_WAIT_CNT_0_WIDTH                4U
#define LPDDR4__PHY_WRLVL_UPDT_WAIT_CNT_0__REG DENALI_PHY_31
#define LPDDR4__PHY_WRLVL_UPDT_WAIT_CNT_0__FLD LPDDR4__DENALI_PHY_31__PHY_WRLVL_UPDT_WAIT_CNT_0

#define LPDDR4__DENALI_PHY_31__PHY_DQ_MASK_0_MASK                    0x0000FF00U
#define LPDDR4__DENALI_PHY_31__PHY_DQ_MASK_0_SHIFT                            8U
#define LPDDR4__DENALI_PHY_31__PHY_DQ_MASK_0_WIDTH                            8U
#define LPDDR4__PHY_DQ_MASK_0__REG DENALI_PHY_31
#define LPDDR4__PHY_DQ_MASK_0__FLD LPDDR4__DENALI_PHY_31__PHY_DQ_MASK_0

#define LPDDR4__DENALI_PHY_31__PHY_GTLVL_CAPTURE_CNT_0_MASK          0x003F0000U
#define LPDDR4__DENALI_PHY_31__PHY_GTLVL_CAPTURE_CNT_0_SHIFT                 16U
#define LPDDR4__DENALI_PHY_31__PHY_GTLVL_CAPTURE_CNT_0_WIDTH                  6U
#define LPDDR4__PHY_GTLVL_CAPTURE_CNT_0__REG DENALI_PHY_31
#define LPDDR4__PHY_GTLVL_CAPTURE_CNT_0__FLD LPDDR4__DENALI_PHY_31__PHY_GTLVL_CAPTURE_CNT_0

#define LPDDR4__DENALI_PHY_31__PHY_GTLVL_UPDT_WAIT_CNT_0_MASK        0x0F000000U
#define LPDDR4__DENALI_PHY_31__PHY_GTLVL_UPDT_WAIT_CNT_0_SHIFT               24U
#define LPDDR4__DENALI_PHY_31__PHY_GTLVL_UPDT_WAIT_CNT_0_WIDTH                4U
#define LPDDR4__PHY_GTLVL_UPDT_WAIT_CNT_0__REG DENALI_PHY_31
#define LPDDR4__PHY_GTLVL_UPDT_WAIT_CNT_0__FLD LPDDR4__DENALI_PHY_31__PHY_GTLVL_UPDT_WAIT_CNT_0

#define LPDDR4__DENALI_PHY_32_READ_MASK                              0x1F030F3FU
#define LPDDR4__DENALI_PHY_32_WRITE_MASK                             0x1F030F3FU
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_CAPTURE_CNT_0_MASK          0x0000003FU
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_CAPTURE_CNT_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_CAPTURE_CNT_0_WIDTH                  6U
#define LPDDR4__PHY_RDLVL_CAPTURE_CNT_0__REG DENALI_PHY_32
#define LPDDR4__PHY_RDLVL_CAPTURE_CNT_0__FLD LPDDR4__DENALI_PHY_32__PHY_RDLVL_CAPTURE_CNT_0

#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_UPDT_WAIT_CNT_0_MASK        0x00000F00U
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_UPDT_WAIT_CNT_0_SHIFT                8U
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_UPDT_WAIT_CNT_0_WIDTH                4U
#define LPDDR4__PHY_RDLVL_UPDT_WAIT_CNT_0__REG DENALI_PHY_32
#define LPDDR4__PHY_RDLVL_UPDT_WAIT_CNT_0__FLD LPDDR4__DENALI_PHY_32__PHY_RDLVL_UPDT_WAIT_CNT_0

#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_OP_MODE_0_MASK              0x00030000U
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_OP_MODE_0_SHIFT                     16U
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_OP_MODE_0_WIDTH                      2U
#define LPDDR4__PHY_RDLVL_OP_MODE_0__REG DENALI_PHY_32
#define LPDDR4__PHY_RDLVL_OP_MODE_0__FLD LPDDR4__DENALI_PHY_32__PHY_RDLVL_OP_MODE_0

#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_0_MASK  0x1F000000U
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_0_SHIFT         24U
#define LPDDR4__DENALI_PHY_32__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_0_WIDTH          5U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_0__REG DENALI_PHY_32
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_32__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_33_READ_MASK                              0x03FFFFFFU
#define LPDDR4__DENALI_PHY_33_WRITE_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PHY_33__PHY_RDLVL_DATA_MASK_0_MASK            0x000000FFU
#define LPDDR4__DENALI_PHY_33__PHY_RDLVL_DATA_MASK_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_33__PHY_RDLVL_DATA_MASK_0_WIDTH                    8U
#define LPDDR4__PHY_RDLVL_DATA_MASK_0__REG DENALI_PHY_33
#define LPDDR4__PHY_RDLVL_DATA_MASK_0__FLD LPDDR4__DENALI_PHY_33__PHY_RDLVL_DATA_MASK_0

#define LPDDR4__DENALI_PHY_33__PHY_RDLVL_DATA_SWIZZLE_0_MASK         0x03FFFF00U
#define LPDDR4__DENALI_PHY_33__PHY_RDLVL_DATA_SWIZZLE_0_SHIFT                 8U
#define LPDDR4__DENALI_PHY_33__PHY_RDLVL_DATA_SWIZZLE_0_WIDTH                18U
#define LPDDR4__PHY_RDLVL_DATA_SWIZZLE_0__REG DENALI_PHY_33
#define LPDDR4__PHY_RDLVL_DATA_SWIZZLE_0__FLD LPDDR4__DENALI_PHY_33__PHY_RDLVL_DATA_SWIZZLE_0

#define LPDDR4__DENALI_PHY_34_READ_MASK                              0x00073FFFU
#define LPDDR4__DENALI_PHY_34_WRITE_MASK                             0x00073FFFU
#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_CLK_JITTER_TOLERANCE_0_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_CLK_JITTER_TOLERANCE_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_CLK_JITTER_TOLERANCE_0_WIDTH        8U
#define LPDDR4__PHY_WDQLVL_CLK_JITTER_TOLERANCE_0__REG DENALI_PHY_34
#define LPDDR4__PHY_WDQLVL_CLK_JITTER_TOLERANCE_0__FLD LPDDR4__DENALI_PHY_34__PHY_WDQLVL_CLK_JITTER_TOLERANCE_0

#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_BURST_CNT_0_MASK           0x00003F00U
#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_BURST_CNT_0_SHIFT                   8U
#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_BURST_CNT_0_WIDTH                   6U
#define LPDDR4__PHY_WDQLVL_BURST_CNT_0__REG DENALI_PHY_34
#define LPDDR4__PHY_WDQLVL_BURST_CNT_0__FLD LPDDR4__DENALI_PHY_34__PHY_WDQLVL_BURST_CNT_0

#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_PATT_0_MASK                0x00070000U
#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_PATT_0_SHIFT                       16U
#define LPDDR4__DENALI_PHY_34__PHY_WDQLVL_PATT_0_WIDTH                        3U
#define LPDDR4__PHY_WDQLVL_PATT_0__REG DENALI_PHY_34
#define LPDDR4__PHY_WDQLVL_PATT_0__FLD LPDDR4__DENALI_PHY_34__PHY_WDQLVL_PATT_0

#define LPDDR4__DENALI_PHY_35_READ_MASK                              0x0F0F07FFU
#define LPDDR4__DENALI_PHY_35_WRITE_MASK                             0x0F0F07FFU
#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_0_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_0_SHIFT    0U
#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_0_WIDTH   11U
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_0__REG DENALI_PHY_35
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_0__FLD LPDDR4__DENALI_PHY_35__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_0

#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_UPDT_WAIT_CNT_0_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_UPDT_WAIT_CNT_0_SHIFT              16U
#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_UPDT_WAIT_CNT_0_WIDTH               4U
#define LPDDR4__PHY_WDQLVL_UPDT_WAIT_CNT_0__REG DENALI_PHY_35
#define LPDDR4__PHY_WDQLVL_UPDT_WAIT_CNT_0__FLD LPDDR4__DENALI_PHY_35__PHY_WDQLVL_UPDT_WAIT_CNT_0

#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_DQDM_OBS_SELECT_0_MASK     0x0F000000U
#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_DQDM_OBS_SELECT_0_SHIFT            24U
#define LPDDR4__DENALI_PHY_35__PHY_WDQLVL_DQDM_OBS_SELECT_0_WIDTH             4U
#define LPDDR4__PHY_WDQLVL_DQDM_OBS_SELECT_0__REG DENALI_PHY_35
#define LPDDR4__PHY_WDQLVL_DQDM_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_35__PHY_WDQLVL_DQDM_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_36_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_PHY_36_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_PERIODIC_OBS_SELECT_0_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_PERIODIC_OBS_SELECT_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_PERIODIC_OBS_SELECT_0_WIDTH         8U
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_SELECT_0__REG DENALI_PHY_36
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_36__PHY_WDQLVL_PERIODIC_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_DQ_SLV_DELTA_0_MASK        0x0000FF00U
#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_DQ_SLV_DELTA_0_SHIFT                8U
#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_DQ_SLV_DELTA_0_WIDTH                8U
#define LPDDR4__PHY_WDQLVL_DQ_SLV_DELTA_0__REG DENALI_PHY_36
#define LPDDR4__PHY_WDQLVL_DQ_SLV_DELTA_0__FLD LPDDR4__DENALI_PHY_36__PHY_WDQLVL_DQ_SLV_DELTA_0

#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_DM_DLY_STEP_0_MASK         0x000F0000U
#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_DM_DLY_STEP_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_36__PHY_WDQLVL_DM_DLY_STEP_0_WIDTH                 4U
#define LPDDR4__PHY_WDQLVL_DM_DLY_STEP_0__REG DENALI_PHY_36
#define LPDDR4__PHY_WDQLVL_DM_DLY_STEP_0__FLD LPDDR4__DENALI_PHY_36__PHY_WDQLVL_DM_DLY_STEP_0

#define LPDDR4__DENALI_PHY_36__SC_PHY_WDQLVL_CLR_PREV_RESULTS_0_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_36__SC_PHY_WDQLVL_CLR_PREV_RESULTS_0_SHIFT        24U
#define LPDDR4__DENALI_PHY_36__SC_PHY_WDQLVL_CLR_PREV_RESULTS_0_WIDTH         1U
#define LPDDR4__DENALI_PHY_36__SC_PHY_WDQLVL_CLR_PREV_RESULTS_0_WOCLR         0U
#define LPDDR4__DENALI_PHY_36__SC_PHY_WDQLVL_CLR_PREV_RESULTS_0_WOSET         0U
#define LPDDR4__SC_PHY_WDQLVL_CLR_PREV_RESULTS_0__REG DENALI_PHY_36
#define LPDDR4__SC_PHY_WDQLVL_CLR_PREV_RESULTS_0__FLD LPDDR4__DENALI_PHY_36__SC_PHY_WDQLVL_CLR_PREV_RESULTS_0

#define LPDDR4__DENALI_PHY_37_READ_MASK                              0x000001FFU
#define LPDDR4__DENALI_PHY_37_WRITE_MASK                             0x000001FFU
#define LPDDR4__DENALI_PHY_37__PHY_WDQLVL_DATADM_MASK_0_MASK         0x000001FFU
#define LPDDR4__DENALI_PHY_37__PHY_WDQLVL_DATADM_MASK_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_37__PHY_WDQLVL_DATADM_MASK_0_WIDTH                 9U
#define LPDDR4__PHY_WDQLVL_DATADM_MASK_0__REG DENALI_PHY_37
#define LPDDR4__PHY_WDQLVL_DATADM_MASK_0__FLD LPDDR4__DENALI_PHY_37__PHY_WDQLVL_DATADM_MASK_0

#define LPDDR4__DENALI_PHY_38_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_38_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_38__PHY_USER_PATT0_0_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_38__PHY_USER_PATT0_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_38__PHY_USER_PATT0_0_WIDTH                        32U
#define LPDDR4__PHY_USER_PATT0_0__REG DENALI_PHY_38
#define LPDDR4__PHY_USER_PATT0_0__FLD LPDDR4__DENALI_PHY_38__PHY_USER_PATT0_0

#define LPDDR4__DENALI_PHY_39_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_39_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_39__PHY_USER_PATT1_0_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_39__PHY_USER_PATT1_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_39__PHY_USER_PATT1_0_WIDTH                        32U
#define LPDDR4__PHY_USER_PATT1_0__REG DENALI_PHY_39
#define LPDDR4__PHY_USER_PATT1_0__FLD LPDDR4__DENALI_PHY_39__PHY_USER_PATT1_0

#define LPDDR4__DENALI_PHY_40_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_40_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_40__PHY_USER_PATT2_0_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_40__PHY_USER_PATT2_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_40__PHY_USER_PATT2_0_WIDTH                        32U
#define LPDDR4__PHY_USER_PATT2_0__REG DENALI_PHY_40
#define LPDDR4__PHY_USER_PATT2_0__FLD LPDDR4__DENALI_PHY_40__PHY_USER_PATT2_0

#define LPDDR4__DENALI_PHY_41_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_41_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_41__PHY_USER_PATT3_0_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_41__PHY_USER_PATT3_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_41__PHY_USER_PATT3_0_WIDTH                        32U
#define LPDDR4__PHY_USER_PATT3_0__REG DENALI_PHY_41
#define LPDDR4__PHY_USER_PATT3_0__FLD LPDDR4__DENALI_PHY_41__PHY_USER_PATT3_0

#define LPDDR4__DENALI_PHY_42_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PHY_42_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PHY_42__PHY_USER_PATT4_0_MASK                 0x0000FFFFU
#define LPDDR4__DENALI_PHY_42__PHY_USER_PATT4_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_42__PHY_USER_PATT4_0_WIDTH                        16U
#define LPDDR4__PHY_USER_PATT4_0__REG DENALI_PHY_42
#define LPDDR4__PHY_USER_PATT4_0__FLD LPDDR4__DENALI_PHY_42__PHY_USER_PATT4_0

#define LPDDR4__DENALI_PHY_42__PHY_NTP_MULT_TRAIN_0_MASK             0x00010000U
#define LPDDR4__DENALI_PHY_42__PHY_NTP_MULT_TRAIN_0_SHIFT                    16U
#define LPDDR4__DENALI_PHY_42__PHY_NTP_MULT_TRAIN_0_WIDTH                     1U
#define LPDDR4__DENALI_PHY_42__PHY_NTP_MULT_TRAIN_0_WOCLR                     0U
#define LPDDR4__DENALI_PHY_42__PHY_NTP_MULT_TRAIN_0_WOSET                     0U
#define LPDDR4__PHY_NTP_MULT_TRAIN_0__REG DENALI_PHY_42
#define LPDDR4__PHY_NTP_MULT_TRAIN_0__FLD LPDDR4__DENALI_PHY_42__PHY_NTP_MULT_TRAIN_0

#define LPDDR4__DENALI_PHY_43_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_43_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_43__PHY_NTP_EARLY_THRESHOLD_0_MASK        0x000003FFU
#define LPDDR4__DENALI_PHY_43__PHY_NTP_EARLY_THRESHOLD_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_43__PHY_NTP_EARLY_THRESHOLD_0_WIDTH               10U
#define LPDDR4__PHY_NTP_EARLY_THRESHOLD_0__REG DENALI_PHY_43
#define LPDDR4__PHY_NTP_EARLY_THRESHOLD_0__FLD LPDDR4__DENALI_PHY_43__PHY_NTP_EARLY_THRESHOLD_0

#define LPDDR4__DENALI_PHY_43__PHY_NTP_PERIOD_THRESHOLD_0_MASK       0x03FF0000U
#define LPDDR4__DENALI_PHY_43__PHY_NTP_PERIOD_THRESHOLD_0_SHIFT              16U
#define LPDDR4__DENALI_PHY_43__PHY_NTP_PERIOD_THRESHOLD_0_WIDTH              10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_0__REG DENALI_PHY_43
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_0__FLD LPDDR4__DENALI_PHY_43__PHY_NTP_PERIOD_THRESHOLD_0

#define LPDDR4__DENALI_PHY_44_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_44_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_44__PHY_NTP_PERIOD_THRESHOLD_MIN_0_MASK   0x000003FFU
#define LPDDR4__DENALI_PHY_44__PHY_NTP_PERIOD_THRESHOLD_MIN_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_44__PHY_NTP_PERIOD_THRESHOLD_MIN_0_WIDTH          10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MIN_0__REG DENALI_PHY_44
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MIN_0__FLD LPDDR4__DENALI_PHY_44__PHY_NTP_PERIOD_THRESHOLD_MIN_0

#define LPDDR4__DENALI_PHY_44__PHY_NTP_PERIOD_THRESHOLD_MAX_0_MASK   0x03FF0000U
#define LPDDR4__DENALI_PHY_44__PHY_NTP_PERIOD_THRESHOLD_MAX_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_44__PHY_NTP_PERIOD_THRESHOLD_MAX_0_WIDTH          10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MAX_0__REG DENALI_PHY_44
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MAX_0__FLD LPDDR4__DENALI_PHY_44__PHY_NTP_PERIOD_THRESHOLD_MAX_0

#define LPDDR4__DENALI_PHY_45_READ_MASK                              0x00FF0001U
#define LPDDR4__DENALI_PHY_45_WRITE_MASK                             0x00FF0001U
#define LPDDR4__DENALI_PHY_45__PHY_CALVL_VREF_DRIVING_SLICE_0_MASK   0x00000001U
#define LPDDR4__DENALI_PHY_45__PHY_CALVL_VREF_DRIVING_SLICE_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_45__PHY_CALVL_VREF_DRIVING_SLICE_0_WIDTH           1U
#define LPDDR4__DENALI_PHY_45__PHY_CALVL_VREF_DRIVING_SLICE_0_WOCLR           0U
#define LPDDR4__DENALI_PHY_45__PHY_CALVL_VREF_DRIVING_SLICE_0_WOSET           0U
#define LPDDR4__PHY_CALVL_VREF_DRIVING_SLICE_0__REG DENALI_PHY_45
#define LPDDR4__PHY_CALVL_VREF_DRIVING_SLICE_0__FLD LPDDR4__DENALI_PHY_45__PHY_CALVL_VREF_DRIVING_SLICE_0

#define LPDDR4__DENALI_PHY_45__SC_PHY_MANUAL_CLEAR_0_MASK            0x00003F00U
#define LPDDR4__DENALI_PHY_45__SC_PHY_MANUAL_CLEAR_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_45__SC_PHY_MANUAL_CLEAR_0_WIDTH                    6U
#define LPDDR4__SC_PHY_MANUAL_CLEAR_0__REG DENALI_PHY_45
#define LPDDR4__SC_PHY_MANUAL_CLEAR_0__FLD LPDDR4__DENALI_PHY_45__SC_PHY_MANUAL_CLEAR_0

#define LPDDR4__DENALI_PHY_45__PHY_FIFO_PTR_OBS_0_MASK               0x00FF0000U
#define LPDDR4__DENALI_PHY_45__PHY_FIFO_PTR_OBS_0_SHIFT                      16U
#define LPDDR4__DENALI_PHY_45__PHY_FIFO_PTR_OBS_0_WIDTH                       8U
#define LPDDR4__PHY_FIFO_PTR_OBS_0__REG DENALI_PHY_45
#define LPDDR4__PHY_FIFO_PTR_OBS_0__FLD LPDDR4__DENALI_PHY_45__PHY_FIFO_PTR_OBS_0

#define LPDDR4__DENALI_PHY_46_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_46_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_46__PHY_LPBK_RESULT_OBS_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_46__PHY_LPBK_RESULT_OBS_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_46__PHY_LPBK_RESULT_OBS_0_WIDTH                   32U
#define LPDDR4__PHY_LPBK_RESULT_OBS_0__REG DENALI_PHY_46
#define LPDDR4__PHY_LPBK_RESULT_OBS_0__FLD LPDDR4__DENALI_PHY_46__PHY_LPBK_RESULT_OBS_0

#define LPDDR4__DENALI_PHY_47_READ_MASK                              0x07FFFFFFU
#define LPDDR4__DENALI_PHY_47_WRITE_MASK                             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_47__PHY_LPBK_ERROR_COUNT_OBS_0_MASK       0x0000FFFFU
#define LPDDR4__DENALI_PHY_47__PHY_LPBK_ERROR_COUNT_OBS_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_47__PHY_LPBK_ERROR_COUNT_OBS_0_WIDTH              16U
#define LPDDR4__PHY_LPBK_ERROR_COUNT_OBS_0__REG DENALI_PHY_47
#define LPDDR4__PHY_LPBK_ERROR_COUNT_OBS_0__FLD LPDDR4__DENALI_PHY_47__PHY_LPBK_ERROR_COUNT_OBS_0

#define LPDDR4__DENALI_PHY_47__PHY_MASTER_DLY_LOCK_OBS_0_MASK        0x07FF0000U
#define LPDDR4__DENALI_PHY_47__PHY_MASTER_DLY_LOCK_OBS_0_SHIFT               16U
#define LPDDR4__DENALI_PHY_47__PHY_MASTER_DLY_LOCK_OBS_0_WIDTH               11U
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_0__REG DENALI_PHY_47
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_0__FLD LPDDR4__DENALI_PHY_47__PHY_MASTER_DLY_LOCK_OBS_0

#define LPDDR4__DENALI_PHY_48_READ_MASK                              0xFFFF7F7FU
#define LPDDR4__DENALI_PHY_48_WRITE_MASK                             0xFFFF7F7FU
#define LPDDR4__DENALI_PHY_48__PHY_RDDQ_SLV_DLY_ENC_OBS_0_MASK       0x0000007FU
#define LPDDR4__DENALI_PHY_48__PHY_RDDQ_SLV_DLY_ENC_OBS_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_48__PHY_RDDQ_SLV_DLY_ENC_OBS_0_WIDTH               7U
#define LPDDR4__PHY_RDDQ_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_48
#define LPDDR4__PHY_RDDQ_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_48__PHY_RDDQ_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_48__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_0_MASK 0x00007F00U
#define LPDDR4__DENALI_PHY_48__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_0_SHIFT         8U
#define LPDDR4__DENALI_PHY_48__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_0_WIDTH         7U
#define LPDDR4__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_48
#define LPDDR4__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_48__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_48__PHY_MEAS_DLY_STEP_VALUE_0_MASK        0x00FF0000U
#define LPDDR4__DENALI_PHY_48__PHY_MEAS_DLY_STEP_VALUE_0_SHIFT               16U
#define LPDDR4__DENALI_PHY_48__PHY_MEAS_DLY_STEP_VALUE_0_WIDTH                8U
#define LPDDR4__PHY_MEAS_DLY_STEP_VALUE_0__REG DENALI_PHY_48
#define LPDDR4__PHY_MEAS_DLY_STEP_VALUE_0__FLD LPDDR4__DENALI_PHY_48__PHY_MEAS_DLY_STEP_VALUE_0

#define LPDDR4__DENALI_PHY_48__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_0_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_48__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_0_SHIFT 24U
#define LPDDR4__DENALI_PHY_48__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_0_WIDTH 8U
#define LPDDR4__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_48
#define LPDDR4__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_48__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_49_READ_MASK                              0x7F07FFFFU
#define LPDDR4__DENALI_PHY_49_WRITE_MASK                             0x7F07FFFFU
#define LPDDR4__DENALI_PHY_49__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_0_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_49__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_0_SHIFT 0U
#define LPDDR4__DENALI_PHY_49__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_0_WIDTH 8U
#define LPDDR4__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_49
#define LPDDR4__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_49__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_49__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_0_MASK 0x0007FF00U
#define LPDDR4__DENALI_PHY_49__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_0_SHIFT         8U
#define LPDDR4__DENALI_PHY_49__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_0_WIDTH        11U
#define LPDDR4__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_49
#define LPDDR4__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_49__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_49__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_0_MASK 0x7F000000U
#define LPDDR4__DENALI_PHY_49__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_0_SHIFT        24U
#define LPDDR4__DENALI_PHY_49__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_0_WIDTH         7U
#define LPDDR4__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_49
#define LPDDR4__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_49__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_50_READ_MASK                              0x0007FFFFU
#define LPDDR4__DENALI_PHY_50_WRITE_MASK                             0x0007FFFFU
#define LPDDR4__DENALI_PHY_50__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_0_MASK  0x000000FFU
#define LPDDR4__DENALI_PHY_50__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_50__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_0_WIDTH          8U
#define LPDDR4__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_50
#define LPDDR4__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_50__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_50__PHY_WR_ADDER_SLV_DLY_ENC_OBS_0_MASK   0x0000FF00U
#define LPDDR4__DENALI_PHY_50__PHY_WR_ADDER_SLV_DLY_ENC_OBS_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_50__PHY_WR_ADDER_SLV_DLY_ENC_OBS_0_WIDTH           8U
#define LPDDR4__PHY_WR_ADDER_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_50
#define LPDDR4__PHY_WR_ADDER_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_50__PHY_WR_ADDER_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_50__PHY_WR_SHIFT_OBS_0_MASK               0x00070000U
#define LPDDR4__DENALI_PHY_50__PHY_WR_SHIFT_OBS_0_SHIFT                      16U
#define LPDDR4__DENALI_PHY_50__PHY_WR_SHIFT_OBS_0_WIDTH                       3U
#define LPDDR4__PHY_WR_SHIFT_OBS_0__REG DENALI_PHY_50
#define LPDDR4__PHY_WR_SHIFT_OBS_0__FLD LPDDR4__DENALI_PHY_50__PHY_WR_SHIFT_OBS_0

#define LPDDR4__DENALI_PHY_51_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_51_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_51__PHY_WRLVL_HARD0_DELAY_OBS_0_MASK      0x000003FFU
#define LPDDR4__DENALI_PHY_51__PHY_WRLVL_HARD0_DELAY_OBS_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_51__PHY_WRLVL_HARD0_DELAY_OBS_0_WIDTH             10U
#define LPDDR4__PHY_WRLVL_HARD0_DELAY_OBS_0__REG DENALI_PHY_51
#define LPDDR4__PHY_WRLVL_HARD0_DELAY_OBS_0__FLD LPDDR4__DENALI_PHY_51__PHY_WRLVL_HARD0_DELAY_OBS_0

#define LPDDR4__DENALI_PHY_51__PHY_WRLVL_HARD1_DELAY_OBS_0_MASK      0x03FF0000U
#define LPDDR4__DENALI_PHY_51__PHY_WRLVL_HARD1_DELAY_OBS_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_51__PHY_WRLVL_HARD1_DELAY_OBS_0_WIDTH             10U
#define LPDDR4__PHY_WRLVL_HARD1_DELAY_OBS_0__REG DENALI_PHY_51
#define LPDDR4__PHY_WRLVL_HARD1_DELAY_OBS_0__FLD LPDDR4__DENALI_PHY_51__PHY_WRLVL_HARD1_DELAY_OBS_0

#define LPDDR4__DENALI_PHY_52_READ_MASK                              0x001FFFFFU
#define LPDDR4__DENALI_PHY_52_WRITE_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_PHY_52__PHY_WRLVL_STATUS_OBS_0_MASK           0x001FFFFFU
#define LPDDR4__DENALI_PHY_52__PHY_WRLVL_STATUS_OBS_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_52__PHY_WRLVL_STATUS_OBS_0_WIDTH                  21U
#define LPDDR4__PHY_WRLVL_STATUS_OBS_0__REG DENALI_PHY_52
#define LPDDR4__PHY_WRLVL_STATUS_OBS_0__FLD LPDDR4__DENALI_PHY_52__PHY_WRLVL_STATUS_OBS_0

#define LPDDR4__DENALI_PHY_53_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_53_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_53__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_53__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_53__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_0_WIDTH        10U
#define LPDDR4__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_53
#define LPDDR4__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_53__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_53__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_53__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_0_SHIFT        16U
#define LPDDR4__DENALI_PHY_53__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_0_WIDTH        10U
#define LPDDR4__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_53
#define LPDDR4__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_53__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_54_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_54_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_54__PHY_WRLVL_ERROR_OBS_0_MASK            0x0000FFFFU
#define LPDDR4__DENALI_PHY_54__PHY_WRLVL_ERROR_OBS_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_54__PHY_WRLVL_ERROR_OBS_0_WIDTH                   16U
#define LPDDR4__PHY_WRLVL_ERROR_OBS_0__REG DENALI_PHY_54
#define LPDDR4__PHY_WRLVL_ERROR_OBS_0__FLD LPDDR4__DENALI_PHY_54__PHY_WRLVL_ERROR_OBS_0

#define LPDDR4__DENALI_PHY_54__PHY_GTLVL_HARD0_DELAY_OBS_0_MASK      0x3FFF0000U
#define LPDDR4__DENALI_PHY_54__PHY_GTLVL_HARD0_DELAY_OBS_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_54__PHY_GTLVL_HARD0_DELAY_OBS_0_WIDTH             14U
#define LPDDR4__PHY_GTLVL_HARD0_DELAY_OBS_0__REG DENALI_PHY_54
#define LPDDR4__PHY_GTLVL_HARD0_DELAY_OBS_0__FLD LPDDR4__DENALI_PHY_54__PHY_GTLVL_HARD0_DELAY_OBS_0

#define LPDDR4__DENALI_PHY_55_READ_MASK                              0x00003FFFU
#define LPDDR4__DENALI_PHY_55_WRITE_MASK                             0x00003FFFU
#define LPDDR4__DENALI_PHY_55__PHY_GTLVL_HARD1_DELAY_OBS_0_MASK      0x00003FFFU
#define LPDDR4__DENALI_PHY_55__PHY_GTLVL_HARD1_DELAY_OBS_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_55__PHY_GTLVL_HARD1_DELAY_OBS_0_WIDTH             14U
#define LPDDR4__PHY_GTLVL_HARD1_DELAY_OBS_0__REG DENALI_PHY_55
#define LPDDR4__PHY_GTLVL_HARD1_DELAY_OBS_0__FLD LPDDR4__DENALI_PHY_55__PHY_GTLVL_HARD1_DELAY_OBS_0

#define LPDDR4__DENALI_PHY_56_READ_MASK                              0x0003FFFFU
#define LPDDR4__DENALI_PHY_56_WRITE_MASK                             0x0003FFFFU
#define LPDDR4__DENALI_PHY_56__PHY_GTLVL_STATUS_OBS_0_MASK           0x0003FFFFU
#define LPDDR4__DENALI_PHY_56__PHY_GTLVL_STATUS_OBS_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_56__PHY_GTLVL_STATUS_OBS_0_WIDTH                  18U
#define LPDDR4__PHY_GTLVL_STATUS_OBS_0__REG DENALI_PHY_56
#define LPDDR4__PHY_GTLVL_STATUS_OBS_0__FLD LPDDR4__DENALI_PHY_56__PHY_GTLVL_STATUS_OBS_0

#define LPDDR4__DENALI_PHY_57_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_57_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_57__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_0_MASK  0x000003FFU
#define LPDDR4__DENALI_PHY_57__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_57__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_0_WIDTH         10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_0__REG DENALI_PHY_57
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_0__FLD LPDDR4__DENALI_PHY_57__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_0

#define LPDDR4__DENALI_PHY_57__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_0_MASK  0x03FF0000U
#define LPDDR4__DENALI_PHY_57__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_0_SHIFT         16U
#define LPDDR4__DENALI_PHY_57__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_0_WIDTH         10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_0__REG DENALI_PHY_57
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_0__FLD LPDDR4__DENALI_PHY_57__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_0

#define LPDDR4__DENALI_PHY_58_READ_MASK                              0x00000003U
#define LPDDR4__DENALI_PHY_58_WRITE_MASK                             0x00000003U
#define LPDDR4__DENALI_PHY_58__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_0_MASK 0x00000003U
#define LPDDR4__DENALI_PHY_58__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_58__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_0_WIDTH     2U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_0__REG DENALI_PHY_58
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_0__FLD LPDDR4__DENALI_PHY_58__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_0

#define LPDDR4__DENALI_PHY_59_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_59_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_59__PHY_RDLVL_STATUS_OBS_0_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_59__PHY_RDLVL_STATUS_OBS_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_59__PHY_RDLVL_STATUS_OBS_0_WIDTH                  32U
#define LPDDR4__PHY_RDLVL_STATUS_OBS_0__REG DENALI_PHY_59
#define LPDDR4__PHY_RDLVL_STATUS_OBS_0__FLD LPDDR4__DENALI_PHY_59__PHY_RDLVL_STATUS_OBS_0

#define LPDDR4__DENALI_PHY_60_READ_MASK                              0x07FF07FFU
#define LPDDR4__DENALI_PHY_60_WRITE_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_60__PHY_WDQLVL_DQDM_LE_DLY_OBS_0_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_60__PHY_WDQLVL_DQDM_LE_DLY_OBS_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_60__PHY_WDQLVL_DQDM_LE_DLY_OBS_0_WIDTH            11U
#define LPDDR4__PHY_WDQLVL_DQDM_LE_DLY_OBS_0__REG DENALI_PHY_60
#define LPDDR4__PHY_WDQLVL_DQDM_LE_DLY_OBS_0__FLD LPDDR4__DENALI_PHY_60__PHY_WDQLVL_DQDM_LE_DLY_OBS_0

#define LPDDR4__DENALI_PHY_60__PHY_WDQLVL_DQDM_TE_DLY_OBS_0_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_60__PHY_WDQLVL_DQDM_TE_DLY_OBS_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_60__PHY_WDQLVL_DQDM_TE_DLY_OBS_0_WIDTH            11U
#define LPDDR4__PHY_WDQLVL_DQDM_TE_DLY_OBS_0__REG DENALI_PHY_60
#define LPDDR4__PHY_WDQLVL_DQDM_TE_DLY_OBS_0__FLD LPDDR4__DENALI_PHY_60__PHY_WDQLVL_DQDM_TE_DLY_OBS_0

#define LPDDR4__DENALI_PHY_61_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_61_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_61__PHY_WDQLVL_STATUS_OBS_0_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_61__PHY_WDQLVL_STATUS_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_61__PHY_WDQLVL_STATUS_OBS_0_WIDTH                 32U
#define LPDDR4__PHY_WDQLVL_STATUS_OBS_0__REG DENALI_PHY_61
#define LPDDR4__PHY_WDQLVL_STATUS_OBS_0__FLD LPDDR4__DENALI_PHY_61__PHY_WDQLVL_STATUS_OBS_0

#define LPDDR4__DENALI_PHY_62_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_62_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_62__PHY_WDQLVL_PERIODIC_OBS_0_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_62__PHY_WDQLVL_PERIODIC_OBS_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_62__PHY_WDQLVL_PERIODIC_OBS_0_WIDTH               32U
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_0__REG DENALI_PHY_62
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_0__FLD LPDDR4__DENALI_PHY_62__PHY_WDQLVL_PERIODIC_OBS_0

#define LPDDR4__DENALI_PHY_63_READ_MASK                              0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_63_WRITE_MASK                             0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_63__PHY_DDL_MODE_0_MASK                   0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_63__PHY_DDL_MODE_0_SHIFT                           0U
#define LPDDR4__DENALI_PHY_63__PHY_DDL_MODE_0_WIDTH                          31U
#define LPDDR4__PHY_DDL_MODE_0__REG DENALI_PHY_63
#define LPDDR4__PHY_DDL_MODE_0__FLD LPDDR4__DENALI_PHY_63__PHY_DDL_MODE_0

#define LPDDR4__DENALI_PHY_64_READ_MASK                              0x0000003FU
#define LPDDR4__DENALI_PHY_64_WRITE_MASK                             0x0000003FU
#define LPDDR4__DENALI_PHY_64__PHY_DDL_MASK_0_MASK                   0x0000003FU
#define LPDDR4__DENALI_PHY_64__PHY_DDL_MASK_0_SHIFT                           0U
#define LPDDR4__DENALI_PHY_64__PHY_DDL_MASK_0_WIDTH                           6U
#define LPDDR4__PHY_DDL_MASK_0__REG DENALI_PHY_64
#define LPDDR4__PHY_DDL_MASK_0__FLD LPDDR4__DENALI_PHY_64__PHY_DDL_MASK_0

#define LPDDR4__DENALI_PHY_65_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_65_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_65__PHY_DDL_TEST_OBS_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_65__PHY_DDL_TEST_OBS_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_65__PHY_DDL_TEST_OBS_0_WIDTH                      32U
#define LPDDR4__PHY_DDL_TEST_OBS_0__REG DENALI_PHY_65
#define LPDDR4__PHY_DDL_TEST_OBS_0__FLD LPDDR4__DENALI_PHY_65__PHY_DDL_TEST_OBS_0

#define LPDDR4__DENALI_PHY_66_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_66_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_66__PHY_DDL_TEST_MSTR_DLY_OBS_0_MASK      0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_66__PHY_DDL_TEST_MSTR_DLY_OBS_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_66__PHY_DDL_TEST_MSTR_DLY_OBS_0_WIDTH             32U
#define LPDDR4__PHY_DDL_TEST_MSTR_DLY_OBS_0__REG DENALI_PHY_66
#define LPDDR4__PHY_DDL_TEST_MSTR_DLY_OBS_0__FLD LPDDR4__DENALI_PHY_66__PHY_DDL_TEST_MSTR_DLY_OBS_0

#define LPDDR4__DENALI_PHY_67_READ_MASK                              0x01FF01FFU
#define LPDDR4__DENALI_PHY_67_WRITE_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_67__PHY_DDL_TRACK_UPD_THRESHOLD_0_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_67__PHY_DDL_TRACK_UPD_THRESHOLD_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_67__PHY_DDL_TRACK_UPD_THRESHOLD_0_WIDTH            8U
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_0__REG DENALI_PHY_67
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_0__FLD LPDDR4__DENALI_PHY_67__PHY_DDL_TRACK_UPD_THRESHOLD_0

#define LPDDR4__DENALI_PHY_67__PHY_LP4_WDQS_OE_EXTEND_0_MASK         0x00000100U
#define LPDDR4__DENALI_PHY_67__PHY_LP4_WDQS_OE_EXTEND_0_SHIFT                 8U
#define LPDDR4__DENALI_PHY_67__PHY_LP4_WDQS_OE_EXTEND_0_WIDTH                 1U
#define LPDDR4__DENALI_PHY_67__PHY_LP4_WDQS_OE_EXTEND_0_WOCLR                 0U
#define LPDDR4__DENALI_PHY_67__PHY_LP4_WDQS_OE_EXTEND_0_WOSET                 0U
#define LPDDR4__PHY_LP4_WDQS_OE_EXTEND_0__REG DENALI_PHY_67
#define LPDDR4__PHY_LP4_WDQS_OE_EXTEND_0__FLD LPDDR4__DENALI_PHY_67__PHY_LP4_WDQS_OE_EXTEND_0

#define LPDDR4__DENALI_PHY_67__PHY_RX_CAL_DQ0_0_MASK                 0x01FF0000U
#define LPDDR4__DENALI_PHY_67__PHY_RX_CAL_DQ0_0_SHIFT                        16U
#define LPDDR4__DENALI_PHY_67__PHY_RX_CAL_DQ0_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQ0_0__REG DENALI_PHY_67
#define LPDDR4__PHY_RX_CAL_DQ0_0__FLD LPDDR4__DENALI_PHY_67__PHY_RX_CAL_DQ0_0

#define LPDDR4__DENALI_PHY_68_READ_MASK                              0x01FF01FFU
#define LPDDR4__DENALI_PHY_68_WRITE_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_68__PHY_RX_CAL_DQ1_0_MASK                 0x000001FFU
#define LPDDR4__DENALI_PHY_68__PHY_RX_CAL_DQ1_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_68__PHY_RX_CAL_DQ1_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQ1_0__REG DENALI_PHY_68
#define LPDDR4__PHY_RX_CAL_DQ1_0__FLD LPDDR4__DENALI_PHY_68__PHY_RX_CAL_DQ1_0

#define LPDDR4__DENALI_PHY_68__PHY_RX_CAL_DQ2_0_MASK                 0x01FF0000U
#define LPDDR4__DENALI_PHY_68__PHY_RX_CAL_DQ2_0_SHIFT                        16U
#define LPDDR4__DENALI_PHY_68__PHY_RX_CAL_DQ2_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQ2_0__REG DENALI_PHY_68
#define LPDDR4__PHY_RX_CAL_DQ2_0__FLD LPDDR4__DENALI_PHY_68__PHY_RX_CAL_DQ2_0

#define LPDDR4__DENALI_PHY_69_READ_MASK                              0x01FF01FFU
#define LPDDR4__DENALI_PHY_69_WRITE_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_69__PHY_RX_CAL_DQ3_0_MASK                 0x000001FFU
#define LPDDR4__DENALI_PHY_69__PHY_RX_CAL_DQ3_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_69__PHY_RX_CAL_DQ3_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQ3_0__REG DENALI_PHY_69
#define LPDDR4__PHY_RX_CAL_DQ3_0__FLD LPDDR4__DENALI_PHY_69__PHY_RX_CAL_DQ3_0

#define LPDDR4__DENALI_PHY_69__PHY_RX_CAL_DQ4_0_MASK                 0x01FF0000U
#define LPDDR4__DENALI_PHY_69__PHY_RX_CAL_DQ4_0_SHIFT                        16U
#define LPDDR4__DENALI_PHY_69__PHY_RX_CAL_DQ4_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQ4_0__REG DENALI_PHY_69
#define LPDDR4__PHY_RX_CAL_DQ4_0__FLD LPDDR4__DENALI_PHY_69__PHY_RX_CAL_DQ4_0

#define LPDDR4__DENALI_PHY_70_READ_MASK                              0x01FF01FFU
#define LPDDR4__DENALI_PHY_70_WRITE_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_70__PHY_RX_CAL_DQ5_0_MASK                 0x000001FFU
#define LPDDR4__DENALI_PHY_70__PHY_RX_CAL_DQ5_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_70__PHY_RX_CAL_DQ5_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQ5_0__REG DENALI_PHY_70
#define LPDDR4__PHY_RX_CAL_DQ5_0__FLD LPDDR4__DENALI_PHY_70__PHY_RX_CAL_DQ5_0

#define LPDDR4__DENALI_PHY_70__PHY_RX_CAL_DQ6_0_MASK                 0x01FF0000U
#define LPDDR4__DENALI_PHY_70__PHY_RX_CAL_DQ6_0_SHIFT                        16U
#define LPDDR4__DENALI_PHY_70__PHY_RX_CAL_DQ6_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQ6_0__REG DENALI_PHY_70
#define LPDDR4__PHY_RX_CAL_DQ6_0__FLD LPDDR4__DENALI_PHY_70__PHY_RX_CAL_DQ6_0

#define LPDDR4__DENALI_PHY_71_READ_MASK                              0x000001FFU
#define LPDDR4__DENALI_PHY_71_WRITE_MASK                             0x000001FFU
#define LPDDR4__DENALI_PHY_71__PHY_RX_CAL_DQ7_0_MASK                 0x000001FFU
#define LPDDR4__DENALI_PHY_71__PHY_RX_CAL_DQ7_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_71__PHY_RX_CAL_DQ7_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQ7_0__REG DENALI_PHY_71
#define LPDDR4__PHY_RX_CAL_DQ7_0__FLD LPDDR4__DENALI_PHY_71__PHY_RX_CAL_DQ7_0

#define LPDDR4__DENALI_PHY_72_READ_MASK                              0x0003FFFFU
#define LPDDR4__DENALI_PHY_72_WRITE_MASK                             0x0003FFFFU
#define LPDDR4__DENALI_PHY_72__PHY_RX_CAL_DM_0_MASK                  0x0003FFFFU
#define LPDDR4__DENALI_PHY_72__PHY_RX_CAL_DM_0_SHIFT                          0U
#define LPDDR4__DENALI_PHY_72__PHY_RX_CAL_DM_0_WIDTH                         18U
#define LPDDR4__PHY_RX_CAL_DM_0__REG DENALI_PHY_72
#define LPDDR4__PHY_RX_CAL_DM_0__FLD LPDDR4__DENALI_PHY_72__PHY_RX_CAL_DM_0

#define LPDDR4__DENALI_PHY_73_READ_MASK                              0x01FF01FFU
#define LPDDR4__DENALI_PHY_73_WRITE_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_73__PHY_RX_CAL_DQS_0_MASK                 0x000001FFU
#define LPDDR4__DENALI_PHY_73__PHY_RX_CAL_DQS_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_73__PHY_RX_CAL_DQS_0_WIDTH                         9U
#define LPDDR4__PHY_RX_CAL_DQS_0__REG DENALI_PHY_73
#define LPDDR4__PHY_RX_CAL_DQS_0__FLD LPDDR4__DENALI_PHY_73__PHY_RX_CAL_DQS_0

#define LPDDR4__DENALI_PHY_73__PHY_RX_CAL_FDBK_0_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_73__PHY_RX_CAL_FDBK_0_SHIFT                       16U
#define LPDDR4__DENALI_PHY_73__PHY_RX_CAL_FDBK_0_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_FDBK_0__REG DENALI_PHY_73
#define LPDDR4__PHY_RX_CAL_FDBK_0__FLD LPDDR4__DENALI_PHY_73__PHY_RX_CAL_FDBK_0

#define LPDDR4__DENALI_PHY_74_READ_MASK                              0xFF1F07FFU
#define LPDDR4__DENALI_PHY_74_WRITE_MASK                             0xFF1F07FFU
#define LPDDR4__DENALI_PHY_74__PHY_PAD_RX_BIAS_EN_0_MASK             0x000007FFU
#define LPDDR4__DENALI_PHY_74__PHY_PAD_RX_BIAS_EN_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_74__PHY_PAD_RX_BIAS_EN_0_WIDTH                    11U
#define LPDDR4__PHY_PAD_RX_BIAS_EN_0__REG DENALI_PHY_74
#define LPDDR4__PHY_PAD_RX_BIAS_EN_0__FLD LPDDR4__DENALI_PHY_74__PHY_PAD_RX_BIAS_EN_0

#define LPDDR4__DENALI_PHY_74__PHY_STATIC_TOG_DISABLE_0_MASK         0x001F0000U
#define LPDDR4__DENALI_PHY_74__PHY_STATIC_TOG_DISABLE_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_74__PHY_STATIC_TOG_DISABLE_0_WIDTH                 5U
#define LPDDR4__PHY_STATIC_TOG_DISABLE_0__REG DENALI_PHY_74
#define LPDDR4__PHY_STATIC_TOG_DISABLE_0__FLD LPDDR4__DENALI_PHY_74__PHY_STATIC_TOG_DISABLE_0

#define LPDDR4__DENALI_PHY_74__PHY_DATA_DC_CAL_SAMPLE_WAIT_0_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_74__PHY_DATA_DC_CAL_SAMPLE_WAIT_0_SHIFT           24U
#define LPDDR4__DENALI_PHY_74__PHY_DATA_DC_CAL_SAMPLE_WAIT_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_CAL_SAMPLE_WAIT_0__REG DENALI_PHY_74
#define LPDDR4__PHY_DATA_DC_CAL_SAMPLE_WAIT_0__FLD LPDDR4__DENALI_PHY_74__PHY_DATA_DC_CAL_SAMPLE_WAIT_0

#define LPDDR4__DENALI_PHY_75_READ_MASK                              0xFF3F03FFU
#define LPDDR4__DENALI_PHY_75_WRITE_MASK                             0xFF3F03FFU
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_CAL_TIMEOUT_0_MASK        0x000000FFU
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_CAL_TIMEOUT_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_CAL_TIMEOUT_0_WIDTH                8U
#define LPDDR4__PHY_DATA_DC_CAL_TIMEOUT_0__REG DENALI_PHY_75
#define LPDDR4__PHY_DATA_DC_CAL_TIMEOUT_0__FLD LPDDR4__DENALI_PHY_75__PHY_DATA_DC_CAL_TIMEOUT_0

#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_WEIGHT_0_MASK             0x00000300U
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_WEIGHT_0_SHIFT                     8U
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_WEIGHT_0_WIDTH                     2U
#define LPDDR4__PHY_DATA_DC_WEIGHT_0__REG DENALI_PHY_75
#define LPDDR4__PHY_DATA_DC_WEIGHT_0__FLD LPDDR4__DENALI_PHY_75__PHY_DATA_DC_WEIGHT_0

#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_ADJUST_START_0_MASK       0x003F0000U
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_ADJUST_START_0_SHIFT              16U
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_ADJUST_START_0_WIDTH               6U
#define LPDDR4__PHY_DATA_DC_ADJUST_START_0__REG DENALI_PHY_75
#define LPDDR4__PHY_DATA_DC_ADJUST_START_0__FLD LPDDR4__DENALI_PHY_75__PHY_DATA_DC_ADJUST_START_0

#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_ADJUST_SAMPLE_CNT_0_MASK  0xFF000000U
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_ADJUST_SAMPLE_CNT_0_SHIFT         24U
#define LPDDR4__DENALI_PHY_75__PHY_DATA_DC_ADJUST_SAMPLE_CNT_0_WIDTH          8U
#define LPDDR4__PHY_DATA_DC_ADJUST_SAMPLE_CNT_0__REG DENALI_PHY_75
#define LPDDR4__PHY_DATA_DC_ADJUST_SAMPLE_CNT_0__FLD LPDDR4__DENALI_PHY_75__PHY_DATA_DC_ADJUST_SAMPLE_CNT_0

#define LPDDR4__DENALI_PHY_76_READ_MASK                              0x010101FFU
#define LPDDR4__DENALI_PHY_76_WRITE_MASK                             0x010101FFU
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_THRSHLD_0_MASK     0x000000FFU
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_THRSHLD_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_THRSHLD_0_WIDTH             8U
#define LPDDR4__PHY_DATA_DC_ADJUST_THRSHLD_0__REG DENALI_PHY_76
#define LPDDR4__PHY_DATA_DC_ADJUST_THRSHLD_0__FLD LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_THRSHLD_0

#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_DIRECT_0_MASK      0x00000100U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_DIRECT_0_SHIFT              8U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_DIRECT_0_WIDTH              1U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_DIRECT_0_WOCLR              0U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_DIRECT_0_WOSET              0U
#define LPDDR4__PHY_DATA_DC_ADJUST_DIRECT_0__REG DENALI_PHY_76
#define LPDDR4__PHY_DATA_DC_ADJUST_DIRECT_0__FLD LPDDR4__DENALI_PHY_76__PHY_DATA_DC_ADJUST_DIRECT_0

#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_POLARITY_0_MASK       0x00010000U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_POLARITY_0_SHIFT              16U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_POLARITY_0_WIDTH               1U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_POLARITY_0_WOCLR               0U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_POLARITY_0_WOSET               0U
#define LPDDR4__PHY_DATA_DC_CAL_POLARITY_0__REG DENALI_PHY_76
#define LPDDR4__PHY_DATA_DC_CAL_POLARITY_0__FLD LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_POLARITY_0

#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_START_0_MASK          0x01000000U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_START_0_SHIFT                 24U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_START_0_WIDTH                  1U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_START_0_WOCLR                  0U
#define LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_START_0_WOSET                  0U
#define LPDDR4__PHY_DATA_DC_CAL_START_0__REG DENALI_PHY_76
#define LPDDR4__PHY_DATA_DC_CAL_START_0__FLD LPDDR4__DENALI_PHY_76__PHY_DATA_DC_CAL_START_0

#define LPDDR4__DENALI_PHY_77_READ_MASK                              0x01010703U
#define LPDDR4__DENALI_PHY_77_WRITE_MASK                             0x01010703U
#define LPDDR4__DENALI_PHY_77__PHY_DATA_DC_SW_RANK_0_MASK            0x00000003U
#define LPDDR4__DENALI_PHY_77__PHY_DATA_DC_SW_RANK_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_77__PHY_DATA_DC_SW_RANK_0_WIDTH                    2U
#define LPDDR4__PHY_DATA_DC_SW_RANK_0__REG DENALI_PHY_77
#define LPDDR4__PHY_DATA_DC_SW_RANK_0__FLD LPDDR4__DENALI_PHY_77__PHY_DATA_DC_SW_RANK_0

#define LPDDR4__DENALI_PHY_77__PHY_FDBK_PWR_CTRL_0_MASK              0x00000700U
#define LPDDR4__DENALI_PHY_77__PHY_FDBK_PWR_CTRL_0_SHIFT                      8U
#define LPDDR4__DENALI_PHY_77__PHY_FDBK_PWR_CTRL_0_WIDTH                      3U
#define LPDDR4__PHY_FDBK_PWR_CTRL_0__REG DENALI_PHY_77
#define LPDDR4__PHY_FDBK_PWR_CTRL_0__FLD LPDDR4__DENALI_PHY_77__PHY_FDBK_PWR_CTRL_0

#define LPDDR4__DENALI_PHY_77__PHY_SLV_DLY_CTRL_GATE_DISABLE_0_MASK  0x00010000U
#define LPDDR4__DENALI_PHY_77__PHY_SLV_DLY_CTRL_GATE_DISABLE_0_SHIFT         16U
#define LPDDR4__DENALI_PHY_77__PHY_SLV_DLY_CTRL_GATE_DISABLE_0_WIDTH          1U
#define LPDDR4__DENALI_PHY_77__PHY_SLV_DLY_CTRL_GATE_DISABLE_0_WOCLR          0U
#define LPDDR4__DENALI_PHY_77__PHY_SLV_DLY_CTRL_GATE_DISABLE_0_WOSET          0U
#define LPDDR4__PHY_SLV_DLY_CTRL_GATE_DISABLE_0__REG DENALI_PHY_77
#define LPDDR4__PHY_SLV_DLY_CTRL_GATE_DISABLE_0__FLD LPDDR4__DENALI_PHY_77__PHY_SLV_DLY_CTRL_GATE_DISABLE_0

#define LPDDR4__DENALI_PHY_77__PHY_RDPATH_GATE_DISABLE_0_MASK        0x01000000U
#define LPDDR4__DENALI_PHY_77__PHY_RDPATH_GATE_DISABLE_0_SHIFT               24U
#define LPDDR4__DENALI_PHY_77__PHY_RDPATH_GATE_DISABLE_0_WIDTH                1U
#define LPDDR4__DENALI_PHY_77__PHY_RDPATH_GATE_DISABLE_0_WOCLR                0U
#define LPDDR4__DENALI_PHY_77__PHY_RDPATH_GATE_DISABLE_0_WOSET                0U
#define LPDDR4__PHY_RDPATH_GATE_DISABLE_0__REG DENALI_PHY_77
#define LPDDR4__PHY_RDPATH_GATE_DISABLE_0__FLD LPDDR4__DENALI_PHY_77__PHY_RDPATH_GATE_DISABLE_0

#define LPDDR4__DENALI_PHY_78_READ_MASK                              0x00000101U
#define LPDDR4__DENALI_PHY_78_WRITE_MASK                             0x00000101U
#define LPDDR4__DENALI_PHY_78__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_0_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_78__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_78__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_0_WIDTH        1U
#define LPDDR4__DENALI_PHY_78__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_0_WOCLR        0U
#define LPDDR4__DENALI_PHY_78__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_0_WOSET        0U
#define LPDDR4__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_0__REG DENALI_PHY_78
#define LPDDR4__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_0__FLD LPDDR4__DENALI_PHY_78__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_0

#define LPDDR4__DENALI_PHY_78__PHY_SLICE_PWR_RDC_DISABLE_0_MASK      0x00000100U
#define LPDDR4__DENALI_PHY_78__PHY_SLICE_PWR_RDC_DISABLE_0_SHIFT              8U
#define LPDDR4__DENALI_PHY_78__PHY_SLICE_PWR_RDC_DISABLE_0_WIDTH              1U
#define LPDDR4__DENALI_PHY_78__PHY_SLICE_PWR_RDC_DISABLE_0_WOCLR              0U
#define LPDDR4__DENALI_PHY_78__PHY_SLICE_PWR_RDC_DISABLE_0_WOSET              0U
#define LPDDR4__PHY_SLICE_PWR_RDC_DISABLE_0__REG DENALI_PHY_78
#define LPDDR4__PHY_SLICE_PWR_RDC_DISABLE_0__FLD LPDDR4__DENALI_PHY_78__PHY_SLICE_PWR_RDC_DISABLE_0

#define LPDDR4__DENALI_PHY_79_READ_MASK                              0x07FFFF07U
#define LPDDR4__DENALI_PHY_79_WRITE_MASK                             0x07FFFF07U
#define LPDDR4__DENALI_PHY_79__PHY_DQ_TSEL_ENABLE_0_MASK             0x00000007U
#define LPDDR4__DENALI_PHY_79__PHY_DQ_TSEL_ENABLE_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_79__PHY_DQ_TSEL_ENABLE_0_WIDTH                     3U
#define LPDDR4__PHY_DQ_TSEL_ENABLE_0__REG DENALI_PHY_79
#define LPDDR4__PHY_DQ_TSEL_ENABLE_0__FLD LPDDR4__DENALI_PHY_79__PHY_DQ_TSEL_ENABLE_0

#define LPDDR4__DENALI_PHY_79__PHY_DQ_TSEL_SELECT_0_MASK             0x00FFFF00U
#define LPDDR4__DENALI_PHY_79__PHY_DQ_TSEL_SELECT_0_SHIFT                     8U
#define LPDDR4__DENALI_PHY_79__PHY_DQ_TSEL_SELECT_0_WIDTH                    16U
#define LPDDR4__PHY_DQ_TSEL_SELECT_0__REG DENALI_PHY_79
#define LPDDR4__PHY_DQ_TSEL_SELECT_0__FLD LPDDR4__DENALI_PHY_79__PHY_DQ_TSEL_SELECT_0

#define LPDDR4__DENALI_PHY_79__PHY_DQS_TSEL_ENABLE_0_MASK            0x07000000U
#define LPDDR4__DENALI_PHY_79__PHY_DQS_TSEL_ENABLE_0_SHIFT                   24U
#define LPDDR4__DENALI_PHY_79__PHY_DQS_TSEL_ENABLE_0_WIDTH                    3U
#define LPDDR4__PHY_DQS_TSEL_ENABLE_0__REG DENALI_PHY_79
#define LPDDR4__PHY_DQS_TSEL_ENABLE_0__FLD LPDDR4__DENALI_PHY_79__PHY_DQS_TSEL_ENABLE_0

#define LPDDR4__DENALI_PHY_80_READ_MASK                              0x7F03FFFFU
#define LPDDR4__DENALI_PHY_80_WRITE_MASK                             0x7F03FFFFU
#define LPDDR4__DENALI_PHY_80__PHY_DQS_TSEL_SELECT_0_MASK            0x0000FFFFU
#define LPDDR4__DENALI_PHY_80__PHY_DQS_TSEL_SELECT_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_80__PHY_DQS_TSEL_SELECT_0_WIDTH                   16U
#define LPDDR4__PHY_DQS_TSEL_SELECT_0__REG DENALI_PHY_80
#define LPDDR4__PHY_DQS_TSEL_SELECT_0__FLD LPDDR4__DENALI_PHY_80__PHY_DQS_TSEL_SELECT_0

#define LPDDR4__DENALI_PHY_80__PHY_TWO_CYC_PREAMBLE_0_MASK           0x00030000U
#define LPDDR4__DENALI_PHY_80__PHY_TWO_CYC_PREAMBLE_0_SHIFT                  16U
#define LPDDR4__DENALI_PHY_80__PHY_TWO_CYC_PREAMBLE_0_WIDTH                   2U
#define LPDDR4__PHY_TWO_CYC_PREAMBLE_0__REG DENALI_PHY_80
#define LPDDR4__PHY_TWO_CYC_PREAMBLE_0__FLD LPDDR4__DENALI_PHY_80__PHY_TWO_CYC_PREAMBLE_0

#define LPDDR4__DENALI_PHY_80__PHY_VREF_INITIAL_START_POINT_0_MASK   0x7F000000U
#define LPDDR4__DENALI_PHY_80__PHY_VREF_INITIAL_START_POINT_0_SHIFT          24U
#define LPDDR4__DENALI_PHY_80__PHY_VREF_INITIAL_START_POINT_0_WIDTH           7U
#define LPDDR4__PHY_VREF_INITIAL_START_POINT_0__REG DENALI_PHY_80
#define LPDDR4__PHY_VREF_INITIAL_START_POINT_0__FLD LPDDR4__DENALI_PHY_80__PHY_VREF_INITIAL_START_POINT_0

#define LPDDR4__DENALI_PHY_81_READ_MASK                              0xFF01037FU
#define LPDDR4__DENALI_PHY_81_WRITE_MASK                             0xFF01037FU
#define LPDDR4__DENALI_PHY_81__PHY_VREF_INITIAL_STOP_POINT_0_MASK    0x0000007FU
#define LPDDR4__DENALI_PHY_81__PHY_VREF_INITIAL_STOP_POINT_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_81__PHY_VREF_INITIAL_STOP_POINT_0_WIDTH            7U
#define LPDDR4__PHY_VREF_INITIAL_STOP_POINT_0__REG DENALI_PHY_81
#define LPDDR4__PHY_VREF_INITIAL_STOP_POINT_0__FLD LPDDR4__DENALI_PHY_81__PHY_VREF_INITIAL_STOP_POINT_0

#define LPDDR4__DENALI_PHY_81__PHY_VREF_TRAINING_CTRL_0_MASK         0x00000300U
#define LPDDR4__DENALI_PHY_81__PHY_VREF_TRAINING_CTRL_0_SHIFT                 8U
#define LPDDR4__DENALI_PHY_81__PHY_VREF_TRAINING_CTRL_0_WIDTH                 2U
#define LPDDR4__PHY_VREF_TRAINING_CTRL_0__REG DENALI_PHY_81
#define LPDDR4__PHY_VREF_TRAINING_CTRL_0__FLD LPDDR4__DENALI_PHY_81__PHY_VREF_TRAINING_CTRL_0

#define LPDDR4__DENALI_PHY_81__PHY_NTP_TRAIN_EN_0_MASK               0x00010000U
#define LPDDR4__DENALI_PHY_81__PHY_NTP_TRAIN_EN_0_SHIFT                      16U
#define LPDDR4__DENALI_PHY_81__PHY_NTP_TRAIN_EN_0_WIDTH                       1U
#define LPDDR4__DENALI_PHY_81__PHY_NTP_TRAIN_EN_0_WOCLR                       0U
#define LPDDR4__DENALI_PHY_81__PHY_NTP_TRAIN_EN_0_WOSET                       0U
#define LPDDR4__PHY_NTP_TRAIN_EN_0__REG DENALI_PHY_81
#define LPDDR4__PHY_NTP_TRAIN_EN_0__FLD LPDDR4__DENALI_PHY_81__PHY_NTP_TRAIN_EN_0

#define LPDDR4__DENALI_PHY_81__PHY_NTP_WDQ_STEP_SIZE_0_MASK          0xFF000000U
#define LPDDR4__DENALI_PHY_81__PHY_NTP_WDQ_STEP_SIZE_0_SHIFT                 24U
#define LPDDR4__DENALI_PHY_81__PHY_NTP_WDQ_STEP_SIZE_0_WIDTH                  8U
#define LPDDR4__PHY_NTP_WDQ_STEP_SIZE_0__REG DENALI_PHY_81
#define LPDDR4__PHY_NTP_WDQ_STEP_SIZE_0__FLD LPDDR4__DENALI_PHY_81__PHY_NTP_WDQ_STEP_SIZE_0

#define LPDDR4__DENALI_PHY_82_READ_MASK                              0x07FF07FFU
#define LPDDR4__DENALI_PHY_82_WRITE_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_82__PHY_NTP_WDQ_START_0_MASK              0x000007FFU
#define LPDDR4__DENALI_PHY_82__PHY_NTP_WDQ_START_0_SHIFT                      0U
#define LPDDR4__DENALI_PHY_82__PHY_NTP_WDQ_START_0_WIDTH                     11U
#define LPDDR4__PHY_NTP_WDQ_START_0__REG DENALI_PHY_82
#define LPDDR4__PHY_NTP_WDQ_START_0__FLD LPDDR4__DENALI_PHY_82__PHY_NTP_WDQ_START_0

#define LPDDR4__DENALI_PHY_82__PHY_NTP_WDQ_STOP_0_MASK               0x07FF0000U
#define LPDDR4__DENALI_PHY_82__PHY_NTP_WDQ_STOP_0_SHIFT                      16U
#define LPDDR4__DENALI_PHY_82__PHY_NTP_WDQ_STOP_0_WIDTH                      11U
#define LPDDR4__PHY_NTP_WDQ_STOP_0__REG DENALI_PHY_82
#define LPDDR4__PHY_NTP_WDQ_STOP_0__FLD LPDDR4__DENALI_PHY_82__PHY_NTP_WDQ_STOP_0

#define LPDDR4__DENALI_PHY_83_READ_MASK                              0x0103FFFFU
#define LPDDR4__DENALI_PHY_83_WRITE_MASK                             0x0103FFFFU
#define LPDDR4__DENALI_PHY_83__PHY_NTP_WDQ_BIT_EN_0_MASK             0x000000FFU
#define LPDDR4__DENALI_PHY_83__PHY_NTP_WDQ_BIT_EN_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_83__PHY_NTP_WDQ_BIT_EN_0_WIDTH                     8U
#define LPDDR4__PHY_NTP_WDQ_BIT_EN_0__REG DENALI_PHY_83
#define LPDDR4__PHY_NTP_WDQ_BIT_EN_0__FLD LPDDR4__DENALI_PHY_83__PHY_NTP_WDQ_BIT_EN_0

#define LPDDR4__DENALI_PHY_83__PHY_WDQLVL_DVW_MIN_0_MASK             0x0003FF00U
#define LPDDR4__DENALI_PHY_83__PHY_WDQLVL_DVW_MIN_0_SHIFT                     8U
#define LPDDR4__DENALI_PHY_83__PHY_WDQLVL_DVW_MIN_0_WIDTH                    10U
#define LPDDR4__PHY_WDQLVL_DVW_MIN_0__REG DENALI_PHY_83
#define LPDDR4__PHY_WDQLVL_DVW_MIN_0__FLD LPDDR4__DENALI_PHY_83__PHY_WDQLVL_DVW_MIN_0

#define LPDDR4__DENALI_PHY_83__PHY_SW_WDQLVL_DVW_MIN_EN_0_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_83__PHY_SW_WDQLVL_DVW_MIN_EN_0_SHIFT              24U
#define LPDDR4__DENALI_PHY_83__PHY_SW_WDQLVL_DVW_MIN_EN_0_WIDTH               1U
#define LPDDR4__DENALI_PHY_83__PHY_SW_WDQLVL_DVW_MIN_EN_0_WOCLR               0U
#define LPDDR4__DENALI_PHY_83__PHY_SW_WDQLVL_DVW_MIN_EN_0_WOSET               0U
#define LPDDR4__PHY_SW_WDQLVL_DVW_MIN_EN_0__REG DENALI_PHY_83
#define LPDDR4__PHY_SW_WDQLVL_DVW_MIN_EN_0__FLD LPDDR4__DENALI_PHY_83__PHY_SW_WDQLVL_DVW_MIN_EN_0

#define LPDDR4__DENALI_PHY_84_READ_MASK                              0x1F1F0F3FU
#define LPDDR4__DENALI_PHY_84_WRITE_MASK                             0x1F1F0F3FU
#define LPDDR4__DENALI_PHY_84__PHY_WDQLVL_PER_START_OFFSET_0_MASK    0x0000003FU
#define LPDDR4__DENALI_PHY_84__PHY_WDQLVL_PER_START_OFFSET_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_84__PHY_WDQLVL_PER_START_OFFSET_0_WIDTH            6U
#define LPDDR4__PHY_WDQLVL_PER_START_OFFSET_0__REG DENALI_PHY_84
#define LPDDR4__PHY_WDQLVL_PER_START_OFFSET_0__FLD LPDDR4__DENALI_PHY_84__PHY_WDQLVL_PER_START_OFFSET_0

#define LPDDR4__DENALI_PHY_84__PHY_FAST_LVL_EN_0_MASK                0x00000F00U
#define LPDDR4__DENALI_PHY_84__PHY_FAST_LVL_EN_0_SHIFT                        8U
#define LPDDR4__DENALI_PHY_84__PHY_FAST_LVL_EN_0_WIDTH                        4U
#define LPDDR4__PHY_FAST_LVL_EN_0__REG DENALI_PHY_84
#define LPDDR4__PHY_FAST_LVL_EN_0__FLD LPDDR4__DENALI_PHY_84__PHY_FAST_LVL_EN_0

#define LPDDR4__DENALI_PHY_84__PHY_PAD_TX_DCD_0_MASK                 0x001F0000U
#define LPDDR4__DENALI_PHY_84__PHY_PAD_TX_DCD_0_SHIFT                        16U
#define LPDDR4__DENALI_PHY_84__PHY_PAD_TX_DCD_0_WIDTH                         5U
#define LPDDR4__PHY_PAD_TX_DCD_0__REG DENALI_PHY_84
#define LPDDR4__PHY_PAD_TX_DCD_0__FLD LPDDR4__DENALI_PHY_84__PHY_PAD_TX_DCD_0

#define LPDDR4__DENALI_PHY_84__PHY_PAD_RX_DCD_0_0_MASK               0x1F000000U
#define LPDDR4__DENALI_PHY_84__PHY_PAD_RX_DCD_0_0_SHIFT                      24U
#define LPDDR4__DENALI_PHY_84__PHY_PAD_RX_DCD_0_0_WIDTH                       5U
#define LPDDR4__PHY_PAD_RX_DCD_0_0__REG DENALI_PHY_84
#define LPDDR4__PHY_PAD_RX_DCD_0_0__FLD LPDDR4__DENALI_PHY_84__PHY_PAD_RX_DCD_0_0

#define LPDDR4__DENALI_PHY_85_READ_MASK                              0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_85_WRITE_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_1_0_MASK               0x0000001FU
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_1_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_1_0_WIDTH                       5U
#define LPDDR4__PHY_PAD_RX_DCD_1_0__REG DENALI_PHY_85
#define LPDDR4__PHY_PAD_RX_DCD_1_0__FLD LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_1_0

#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_2_0_MASK               0x00001F00U
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_2_0_SHIFT                       8U
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_2_0_WIDTH                       5U
#define LPDDR4__PHY_PAD_RX_DCD_2_0__REG DENALI_PHY_85
#define LPDDR4__PHY_PAD_RX_DCD_2_0__FLD LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_2_0

#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_3_0_MASK               0x001F0000U
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_3_0_SHIFT                      16U
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_3_0_WIDTH                       5U
#define LPDDR4__PHY_PAD_RX_DCD_3_0__REG DENALI_PHY_85
#define LPDDR4__PHY_PAD_RX_DCD_3_0__FLD LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_3_0

#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_4_0_MASK               0x1F000000U
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_4_0_SHIFT                      24U
#define LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_4_0_WIDTH                       5U
#define LPDDR4__PHY_PAD_RX_DCD_4_0__REG DENALI_PHY_85
#define LPDDR4__PHY_PAD_RX_DCD_4_0__FLD LPDDR4__DENALI_PHY_85__PHY_PAD_RX_DCD_4_0

#define LPDDR4__DENALI_PHY_86_READ_MASK                              0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_86_WRITE_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_5_0_MASK               0x0000001FU
#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_5_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_5_0_WIDTH                       5U
#define LPDDR4__PHY_PAD_RX_DCD_5_0__REG DENALI_PHY_86
#define LPDDR4__PHY_PAD_RX_DCD_5_0__FLD LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_5_0

#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_6_0_MASK               0x00001F00U
#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_6_0_SHIFT                       8U
#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_6_0_WIDTH                       5U
#define LPDDR4__PHY_PAD_RX_DCD_6_0__REG DENALI_PHY_86
#define LPDDR4__PHY_PAD_RX_DCD_6_0__FLD LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_6_0

#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_7_0_MASK               0x001F0000U
#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_7_0_SHIFT                      16U
#define LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_7_0_WIDTH                       5U
#define LPDDR4__PHY_PAD_RX_DCD_7_0__REG DENALI_PHY_86
#define LPDDR4__PHY_PAD_RX_DCD_7_0__FLD LPDDR4__DENALI_PHY_86__PHY_PAD_RX_DCD_7_0

#define LPDDR4__DENALI_PHY_86__PHY_PAD_DM_RX_DCD_0_MASK              0x1F000000U
#define LPDDR4__DENALI_PHY_86__PHY_PAD_DM_RX_DCD_0_SHIFT                     24U
#define LPDDR4__DENALI_PHY_86__PHY_PAD_DM_RX_DCD_0_WIDTH                      5U
#define LPDDR4__PHY_PAD_DM_RX_DCD_0__REG DENALI_PHY_86
#define LPDDR4__PHY_PAD_DM_RX_DCD_0__FLD LPDDR4__DENALI_PHY_86__PHY_PAD_DM_RX_DCD_0

#define LPDDR4__DENALI_PHY_87_READ_MASK                              0x007F1F1FU
#define LPDDR4__DENALI_PHY_87_WRITE_MASK                             0x007F1F1FU
#define LPDDR4__DENALI_PHY_87__PHY_PAD_DQS_RX_DCD_0_MASK             0x0000001FU
#define LPDDR4__DENALI_PHY_87__PHY_PAD_DQS_RX_DCD_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_87__PHY_PAD_DQS_RX_DCD_0_WIDTH                     5U
#define LPDDR4__PHY_PAD_DQS_RX_DCD_0__REG DENALI_PHY_87
#define LPDDR4__PHY_PAD_DQS_RX_DCD_0__FLD LPDDR4__DENALI_PHY_87__PHY_PAD_DQS_RX_DCD_0

#define LPDDR4__DENALI_PHY_87__PHY_PAD_FDBK_RX_DCD_0_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_87__PHY_PAD_FDBK_RX_DCD_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_87__PHY_PAD_FDBK_RX_DCD_0_WIDTH                    5U
#define LPDDR4__PHY_PAD_FDBK_RX_DCD_0__REG DENALI_PHY_87
#define LPDDR4__PHY_PAD_FDBK_RX_DCD_0__FLD LPDDR4__DENALI_PHY_87__PHY_PAD_FDBK_RX_DCD_0

#define LPDDR4__DENALI_PHY_87__PHY_PAD_DSLICE_IO_CFG_0_MASK          0x007F0000U
#define LPDDR4__DENALI_PHY_87__PHY_PAD_DSLICE_IO_CFG_0_SHIFT                 16U
#define LPDDR4__DENALI_PHY_87__PHY_PAD_DSLICE_IO_CFG_0_WIDTH                  7U
#define LPDDR4__PHY_PAD_DSLICE_IO_CFG_0__REG DENALI_PHY_87
#define LPDDR4__PHY_PAD_DSLICE_IO_CFG_0__FLD LPDDR4__DENALI_PHY_87__PHY_PAD_DSLICE_IO_CFG_0

#define LPDDR4__DENALI_PHY_88_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_88_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_88__PHY_RDDQ0_SLAVE_DELAY_0_MASK          0x000003FFU
#define LPDDR4__DENALI_PHY_88__PHY_RDDQ0_SLAVE_DELAY_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_88__PHY_RDDQ0_SLAVE_DELAY_0_WIDTH                 10U
#define LPDDR4__PHY_RDDQ0_SLAVE_DELAY_0__REG DENALI_PHY_88
#define LPDDR4__PHY_RDDQ0_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_88__PHY_RDDQ0_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_88__PHY_RDDQ1_SLAVE_DELAY_0_MASK          0x03FF0000U
#define LPDDR4__DENALI_PHY_88__PHY_RDDQ1_SLAVE_DELAY_0_SHIFT                 16U
#define LPDDR4__DENALI_PHY_88__PHY_RDDQ1_SLAVE_DELAY_0_WIDTH                 10U
#define LPDDR4__PHY_RDDQ1_SLAVE_DELAY_0__REG DENALI_PHY_88
#define LPDDR4__PHY_RDDQ1_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_88__PHY_RDDQ1_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_89_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_89_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_89__PHY_RDDQ2_SLAVE_DELAY_0_MASK          0x000003FFU
#define LPDDR4__DENALI_PHY_89__PHY_RDDQ2_SLAVE_DELAY_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_89__PHY_RDDQ2_SLAVE_DELAY_0_WIDTH                 10U
#define LPDDR4__PHY_RDDQ2_SLAVE_DELAY_0__REG DENALI_PHY_89
#define LPDDR4__PHY_RDDQ2_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_89__PHY_RDDQ2_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_89__PHY_RDDQ3_SLAVE_DELAY_0_MASK          0x03FF0000U
#define LPDDR4__DENALI_PHY_89__PHY_RDDQ3_SLAVE_DELAY_0_SHIFT                 16U
#define LPDDR4__DENALI_PHY_89__PHY_RDDQ3_SLAVE_DELAY_0_WIDTH                 10U
#define LPDDR4__PHY_RDDQ3_SLAVE_DELAY_0__REG DENALI_PHY_89
#define LPDDR4__PHY_RDDQ3_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_89__PHY_RDDQ3_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_90_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_90_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_90__PHY_RDDQ4_SLAVE_DELAY_0_MASK          0x000003FFU
#define LPDDR4__DENALI_PHY_90__PHY_RDDQ4_SLAVE_DELAY_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_90__PHY_RDDQ4_SLAVE_DELAY_0_WIDTH                 10U
#define LPDDR4__PHY_RDDQ4_SLAVE_DELAY_0__REG DENALI_PHY_90
#define LPDDR4__PHY_RDDQ4_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_90__PHY_RDDQ4_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_90__PHY_RDDQ5_SLAVE_DELAY_0_MASK          0x03FF0000U
#define LPDDR4__DENALI_PHY_90__PHY_RDDQ5_SLAVE_DELAY_0_SHIFT                 16U
#define LPDDR4__DENALI_PHY_90__PHY_RDDQ5_SLAVE_DELAY_0_WIDTH                 10U
#define LPDDR4__PHY_RDDQ5_SLAVE_DELAY_0__REG DENALI_PHY_90
#define LPDDR4__PHY_RDDQ5_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_90__PHY_RDDQ5_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_91_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PHY_91_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_91__PHY_RDDQ6_SLAVE_DELAY_0_MASK          0x000003FFU
#define LPDDR4__DENALI_PHY_91__PHY_RDDQ6_SLAVE_DELAY_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_91__PHY_RDDQ6_SLAVE_DELAY_0_WIDTH                 10U
#define LPDDR4__PHY_RDDQ6_SLAVE_DELAY_0__REG DENALI_PHY_91
#define LPDDR4__PHY_RDDQ6_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_91__PHY_RDDQ6_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_91__PHY_RDDQ7_SLAVE_DELAY_0_MASK          0x03FF0000U
#define LPDDR4__DENALI_PHY_91__PHY_RDDQ7_SLAVE_DELAY_0_SHIFT                 16U
#define LPDDR4__DENALI_PHY_91__PHY_RDDQ7_SLAVE_DELAY_0_WIDTH                 10U
#define LPDDR4__PHY_RDDQ7_SLAVE_DELAY_0__REG DENALI_PHY_91
#define LPDDR4__PHY_RDDQ7_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_91__PHY_RDDQ7_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_92_READ_MASK                              0x1F0703FFU
#define LPDDR4__DENALI_PHY_92_WRITE_MASK                             0x1F0703FFU
#define LPDDR4__DENALI_PHY_92__PHY_RDDM_SLAVE_DELAY_0_MASK           0x000003FFU
#define LPDDR4__DENALI_PHY_92__PHY_RDDM_SLAVE_DELAY_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_92__PHY_RDDM_SLAVE_DELAY_0_WIDTH                  10U
#define LPDDR4__PHY_RDDM_SLAVE_DELAY_0__REG DENALI_PHY_92
#define LPDDR4__PHY_RDDM_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_92__PHY_RDDM_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_92__PHY_RX_PCLK_CLK_SEL_0_MASK            0x00070000U
#define LPDDR4__DENALI_PHY_92__PHY_RX_PCLK_CLK_SEL_0_SHIFT                   16U
#define LPDDR4__DENALI_PHY_92__PHY_RX_PCLK_CLK_SEL_0_WIDTH                    3U
#define LPDDR4__PHY_RX_PCLK_CLK_SEL_0__REG DENALI_PHY_92
#define LPDDR4__PHY_RX_PCLK_CLK_SEL_0__FLD LPDDR4__DENALI_PHY_92__PHY_RX_PCLK_CLK_SEL_0

#define LPDDR4__DENALI_PHY_92__PHY_RX_CAL_ALL_DLY_0_MASK             0x1F000000U
#define LPDDR4__DENALI_PHY_92__PHY_RX_CAL_ALL_DLY_0_SHIFT                    24U
#define LPDDR4__DENALI_PHY_92__PHY_RX_CAL_ALL_DLY_0_WIDTH                     5U
#define LPDDR4__PHY_RX_CAL_ALL_DLY_0__REG DENALI_PHY_92
#define LPDDR4__PHY_RX_CAL_ALL_DLY_0__FLD LPDDR4__DENALI_PHY_92__PHY_RX_CAL_ALL_DLY_0

#define LPDDR4__DENALI_PHY_93_READ_MASK                              0x00000007U
#define LPDDR4__DENALI_PHY_93_WRITE_MASK                             0x00000007U
#define LPDDR4__DENALI_PHY_93__PHY_DATA_DC_CAL_CLK_SEL_0_MASK        0x00000007U
#define LPDDR4__DENALI_PHY_93__PHY_DATA_DC_CAL_CLK_SEL_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_93__PHY_DATA_DC_CAL_CLK_SEL_0_WIDTH                3U
#define LPDDR4__PHY_DATA_DC_CAL_CLK_SEL_0__REG DENALI_PHY_93
#define LPDDR4__PHY_DATA_DC_CAL_CLK_SEL_0__FLD LPDDR4__DENALI_PHY_93__PHY_DATA_DC_CAL_CLK_SEL_0

#define LPDDR4__DENALI_PHY_94_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_94_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_94__PHY_DQ_OE_TIMING_0_MASK               0x000000FFU
#define LPDDR4__DENALI_PHY_94__PHY_DQ_OE_TIMING_0_SHIFT                       0U
#define LPDDR4__DENALI_PHY_94__PHY_DQ_OE_TIMING_0_WIDTH                       8U
#define LPDDR4__PHY_DQ_OE_TIMING_0__REG DENALI_PHY_94
#define LPDDR4__PHY_DQ_OE_TIMING_0__FLD LPDDR4__DENALI_PHY_94__PHY_DQ_OE_TIMING_0

#define LPDDR4__DENALI_PHY_94__PHY_DQ_TSEL_RD_TIMING_0_MASK          0x0000FF00U
#define LPDDR4__DENALI_PHY_94__PHY_DQ_TSEL_RD_TIMING_0_SHIFT                  8U
#define LPDDR4__DENALI_PHY_94__PHY_DQ_TSEL_RD_TIMING_0_WIDTH                  8U
#define LPDDR4__PHY_DQ_TSEL_RD_TIMING_0__REG DENALI_PHY_94
#define LPDDR4__PHY_DQ_TSEL_RD_TIMING_0__FLD LPDDR4__DENALI_PHY_94__PHY_DQ_TSEL_RD_TIMING_0

#define LPDDR4__DENALI_PHY_94__PHY_DQ_TSEL_WR_TIMING_0_MASK          0x00FF0000U
#define LPDDR4__DENALI_PHY_94__PHY_DQ_TSEL_WR_TIMING_0_SHIFT                 16U
#define LPDDR4__DENALI_PHY_94__PHY_DQ_TSEL_WR_TIMING_0_WIDTH                  8U
#define LPDDR4__PHY_DQ_TSEL_WR_TIMING_0__REG DENALI_PHY_94
#define LPDDR4__PHY_DQ_TSEL_WR_TIMING_0__FLD LPDDR4__DENALI_PHY_94__PHY_DQ_TSEL_WR_TIMING_0

#define LPDDR4__DENALI_PHY_94__PHY_DQS_OE_TIMING_0_MASK              0xFF000000U
#define LPDDR4__DENALI_PHY_94__PHY_DQS_OE_TIMING_0_SHIFT                     24U
#define LPDDR4__DENALI_PHY_94__PHY_DQS_OE_TIMING_0_WIDTH                      8U
#define LPDDR4__PHY_DQS_OE_TIMING_0__REG DENALI_PHY_94
#define LPDDR4__PHY_DQS_OE_TIMING_0__FLD LPDDR4__DENALI_PHY_94__PHY_DQS_OE_TIMING_0

#define LPDDR4__DENALI_PHY_95_READ_MASK                              0xFFFFFF0FU
#define LPDDR4__DENALI_PHY_95_WRITE_MASK                             0xFFFFFF0FU
#define LPDDR4__DENALI_PHY_95__PHY_IO_PAD_DELAY_TIMING_0_MASK        0x0000000FU
#define LPDDR4__DENALI_PHY_95__PHY_IO_PAD_DELAY_TIMING_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_95__PHY_IO_PAD_DELAY_TIMING_0_WIDTH                4U
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_0__REG DENALI_PHY_95
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_0__FLD LPDDR4__DENALI_PHY_95__PHY_IO_PAD_DELAY_TIMING_0

#define LPDDR4__DENALI_PHY_95__PHY_DQS_TSEL_RD_TIMING_0_MASK         0x0000FF00U
#define LPDDR4__DENALI_PHY_95__PHY_DQS_TSEL_RD_TIMING_0_SHIFT                 8U
#define LPDDR4__DENALI_PHY_95__PHY_DQS_TSEL_RD_TIMING_0_WIDTH                 8U
#define LPDDR4__PHY_DQS_TSEL_RD_TIMING_0__REG DENALI_PHY_95
#define LPDDR4__PHY_DQS_TSEL_RD_TIMING_0__FLD LPDDR4__DENALI_PHY_95__PHY_DQS_TSEL_RD_TIMING_0

#define LPDDR4__DENALI_PHY_95__PHY_DQS_OE_RD_TIMING_0_MASK           0x00FF0000U
#define LPDDR4__DENALI_PHY_95__PHY_DQS_OE_RD_TIMING_0_SHIFT                  16U
#define LPDDR4__DENALI_PHY_95__PHY_DQS_OE_RD_TIMING_0_WIDTH                   8U
#define LPDDR4__PHY_DQS_OE_RD_TIMING_0__REG DENALI_PHY_95
#define LPDDR4__PHY_DQS_OE_RD_TIMING_0__FLD LPDDR4__DENALI_PHY_95__PHY_DQS_OE_RD_TIMING_0

#define LPDDR4__DENALI_PHY_95__PHY_DQS_TSEL_WR_TIMING_0_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_95__PHY_DQS_TSEL_WR_TIMING_0_SHIFT                24U
#define LPDDR4__DENALI_PHY_95__PHY_DQS_TSEL_WR_TIMING_0_WIDTH                 8U
#define LPDDR4__PHY_DQS_TSEL_WR_TIMING_0__REG DENALI_PHY_95
#define LPDDR4__PHY_DQS_TSEL_WR_TIMING_0__FLD LPDDR4__DENALI_PHY_95__PHY_DQS_TSEL_WR_TIMING_0

#define LPDDR4__DENALI_PHY_96_READ_MASK                              0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_96_WRITE_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_96__PHY_VREF_SETTING_TIME_0_MASK          0x0000FFFFU
#define LPDDR4__DENALI_PHY_96__PHY_VREF_SETTING_TIME_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_96__PHY_VREF_SETTING_TIME_0_WIDTH                 16U
#define LPDDR4__PHY_VREF_SETTING_TIME_0__REG DENALI_PHY_96
#define LPDDR4__PHY_VREF_SETTING_TIME_0__FLD LPDDR4__DENALI_PHY_96__PHY_VREF_SETTING_TIME_0

#define LPDDR4__DENALI_PHY_96__PHY_PAD_VREF_CTRL_DQ_0_MASK           0x0FFF0000U
#define LPDDR4__DENALI_PHY_96__PHY_PAD_VREF_CTRL_DQ_0_SHIFT                  16U
#define LPDDR4__DENALI_PHY_96__PHY_PAD_VREF_CTRL_DQ_0_WIDTH                  12U
#define LPDDR4__PHY_PAD_VREF_CTRL_DQ_0__REG DENALI_PHY_96
#define LPDDR4__PHY_PAD_VREF_CTRL_DQ_0__FLD LPDDR4__DENALI_PHY_96__PHY_PAD_VREF_CTRL_DQ_0

#define LPDDR4__DENALI_PHY_97_READ_MASK                              0x03FFFF01U
#define LPDDR4__DENALI_PHY_97_WRITE_MASK                             0x03FFFF01U
#define LPDDR4__DENALI_PHY_97__PHY_PER_CS_TRAINING_EN_0_MASK         0x00000001U
#define LPDDR4__DENALI_PHY_97__PHY_PER_CS_TRAINING_EN_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_97__PHY_PER_CS_TRAINING_EN_0_WIDTH                 1U
#define LPDDR4__DENALI_PHY_97__PHY_PER_CS_TRAINING_EN_0_WOCLR                 0U
#define LPDDR4__DENALI_PHY_97__PHY_PER_CS_TRAINING_EN_0_WOSET                 0U
#define LPDDR4__PHY_PER_CS_TRAINING_EN_0__REG DENALI_PHY_97
#define LPDDR4__PHY_PER_CS_TRAINING_EN_0__FLD LPDDR4__DENALI_PHY_97__PHY_PER_CS_TRAINING_EN_0

#define LPDDR4__DENALI_PHY_97__PHY_DQ_IE_TIMING_0_MASK               0x0000FF00U
#define LPDDR4__DENALI_PHY_97__PHY_DQ_IE_TIMING_0_SHIFT                       8U
#define LPDDR4__DENALI_PHY_97__PHY_DQ_IE_TIMING_0_WIDTH                       8U
#define LPDDR4__PHY_DQ_IE_TIMING_0__REG DENALI_PHY_97
#define LPDDR4__PHY_DQ_IE_TIMING_0__FLD LPDDR4__DENALI_PHY_97__PHY_DQ_IE_TIMING_0

#define LPDDR4__DENALI_PHY_97__PHY_DQS_IE_TIMING_0_MASK              0x00FF0000U
#define LPDDR4__DENALI_PHY_97__PHY_DQS_IE_TIMING_0_SHIFT                     16U
#define LPDDR4__DENALI_PHY_97__PHY_DQS_IE_TIMING_0_WIDTH                      8U
#define LPDDR4__PHY_DQS_IE_TIMING_0__REG DENALI_PHY_97
#define LPDDR4__PHY_DQS_IE_TIMING_0__FLD LPDDR4__DENALI_PHY_97__PHY_DQS_IE_TIMING_0

#define LPDDR4__DENALI_PHY_97__PHY_RDDATA_EN_IE_DLY_0_MASK           0x03000000U
#define LPDDR4__DENALI_PHY_97__PHY_RDDATA_EN_IE_DLY_0_SHIFT                  24U
#define LPDDR4__DENALI_PHY_97__PHY_RDDATA_EN_IE_DLY_0_WIDTH                   2U
#define LPDDR4__PHY_RDDATA_EN_IE_DLY_0__REG DENALI_PHY_97
#define LPDDR4__PHY_RDDATA_EN_IE_DLY_0__FLD LPDDR4__DENALI_PHY_97__PHY_RDDATA_EN_IE_DLY_0

#define LPDDR4__DENALI_PHY_98_READ_MASK                              0x1F010303U
#define LPDDR4__DENALI_PHY_98_WRITE_MASK                             0x1F010303U
#define LPDDR4__DENALI_PHY_98__PHY_IE_MODE_0_MASK                    0x00000003U
#define LPDDR4__DENALI_PHY_98__PHY_IE_MODE_0_SHIFT                            0U
#define LPDDR4__DENALI_PHY_98__PHY_IE_MODE_0_WIDTH                            2U
#define LPDDR4__PHY_IE_MODE_0__REG DENALI_PHY_98
#define LPDDR4__PHY_IE_MODE_0__FLD LPDDR4__DENALI_PHY_98__PHY_IE_MODE_0

#define LPDDR4__DENALI_PHY_98__PHY_DBI_MODE_0_MASK                   0x00000300U
#define LPDDR4__DENALI_PHY_98__PHY_DBI_MODE_0_SHIFT                           8U
#define LPDDR4__DENALI_PHY_98__PHY_DBI_MODE_0_WIDTH                           2U
#define LPDDR4__PHY_DBI_MODE_0__REG DENALI_PHY_98
#define LPDDR4__PHY_DBI_MODE_0__FLD LPDDR4__DENALI_PHY_98__PHY_DBI_MODE_0

#define LPDDR4__DENALI_PHY_98__PHY_WDQLVL_IE_ON_0_MASK               0x00010000U
#define LPDDR4__DENALI_PHY_98__PHY_WDQLVL_IE_ON_0_SHIFT                      16U
#define LPDDR4__DENALI_PHY_98__PHY_WDQLVL_IE_ON_0_WIDTH                       1U
#define LPDDR4__DENALI_PHY_98__PHY_WDQLVL_IE_ON_0_WOCLR                       0U
#define LPDDR4__DENALI_PHY_98__PHY_WDQLVL_IE_ON_0_WOSET                       0U
#define LPDDR4__PHY_WDQLVL_IE_ON_0__REG DENALI_PHY_98
#define LPDDR4__PHY_WDQLVL_IE_ON_0__FLD LPDDR4__DENALI_PHY_98__PHY_WDQLVL_IE_ON_0

#define LPDDR4__DENALI_PHY_98__PHY_WDQLVL_RDDATA_EN_DLY_0_MASK       0x1F000000U
#define LPDDR4__DENALI_PHY_98__PHY_WDQLVL_RDDATA_EN_DLY_0_SHIFT              24U
#define LPDDR4__DENALI_PHY_98__PHY_WDQLVL_RDDATA_EN_DLY_0_WIDTH               5U
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_DLY_0__REG DENALI_PHY_98
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_DLY_0__FLD LPDDR4__DENALI_PHY_98__PHY_WDQLVL_RDDATA_EN_DLY_0

#define LPDDR4__DENALI_PHY_99_READ_MASK                              0x0F1F1F1FU
#define LPDDR4__DENALI_PHY_99_WRITE_MASK                             0x0F1F1F1FU
#define LPDDR4__DENALI_PHY_99__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_0_MASK  0x0000001FU
#define LPDDR4__DENALI_PHY_99__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_99__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_0_WIDTH          5U
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_0__REG DENALI_PHY_99
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_0__FLD LPDDR4__DENALI_PHY_99__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_0

#define LPDDR4__DENALI_PHY_99__PHY_RDDATA_EN_TSEL_DLY_0_MASK         0x00001F00U
#define LPDDR4__DENALI_PHY_99__PHY_RDDATA_EN_TSEL_DLY_0_SHIFT                 8U
#define LPDDR4__DENALI_PHY_99__PHY_RDDATA_EN_TSEL_DLY_0_WIDTH                 5U
#define LPDDR4__PHY_RDDATA_EN_TSEL_DLY_0__REG DENALI_PHY_99
#define LPDDR4__PHY_RDDATA_EN_TSEL_DLY_0__FLD LPDDR4__DENALI_PHY_99__PHY_RDDATA_EN_TSEL_DLY_0

#define LPDDR4__DENALI_PHY_99__PHY_RDDATA_EN_OE_DLY_0_MASK           0x001F0000U
#define LPDDR4__DENALI_PHY_99__PHY_RDDATA_EN_OE_DLY_0_SHIFT                  16U
#define LPDDR4__DENALI_PHY_99__PHY_RDDATA_EN_OE_DLY_0_WIDTH                   5U
#define LPDDR4__PHY_RDDATA_EN_OE_DLY_0__REG DENALI_PHY_99
#define LPDDR4__PHY_RDDATA_EN_OE_DLY_0__FLD LPDDR4__DENALI_PHY_99__PHY_RDDATA_EN_OE_DLY_0

#define LPDDR4__DENALI_PHY_99__PHY_SW_MASTER_MODE_0_MASK             0x0F000000U
#define LPDDR4__DENALI_PHY_99__PHY_SW_MASTER_MODE_0_SHIFT                    24U
#define LPDDR4__DENALI_PHY_99__PHY_SW_MASTER_MODE_0_WIDTH                     4U
#define LPDDR4__PHY_SW_MASTER_MODE_0__REG DENALI_PHY_99
#define LPDDR4__PHY_SW_MASTER_MODE_0__FLD LPDDR4__DENALI_PHY_99__PHY_SW_MASTER_MODE_0

#define LPDDR4__DENALI_PHY_100_READ_MASK                             0xFF3F07FFU
#define LPDDR4__DENALI_PHY_100_WRITE_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_START_0_MASK        0x000007FFU
#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_START_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_START_0_WIDTH               11U
#define LPDDR4__PHY_MASTER_DELAY_START_0__REG DENALI_PHY_100
#define LPDDR4__PHY_MASTER_DELAY_START_0__FLD LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_START_0

#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_STEP_0_MASK         0x003F0000U
#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_STEP_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_STEP_0_WIDTH                 6U
#define LPDDR4__PHY_MASTER_DELAY_STEP_0__REG DENALI_PHY_100
#define LPDDR4__PHY_MASTER_DELAY_STEP_0__FLD LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_STEP_0

#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_WAIT_0_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_WAIT_0_SHIFT                24U
#define LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_WAIT_0_WIDTH                 8U
#define LPDDR4__PHY_MASTER_DELAY_WAIT_0__REG DENALI_PHY_100
#define LPDDR4__PHY_MASTER_DELAY_WAIT_0__FLD LPDDR4__DENALI_PHY_100__PHY_MASTER_DELAY_WAIT_0

#define LPDDR4__DENALI_PHY_101_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_101_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_101__PHY_MASTER_DELAY_HALF_MEASURE_0_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_101__PHY_MASTER_DELAY_HALF_MEASURE_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_101__PHY_MASTER_DELAY_HALF_MEASURE_0_WIDTH         8U
#define LPDDR4__PHY_MASTER_DELAY_HALF_MEASURE_0__REG DENALI_PHY_101
#define LPDDR4__PHY_MASTER_DELAY_HALF_MEASURE_0__FLD LPDDR4__DENALI_PHY_101__PHY_MASTER_DELAY_HALF_MEASURE_0

#define LPDDR4__DENALI_PHY_101__PHY_RPTR_UPDATE_0_MASK               0x00000F00U
#define LPDDR4__DENALI_PHY_101__PHY_RPTR_UPDATE_0_SHIFT                       8U
#define LPDDR4__DENALI_PHY_101__PHY_RPTR_UPDATE_0_WIDTH                       4U
#define LPDDR4__PHY_RPTR_UPDATE_0__REG DENALI_PHY_101
#define LPDDR4__PHY_RPTR_UPDATE_0__FLD LPDDR4__DENALI_PHY_101__PHY_RPTR_UPDATE_0

#define LPDDR4__DENALI_PHY_101__PHY_WRLVL_DLY_STEP_0_MASK            0x00FF0000U
#define LPDDR4__DENALI_PHY_101__PHY_WRLVL_DLY_STEP_0_SHIFT                   16U
#define LPDDR4__DENALI_PHY_101__PHY_WRLVL_DLY_STEP_0_WIDTH                    8U
#define LPDDR4__PHY_WRLVL_DLY_STEP_0__REG DENALI_PHY_101
#define LPDDR4__PHY_WRLVL_DLY_STEP_0__FLD LPDDR4__DENALI_PHY_101__PHY_WRLVL_DLY_STEP_0

#define LPDDR4__DENALI_PHY_101__PHY_WRLVL_DLY_FINE_STEP_0_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_101__PHY_WRLVL_DLY_FINE_STEP_0_SHIFT              24U
#define LPDDR4__DENALI_PHY_101__PHY_WRLVL_DLY_FINE_STEP_0_WIDTH               4U
#define LPDDR4__PHY_WRLVL_DLY_FINE_STEP_0__REG DENALI_PHY_101
#define LPDDR4__PHY_WRLVL_DLY_FINE_STEP_0__FLD LPDDR4__DENALI_PHY_101__PHY_WRLVL_DLY_FINE_STEP_0

#define LPDDR4__DENALI_PHY_102_READ_MASK                             0x001F0F3FU
#define LPDDR4__DENALI_PHY_102_WRITE_MASK                            0x001F0F3FU
#define LPDDR4__DENALI_PHY_102__PHY_WRLVL_RESP_WAIT_CNT_0_MASK       0x0000003FU
#define LPDDR4__DENALI_PHY_102__PHY_WRLVL_RESP_WAIT_CNT_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_102__PHY_WRLVL_RESP_WAIT_CNT_0_WIDTH               6U
#define LPDDR4__PHY_WRLVL_RESP_WAIT_CNT_0__REG DENALI_PHY_102
#define LPDDR4__PHY_WRLVL_RESP_WAIT_CNT_0__FLD LPDDR4__DENALI_PHY_102__PHY_WRLVL_RESP_WAIT_CNT_0

#define LPDDR4__DENALI_PHY_102__PHY_GTLVL_DLY_STEP_0_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_102__PHY_GTLVL_DLY_STEP_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_102__PHY_GTLVL_DLY_STEP_0_WIDTH                    4U
#define LPDDR4__PHY_GTLVL_DLY_STEP_0__REG DENALI_PHY_102
#define LPDDR4__PHY_GTLVL_DLY_STEP_0__FLD LPDDR4__DENALI_PHY_102__PHY_GTLVL_DLY_STEP_0

#define LPDDR4__DENALI_PHY_102__PHY_GTLVL_RESP_WAIT_CNT_0_MASK       0x001F0000U
#define LPDDR4__DENALI_PHY_102__PHY_GTLVL_RESP_WAIT_CNT_0_SHIFT              16U
#define LPDDR4__DENALI_PHY_102__PHY_GTLVL_RESP_WAIT_CNT_0_WIDTH               5U
#define LPDDR4__PHY_GTLVL_RESP_WAIT_CNT_0__REG DENALI_PHY_102
#define LPDDR4__PHY_GTLVL_RESP_WAIT_CNT_0__FLD LPDDR4__DENALI_PHY_102__PHY_GTLVL_RESP_WAIT_CNT_0

#define LPDDR4__DENALI_PHY_103_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_103_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_103__PHY_GTLVL_BACK_STEP_0_MASK           0x000003FFU
#define LPDDR4__DENALI_PHY_103__PHY_GTLVL_BACK_STEP_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_103__PHY_GTLVL_BACK_STEP_0_WIDTH                  10U
#define LPDDR4__PHY_GTLVL_BACK_STEP_0__REG DENALI_PHY_103
#define LPDDR4__PHY_GTLVL_BACK_STEP_0__FLD LPDDR4__DENALI_PHY_103__PHY_GTLVL_BACK_STEP_0

#define LPDDR4__DENALI_PHY_103__PHY_GTLVL_FINAL_STEP_0_MASK          0x03FF0000U
#define LPDDR4__DENALI_PHY_103__PHY_GTLVL_FINAL_STEP_0_SHIFT                 16U
#define LPDDR4__DENALI_PHY_103__PHY_GTLVL_FINAL_STEP_0_WIDTH                 10U
#define LPDDR4__PHY_GTLVL_FINAL_STEP_0__REG DENALI_PHY_103
#define LPDDR4__PHY_GTLVL_FINAL_STEP_0__FLD LPDDR4__DENALI_PHY_103__PHY_GTLVL_FINAL_STEP_0

#define LPDDR4__DENALI_PHY_104_READ_MASK                             0x01FF0FFFU
#define LPDDR4__DENALI_PHY_104_WRITE_MASK                            0x01FF0FFFU
#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_DLY_STEP_0_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_DLY_STEP_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_DLY_STEP_0_WIDTH                   8U
#define LPDDR4__PHY_WDQLVL_DLY_STEP_0__REG DENALI_PHY_104
#define LPDDR4__PHY_WDQLVL_DLY_STEP_0__FLD LPDDR4__DENALI_PHY_104__PHY_WDQLVL_DLY_STEP_0

#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_QTR_DLY_STEP_0_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_QTR_DLY_STEP_0_SHIFT               8U
#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_QTR_DLY_STEP_0_WIDTH               4U
#define LPDDR4__PHY_WDQLVL_QTR_DLY_STEP_0__REG DENALI_PHY_104
#define LPDDR4__PHY_WDQLVL_QTR_DLY_STEP_0__FLD LPDDR4__DENALI_PHY_104__PHY_WDQLVL_QTR_DLY_STEP_0

#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_DM_SEARCH_RANGE_0_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_DM_SEARCH_RANGE_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_104__PHY_WDQLVL_DM_SEARCH_RANGE_0_WIDTH            9U
#define LPDDR4__PHY_WDQLVL_DM_SEARCH_RANGE_0__REG DENALI_PHY_104
#define LPDDR4__PHY_WDQLVL_DM_SEARCH_RANGE_0__FLD LPDDR4__DENALI_PHY_104__PHY_WDQLVL_DM_SEARCH_RANGE_0

#define LPDDR4__DENALI_PHY_105_READ_MASK                             0x00000F01U
#define LPDDR4__DENALI_PHY_105_WRITE_MASK                            0x00000F01U
#define LPDDR4__DENALI_PHY_105__PHY_TOGGLE_PRE_SUPPORT_0_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_105__PHY_TOGGLE_PRE_SUPPORT_0_SHIFT                0U
#define LPDDR4__DENALI_PHY_105__PHY_TOGGLE_PRE_SUPPORT_0_WIDTH                1U
#define LPDDR4__DENALI_PHY_105__PHY_TOGGLE_PRE_SUPPORT_0_WOCLR                0U
#define LPDDR4__DENALI_PHY_105__PHY_TOGGLE_PRE_SUPPORT_0_WOSET                0U
#define LPDDR4__PHY_TOGGLE_PRE_SUPPORT_0__REG DENALI_PHY_105
#define LPDDR4__PHY_TOGGLE_PRE_SUPPORT_0__FLD LPDDR4__DENALI_PHY_105__PHY_TOGGLE_PRE_SUPPORT_0

#define LPDDR4__DENALI_PHY_105__PHY_RDLVL_DLY_STEP_0_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_105__PHY_RDLVL_DLY_STEP_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_105__PHY_RDLVL_DLY_STEP_0_WIDTH                    4U
#define LPDDR4__PHY_RDLVL_DLY_STEP_0__REG DENALI_PHY_105
#define LPDDR4__PHY_RDLVL_DLY_STEP_0__FLD LPDDR4__DENALI_PHY_105__PHY_RDLVL_DLY_STEP_0

#define LPDDR4__DENALI_PHY_106_READ_MASK                             0x000003FFU
#define LPDDR4__DENALI_PHY_106_WRITE_MASK                            0x000003FFU
#define LPDDR4__DENALI_PHY_106__PHY_RDLVL_MAX_EDGE_0_MASK            0x000003FFU
#define LPDDR4__DENALI_PHY_106__PHY_RDLVL_MAX_EDGE_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_106__PHY_RDLVL_MAX_EDGE_0_WIDTH                   10U
#define LPDDR4__PHY_RDLVL_MAX_EDGE_0__REG DENALI_PHY_106
#define LPDDR4__PHY_RDLVL_MAX_EDGE_0__FLD LPDDR4__DENALI_PHY_106__PHY_RDLVL_MAX_EDGE_0

#define LPDDR4__DENALI_PHY_107_READ_MASK                             0x00030703U
#define LPDDR4__DENALI_PHY_107_WRITE_MASK                            0x00030703U
#define LPDDR4__DENALI_PHY_107__PHY_WRPATH_GATE_DISABLE_0_MASK       0x00000003U
#define LPDDR4__DENALI_PHY_107__PHY_WRPATH_GATE_DISABLE_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_107__PHY_WRPATH_GATE_DISABLE_0_WIDTH               2U
#define LPDDR4__PHY_WRPATH_GATE_DISABLE_0__REG DENALI_PHY_107
#define LPDDR4__PHY_WRPATH_GATE_DISABLE_0__FLD LPDDR4__DENALI_PHY_107__PHY_WRPATH_GATE_DISABLE_0

#define LPDDR4__DENALI_PHY_107__PHY_WRPATH_GATE_TIMING_0_MASK        0x00000700U
#define LPDDR4__DENALI_PHY_107__PHY_WRPATH_GATE_TIMING_0_SHIFT                8U
#define LPDDR4__DENALI_PHY_107__PHY_WRPATH_GATE_TIMING_0_WIDTH                3U
#define LPDDR4__PHY_WRPATH_GATE_TIMING_0__REG DENALI_PHY_107
#define LPDDR4__PHY_WRPATH_GATE_TIMING_0__FLD LPDDR4__DENALI_PHY_107__PHY_WRPATH_GATE_TIMING_0

#define LPDDR4__DENALI_PHY_107__PHY_DATA_DC_INIT_DISABLE_0_MASK      0x00030000U
#define LPDDR4__DENALI_PHY_107__PHY_DATA_DC_INIT_DISABLE_0_SHIFT             16U
#define LPDDR4__DENALI_PHY_107__PHY_DATA_DC_INIT_DISABLE_0_WIDTH              2U
#define LPDDR4__PHY_DATA_DC_INIT_DISABLE_0__REG DENALI_PHY_107
#define LPDDR4__PHY_DATA_DC_INIT_DISABLE_0__FLD LPDDR4__DENALI_PHY_107__PHY_DATA_DC_INIT_DISABLE_0

#define LPDDR4__DENALI_PHY_108_READ_MASK                             0x07FF03FFU
#define LPDDR4__DENALI_PHY_108_WRITE_MASK                            0x07FF03FFU
#define LPDDR4__DENALI_PHY_108__PHY_DATA_DC_DQS_INIT_SLV_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_108__PHY_DATA_DC_DQS_INIT_SLV_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_108__PHY_DATA_DC_DQS_INIT_SLV_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_DATA_DC_DQS_INIT_SLV_DELAY_0__REG DENALI_PHY_108
#define LPDDR4__PHY_DATA_DC_DQS_INIT_SLV_DELAY_0__FLD LPDDR4__DENALI_PHY_108__PHY_DATA_DC_DQS_INIT_SLV_DELAY_0

#define LPDDR4__DENALI_PHY_108__PHY_DATA_DC_DQ_INIT_SLV_DELAY_0_MASK 0x07FF0000U
#define LPDDR4__DENALI_PHY_108__PHY_DATA_DC_DQ_INIT_SLV_DELAY_0_SHIFT        16U
#define LPDDR4__DENALI_PHY_108__PHY_DATA_DC_DQ_INIT_SLV_DELAY_0_WIDTH        11U
#define LPDDR4__PHY_DATA_DC_DQ_INIT_SLV_DELAY_0__REG DENALI_PHY_108
#define LPDDR4__PHY_DATA_DC_DQ_INIT_SLV_DELAY_0__FLD LPDDR4__DENALI_PHY_108__PHY_DATA_DC_DQ_INIT_SLV_DELAY_0

#define LPDDR4__DENALI_PHY_109_READ_MASK                             0xFFFF0101U
#define LPDDR4__DENALI_PHY_109_WRITE_MASK                            0xFFFF0101U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WRLVL_ENABLE_0_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WRLVL_ENABLE_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WRLVL_ENABLE_0_WIDTH              1U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WRLVL_ENABLE_0_WOCLR              0U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WRLVL_ENABLE_0_WOSET              0U
#define LPDDR4__PHY_DATA_DC_WRLVL_ENABLE_0__REG DENALI_PHY_109
#define LPDDR4__PHY_DATA_DC_WRLVL_ENABLE_0__FLD LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WRLVL_ENABLE_0

#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WDQLVL_ENABLE_0_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WDQLVL_ENABLE_0_SHIFT             8U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WDQLVL_ENABLE_0_WIDTH             1U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WDQLVL_ENABLE_0_WOCLR             0U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WDQLVL_ENABLE_0_WOSET             0U
#define LPDDR4__PHY_DATA_DC_WDQLVL_ENABLE_0__REG DENALI_PHY_109
#define LPDDR4__PHY_DATA_DC_WDQLVL_ENABLE_0__FLD LPDDR4__DENALI_PHY_109__PHY_DATA_DC_WDQLVL_ENABLE_0

#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_DM_CLK_SE_THRSHLD_0_MASK 0x00FF0000U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_DM_CLK_SE_THRSHLD_0_SHIFT        16U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_DM_CLK_SE_THRSHLD_0_WIDTH         8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_SE_THRSHLD_0__REG DENALI_PHY_109
#define LPDDR4__PHY_DATA_DC_DM_CLK_SE_THRSHLD_0__FLD LPDDR4__DENALI_PHY_109__PHY_DATA_DC_DM_CLK_SE_THRSHLD_0

#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_0_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_0_SHIFT      24U
#define LPDDR4__DENALI_PHY_109__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_0_WIDTH       8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_0__REG DENALI_PHY_109
#define LPDDR4__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_0__FLD LPDDR4__DENALI_PHY_109__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_0

#define LPDDR4__DENALI_PHY_110_READ_MASK                             0x001F7F7FU
#define LPDDR4__DENALI_PHY_110_WRITE_MASK                            0x001F7F7FU
#define LPDDR4__DENALI_PHY_110__PHY_WDQ_OSC_DELTA_0_MASK             0x0000007FU
#define LPDDR4__DENALI_PHY_110__PHY_WDQ_OSC_DELTA_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_110__PHY_WDQ_OSC_DELTA_0_WIDTH                     7U
#define LPDDR4__PHY_WDQ_OSC_DELTA_0__REG DENALI_PHY_110
#define LPDDR4__PHY_WDQ_OSC_DELTA_0__FLD LPDDR4__DENALI_PHY_110__PHY_WDQ_OSC_DELTA_0

#define LPDDR4__DENALI_PHY_110__PHY_MEAS_DLY_STEP_ENABLE_0_MASK      0x00007F00U
#define LPDDR4__DENALI_PHY_110__PHY_MEAS_DLY_STEP_ENABLE_0_SHIFT              8U
#define LPDDR4__DENALI_PHY_110__PHY_MEAS_DLY_STEP_ENABLE_0_WIDTH              7U
#define LPDDR4__PHY_MEAS_DLY_STEP_ENABLE_0__REG DENALI_PHY_110
#define LPDDR4__PHY_MEAS_DLY_STEP_ENABLE_0__FLD LPDDR4__DENALI_PHY_110__PHY_MEAS_DLY_STEP_ENABLE_0

#define LPDDR4__DENALI_PHY_110__PHY_RDDATA_EN_DLY_0_MASK             0x001F0000U
#define LPDDR4__DENALI_PHY_110__PHY_RDDATA_EN_DLY_0_SHIFT                    16U
#define LPDDR4__DENALI_PHY_110__PHY_RDDATA_EN_DLY_0_WIDTH                     5U
#define LPDDR4__PHY_RDDATA_EN_DLY_0__REG DENALI_PHY_110
#define LPDDR4__PHY_RDDATA_EN_DLY_0__FLD LPDDR4__DENALI_PHY_110__PHY_RDDATA_EN_DLY_0

#define LPDDR4__DENALI_PHY_111_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_111_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_111__PHY_DQ_DM_SWIZZLE0_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_111__PHY_DQ_DM_SWIZZLE0_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_111__PHY_DQ_DM_SWIZZLE0_0_WIDTH                   32U
#define LPDDR4__PHY_DQ_DM_SWIZZLE0_0__REG DENALI_PHY_111
#define LPDDR4__PHY_DQ_DM_SWIZZLE0_0__FLD LPDDR4__DENALI_PHY_111__PHY_DQ_DM_SWIZZLE0_0

#define LPDDR4__DENALI_PHY_112_READ_MASK                             0x0000000FU
#define LPDDR4__DENALI_PHY_112_WRITE_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_112__PHY_DQ_DM_SWIZZLE1_0_MASK            0x0000000FU
#define LPDDR4__DENALI_PHY_112__PHY_DQ_DM_SWIZZLE1_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_112__PHY_DQ_DM_SWIZZLE1_0_WIDTH                    4U
#define LPDDR4__PHY_DQ_DM_SWIZZLE1_0__REG DENALI_PHY_112
#define LPDDR4__PHY_DQ_DM_SWIZZLE1_0__FLD LPDDR4__DENALI_PHY_112__PHY_DQ_DM_SWIZZLE1_0

#define LPDDR4__DENALI_PHY_113_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_113_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_113__PHY_CLK_WRDQ0_SLAVE_DELAY_0_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_113__PHY_CLK_WRDQ0_SLAVE_DELAY_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_113__PHY_CLK_WRDQ0_SLAVE_DELAY_0_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ0_SLAVE_DELAY_0__REG DENALI_PHY_113
#define LPDDR4__PHY_CLK_WRDQ0_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_113__PHY_CLK_WRDQ0_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_113__PHY_CLK_WRDQ1_SLAVE_DELAY_0_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_113__PHY_CLK_WRDQ1_SLAVE_DELAY_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_113__PHY_CLK_WRDQ1_SLAVE_DELAY_0_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ1_SLAVE_DELAY_0__REG DENALI_PHY_113
#define LPDDR4__PHY_CLK_WRDQ1_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_113__PHY_CLK_WRDQ1_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_114_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_114_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_114__PHY_CLK_WRDQ2_SLAVE_DELAY_0_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_114__PHY_CLK_WRDQ2_SLAVE_DELAY_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_114__PHY_CLK_WRDQ2_SLAVE_DELAY_0_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ2_SLAVE_DELAY_0__REG DENALI_PHY_114
#define LPDDR4__PHY_CLK_WRDQ2_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_114__PHY_CLK_WRDQ2_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_114__PHY_CLK_WRDQ3_SLAVE_DELAY_0_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_114__PHY_CLK_WRDQ3_SLAVE_DELAY_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_114__PHY_CLK_WRDQ3_SLAVE_DELAY_0_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ3_SLAVE_DELAY_0__REG DENALI_PHY_114
#define LPDDR4__PHY_CLK_WRDQ3_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_114__PHY_CLK_WRDQ3_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_115_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_115_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_115__PHY_CLK_WRDQ4_SLAVE_DELAY_0_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_115__PHY_CLK_WRDQ4_SLAVE_DELAY_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_115__PHY_CLK_WRDQ4_SLAVE_DELAY_0_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ4_SLAVE_DELAY_0__REG DENALI_PHY_115
#define LPDDR4__PHY_CLK_WRDQ4_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_115__PHY_CLK_WRDQ4_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_115__PHY_CLK_WRDQ5_SLAVE_DELAY_0_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_115__PHY_CLK_WRDQ5_SLAVE_DELAY_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_115__PHY_CLK_WRDQ5_SLAVE_DELAY_0_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ5_SLAVE_DELAY_0__REG DENALI_PHY_115
#define LPDDR4__PHY_CLK_WRDQ5_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_115__PHY_CLK_WRDQ5_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_116_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_116_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_116__PHY_CLK_WRDQ6_SLAVE_DELAY_0_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_116__PHY_CLK_WRDQ6_SLAVE_DELAY_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_116__PHY_CLK_WRDQ6_SLAVE_DELAY_0_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ6_SLAVE_DELAY_0__REG DENALI_PHY_116
#define LPDDR4__PHY_CLK_WRDQ6_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_116__PHY_CLK_WRDQ6_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_116__PHY_CLK_WRDQ7_SLAVE_DELAY_0_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_116__PHY_CLK_WRDQ7_SLAVE_DELAY_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_116__PHY_CLK_WRDQ7_SLAVE_DELAY_0_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ7_SLAVE_DELAY_0__REG DENALI_PHY_116
#define LPDDR4__PHY_CLK_WRDQ7_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_116__PHY_CLK_WRDQ7_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_117_READ_MASK                             0x03FF07FFU
#define LPDDR4__DENALI_PHY_117_WRITE_MASK                            0x03FF07FFU
#define LPDDR4__DENALI_PHY_117__PHY_CLK_WRDM_SLAVE_DELAY_0_MASK      0x000007FFU
#define LPDDR4__DENALI_PHY_117__PHY_CLK_WRDM_SLAVE_DELAY_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_117__PHY_CLK_WRDM_SLAVE_DELAY_0_WIDTH             11U
#define LPDDR4__PHY_CLK_WRDM_SLAVE_DELAY_0__REG DENALI_PHY_117
#define LPDDR4__PHY_CLK_WRDM_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_117__PHY_CLK_WRDM_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_117__PHY_CLK_WRDQS_SLAVE_DELAY_0_MASK     0x03FF0000U
#define LPDDR4__DENALI_PHY_117__PHY_CLK_WRDQS_SLAVE_DELAY_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_117__PHY_CLK_WRDQS_SLAVE_DELAY_0_WIDTH            10U
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_0__REG DENALI_PHY_117
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_117__PHY_CLK_WRDQS_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_118_READ_MASK                             0x0003FF03U
#define LPDDR4__DENALI_PHY_118_WRITE_MASK                            0x0003FF03U
#define LPDDR4__DENALI_PHY_118__PHY_WRLVL_THRESHOLD_ADJUST_0_MASK    0x00000003U
#define LPDDR4__DENALI_PHY_118__PHY_WRLVL_THRESHOLD_ADJUST_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_118__PHY_WRLVL_THRESHOLD_ADJUST_0_WIDTH            2U
#define LPDDR4__PHY_WRLVL_THRESHOLD_ADJUST_0__REG DENALI_PHY_118
#define LPDDR4__PHY_WRLVL_THRESHOLD_ADJUST_0__FLD LPDDR4__DENALI_PHY_118__PHY_WRLVL_THRESHOLD_ADJUST_0

#define LPDDR4__DENALI_PHY_118__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_0_MASK 0x0003FF00U
#define LPDDR4__DENALI_PHY_118__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_0_SHIFT        8U
#define LPDDR4__DENALI_PHY_118__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_0__REG DENALI_PHY_118
#define LPDDR4__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_118__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_119_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_119_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_119__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_119__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_119__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_0__REG DENALI_PHY_119
#define LPDDR4__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_119__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_119__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_119__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_119__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_0__REG DENALI_PHY_119
#define LPDDR4__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_119__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_120_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_120_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_120__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_120__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_120__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_0__REG DENALI_PHY_120
#define LPDDR4__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_120__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_120__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_120__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_120__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_0__REG DENALI_PHY_120
#define LPDDR4__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_120__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_121_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_121_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_121__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_121__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_121__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_0__REG DENALI_PHY_121
#define LPDDR4__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_121__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_121__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_121__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_121__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_0__REG DENALI_PHY_121
#define LPDDR4__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_121__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_122_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_122_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_122__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_122__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_122__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_0__REG DENALI_PHY_122
#define LPDDR4__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_122__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_122__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_122__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_122__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_0__REG DENALI_PHY_122
#define LPDDR4__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_122__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_123_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_123_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_123__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_123__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_123__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_0__REG DENALI_PHY_123
#define LPDDR4__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_123__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_123__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_123__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_123__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_0__REG DENALI_PHY_123
#define LPDDR4__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_123__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_124_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_124_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_124__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_124__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_124__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_0__REG DENALI_PHY_124
#define LPDDR4__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_124__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_124__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_124__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_124__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_0__REG DENALI_PHY_124
#define LPDDR4__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_124__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_125_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_125_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_125__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_125__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_125__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_0__REG DENALI_PHY_125
#define LPDDR4__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_125__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_125__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_125__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_125__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_0__REG DENALI_PHY_125
#define LPDDR4__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_125__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_126_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_126_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_126__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_126__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_126__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_0_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_0__REG DENALI_PHY_126
#define LPDDR4__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_126__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_126__PHY_RDDQS_DM_RISE_SLAVE_DELAY_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_126__PHY_RDDQS_DM_RISE_SLAVE_DELAY_0_SHIFT        16U
#define LPDDR4__DENALI_PHY_126__PHY_RDDQS_DM_RISE_SLAVE_DELAY_0_WIDTH        10U
#define LPDDR4__PHY_RDDQS_DM_RISE_SLAVE_DELAY_0__REG DENALI_PHY_126
#define LPDDR4__PHY_RDDQS_DM_RISE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_126__PHY_RDDQS_DM_RISE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_127_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_127_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_127__PHY_RDDQS_DM_FALL_SLAVE_DELAY_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_127__PHY_RDDQS_DM_FALL_SLAVE_DELAY_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_127__PHY_RDDQS_DM_FALL_SLAVE_DELAY_0_WIDTH        10U
#define LPDDR4__PHY_RDDQS_DM_FALL_SLAVE_DELAY_0__REG DENALI_PHY_127
#define LPDDR4__PHY_RDDQS_DM_FALL_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_127__PHY_RDDQS_DM_FALL_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_127__PHY_RDDQS_GATE_SLAVE_DELAY_0_MASK    0x03FF0000U
#define LPDDR4__DENALI_PHY_127__PHY_RDDQS_GATE_SLAVE_DELAY_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_127__PHY_RDDQS_GATE_SLAVE_DELAY_0_WIDTH           10U
#define LPDDR4__PHY_RDDQS_GATE_SLAVE_DELAY_0__REG DENALI_PHY_127
#define LPDDR4__PHY_RDDQS_GATE_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_127__PHY_RDDQS_GATE_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_128_READ_MASK                             0x03FF070FU
#define LPDDR4__DENALI_PHY_128_WRITE_MASK                            0x03FF070FU
#define LPDDR4__DENALI_PHY_128__PHY_RDDQS_LATENCY_ADJUST_0_MASK      0x0000000FU
#define LPDDR4__DENALI_PHY_128__PHY_RDDQS_LATENCY_ADJUST_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_128__PHY_RDDQS_LATENCY_ADJUST_0_WIDTH              4U
#define LPDDR4__PHY_RDDQS_LATENCY_ADJUST_0__REG DENALI_PHY_128
#define LPDDR4__PHY_RDDQS_LATENCY_ADJUST_0__FLD LPDDR4__DENALI_PHY_128__PHY_RDDQS_LATENCY_ADJUST_0

#define LPDDR4__DENALI_PHY_128__PHY_WRITE_PATH_LAT_ADD_0_MASK        0x00000700U
#define LPDDR4__DENALI_PHY_128__PHY_WRITE_PATH_LAT_ADD_0_SHIFT                8U
#define LPDDR4__DENALI_PHY_128__PHY_WRITE_PATH_LAT_ADD_0_WIDTH                3U
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_0__REG DENALI_PHY_128
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_0__FLD LPDDR4__DENALI_PHY_128__PHY_WRITE_PATH_LAT_ADD_0

#define LPDDR4__DENALI_PHY_128__PHY_WRLVL_DELAY_EARLY_THRESHOLD_0_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_128__PHY_WRLVL_DELAY_EARLY_THRESHOLD_0_SHIFT      16U
#define LPDDR4__DENALI_PHY_128__PHY_WRLVL_DELAY_EARLY_THRESHOLD_0_WIDTH      10U
#define LPDDR4__PHY_WRLVL_DELAY_EARLY_THRESHOLD_0__REG DENALI_PHY_128
#define LPDDR4__PHY_WRLVL_DELAY_EARLY_THRESHOLD_0__FLD LPDDR4__DENALI_PHY_128__PHY_WRLVL_DELAY_EARLY_THRESHOLD_0

#define LPDDR4__DENALI_PHY_129_READ_MASK                             0x000103FFU
#define LPDDR4__DENALI_PHY_129_WRITE_MASK                            0x000103FFU
#define LPDDR4__DENALI_PHY_129__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_129__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_0_SHIFT      0U
#define LPDDR4__DENALI_PHY_129__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_0_WIDTH     10U
#define LPDDR4__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_0__REG DENALI_PHY_129
#define LPDDR4__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_0__FLD LPDDR4__DENALI_PHY_129__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_0

#define LPDDR4__DENALI_PHY_129__PHY_WRLVL_EARLY_FORCE_ZERO_0_MASK    0x00010000U
#define LPDDR4__DENALI_PHY_129__PHY_WRLVL_EARLY_FORCE_ZERO_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_129__PHY_WRLVL_EARLY_FORCE_ZERO_0_WIDTH            1U
#define LPDDR4__DENALI_PHY_129__PHY_WRLVL_EARLY_FORCE_ZERO_0_WOCLR            0U
#define LPDDR4__DENALI_PHY_129__PHY_WRLVL_EARLY_FORCE_ZERO_0_WOSET            0U
#define LPDDR4__PHY_WRLVL_EARLY_FORCE_ZERO_0__REG DENALI_PHY_129
#define LPDDR4__PHY_WRLVL_EARLY_FORCE_ZERO_0__FLD LPDDR4__DENALI_PHY_129__PHY_WRLVL_EARLY_FORCE_ZERO_0

#define LPDDR4__DENALI_PHY_130_READ_MASK                             0x000F03FFU
#define LPDDR4__DENALI_PHY_130_WRITE_MASK                            0x000F03FFU
#define LPDDR4__DENALI_PHY_130__PHY_GTLVL_RDDQS_SLV_DLY_START_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_130__PHY_GTLVL_RDDQS_SLV_DLY_START_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_130__PHY_GTLVL_RDDQS_SLV_DLY_START_0_WIDTH        10U
#define LPDDR4__PHY_GTLVL_RDDQS_SLV_DLY_START_0__REG DENALI_PHY_130
#define LPDDR4__PHY_GTLVL_RDDQS_SLV_DLY_START_0__FLD LPDDR4__DENALI_PHY_130__PHY_GTLVL_RDDQS_SLV_DLY_START_0

#define LPDDR4__DENALI_PHY_130__PHY_GTLVL_LAT_ADJ_START_0_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_130__PHY_GTLVL_LAT_ADJ_START_0_SHIFT              16U
#define LPDDR4__DENALI_PHY_130__PHY_GTLVL_LAT_ADJ_START_0_WIDTH               4U
#define LPDDR4__PHY_GTLVL_LAT_ADJ_START_0__REG DENALI_PHY_130
#define LPDDR4__PHY_GTLVL_LAT_ADJ_START_0__FLD LPDDR4__DENALI_PHY_130__PHY_GTLVL_LAT_ADJ_START_0

#define LPDDR4__DENALI_PHY_131_READ_MASK                             0x010F07FFU
#define LPDDR4__DENALI_PHY_131_WRITE_MASK                            0x010F07FFU
#define LPDDR4__DENALI_PHY_131__PHY_WDQLVL_DQDM_SLV_DLY_START_0_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_131__PHY_WDQLVL_DQDM_SLV_DLY_START_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_131__PHY_WDQLVL_DQDM_SLV_DLY_START_0_WIDTH        11U
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_START_0__REG DENALI_PHY_131
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_START_0__FLD LPDDR4__DENALI_PHY_131__PHY_WDQLVL_DQDM_SLV_DLY_START_0

#define LPDDR4__DENALI_PHY_131__PHY_NTP_WRLAT_START_0_MASK           0x000F0000U
#define LPDDR4__DENALI_PHY_131__PHY_NTP_WRLAT_START_0_SHIFT                  16U
#define LPDDR4__DENALI_PHY_131__PHY_NTP_WRLAT_START_0_WIDTH                   4U
#define LPDDR4__PHY_NTP_WRLAT_START_0__REG DENALI_PHY_131
#define LPDDR4__PHY_NTP_WRLAT_START_0__FLD LPDDR4__DENALI_PHY_131__PHY_NTP_WRLAT_START_0

#define LPDDR4__DENALI_PHY_131__PHY_NTP_PASS_0_MASK                  0x01000000U
#define LPDDR4__DENALI_PHY_131__PHY_NTP_PASS_0_SHIFT                         24U
#define LPDDR4__DENALI_PHY_131__PHY_NTP_PASS_0_WIDTH                          1U
#define LPDDR4__DENALI_PHY_131__PHY_NTP_PASS_0_WOCLR                          0U
#define LPDDR4__DENALI_PHY_131__PHY_NTP_PASS_0_WOSET                          0U
#define LPDDR4__PHY_NTP_PASS_0__REG DENALI_PHY_131
#define LPDDR4__PHY_NTP_PASS_0__FLD LPDDR4__DENALI_PHY_131__PHY_NTP_PASS_0

#define LPDDR4__DENALI_PHY_132_READ_MASK                             0x000003FFU
#define LPDDR4__DENALI_PHY_132_WRITE_MASK                            0x000003FFU
#define LPDDR4__DENALI_PHY_132__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_0_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_132__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_0_SHIFT      0U
#define LPDDR4__DENALI_PHY_132__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_0_WIDTH     10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_0__REG DENALI_PHY_132
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_0__FLD LPDDR4__DENALI_PHY_132__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_0

#define LPDDR4__DENALI_PHY_133_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_133_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQS_CLK_ADJUST_0_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQS_CLK_ADJUST_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQS_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQS_CLK_ADJUST_0__REG DENALI_PHY_133
#define LPDDR4__PHY_DATA_DC_DQS_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQS_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ0_CLK_ADJUST_0_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ0_CLK_ADJUST_0_SHIFT            8U
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ0_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ0_CLK_ADJUST_0__REG DENALI_PHY_133
#define LPDDR4__PHY_DATA_DC_DQ0_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ0_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ1_CLK_ADJUST_0_MASK    0x00FF0000U
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ1_CLK_ADJUST_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ1_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ1_CLK_ADJUST_0__REG DENALI_PHY_133
#define LPDDR4__PHY_DATA_DC_DQ1_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ1_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ2_CLK_ADJUST_0_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ2_CLK_ADJUST_0_SHIFT           24U
#define LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ2_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ2_CLK_ADJUST_0__REG DENALI_PHY_133
#define LPDDR4__PHY_DATA_DC_DQ2_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_133__PHY_DATA_DC_DQ2_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_134_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_134_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ3_CLK_ADJUST_0_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ3_CLK_ADJUST_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ3_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ3_CLK_ADJUST_0__REG DENALI_PHY_134
#define LPDDR4__PHY_DATA_DC_DQ3_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ3_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ4_CLK_ADJUST_0_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ4_CLK_ADJUST_0_SHIFT            8U
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ4_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ4_CLK_ADJUST_0__REG DENALI_PHY_134
#define LPDDR4__PHY_DATA_DC_DQ4_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ4_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ5_CLK_ADJUST_0_MASK    0x00FF0000U
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ5_CLK_ADJUST_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ5_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ5_CLK_ADJUST_0__REG DENALI_PHY_134
#define LPDDR4__PHY_DATA_DC_DQ5_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ5_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ6_CLK_ADJUST_0_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ6_CLK_ADJUST_0_SHIFT           24U
#define LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ6_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ6_CLK_ADJUST_0__REG DENALI_PHY_134
#define LPDDR4__PHY_DATA_DC_DQ6_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_134__PHY_DATA_DC_DQ6_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_135_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_135_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_135__PHY_DATA_DC_DQ7_CLK_ADJUST_0_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_135__PHY_DATA_DC_DQ7_CLK_ADJUST_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_135__PHY_DATA_DC_DQ7_CLK_ADJUST_0_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ7_CLK_ADJUST_0__REG DENALI_PHY_135
#define LPDDR4__PHY_DATA_DC_DQ7_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_135__PHY_DATA_DC_DQ7_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_135__PHY_DATA_DC_DM_CLK_ADJUST_0_MASK     0x0000FF00U
#define LPDDR4__DENALI_PHY_135__PHY_DATA_DC_DM_CLK_ADJUST_0_SHIFT             8U
#define LPDDR4__DENALI_PHY_135__PHY_DATA_DC_DM_CLK_ADJUST_0_WIDTH             8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_ADJUST_0__REG DENALI_PHY_135
#define LPDDR4__PHY_DATA_DC_DM_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_135__PHY_DATA_DC_DM_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_135__PHY_DSLICE_PAD_BOOSTPN_SETTING_0_MASK 0xFFFF0000U
#define LPDDR4__DENALI_PHY_135__PHY_DSLICE_PAD_BOOSTPN_SETTING_0_SHIFT       16U
#define LPDDR4__DENALI_PHY_135__PHY_DSLICE_PAD_BOOSTPN_SETTING_0_WIDTH       16U
#define LPDDR4__PHY_DSLICE_PAD_BOOSTPN_SETTING_0__REG DENALI_PHY_135
#define LPDDR4__PHY_DSLICE_PAD_BOOSTPN_SETTING_0__FLD LPDDR4__DENALI_PHY_135__PHY_DSLICE_PAD_BOOSTPN_SETTING_0

#define LPDDR4__DENALI_PHY_136_READ_MASK                             0x0003033FU
#define LPDDR4__DENALI_PHY_136_WRITE_MASK                            0x0003033FU
#define LPDDR4__DENALI_PHY_136__PHY_DSLICE_PAD_RX_CTLE_SETTING_0_MASK 0x0000003FU
#define LPDDR4__DENALI_PHY_136__PHY_DSLICE_PAD_RX_CTLE_SETTING_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_136__PHY_DSLICE_PAD_RX_CTLE_SETTING_0_WIDTH        6U
#define LPDDR4__PHY_DSLICE_PAD_RX_CTLE_SETTING_0__REG DENALI_PHY_136
#define LPDDR4__PHY_DSLICE_PAD_RX_CTLE_SETTING_0__FLD LPDDR4__DENALI_PHY_136__PHY_DSLICE_PAD_RX_CTLE_SETTING_0

#define LPDDR4__DENALI_PHY_136__PHY_DQ_FFE_0_MASK                    0x00000300U
#define LPDDR4__DENALI_PHY_136__PHY_DQ_FFE_0_SHIFT                            8U
#define LPDDR4__DENALI_PHY_136__PHY_DQ_FFE_0_WIDTH                            2U
#define LPDDR4__PHY_DQ_FFE_0__REG DENALI_PHY_136
#define LPDDR4__PHY_DQ_FFE_0__FLD LPDDR4__DENALI_PHY_136__PHY_DQ_FFE_0

#define LPDDR4__DENALI_PHY_136__PHY_DQS_FFE_0_MASK                   0x00030000U
#define LPDDR4__DENALI_PHY_136__PHY_DQS_FFE_0_SHIFT                          16U
#define LPDDR4__DENALI_PHY_136__PHY_DQS_FFE_0_WIDTH                           2U
#define LPDDR4__PHY_DQS_FFE_0__REG DENALI_PHY_136
#define LPDDR4__PHY_DQS_FFE_0__FLD LPDDR4__DENALI_PHY_136__PHY_DQS_FFE_0

#endif /* REG_LPDDR4_DATA_SLICE_0_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

