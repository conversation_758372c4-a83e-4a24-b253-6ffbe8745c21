/*
 * AM62x GPIO Driver - Linux Kernel Implementation
 * 
 * 将AM62x SDK中的GPIO驱动代码转换为Linux内核实现
 * 原始代码使用CSL（Chip Support Library）寄存器访问方式
 * 转换后使用Linux标准的寄存器读写操作
 */

#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/gpio/driver.h>
#include <linux/platform_device.h>
#include <linux/of.h>
#include <linux/of_device.h>
#include <linux/io.h>
#include <linux/clk.h>
#include <linux/pm_runtime.h>
#include <linux/interrupt.h>

/* AM62x GPIO寄存器偏移定义 - 基于原始CSL定义转换 */
/* GPIO v0 寄存器偏移 (主要GPIO控制器) */
#define GPIO_PID                    0x00
#define GPIO_PCR                    0x04
#define GPIO_BINTEN                 0x08
#define GPIO_DIR(n)                 (0x10 + ((n) * 0x28))
#define GPIO_OUT_DATA(n)            (0x14 + ((n) * 0x28))
#define GPIO_SET_DATA(n)            (0x18 + ((n) * 0x28))
#define GPIO_CLR_DATA(n)            (0x1C + ((n) * 0x28))
#define GPIO_IN_DATA(n)             (0x20 + ((n) * 0x28))
#define GPIO_SET_RIS_TRIG(n)        (0x24 + ((n) * 0x28))

/* GPIO v1 寄存器偏移 (MCU GPIO控制器) */
#define GPIO_V1_GIODIR(n)           (0x34 + ((n) * 0x20))
#define GPIO_V1_GIODIN(n)           (0x38 + ((n) * 0x20))
#define GPIO_V1_GIODOUT(n)          (0x3C + ((n) * 0x20))
#define GPIO_V1_GIOSET(n)           (0x40 + ((n) * 0x20))
#define GPIO_V1_GIOCLR(n)           (0x44 + ((n) * 0x20))
#define GPIO_V1_GIOPDR(n)           (0x48 + ((n) * 0x20))
#define GPIO_V1_GIOPULDIS(n)        (0x4C + ((n) * 0x20))

/* GPIO方向定义 - 根据AM62x技术手册修正 */
#define GPIO_DIRECTION_OUTPUT       0x0  /* DIR位=0表示输出模式 */
#define GPIO_DIRECTION_INPUT        0x1  /* DIR位=1表示输入模式 */

/* GPIO引脚计算宏 - 基于原始SDK宏转换 */
#define GPIO_PINS_PER_REG_SHIFT     5U
#define GPIO_PINS_PER_BANK_SHIFT    4U
#define GPIO_MAX_PINS_PER_PORT      8U
#define GPIO_MAX_PORT               8U

/* v0版本宏 */
#define GPIO_GET_REG_INDEX(pinNum)      (((uint32_t) pinNum) >> GPIO_PINS_PER_REG_SHIFT)
#define GPIO_GET_BIT_POS(pinNum)        (pinNum - ((GPIO_GET_REG_INDEX(pinNum)) << GPIO_PINS_PER_REG_SHIFT))
#define GPIO_GET_BIT_MASK(pinNum)       (((uint32_t) 1U) << GPIO_GET_BIT_POS(pinNum))

/* v1版本宏 */
#define GPIO_GET_PIN_OFFSET(pinNum)     (((uint32_t)pinNum) % (GPIO_MAX_PORT * GPIO_MAX_PINS_PER_PORT))
#define GPIO_GET_PORT_NUM(pinNum)       ((GPIO_GET_PIN_OFFSET(pinNum)) / GPIO_MAX_PINS_PER_PORT)
#define GPIO_GET_PIN_INDEX(pinNum)      ((GPIO_GET_PIN_OFFSET(pinNum)) % GPIO_MAX_PINS_PER_PORT)

/* AM62x GPIO基地址定义 */
#define AM62X_GPIO0_BASE            0x00600000UL
#define AM62X_GPIO1_BASE            0x00601000UL
#define AM62X_MCU_GPIO0_BASE        0x04201000UL

/* GPIO控制器类型 */
enum am62x_gpio_type {
    AM62X_GPIO_TYPE_V0,     /* 主域GPIO */
    AM62X_GPIO_TYPE_V1,     /* MCU域GPIO */
};

/* GPIO控制器结构体 */
struct am62x_gpio_chip {
    struct gpio_chip chip;
    void __iomem *base;
    struct device *dev;
    struct clk *clk;
    enum am62x_gpio_type type;
    int irq;
    spinlock_t lock;
};

/* Linux内核寄存器读写函数 */
static inline uint32_t read_reg(void __iomem *base, uint32_t offset)
{
    return readl(base + offset);
}

static inline void write_reg(void __iomem *base, uint32_t offset, uint32_t value)
{
    writel(value, base + offset);
}

/* 位域操作函数 - 替代CSL_FEXTR和CSL_FINSR */
static inline uint32_t extract_field(uint32_t reg, uint32_t msb, uint32_t lsb)
{
    return ((reg) >> (lsb)) & ((((uint32_t)1U) << ((msb) - (lsb) + ((uint32_t)1U))) - ((uint32_t)1U));
}

static inline uint32_t insert_field(uint32_t reg, uint32_t msb, uint32_t lsb, uint32_t val)
{
    uint32_t mask = ((((uint32_t)1U) << ((msb) - (lsb) + ((uint32_t)1U))) - ((uint32_t)1U)) << (lsb);
    return (reg & (~mask)) | (((val) << (lsb)) & mask);
}

/*
 * GPIO方向设置函数 - 转换自GPIO_setDirMode
 * 原始函数使用CSL_FINSR进行位域操作
 *
 * 根据AM62x技术手册修正：
 * - DIR位=0表示输出模式(OUTPUT)
 * - DIR位=1表示输入模式(INPUT)
 */
static int am62x_gpio_set_direction(struct am62x_gpio_chip *gpio_chip,
                                   uint32_t pin_num, uint32_t pin_dir)
{
    uint32_t reg_val;
    unsigned long flags;

    if (!gpio_chip || !gpio_chip->base) {
        return -EINVAL;
    }

    spin_lock_irqsave(&gpio_chip->lock, flags);

    if (gpio_chip->type == AM62X_GPIO_TYPE_V0) {
        /* GPIO v0版本实现 */
        uint32_t reg_index = GPIO_GET_REG_INDEX(pin_num);
        uint32_t bit_pos = GPIO_GET_BIT_POS(pin_num);

        reg_val = read_reg(gpio_chip->base, GPIO_DIR(reg_index));

        /* 修正方向设置逻辑：
         * - 输出模式(pin_dir=0)：清除对应位(设为0)
         * - 输入模式(pin_dir=1)：设置对应位(设为1)
         */
        if (pin_dir == GPIO_DIRECTION_OUTPUT) {
            /* 输出模式：清除对应位 */
            reg_val &= ~(1U << bit_pos);
        } else {
            /* 输入模式：设置对应位 */
            reg_val |= (1U << bit_pos);
        }

        write_reg(gpio_chip->base, GPIO_DIR(reg_index), reg_val);

    } else if (gpio_chip->type == AM62X_GPIO_TYPE_V1) {
        /* GPIO v1版本实现 - 同样需要修正 */
        uint32_t port_num = GPIO_GET_PORT_NUM(pin_num);
        uint32_t pin_index = GPIO_GET_PIN_INDEX(pin_num);

        reg_val = read_reg(gpio_chip->base, GPIO_V1_GIODIR(port_num));

        /* 修正v1版本的方向设置逻辑 */
        if (pin_dir == GPIO_DIRECTION_OUTPUT) {
            /* 输出模式：清除对应位 */
            reg_val &= ~(1U << pin_index);
        } else {
            /* 输入模式：设置对应位 */
            reg_val |= (1U << pin_index);
        }

        write_reg(gpio_chip->base, GPIO_V1_GIODIR(port_num), reg_val);
    }

    spin_unlock_irqrestore(&gpio_chip->lock, flags);
    return 0;
}

/*
 * GPIO输入值读取函数 - 转换自GPIO_pinRead
 * 原始函数使用CSL_FEXTR进行位域提取
 */
static int am62x_gpio_get_value(struct am62x_gpio_chip *gpio_chip, uint32_t pin_num)
{
    uint32_t in_data;
    
    if (!gpio_chip || !gpio_chip->base) {
        return -EINVAL;
    }
    
    if (gpio_chip->type == AM62X_GPIO_TYPE_V0) {
        /* GPIO v0版本实现 */
        uint32_t reg_index = GPIO_GET_REG_INDEX(pin_num);
        uint32_t bit_pos = GPIO_GET_BIT_POS(pin_num);
        
        in_data = read_reg(gpio_chip->base, GPIO_IN_DATA(reg_index));
        in_data = extract_field(in_data, bit_pos, bit_pos);
        
    } else if (gpio_chip->type == AM62X_GPIO_TYPE_V1) {
        /* GPIO v1版本实现 */
        uint32_t port_num = GPIO_GET_PORT_NUM(pin_num);
        uint32_t pin_index = GPIO_GET_PIN_INDEX(pin_num);
        
        in_data = read_reg(gpio_chip->base, GPIO_V1_GIODIN(port_num));
        in_data &= 1U << pin_index;
        in_data = in_data >> pin_index;
    } else {
        return -EINVAL;
    }
    
    return (int)in_data;
}

/*
 * GPIO输出值设置函数 - 转换自GPIO_pinWriteHigh/GPIO_pinWriteLow
 * 原始函数使用CSL_REG32_WR进行寄存器写入
 */
static void am62x_gpio_set_value(struct am62x_gpio_chip *gpio_chip, 
                                uint32_t pin_num, int value)
{
    unsigned long flags;
    
    if (!gpio_chip || !gpio_chip->base) {
        return;
    }
    
    spin_lock_irqsave(&gpio_chip->lock, flags);
    
    if (gpio_chip->type == AM62X_GPIO_TYPE_V0) {
        /* GPIO v0版本实现 */
        uint32_t reg_index = GPIO_GET_REG_INDEX(pin_num);
        uint32_t reg_val = GPIO_GET_BIT_MASK(pin_num);
        
        if (value) {
            write_reg(gpio_chip->base, GPIO_SET_DATA(reg_index), reg_val);
        } else {
            write_reg(gpio_chip->base, GPIO_CLR_DATA(reg_index), reg_val);
        }
        
    } else if (gpio_chip->type == AM62X_GPIO_TYPE_V1) {
        /* GPIO v1版本实现 */
        uint32_t port_num = GPIO_GET_PORT_NUM(pin_num);
        uint32_t pin_index = GPIO_GET_PIN_INDEX(pin_num);
        
        if (value) {
            write_reg(gpio_chip->base, GPIO_V1_GIOSET(port_num), (1U << pin_index));
        } else {
            write_reg(gpio_chip->base, GPIO_V1_GIOCLR(port_num), (1U << pin_index));
        }
    }
    
    spin_unlock_irqrestore(&gpio_chip->lock, flags);
}

/* Linux GPIO子系统回调函数 */
static int am62x_gpio_direction_input(struct gpio_chip *chip, unsigned offset)
{
    struct am62x_gpio_chip *gpio_chip = gpiochip_get_data(chip);
    return am62x_gpio_set_direction(gpio_chip, offset, GPIO_DIRECTION_INPUT);
}

static int am62x_gpio_direction_output(struct gpio_chip *chip, unsigned offset, int value)
{
    struct am62x_gpio_chip *gpio_chip = gpiochip_get_data(chip);
    int ret;
    
    /* 先设置输出值，再设置方向 */
    am62x_gpio_set_value(gpio_chip, offset, value);
    ret = am62x_gpio_set_direction(gpio_chip, offset, GPIO_DIRECTION_OUTPUT);
    
    return ret;
}

static int am62x_gpio_get(struct gpio_chip *chip, unsigned offset)
{
    struct am62x_gpio_chip *gpio_chip = gpiochip_get_data(chip);
    return am62x_gpio_get_value(gpio_chip, offset);
}

static void am62x_gpio_set(struct gpio_chip *chip, unsigned offset, int value)
{
    struct am62x_gpio_chip *gpio_chip = gpiochip_get_data(chip);
    am62x_gpio_set_value(gpio_chip, offset, value);
}

/* 设备树匹配表 */
static const struct of_device_id am62x_gpio_of_match[] = {
    { .compatible = "ti,am62x-gpio", .data = (void *)AM62X_GPIO_TYPE_V0 },
    { .compatible = "ti,am62x-mcu-gpio", .data = (void *)AM62X_GPIO_TYPE_V1 },
    { }
};
MODULE_DEVICE_TABLE(of, am62x_gpio_of_match);

/* 平台驱动探测函数 */
static int am62x_gpio_probe(struct platform_device *pdev)
{
    struct am62x_gpio_chip *gpio_chip;
    struct resource *res;
    const struct of_device_id *match;
    int ret;
    u32 ngpios = 32; /* 默认32个GPIO */

    gpio_chip = devm_kzalloc(&pdev->dev, sizeof(*gpio_chip), GFP_KERNEL);
    if (!gpio_chip)
        return -ENOMEM;

    gpio_chip->dev = &pdev->dev;
    spin_lock_init(&gpio_chip->lock);

    /* 获取设备类型 */
    match = of_match_device(am62x_gpio_of_match, &pdev->dev);
    if (match) {
        gpio_chip->type = (enum am62x_gpio_type)match->data;
    } else {
        dev_err(&pdev->dev, "无法匹配设备类型\n");
        return -EINVAL;
    }

    /* 获取内存资源 */
    res = platform_get_resource(pdev, IORESOURCE_MEM, 0);
    gpio_chip->base = devm_ioremap_resource(&pdev->dev, res);
    if (IS_ERR(gpio_chip->base)) {
        dev_err(&pdev->dev, "无法映射寄存器内存\n");
        return PTR_ERR(gpio_chip->base);
    }

    /* 获取时钟 */
    gpio_chip->clk = devm_clk_get(&pdev->dev, NULL);
    if (IS_ERR(gpio_chip->clk)) {
        dev_warn(&pdev->dev, "无法获取时钟，继续执行\n");
        gpio_chip->clk = NULL;
    } else {
        ret = clk_prepare_enable(gpio_chip->clk);
        if (ret) {
            dev_err(&pdev->dev, "无法使能时钟: %d\n", ret);
            return ret;
        }
    }

    /* 从设备树获取GPIO数量 */
    of_property_read_u32(pdev->dev.of_node, "ngpios", &ngpios);

    /* 初始化GPIO芯片结构 */
    gpio_chip->chip.label = dev_name(&pdev->dev);
    gpio_chip->chip.parent = &pdev->dev;
    gpio_chip->chip.owner = THIS_MODULE;
    gpio_chip->chip.of_node = pdev->dev.of_node;
    gpio_chip->chip.base = -1; /* 动态分配基地址 */
    gpio_chip->chip.ngpio = ngpios;
    gpio_chip->chip.direction_input = am62x_gpio_direction_input;
    gpio_chip->chip.direction_output = am62x_gpio_direction_output;
    gpio_chip->chip.get = am62x_gpio_get;
    gpio_chip->chip.set = am62x_gpio_set;
    gpio_chip->chip.can_sleep = false;

    /* 启用运行时电源管理 */
    pm_runtime_enable(&pdev->dev);
    ret = pm_runtime_get_sync(&pdev->dev);
    if (ret < 0) {
        dev_err(&pdev->dev, "运行时PM同步失败: %d\n", ret);
        goto err_pm_disable;
    }

    /* 注册GPIO芯片 */
    ret = devm_gpiochip_add_data(&pdev->dev, &gpio_chip->chip, gpio_chip);
    if (ret) {
        dev_err(&pdev->dev, "无法注册GPIO芯片: %d\n", ret);
        goto err_pm_put;
    }

    platform_set_drvdata(pdev, gpio_chip);

    dev_info(&pdev->dev, "AM62x GPIO驱动初始化成功，类型: %s，GPIO数量: %d\n",
             gpio_chip->type == AM62X_GPIO_TYPE_V0 ? "主域" : "MCU域", ngpios);

    return 0;

err_pm_put:
    pm_runtime_put_sync(&pdev->dev);
err_pm_disable:
    pm_runtime_disable(&pdev->dev);
    if (gpio_chip->clk)
        clk_disable_unprepare(gpio_chip->clk);

    return ret;
}

/* 平台驱动移除函数 */
static int am62x_gpio_remove(struct platform_device *pdev)
{
    struct am62x_gpio_chip *gpio_chip = platform_get_drvdata(pdev);

    pm_runtime_put_sync(&pdev->dev);
    pm_runtime_disable(&pdev->dev);

    if (gpio_chip->clk)
        clk_disable_unprepare(gpio_chip->clk);

    return 0;
}

/* 运行时电源管理回调 */
static int am62x_gpio_runtime_suspend(struct device *dev)
{
    struct am62x_gpio_chip *gpio_chip = dev_get_drvdata(dev);

    if (gpio_chip->clk)
        clk_disable_unprepare(gpio_chip->clk);

    return 0;
}

static int am62x_gpio_runtime_resume(struct device *dev)
{
    struct am62x_gpio_chip *gpio_chip = dev_get_drvdata(dev);
    int ret = 0;

    if (gpio_chip->clk) {
        ret = clk_prepare_enable(gpio_chip->clk);
        if (ret)
            dev_err(dev, "无法重新使能时钟: %d\n", ret);
    }

    return ret;
}

static const struct dev_pm_ops am62x_gpio_pm_ops = {
    SET_RUNTIME_PM_OPS(am62x_gpio_runtime_suspend,
                       am62x_gpio_runtime_resume, NULL)
};

/* 平台驱动结构 */
static struct platform_driver am62x_gpio_driver = {
    .probe = am62x_gpio_probe,
    .remove = am62x_gpio_remove,
    .driver = {
        .name = "am62x-gpio",
        .of_match_table = am62x_gpio_of_match,
        .pm = &am62x_gpio_pm_ops,
    },
};

module_platform_driver(am62x_gpio_driver);

MODULE_DESCRIPTION("AM62x GPIO驱动 - Linux内核实现");
MODULE_AUTHOR("TI AM62x GPIO Driver Conversion");
MODULE_LICENSE("GPL v2");
MODULE_ALIAS("platform:am62x-gpio");
