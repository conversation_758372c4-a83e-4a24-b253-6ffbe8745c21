%%{
    let common = system.getScript("/common");
    let module = system.modules['/drivers/udma/udma'];
    let blkcopy_ch = system.modules[`/drivers/udma/udma_blkcopy_channel`];
%%}
/*
 * UDMA
 */
#include <drivers/udma.h>

% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    % let instNameCamelCase = common.camelSentence(instance.$name);
    % let ch_instances = instance.udmaBlkCopyChannel;
    % if(ch_instances && ch_instances.length > 0) {
/*
 * UDMA `config.name.toUpperCase()` Blockcopy Parameters
 */
#define `instance.$name.toUpperCase()`_NUM_BLKCOPY_CH (`ch_instances.length`U)
/* UDMA `config.name.toUpperCase()` Blockcopy Channel Handle */
extern Udma_ChHandle g`instNameCamelCase`BlkCopyChHandle[`instance.$name.toUpperCase()`_NUM_BLKCOPY_CH];

/* UDMA `config.name.toUpperCase()` Blockcopy Channel CQ Event Handle - applicable only when interrupt is enabled */
extern Udma_EventHandle g`instNameCamelCase`BlkCopyCqEventHandle[`instance.$name.toUpperCase()`_NUM_BLKCOPY_CH];
/* UDMA `config.name.toUpperCase()` Blockcopy Channel Callback declaration */
        % for(let ch = 0; ch < ch_instances.length; ch++) {
            % let ch_instance = ch_instances[ch];
            % let ch_config = blkcopy_ch.getInstanceConfig(ch_instance);
            % if(ch_config.intrEnable == true) {
extern void `ch_config.transferCallbackFxn`(Udma_EventHandle eventHandle, uint32_t eventType, void *appData);
            % }
        % }

    % }
% }
/* UDMA Driver open/close - can be used by application when Driver_open() and
 * Driver_close() is not used directly and app wants to control the various driver
 * open/close sequences */
void Drivers_udmaOpen(void);
void Drivers_udmaClose(void);
