/*
 *  Copyright (C) 2022 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef SIPC_NOTIFY_H_
#define SIPC_NOTIFY_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <kernel/dpl/SystemP.h>
#include <drivers/secure_ipc_notify/soc/sipc_notify_soc.h>

/**
 * \defgroup DRV_SIPC_NOTIFY_MODULE APIs for Secure IPC Notify
 * \ingroup DRV_MODULE
 *
 * See \ref DRIVERS_SIPC_NOTIFY_PAGE for more details.
 *
 * @{
 */

/**
 * \brief User callback that is invoked when a message is received from a reote core for a given client ID
 *
 * Before invoking the API, the IPC module would have 'popped` the message from the HW or SW FIFO already.
 * This callback is called frm ISR context, so all constraints of ISR should be applied by the callback,
 * e.g. no blocking wait, quickly handle message and exit ISR.
 *
 * For most applications, it is recommended to put the message value into a local SW queue and defer the
 * message handling itself to a application task.
 *
 * @note
 * This callback will process the *msgValue . Registered function should not be blocking.
 *  as this is called from isr context.
 *
 * \param remoteSecCoreId  [in] Remote core that has sent the message
 * \param localClientId [in] Local client ID to which the message is sent
 * \param remoteClientId [in] remote client Id from which this message has been sent
 * \param msgValue      [in] pointer to the buffer with SIPC_MSG_SIZE elements
 * \param args          [in] Argument pointer passed by user when \ref SIPC_registerClient is called
 */

typedef void (*SIPC_FxnCallback)(uint8_t remoteSecCoreId, uint8_t localClientId, uint8_t remoteClientId ,uint8_t *msgValue, void *args);


/**
 * @brief Parameters used by @ref SIPC_init
 *
 * Recommend to call @ref SIPC_Params_init before setting values to this structure
 *
 * @note This structure and call to @ref SIPC_init would be generated by SysConfig.
 */
typedef struct SIPC_Params_ {

    uint32_t numCores;  /**< Number of remote cores participating in IPC, excluding the core on
                         *  which this API is called.
                         */
    uint32_t coreIdList[MAX_SEC_CORES_WITH_HSM]; /**< List of secure cores participating in IPC, excluding the core on
                                           *   which this API is called.
                                           *
                                           *   See \ref CSL_CoreID for valid values for this field. */

    uint32_t ipcQueue_length ; /** < Number of elements in IpcQueue */
    uint32_t ipcQueue_eleSize_inBytes ; /** < size of each element in words */
    uint32_t ipcQueue_totalSize_inBytes ; /** < Total size of IpcQueue */

    uintptr_t tx_SipcQueues[MAX_SEC_CORES_WITH_HSM]; /**< List of pointer to the queue locaton which is indexed
                                                      * with secure core index.*/
    uintptr_t rx_SipcQueues[MAX_SEC_CORES_WITH_HSM]; /** < to specify the queues thorugh which current core is going to
                                                    * read the message */
    /* The size of SecureMaster_CoreId array is 2 so the coreId value written on INDEX = 0 will represent the
     * Secure Master 1's CORE ID and so on */
    uint32_t secHostCoreId[MAX_SEC_CORES_WITH_HSM - 1];
    /* This field is for soc extension if user wants to configure different interrupt for SIPC communication*/
    /* for am263x it is fixed to zero */
    uint8_t interruptConfig_Num ;

} SIPC_Params;

/**
 * @brief flags to pass with the SIPC_sendMsg Api which indicates the flow
 * of execution when the write FIFO is full */
typedef enum SIPC_fifoFlags_
{
    ABORT_ON_FIFO_FULL,
    WAIT_IF_FIFO_FULL,

}SIPC_fifoFlags;

/**
 *  @brief Set default value to @ref SIPC_Params
 *
 *  @param  params  [out] Default initialized structure
 */
void SIPC_Params_init(SIPC_Params *params);

/**
 * @brief Initialize Secure IPC notify module
 *
 * This API will initialize the HW used for IPC including registering
 * interrupts for receiving messages.
 *
 * @param params [in] Initializaion parameters
 */
int32_t SIPC_init(SIPC_Params *params);

/**
 * @brief De-initialize secure IPC Notify module
 *
 * This API will de-initialize the HW used for IPC including un-registering
 * interrupts for receiving messages.
 */
void  SIPC_deInit();

/**
 * @brief Send message to a specific remote core and specific client ID on that remote core
 *
 * @note To reduce latency, error checks are avoided in this API.
 * Users need to make sure the client ID value is < @ref SIPC_CLIENT_ID_MAX
 * and message value is < @ref SIPC_CLIENT_ID_MAX
 *
 * @note This API can be called from within ISRs and is also thread-safe.
 * Internally this API disables interrupts for a short while to make the API ISR and thread safe.
 *
 * @note One cannot send messages to self,
 *       i.e remoteCoreId, cannot be same as core ID of the CPU that called this API.
 *
 * @param remoteSecCoreId   [in] Remote core to sent message to, see
 * @param remoteClientId [in] Remote core client ID to send message to
 * @param localClientId   [in] self core client ID from which the meessage is being sent
 * @param msgValue       [in] Message value to send, MUST be < IPC_NOTIFY_MSG_VALUE_MAX
 * @param waitForFifoNotFull [in] 1: wait for message to be inserted into HW or SW FIFO
 *                           [in] 0: if FIFO is full, dont send message and return with error.
 *
 * @return SystemP_SUCCESS, message sent successfully
 * @return SystemP_FAILURE, message could not be sent since HW or SW FIFO for holding the message is full.
 */
int32_t SIPC_sendMsg(uint8_t remoteSecCoreId, uint8_t remoteClientId, uint8_t localClientId ,uint8_t* msgValue, SIPC_fifoFlags waitForFifoNotFull);

/**
 * @brief Register a callback to handle messages received from a specific remote core and for a specific local client ID
 *
 * @param localClientId [in] Client ID to which the message has been sent
 * @param msgCallback [in] Callback to invoke, if callback is already registered, error will be returned.
 * @param args [in] User arguments, that are passed back to user when the callback is invoked
 *
 * @return SystemP_SUCCESS, callback registered sucessfully
 * @return SystemP_FAILURE, callback registration failed, either remoteCoreId or localClientId is invalid or callback already registered.
 */
int32_t SIPC_registerClient(uint8_t localClientId, SIPC_FxnCallback msgCallback, void *args);


/**
 * @brief Un-register a previously registered callback
 *
 * @param localClientId [in] Client ID to which the message has been sent
 *
 * @return SystemP_SUCCESS, callback un-registered sucessfully
 * @return SystemP_FAILURE, callback un-registration failed, either remoteCoreId or localClientId is invalid
 */
int32_t SIPC_unregisterClient(uint16_t localClientId);

/**
 * @brief Return current core ID
 *
 * @return Core ID, see @ref SIPC_coreId for valid values.
 */
uint32_t SIPC_getSelfCoreId();

/**
 * @brief Return current core sec master ID
 *
 * @return Core ID, see @ref SIPC_SecCoreId for valid values.
 */
uint32_t SIPC_getSelfSecMasterId();

/**
 * @brief Check if a core is enabled for SIPC
 *
 * @param coreId [in] Core ID, see @ref SIPC_coreId for valid values.
 *
 * @return 1: core is enabled for SIPC, 0: core is not enabled for SIPC
 */
uint32_t SIPC_isCoreEnabled(uint32_t coreId);


/** @} */

#ifdef __cplusplus
}
#endif

#endif /* IPC_NOTIFY_H_ */

