/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_DATA_SLICE_2_MACROS_H_
#define REG_LPDDR4_DATA_SLICE_2_MACROS_H_

#define LPDDR4__DENALI_PHY_512_READ_MASK                             0x07FF7F07U
#define LPDDR4__DENALI_PHY_512_WRITE_MASK                            0x07FF7F07U
#define LPDDR4__DENALI_PHY_512__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_2_MASK  0x00000007U
#define LPDDR4__DENALI_PHY_512__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_512__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_2_WIDTH          3U
#define LPDDR4__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_2__REG DENALI_PHY_512
#define LPDDR4__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_2__FLD LPDDR4__DENALI_PHY_512__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_2

#define LPDDR4__DENALI_PHY_512__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_2_MASK 0x00007F00U
#define LPDDR4__DENALI_PHY_512__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_2_SHIFT        8U
#define LPDDR4__DENALI_PHY_512__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_2_WIDTH        7U
#define LPDDR4__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_2__REG DENALI_PHY_512
#define LPDDR4__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_2__FLD LPDDR4__DENALI_PHY_512__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_2

#define LPDDR4__DENALI_PHY_512__PHY_CLK_WR_BYPASS_SLAVE_DELAY_2_MASK 0x07FF0000U
#define LPDDR4__DENALI_PHY_512__PHY_CLK_WR_BYPASS_SLAVE_DELAY_2_SHIFT        16U
#define LPDDR4__DENALI_PHY_512__PHY_CLK_WR_BYPASS_SLAVE_DELAY_2_WIDTH        11U
#define LPDDR4__PHY_CLK_WR_BYPASS_SLAVE_DELAY_2__REG DENALI_PHY_512
#define LPDDR4__PHY_CLK_WR_BYPASS_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_512__PHY_CLK_WR_BYPASS_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_513_READ_MASK                             0x0703FF0FU
#define LPDDR4__DENALI_PHY_513_WRITE_MASK                            0x0703FF0FU
#define LPDDR4__DENALI_PHY_513__PHY_IO_PAD_DELAY_TIMING_BYPASS_2_MASK 0x0000000FU
#define LPDDR4__DENALI_PHY_513__PHY_IO_PAD_DELAY_TIMING_BYPASS_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_513__PHY_IO_PAD_DELAY_TIMING_BYPASS_2_WIDTH        4U
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_BYPASS_2__REG DENALI_PHY_513
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_BYPASS_2__FLD LPDDR4__DENALI_PHY_513__PHY_IO_PAD_DELAY_TIMING_BYPASS_2

#define LPDDR4__DENALI_PHY_513__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_2_MASK 0x0003FF00U
#define LPDDR4__DENALI_PHY_513__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_2_SHIFT      8U
#define LPDDR4__DENALI_PHY_513__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_2_WIDTH     10U
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_2__REG DENALI_PHY_513
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_2__FLD LPDDR4__DENALI_PHY_513__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_2

#define LPDDR4__DENALI_PHY_513__PHY_WRITE_PATH_LAT_ADD_BYPASS_2_MASK 0x07000000U
#define LPDDR4__DENALI_PHY_513__PHY_WRITE_PATH_LAT_ADD_BYPASS_2_SHIFT        24U
#define LPDDR4__DENALI_PHY_513__PHY_WRITE_PATH_LAT_ADD_BYPASS_2_WIDTH         3U
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_BYPASS_2__REG DENALI_PHY_513
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_BYPASS_2__FLD LPDDR4__DENALI_PHY_513__PHY_WRITE_PATH_LAT_ADD_BYPASS_2

#define LPDDR4__DENALI_PHY_514_READ_MASK                             0x010303FFU
#define LPDDR4__DENALI_PHY_514_WRITE_MASK                            0x010303FFU
#define LPDDR4__DENALI_PHY_514__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_514__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_2_SHIFT     0U
#define LPDDR4__DENALI_PHY_514__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_2_WIDTH    10U
#define LPDDR4__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_2__REG DENALI_PHY_514
#define LPDDR4__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_514__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_514__PHY_BYPASS_TWO_CYC_PREAMBLE_2_MASK   0x00030000U
#define LPDDR4__DENALI_PHY_514__PHY_BYPASS_TWO_CYC_PREAMBLE_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_514__PHY_BYPASS_TWO_CYC_PREAMBLE_2_WIDTH           2U
#define LPDDR4__PHY_BYPASS_TWO_CYC_PREAMBLE_2__REG DENALI_PHY_514
#define LPDDR4__PHY_BYPASS_TWO_CYC_PREAMBLE_2__FLD LPDDR4__DENALI_PHY_514__PHY_BYPASS_TWO_CYC_PREAMBLE_2

#define LPDDR4__DENALI_PHY_514__PHY_CLK_BYPASS_OVERRIDE_2_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_514__PHY_CLK_BYPASS_OVERRIDE_2_SHIFT              24U
#define LPDDR4__DENALI_PHY_514__PHY_CLK_BYPASS_OVERRIDE_2_WIDTH               1U
#define LPDDR4__DENALI_PHY_514__PHY_CLK_BYPASS_OVERRIDE_2_WOCLR               0U
#define LPDDR4__DENALI_PHY_514__PHY_CLK_BYPASS_OVERRIDE_2_WOSET               0U
#define LPDDR4__PHY_CLK_BYPASS_OVERRIDE_2__REG DENALI_PHY_514
#define LPDDR4__PHY_CLK_BYPASS_OVERRIDE_2__FLD LPDDR4__DENALI_PHY_514__PHY_CLK_BYPASS_OVERRIDE_2

#define LPDDR4__DENALI_PHY_515_READ_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_515_WRITE_MASK                            0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ0_SHIFT_2_MASK            0x0000003FU
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ0_SHIFT_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ0_SHIFT_2_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ0_SHIFT_2__REG DENALI_PHY_515
#define LPDDR4__PHY_SW_WRDQ0_SHIFT_2__FLD LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ0_SHIFT_2

#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ1_SHIFT_2_MASK            0x00003F00U
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ1_SHIFT_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ1_SHIFT_2_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ1_SHIFT_2__REG DENALI_PHY_515
#define LPDDR4__PHY_SW_WRDQ1_SHIFT_2__FLD LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ1_SHIFT_2

#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ2_SHIFT_2_MASK            0x003F0000U
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ2_SHIFT_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ2_SHIFT_2_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ2_SHIFT_2__REG DENALI_PHY_515
#define LPDDR4__PHY_SW_WRDQ2_SHIFT_2__FLD LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ2_SHIFT_2

#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ3_SHIFT_2_MASK            0x3F000000U
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ3_SHIFT_2_SHIFT                   24U
#define LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ3_SHIFT_2_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ3_SHIFT_2__REG DENALI_PHY_515
#define LPDDR4__PHY_SW_WRDQ3_SHIFT_2__FLD LPDDR4__DENALI_PHY_515__PHY_SW_WRDQ3_SHIFT_2

#define LPDDR4__DENALI_PHY_516_READ_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_516_WRITE_MASK                            0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ4_SHIFT_2_MASK            0x0000003FU
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ4_SHIFT_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ4_SHIFT_2_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ4_SHIFT_2__REG DENALI_PHY_516
#define LPDDR4__PHY_SW_WRDQ4_SHIFT_2__FLD LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ4_SHIFT_2

#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ5_SHIFT_2_MASK            0x00003F00U
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ5_SHIFT_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ5_SHIFT_2_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ5_SHIFT_2__REG DENALI_PHY_516
#define LPDDR4__PHY_SW_WRDQ5_SHIFT_2__FLD LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ5_SHIFT_2

#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ6_SHIFT_2_MASK            0x003F0000U
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ6_SHIFT_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ6_SHIFT_2_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ6_SHIFT_2__REG DENALI_PHY_516
#define LPDDR4__PHY_SW_WRDQ6_SHIFT_2__FLD LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ6_SHIFT_2

#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ7_SHIFT_2_MASK            0x3F000000U
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ7_SHIFT_2_SHIFT                   24U
#define LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ7_SHIFT_2_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ7_SHIFT_2__REG DENALI_PHY_516
#define LPDDR4__PHY_SW_WRDQ7_SHIFT_2__FLD LPDDR4__DENALI_PHY_516__PHY_SW_WRDQ7_SHIFT_2

#define LPDDR4__DENALI_PHY_517_READ_MASK                             0x01030F3FU
#define LPDDR4__DENALI_PHY_517_WRITE_MASK                            0x01030F3FU
#define LPDDR4__DENALI_PHY_517__PHY_SW_WRDM_SHIFT_2_MASK             0x0000003FU
#define LPDDR4__DENALI_PHY_517__PHY_SW_WRDM_SHIFT_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_517__PHY_SW_WRDM_SHIFT_2_WIDTH                     6U
#define LPDDR4__PHY_SW_WRDM_SHIFT_2__REG DENALI_PHY_517
#define LPDDR4__PHY_SW_WRDM_SHIFT_2__FLD LPDDR4__DENALI_PHY_517__PHY_SW_WRDM_SHIFT_2

#define LPDDR4__DENALI_PHY_517__PHY_SW_WRDQS_SHIFT_2_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_517__PHY_SW_WRDQS_SHIFT_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_517__PHY_SW_WRDQS_SHIFT_2_WIDTH                    4U
#define LPDDR4__PHY_SW_WRDQS_SHIFT_2__REG DENALI_PHY_517
#define LPDDR4__PHY_SW_WRDQS_SHIFT_2__FLD LPDDR4__DENALI_PHY_517__PHY_SW_WRDQS_SHIFT_2

#define LPDDR4__DENALI_PHY_517__PHY_PER_RANK_CS_MAP_2_MASK           0x00030000U
#define LPDDR4__DENALI_PHY_517__PHY_PER_RANK_CS_MAP_2_SHIFT                  16U
#define LPDDR4__DENALI_PHY_517__PHY_PER_RANK_CS_MAP_2_WIDTH                   2U
#define LPDDR4__PHY_PER_RANK_CS_MAP_2__REG DENALI_PHY_517
#define LPDDR4__PHY_PER_RANK_CS_MAP_2__FLD LPDDR4__DENALI_PHY_517__PHY_PER_RANK_CS_MAP_2

#define LPDDR4__DENALI_PHY_517__PHY_PER_CS_TRAINING_MULTICAST_EN_2_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_517__PHY_PER_CS_TRAINING_MULTICAST_EN_2_SHIFT     24U
#define LPDDR4__DENALI_PHY_517__PHY_PER_CS_TRAINING_MULTICAST_EN_2_WIDTH      1U
#define LPDDR4__DENALI_PHY_517__PHY_PER_CS_TRAINING_MULTICAST_EN_2_WOCLR      0U
#define LPDDR4__DENALI_PHY_517__PHY_PER_CS_TRAINING_MULTICAST_EN_2_WOSET      0U
#define LPDDR4__PHY_PER_CS_TRAINING_MULTICAST_EN_2__REG DENALI_PHY_517
#define LPDDR4__PHY_PER_CS_TRAINING_MULTICAST_EN_2__FLD LPDDR4__DENALI_PHY_517__PHY_PER_CS_TRAINING_MULTICAST_EN_2

#define LPDDR4__DENALI_PHY_518_READ_MASK                             0x1F1F0301U
#define LPDDR4__DENALI_PHY_518_WRITE_MASK                            0x1F1F0301U
#define LPDDR4__DENALI_PHY_518__PHY_PER_CS_TRAINING_INDEX_2_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_518__PHY_PER_CS_TRAINING_INDEX_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_518__PHY_PER_CS_TRAINING_INDEX_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_518__PHY_PER_CS_TRAINING_INDEX_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_518__PHY_PER_CS_TRAINING_INDEX_2_WOSET             0U
#define LPDDR4__PHY_PER_CS_TRAINING_INDEX_2__REG DENALI_PHY_518
#define LPDDR4__PHY_PER_CS_TRAINING_INDEX_2__FLD LPDDR4__DENALI_PHY_518__PHY_PER_CS_TRAINING_INDEX_2

#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_2_MASK 0x00000300U
#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_2_SHIFT         8U
#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_2_WIDTH         2U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_2__REG DENALI_PHY_518
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_2__FLD LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_2

#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_DLY_2_MASK    0x001F0000U
#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_DLY_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_DLY_2_WIDTH            5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_DLY_2__REG DENALI_PHY_518
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_DLY_2__FLD LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_DLY_2

#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_2_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_2_SHIFT      24U
#define LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_2_WIDTH       5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_2__REG DENALI_PHY_518
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_2__FLD LPDDR4__DENALI_PHY_518__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_2

#define LPDDR4__DENALI_PHY_519_READ_MASK                             0x1F030F0FU
#define LPDDR4__DENALI_PHY_519_WRITE_MASK                            0x1F030F0FU
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RPTR_UPDATE_2_MASK      0x0000000FU
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RPTR_UPDATE_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RPTR_UPDATE_2_WIDTH              4U
#define LPDDR4__PHY_LP4_BOOT_RPTR_UPDATE_2__REG DENALI_PHY_519
#define LPDDR4__PHY_LP4_BOOT_RPTR_UPDATE_2__FLD LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RPTR_UPDATE_2

#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_2_MASK 0x00000F00U
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_2_SHIFT     8U
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_2_WIDTH     4U
#define LPDDR4__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_2__REG DENALI_PHY_519
#define LPDDR4__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_2__FLD LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_2

#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_2_MASK 0x00030000U
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_2_SHIFT     16U
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_2_WIDTH      2U
#define LPDDR4__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_2__REG DENALI_PHY_519
#define LPDDR4__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_2__FLD LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_2

#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_2_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_2_SHIFT        24U
#define LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_2_WIDTH         5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_2__REG DENALI_PHY_519
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_2__FLD LPDDR4__DENALI_PHY_519__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_2

#define LPDDR4__DENALI_PHY_520_READ_MASK                             0x0101FF03U
#define LPDDR4__DENALI_PHY_520_WRITE_MASK                            0x0101FF03U
#define LPDDR4__DENALI_PHY_520__PHY_CTRL_LPBK_EN_2_MASK              0x00000003U
#define LPDDR4__DENALI_PHY_520__PHY_CTRL_LPBK_EN_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_520__PHY_CTRL_LPBK_EN_2_WIDTH                      2U
#define LPDDR4__PHY_CTRL_LPBK_EN_2__REG DENALI_PHY_520
#define LPDDR4__PHY_CTRL_LPBK_EN_2__FLD LPDDR4__DENALI_PHY_520__PHY_CTRL_LPBK_EN_2

#define LPDDR4__DENALI_PHY_520__PHY_LPBK_CONTROL_2_MASK              0x0001FF00U
#define LPDDR4__DENALI_PHY_520__PHY_LPBK_CONTROL_2_SHIFT                      8U
#define LPDDR4__DENALI_PHY_520__PHY_LPBK_CONTROL_2_WIDTH                      9U
#define LPDDR4__PHY_LPBK_CONTROL_2__REG DENALI_PHY_520
#define LPDDR4__PHY_LPBK_CONTROL_2__FLD LPDDR4__DENALI_PHY_520__PHY_LPBK_CONTROL_2

#define LPDDR4__DENALI_PHY_520__PHY_LPBK_DFX_TIMEOUT_EN_2_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_520__PHY_LPBK_DFX_TIMEOUT_EN_2_SHIFT              24U
#define LPDDR4__DENALI_PHY_520__PHY_LPBK_DFX_TIMEOUT_EN_2_WIDTH               1U
#define LPDDR4__DENALI_PHY_520__PHY_LPBK_DFX_TIMEOUT_EN_2_WOCLR               0U
#define LPDDR4__DENALI_PHY_520__PHY_LPBK_DFX_TIMEOUT_EN_2_WOSET               0U
#define LPDDR4__PHY_LPBK_DFX_TIMEOUT_EN_2__REG DENALI_PHY_520
#define LPDDR4__PHY_LPBK_DFX_TIMEOUT_EN_2__FLD LPDDR4__DENALI_PHY_520__PHY_LPBK_DFX_TIMEOUT_EN_2

#define LPDDR4__DENALI_PHY_521_READ_MASK                             0x00000001U
#define LPDDR4__DENALI_PHY_521_WRITE_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_521__PHY_GATE_DELAY_COMP_DISABLE_2_MASK   0x00000001U
#define LPDDR4__DENALI_PHY_521__PHY_GATE_DELAY_COMP_DISABLE_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_521__PHY_GATE_DELAY_COMP_DISABLE_2_WIDTH           1U
#define LPDDR4__DENALI_PHY_521__PHY_GATE_DELAY_COMP_DISABLE_2_WOCLR           0U
#define LPDDR4__DENALI_PHY_521__PHY_GATE_DELAY_COMP_DISABLE_2_WOSET           0U
#define LPDDR4__PHY_GATE_DELAY_COMP_DISABLE_2__REG DENALI_PHY_521
#define LPDDR4__PHY_GATE_DELAY_COMP_DISABLE_2__FLD LPDDR4__DENALI_PHY_521__PHY_GATE_DELAY_COMP_DISABLE_2

#define LPDDR4__DENALI_PHY_522_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_522_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_522__PHY_AUTO_TIMING_MARGIN_CONTROL_2_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_522__PHY_AUTO_TIMING_MARGIN_CONTROL_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_522__PHY_AUTO_TIMING_MARGIN_CONTROL_2_WIDTH       32U
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_CONTROL_2__REG DENALI_PHY_522
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_CONTROL_2__FLD LPDDR4__DENALI_PHY_522__PHY_AUTO_TIMING_MARGIN_CONTROL_2

#define LPDDR4__DENALI_PHY_523_READ_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_523_WRITE_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_523__PHY_AUTO_TIMING_MARGIN_OBS_2_MASK    0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_523__PHY_AUTO_TIMING_MARGIN_OBS_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_523__PHY_AUTO_TIMING_MARGIN_OBS_2_WIDTH           28U
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_OBS_2__REG DENALI_PHY_523
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_OBS_2__FLD LPDDR4__DENALI_PHY_523__PHY_AUTO_TIMING_MARGIN_OBS_2

#define LPDDR4__DENALI_PHY_524_READ_MASK                             0x7F0101FFU
#define LPDDR4__DENALI_PHY_524_WRITE_MASK                            0x7F0101FFU
#define LPDDR4__DENALI_PHY_524__PHY_DQ_IDLE_2_MASK                   0x000001FFU
#define LPDDR4__DENALI_PHY_524__PHY_DQ_IDLE_2_SHIFT                           0U
#define LPDDR4__DENALI_PHY_524__PHY_DQ_IDLE_2_WIDTH                           9U
#define LPDDR4__PHY_DQ_IDLE_2__REG DENALI_PHY_524
#define LPDDR4__PHY_DQ_IDLE_2__FLD LPDDR4__DENALI_PHY_524__PHY_DQ_IDLE_2

#define LPDDR4__DENALI_PHY_524__PHY_PDA_MODE_EN_2_MASK               0x00010000U
#define LPDDR4__DENALI_PHY_524__PHY_PDA_MODE_EN_2_SHIFT                      16U
#define LPDDR4__DENALI_PHY_524__PHY_PDA_MODE_EN_2_WIDTH                       1U
#define LPDDR4__DENALI_PHY_524__PHY_PDA_MODE_EN_2_WOCLR                       0U
#define LPDDR4__DENALI_PHY_524__PHY_PDA_MODE_EN_2_WOSET                       0U
#define LPDDR4__PHY_PDA_MODE_EN_2__REG DENALI_PHY_524
#define LPDDR4__PHY_PDA_MODE_EN_2__FLD LPDDR4__DENALI_PHY_524__PHY_PDA_MODE_EN_2

#define LPDDR4__DENALI_PHY_524__PHY_PRBS_PATTERN_START_2_MASK        0x7F000000U
#define LPDDR4__DENALI_PHY_524__PHY_PRBS_PATTERN_START_2_SHIFT               24U
#define LPDDR4__DENALI_PHY_524__PHY_PRBS_PATTERN_START_2_WIDTH                7U
#define LPDDR4__PHY_PRBS_PATTERN_START_2__REG DENALI_PHY_524
#define LPDDR4__PHY_PRBS_PATTERN_START_2__FLD LPDDR4__DENALI_PHY_524__PHY_PRBS_PATTERN_START_2

#define LPDDR4__DENALI_PHY_525_READ_MASK                             0x010101FFU
#define LPDDR4__DENALI_PHY_525_WRITE_MASK                            0x010101FFU
#define LPDDR4__DENALI_PHY_525__PHY_PRBS_PATTERN_MASK_2_MASK         0x000001FFU
#define LPDDR4__DENALI_PHY_525__PHY_PRBS_PATTERN_MASK_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_525__PHY_PRBS_PATTERN_MASK_2_WIDTH                 9U
#define LPDDR4__PHY_PRBS_PATTERN_MASK_2__REG DENALI_PHY_525
#define LPDDR4__PHY_PRBS_PATTERN_MASK_2__FLD LPDDR4__DENALI_PHY_525__PHY_PRBS_PATTERN_MASK_2

#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_ENABLE_2_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_ENABLE_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_ENABLE_2_WIDTH           1U
#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_ENABLE_2_WOCLR           0U
#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_ENABLE_2_WOSET           0U
#define LPDDR4__PHY_RDLVL_MULTI_PATT_ENABLE_2__REG DENALI_PHY_525
#define LPDDR4__PHY_RDLVL_MULTI_PATT_ENABLE_2__FLD LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_ENABLE_2

#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_RST_DISABLE_2_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_RST_DISABLE_2_SHIFT     24U
#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_RST_DISABLE_2_WIDTH      1U
#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_RST_DISABLE_2_WOCLR      0U
#define LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_RST_DISABLE_2_WOSET      0U
#define LPDDR4__PHY_RDLVL_MULTI_PATT_RST_DISABLE_2__REG DENALI_PHY_525
#define LPDDR4__PHY_RDLVL_MULTI_PATT_RST_DISABLE_2__FLD LPDDR4__DENALI_PHY_525__PHY_RDLVL_MULTI_PATT_RST_DISABLE_2

#define LPDDR4__DENALI_PHY_526_READ_MASK                             0x03FF7F3FU
#define LPDDR4__DENALI_PHY_526_WRITE_MASK                            0x03FF7F3FU
#define LPDDR4__DENALI_PHY_526__PHY_VREF_INITIAL_STEPSIZE_2_MASK     0x0000003FU
#define LPDDR4__DENALI_PHY_526__PHY_VREF_INITIAL_STEPSIZE_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_526__PHY_VREF_INITIAL_STEPSIZE_2_WIDTH             6U
#define LPDDR4__PHY_VREF_INITIAL_STEPSIZE_2__REG DENALI_PHY_526
#define LPDDR4__PHY_VREF_INITIAL_STEPSIZE_2__FLD LPDDR4__DENALI_PHY_526__PHY_VREF_INITIAL_STEPSIZE_2

#define LPDDR4__DENALI_PHY_526__PHY_VREF_TRAIN_OBS_2_MASK            0x00007F00U
#define LPDDR4__DENALI_PHY_526__PHY_VREF_TRAIN_OBS_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_526__PHY_VREF_TRAIN_OBS_2_WIDTH                    7U
#define LPDDR4__PHY_VREF_TRAIN_OBS_2__REG DENALI_PHY_526
#define LPDDR4__PHY_VREF_TRAIN_OBS_2__FLD LPDDR4__DENALI_PHY_526__PHY_VREF_TRAIN_OBS_2

#define LPDDR4__DENALI_PHY_526__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_526__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_2_SHIFT      16U
#define LPDDR4__DENALI_PHY_526__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_2_WIDTH      10U
#define LPDDR4__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_2__REG DENALI_PHY_526
#define LPDDR4__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_526__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_527_READ_MASK                             0x01FF000FU
#define LPDDR4__DENALI_PHY_527_WRITE_MASK                            0x01FF000FU
#define LPDDR4__DENALI_PHY_527__PHY_GATE_ERROR_DELAY_SELECT_2_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_527__PHY_GATE_ERROR_DELAY_SELECT_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_527__PHY_GATE_ERROR_DELAY_SELECT_2_WIDTH           4U
#define LPDDR4__PHY_GATE_ERROR_DELAY_SELECT_2__REG DENALI_PHY_527
#define LPDDR4__PHY_GATE_ERROR_DELAY_SELECT_2__FLD LPDDR4__DENALI_PHY_527__PHY_GATE_ERROR_DELAY_SELECT_2

#define LPDDR4__DENALI_PHY_527__SC_PHY_SNAP_OBS_REGS_2_MASK          0x00000100U
#define LPDDR4__DENALI_PHY_527__SC_PHY_SNAP_OBS_REGS_2_SHIFT                  8U
#define LPDDR4__DENALI_PHY_527__SC_PHY_SNAP_OBS_REGS_2_WIDTH                  1U
#define LPDDR4__DENALI_PHY_527__SC_PHY_SNAP_OBS_REGS_2_WOCLR                  0U
#define LPDDR4__DENALI_PHY_527__SC_PHY_SNAP_OBS_REGS_2_WOSET                  0U
#define LPDDR4__SC_PHY_SNAP_OBS_REGS_2__REG DENALI_PHY_527
#define LPDDR4__SC_PHY_SNAP_OBS_REGS_2__FLD LPDDR4__DENALI_PHY_527__SC_PHY_SNAP_OBS_REGS_2

#define LPDDR4__DENALI_PHY_527__PHY_GATE_SMPL1_SLAVE_DELAY_2_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_527__PHY_GATE_SMPL1_SLAVE_DELAY_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_527__PHY_GATE_SMPL1_SLAVE_DELAY_2_WIDTH            9U
#define LPDDR4__PHY_GATE_SMPL1_SLAVE_DELAY_2__REG DENALI_PHY_527
#define LPDDR4__PHY_GATE_SMPL1_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_527__PHY_GATE_SMPL1_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_528_READ_MASK                             0x01FF0701U
#define LPDDR4__DENALI_PHY_528_WRITE_MASK                            0x01FF0701U
#define LPDDR4__DENALI_PHY_528__PHY_LPDDR_2_MASK                     0x00000001U
#define LPDDR4__DENALI_PHY_528__PHY_LPDDR_2_SHIFT                             0U
#define LPDDR4__DENALI_PHY_528__PHY_LPDDR_2_WIDTH                             1U
#define LPDDR4__DENALI_PHY_528__PHY_LPDDR_2_WOCLR                             0U
#define LPDDR4__DENALI_PHY_528__PHY_LPDDR_2_WOSET                             0U
#define LPDDR4__PHY_LPDDR_2__REG DENALI_PHY_528
#define LPDDR4__PHY_LPDDR_2__FLD LPDDR4__DENALI_PHY_528__PHY_LPDDR_2

#define LPDDR4__DENALI_PHY_528__PHY_MEM_CLASS_2_MASK                 0x00000700U
#define LPDDR4__DENALI_PHY_528__PHY_MEM_CLASS_2_SHIFT                         8U
#define LPDDR4__DENALI_PHY_528__PHY_MEM_CLASS_2_WIDTH                         3U
#define LPDDR4__PHY_MEM_CLASS_2__REG DENALI_PHY_528
#define LPDDR4__PHY_MEM_CLASS_2__FLD LPDDR4__DENALI_PHY_528__PHY_MEM_CLASS_2

#define LPDDR4__DENALI_PHY_528__PHY_GATE_SMPL2_SLAVE_DELAY_2_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_528__PHY_GATE_SMPL2_SLAVE_DELAY_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_528__PHY_GATE_SMPL2_SLAVE_DELAY_2_WIDTH            9U
#define LPDDR4__PHY_GATE_SMPL2_SLAVE_DELAY_2__REG DENALI_PHY_528
#define LPDDR4__PHY_GATE_SMPL2_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_528__PHY_GATE_SMPL2_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_529_READ_MASK                             0x00000003U
#define LPDDR4__DENALI_PHY_529_WRITE_MASK                            0x00000003U
#define LPDDR4__DENALI_PHY_529__ON_FLY_GATE_ADJUST_EN_2_MASK         0x00000003U
#define LPDDR4__DENALI_PHY_529__ON_FLY_GATE_ADJUST_EN_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_529__ON_FLY_GATE_ADJUST_EN_2_WIDTH                 2U
#define LPDDR4__ON_FLY_GATE_ADJUST_EN_2__REG DENALI_PHY_529
#define LPDDR4__ON_FLY_GATE_ADJUST_EN_2__FLD LPDDR4__DENALI_PHY_529__ON_FLY_GATE_ADJUST_EN_2

#define LPDDR4__DENALI_PHY_530_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_530_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_530__PHY_GATE_TRACKING_OBS_2_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_530__PHY_GATE_TRACKING_OBS_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_530__PHY_GATE_TRACKING_OBS_2_WIDTH                32U
#define LPDDR4__PHY_GATE_TRACKING_OBS_2__REG DENALI_PHY_530
#define LPDDR4__PHY_GATE_TRACKING_OBS_2__FLD LPDDR4__DENALI_PHY_530__PHY_GATE_TRACKING_OBS_2

#define LPDDR4__DENALI_PHY_531_READ_MASK                             0x00000301U
#define LPDDR4__DENALI_PHY_531_WRITE_MASK                            0x00000301U
#define LPDDR4__DENALI_PHY_531__PHY_DFI40_POLARITY_2_MASK            0x00000001U
#define LPDDR4__DENALI_PHY_531__PHY_DFI40_POLARITY_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_531__PHY_DFI40_POLARITY_2_WIDTH                    1U
#define LPDDR4__DENALI_PHY_531__PHY_DFI40_POLARITY_2_WOCLR                    0U
#define LPDDR4__DENALI_PHY_531__PHY_DFI40_POLARITY_2_WOSET                    0U
#define LPDDR4__PHY_DFI40_POLARITY_2__REG DENALI_PHY_531
#define LPDDR4__PHY_DFI40_POLARITY_2__FLD LPDDR4__DENALI_PHY_531__PHY_DFI40_POLARITY_2

#define LPDDR4__DENALI_PHY_531__PHY_LP4_PST_AMBLE_2_MASK             0x00000300U
#define LPDDR4__DENALI_PHY_531__PHY_LP4_PST_AMBLE_2_SHIFT                     8U
#define LPDDR4__DENALI_PHY_531__PHY_LP4_PST_AMBLE_2_WIDTH                     2U
#define LPDDR4__PHY_LP4_PST_AMBLE_2__REG DENALI_PHY_531
#define LPDDR4__PHY_LP4_PST_AMBLE_2__FLD LPDDR4__DENALI_PHY_531__PHY_LP4_PST_AMBLE_2

#define LPDDR4__DENALI_PHY_532_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_532_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_532__PHY_RDLVL_PATT8_2_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_532__PHY_RDLVL_PATT8_2_SHIFT                       0U
#define LPDDR4__DENALI_PHY_532__PHY_RDLVL_PATT8_2_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT8_2__REG DENALI_PHY_532
#define LPDDR4__PHY_RDLVL_PATT8_2__FLD LPDDR4__DENALI_PHY_532__PHY_RDLVL_PATT8_2

#define LPDDR4__DENALI_PHY_533_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_533_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_533__PHY_RDLVL_PATT9_2_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_533__PHY_RDLVL_PATT9_2_SHIFT                       0U
#define LPDDR4__DENALI_PHY_533__PHY_RDLVL_PATT9_2_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT9_2__REG DENALI_PHY_533
#define LPDDR4__PHY_RDLVL_PATT9_2__FLD LPDDR4__DENALI_PHY_533__PHY_RDLVL_PATT9_2

#define LPDDR4__DENALI_PHY_534_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_534_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_534__PHY_RDLVL_PATT10_2_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_534__PHY_RDLVL_PATT10_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_534__PHY_RDLVL_PATT10_2_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT10_2__REG DENALI_PHY_534
#define LPDDR4__PHY_RDLVL_PATT10_2__FLD LPDDR4__DENALI_PHY_534__PHY_RDLVL_PATT10_2

#define LPDDR4__DENALI_PHY_535_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_535_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_535__PHY_RDLVL_PATT11_2_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_535__PHY_RDLVL_PATT11_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_535__PHY_RDLVL_PATT11_2_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT11_2__REG DENALI_PHY_535
#define LPDDR4__PHY_RDLVL_PATT11_2__FLD LPDDR4__DENALI_PHY_535__PHY_RDLVL_PATT11_2

#define LPDDR4__DENALI_PHY_536_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_536_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_536__PHY_RDLVL_PATT12_2_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_536__PHY_RDLVL_PATT12_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_536__PHY_RDLVL_PATT12_2_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT12_2__REG DENALI_PHY_536
#define LPDDR4__PHY_RDLVL_PATT12_2__FLD LPDDR4__DENALI_PHY_536__PHY_RDLVL_PATT12_2

#define LPDDR4__DENALI_PHY_537_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_537_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_537__PHY_RDLVL_PATT13_2_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_537__PHY_RDLVL_PATT13_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_537__PHY_RDLVL_PATT13_2_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT13_2__REG DENALI_PHY_537
#define LPDDR4__PHY_RDLVL_PATT13_2__FLD LPDDR4__DENALI_PHY_537__PHY_RDLVL_PATT13_2

#define LPDDR4__DENALI_PHY_538_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_538_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_538__PHY_RDLVL_PATT14_2_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_538__PHY_RDLVL_PATT14_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_538__PHY_RDLVL_PATT14_2_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT14_2__REG DENALI_PHY_538
#define LPDDR4__PHY_RDLVL_PATT14_2__FLD LPDDR4__DENALI_PHY_538__PHY_RDLVL_PATT14_2

#define LPDDR4__DENALI_PHY_539_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_539_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_539__PHY_RDLVL_PATT15_2_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_539__PHY_RDLVL_PATT15_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_539__PHY_RDLVL_PATT15_2_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT15_2__REG DENALI_PHY_539
#define LPDDR4__PHY_RDLVL_PATT15_2__FLD LPDDR4__DENALI_PHY_539__PHY_RDLVL_PATT15_2

#define LPDDR4__DENALI_PHY_540_READ_MASK                             0x070F0107U
#define LPDDR4__DENALI_PHY_540_WRITE_MASK                            0x070F0107U
#define LPDDR4__DENALI_PHY_540__PHY_SLAVE_LOOP_CNT_UPDATE_2_MASK     0x00000007U
#define LPDDR4__DENALI_PHY_540__PHY_SLAVE_LOOP_CNT_UPDATE_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_540__PHY_SLAVE_LOOP_CNT_UPDATE_2_WIDTH             3U
#define LPDDR4__PHY_SLAVE_LOOP_CNT_UPDATE_2__REG DENALI_PHY_540
#define LPDDR4__PHY_SLAVE_LOOP_CNT_UPDATE_2__FLD LPDDR4__DENALI_PHY_540__PHY_SLAVE_LOOP_CNT_UPDATE_2

#define LPDDR4__DENALI_PHY_540__PHY_SW_FIFO_PTR_RST_DISABLE_2_MASK   0x00000100U
#define LPDDR4__DENALI_PHY_540__PHY_SW_FIFO_PTR_RST_DISABLE_2_SHIFT           8U
#define LPDDR4__DENALI_PHY_540__PHY_SW_FIFO_PTR_RST_DISABLE_2_WIDTH           1U
#define LPDDR4__DENALI_PHY_540__PHY_SW_FIFO_PTR_RST_DISABLE_2_WOCLR           0U
#define LPDDR4__DENALI_PHY_540__PHY_SW_FIFO_PTR_RST_DISABLE_2_WOSET           0U
#define LPDDR4__PHY_SW_FIFO_PTR_RST_DISABLE_2__REG DENALI_PHY_540
#define LPDDR4__PHY_SW_FIFO_PTR_RST_DISABLE_2__FLD LPDDR4__DENALI_PHY_540__PHY_SW_FIFO_PTR_RST_DISABLE_2

#define LPDDR4__DENALI_PHY_540__PHY_MASTER_DLY_LOCK_OBS_SELECT_2_MASK 0x000F0000U
#define LPDDR4__DENALI_PHY_540__PHY_MASTER_DLY_LOCK_OBS_SELECT_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_540__PHY_MASTER_DLY_LOCK_OBS_SELECT_2_WIDTH        4U
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_SELECT_2__REG DENALI_PHY_540
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_540__PHY_MASTER_DLY_LOCK_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_540__PHY_RDDQ_ENC_OBS_SELECT_2_MASK       0x07000000U
#define LPDDR4__DENALI_PHY_540__PHY_RDDQ_ENC_OBS_SELECT_2_SHIFT              24U
#define LPDDR4__DENALI_PHY_540__PHY_RDDQ_ENC_OBS_SELECT_2_WIDTH               3U
#define LPDDR4__PHY_RDDQ_ENC_OBS_SELECT_2__REG DENALI_PHY_540
#define LPDDR4__PHY_RDDQ_ENC_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_540__PHY_RDDQ_ENC_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_541_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_541_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_541__PHY_RDDQS_DQ_ENC_OBS_SELECT_2_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_541__PHY_RDDQS_DQ_ENC_OBS_SELECT_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_541__PHY_RDDQS_DQ_ENC_OBS_SELECT_2_WIDTH           4U
#define LPDDR4__PHY_RDDQS_DQ_ENC_OBS_SELECT_2__REG DENALI_PHY_541
#define LPDDR4__PHY_RDDQS_DQ_ENC_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_541__PHY_RDDQS_DQ_ENC_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_541__PHY_WR_ENC_OBS_SELECT_2_MASK         0x00000F00U
#define LPDDR4__DENALI_PHY_541__PHY_WR_ENC_OBS_SELECT_2_SHIFT                 8U
#define LPDDR4__DENALI_PHY_541__PHY_WR_ENC_OBS_SELECT_2_WIDTH                 4U
#define LPDDR4__PHY_WR_ENC_OBS_SELECT_2__REG DENALI_PHY_541
#define LPDDR4__PHY_WR_ENC_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_541__PHY_WR_ENC_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_541__PHY_WR_SHIFT_OBS_SELECT_2_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_541__PHY_WR_SHIFT_OBS_SELECT_2_SHIFT              16U
#define LPDDR4__DENALI_PHY_541__PHY_WR_SHIFT_OBS_SELECT_2_WIDTH               4U
#define LPDDR4__PHY_WR_SHIFT_OBS_SELECT_2__REG DENALI_PHY_541
#define LPDDR4__PHY_WR_SHIFT_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_541__PHY_WR_SHIFT_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_541__PHY_FIFO_PTR_OBS_SELECT_2_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_541__PHY_FIFO_PTR_OBS_SELECT_2_SHIFT              24U
#define LPDDR4__DENALI_PHY_541__PHY_FIFO_PTR_OBS_SELECT_2_WIDTH               4U
#define LPDDR4__PHY_FIFO_PTR_OBS_SELECT_2__REG DENALI_PHY_541
#define LPDDR4__PHY_FIFO_PTR_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_541__PHY_FIFO_PTR_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_542_READ_MASK                             0x3F030001U
#define LPDDR4__DENALI_PHY_542_WRITE_MASK                            0x3F030001U
#define LPDDR4__DENALI_PHY_542__PHY_LVL_DEBUG_MODE_2_MASK            0x00000001U
#define LPDDR4__DENALI_PHY_542__PHY_LVL_DEBUG_MODE_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_542__PHY_LVL_DEBUG_MODE_2_WIDTH                    1U
#define LPDDR4__DENALI_PHY_542__PHY_LVL_DEBUG_MODE_2_WOCLR                    0U
#define LPDDR4__DENALI_PHY_542__PHY_LVL_DEBUG_MODE_2_WOSET                    0U
#define LPDDR4__PHY_LVL_DEBUG_MODE_2__REG DENALI_PHY_542
#define LPDDR4__PHY_LVL_DEBUG_MODE_2__FLD LPDDR4__DENALI_PHY_542__PHY_LVL_DEBUG_MODE_2

#define LPDDR4__DENALI_PHY_542__SC_PHY_LVL_DEBUG_CONT_2_MASK         0x00000100U
#define LPDDR4__DENALI_PHY_542__SC_PHY_LVL_DEBUG_CONT_2_SHIFT                 8U
#define LPDDR4__DENALI_PHY_542__SC_PHY_LVL_DEBUG_CONT_2_WIDTH                 1U
#define LPDDR4__DENALI_PHY_542__SC_PHY_LVL_DEBUG_CONT_2_WOCLR                 0U
#define LPDDR4__DENALI_PHY_542__SC_PHY_LVL_DEBUG_CONT_2_WOSET                 0U
#define LPDDR4__SC_PHY_LVL_DEBUG_CONT_2__REG DENALI_PHY_542
#define LPDDR4__SC_PHY_LVL_DEBUG_CONT_2__FLD LPDDR4__DENALI_PHY_542__SC_PHY_LVL_DEBUG_CONT_2

#define LPDDR4__DENALI_PHY_542__PHY_WRLVL_ALGO_2_MASK                0x00030000U
#define LPDDR4__DENALI_PHY_542__PHY_WRLVL_ALGO_2_SHIFT                       16U
#define LPDDR4__DENALI_PHY_542__PHY_WRLVL_ALGO_2_WIDTH                        2U
#define LPDDR4__PHY_WRLVL_ALGO_2__REG DENALI_PHY_542
#define LPDDR4__PHY_WRLVL_ALGO_2__FLD LPDDR4__DENALI_PHY_542__PHY_WRLVL_ALGO_2

#define LPDDR4__DENALI_PHY_542__PHY_WRLVL_CAPTURE_CNT_2_MASK         0x3F000000U
#define LPDDR4__DENALI_PHY_542__PHY_WRLVL_CAPTURE_CNT_2_SHIFT                24U
#define LPDDR4__DENALI_PHY_542__PHY_WRLVL_CAPTURE_CNT_2_WIDTH                 6U
#define LPDDR4__PHY_WRLVL_CAPTURE_CNT_2__REG DENALI_PHY_542
#define LPDDR4__PHY_WRLVL_CAPTURE_CNT_2__FLD LPDDR4__DENALI_PHY_542__PHY_WRLVL_CAPTURE_CNT_2

#define LPDDR4__DENALI_PHY_543_READ_MASK                             0x0F3FFF0FU
#define LPDDR4__DENALI_PHY_543_WRITE_MASK                            0x0F3FFF0FU
#define LPDDR4__DENALI_PHY_543__PHY_WRLVL_UPDT_WAIT_CNT_2_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_543__PHY_WRLVL_UPDT_WAIT_CNT_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_543__PHY_WRLVL_UPDT_WAIT_CNT_2_WIDTH               4U
#define LPDDR4__PHY_WRLVL_UPDT_WAIT_CNT_2__REG DENALI_PHY_543
#define LPDDR4__PHY_WRLVL_UPDT_WAIT_CNT_2__FLD LPDDR4__DENALI_PHY_543__PHY_WRLVL_UPDT_WAIT_CNT_2

#define LPDDR4__DENALI_PHY_543__PHY_DQ_MASK_2_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PHY_543__PHY_DQ_MASK_2_SHIFT                           8U
#define LPDDR4__DENALI_PHY_543__PHY_DQ_MASK_2_WIDTH                           8U
#define LPDDR4__PHY_DQ_MASK_2__REG DENALI_PHY_543
#define LPDDR4__PHY_DQ_MASK_2__FLD LPDDR4__DENALI_PHY_543__PHY_DQ_MASK_2

#define LPDDR4__DENALI_PHY_543__PHY_GTLVL_CAPTURE_CNT_2_MASK         0x003F0000U
#define LPDDR4__DENALI_PHY_543__PHY_GTLVL_CAPTURE_CNT_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_543__PHY_GTLVL_CAPTURE_CNT_2_WIDTH                 6U
#define LPDDR4__PHY_GTLVL_CAPTURE_CNT_2__REG DENALI_PHY_543
#define LPDDR4__PHY_GTLVL_CAPTURE_CNT_2__FLD LPDDR4__DENALI_PHY_543__PHY_GTLVL_CAPTURE_CNT_2

#define LPDDR4__DENALI_PHY_543__PHY_GTLVL_UPDT_WAIT_CNT_2_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_543__PHY_GTLVL_UPDT_WAIT_CNT_2_SHIFT              24U
#define LPDDR4__DENALI_PHY_543__PHY_GTLVL_UPDT_WAIT_CNT_2_WIDTH               4U
#define LPDDR4__PHY_GTLVL_UPDT_WAIT_CNT_2__REG DENALI_PHY_543
#define LPDDR4__PHY_GTLVL_UPDT_WAIT_CNT_2__FLD LPDDR4__DENALI_PHY_543__PHY_GTLVL_UPDT_WAIT_CNT_2

#define LPDDR4__DENALI_PHY_544_READ_MASK                             0x1F030F3FU
#define LPDDR4__DENALI_PHY_544_WRITE_MASK                            0x1F030F3FU
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_CAPTURE_CNT_2_MASK         0x0000003FU
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_CAPTURE_CNT_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_CAPTURE_CNT_2_WIDTH                 6U
#define LPDDR4__PHY_RDLVL_CAPTURE_CNT_2__REG DENALI_PHY_544
#define LPDDR4__PHY_RDLVL_CAPTURE_CNT_2__FLD LPDDR4__DENALI_PHY_544__PHY_RDLVL_CAPTURE_CNT_2

#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_UPDT_WAIT_CNT_2_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_UPDT_WAIT_CNT_2_SHIFT               8U
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_UPDT_WAIT_CNT_2_WIDTH               4U
#define LPDDR4__PHY_RDLVL_UPDT_WAIT_CNT_2__REG DENALI_PHY_544
#define LPDDR4__PHY_RDLVL_UPDT_WAIT_CNT_2__FLD LPDDR4__DENALI_PHY_544__PHY_RDLVL_UPDT_WAIT_CNT_2

#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_OP_MODE_2_MASK             0x00030000U
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_OP_MODE_2_SHIFT                    16U
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_OP_MODE_2_WIDTH                     2U
#define LPDDR4__PHY_RDLVL_OP_MODE_2__REG DENALI_PHY_544
#define LPDDR4__PHY_RDLVL_OP_MODE_2__FLD LPDDR4__DENALI_PHY_544__PHY_RDLVL_OP_MODE_2

#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_2_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_2_SHIFT        24U
#define LPDDR4__DENALI_PHY_544__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_2_WIDTH         5U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_2__REG DENALI_PHY_544
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_544__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_545_READ_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PHY_545_WRITE_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_545__PHY_RDLVL_DATA_MASK_2_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_545__PHY_RDLVL_DATA_MASK_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_545__PHY_RDLVL_DATA_MASK_2_WIDTH                   8U
#define LPDDR4__PHY_RDLVL_DATA_MASK_2__REG DENALI_PHY_545
#define LPDDR4__PHY_RDLVL_DATA_MASK_2__FLD LPDDR4__DENALI_PHY_545__PHY_RDLVL_DATA_MASK_2

#define LPDDR4__DENALI_PHY_545__PHY_RDLVL_DATA_SWIZZLE_2_MASK        0x03FFFF00U
#define LPDDR4__DENALI_PHY_545__PHY_RDLVL_DATA_SWIZZLE_2_SHIFT                8U
#define LPDDR4__DENALI_PHY_545__PHY_RDLVL_DATA_SWIZZLE_2_WIDTH               18U
#define LPDDR4__PHY_RDLVL_DATA_SWIZZLE_2__REG DENALI_PHY_545
#define LPDDR4__PHY_RDLVL_DATA_SWIZZLE_2__FLD LPDDR4__DENALI_PHY_545__PHY_RDLVL_DATA_SWIZZLE_2

#define LPDDR4__DENALI_PHY_546_READ_MASK                             0x00073FFFU
#define LPDDR4__DENALI_PHY_546_WRITE_MASK                            0x00073FFFU
#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_CLK_JITTER_TOLERANCE_2_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_CLK_JITTER_TOLERANCE_2_SHIFT       0U
#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_CLK_JITTER_TOLERANCE_2_WIDTH       8U
#define LPDDR4__PHY_WDQLVL_CLK_JITTER_TOLERANCE_2__REG DENALI_PHY_546
#define LPDDR4__PHY_WDQLVL_CLK_JITTER_TOLERANCE_2__FLD LPDDR4__DENALI_PHY_546__PHY_WDQLVL_CLK_JITTER_TOLERANCE_2

#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_BURST_CNT_2_MASK          0x00003F00U
#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_BURST_CNT_2_SHIFT                  8U
#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_BURST_CNT_2_WIDTH                  6U
#define LPDDR4__PHY_WDQLVL_BURST_CNT_2__REG DENALI_PHY_546
#define LPDDR4__PHY_WDQLVL_BURST_CNT_2__FLD LPDDR4__DENALI_PHY_546__PHY_WDQLVL_BURST_CNT_2

#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_PATT_2_MASK               0x00070000U
#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_PATT_2_SHIFT                      16U
#define LPDDR4__DENALI_PHY_546__PHY_WDQLVL_PATT_2_WIDTH                       3U
#define LPDDR4__PHY_WDQLVL_PATT_2__REG DENALI_PHY_546
#define LPDDR4__PHY_WDQLVL_PATT_2__FLD LPDDR4__DENALI_PHY_546__PHY_WDQLVL_PATT_2

#define LPDDR4__DENALI_PHY_547_READ_MASK                             0x0F0F07FFU
#define LPDDR4__DENALI_PHY_547_WRITE_MASK                            0x0F0F07FFU
#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_2_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_2_SHIFT   0U
#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_2_WIDTH  11U
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_2__REG DENALI_PHY_547
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_2__FLD LPDDR4__DENALI_PHY_547__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_2

#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_UPDT_WAIT_CNT_2_MASK      0x000F0000U
#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_UPDT_WAIT_CNT_2_SHIFT             16U
#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_UPDT_WAIT_CNT_2_WIDTH              4U
#define LPDDR4__PHY_WDQLVL_UPDT_WAIT_CNT_2__REG DENALI_PHY_547
#define LPDDR4__PHY_WDQLVL_UPDT_WAIT_CNT_2__FLD LPDDR4__DENALI_PHY_547__PHY_WDQLVL_UPDT_WAIT_CNT_2

#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_DQDM_OBS_SELECT_2_MASK    0x0F000000U
#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_DQDM_OBS_SELECT_2_SHIFT           24U
#define LPDDR4__DENALI_PHY_547__PHY_WDQLVL_DQDM_OBS_SELECT_2_WIDTH            4U
#define LPDDR4__PHY_WDQLVL_DQDM_OBS_SELECT_2__REG DENALI_PHY_547
#define LPDDR4__PHY_WDQLVL_DQDM_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_547__PHY_WDQLVL_DQDM_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_548_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_548_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_PERIODIC_OBS_SELECT_2_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_PERIODIC_OBS_SELECT_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_PERIODIC_OBS_SELECT_2_WIDTH        8U
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_SELECT_2__REG DENALI_PHY_548
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_548__PHY_WDQLVL_PERIODIC_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_DQ_SLV_DELTA_2_MASK       0x0000FF00U
#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_DQ_SLV_DELTA_2_SHIFT               8U
#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_DQ_SLV_DELTA_2_WIDTH               8U
#define LPDDR4__PHY_WDQLVL_DQ_SLV_DELTA_2__REG DENALI_PHY_548
#define LPDDR4__PHY_WDQLVL_DQ_SLV_DELTA_2__FLD LPDDR4__DENALI_PHY_548__PHY_WDQLVL_DQ_SLV_DELTA_2

#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_DM_DLY_STEP_2_MASK        0x000F0000U
#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_DM_DLY_STEP_2_SHIFT               16U
#define LPDDR4__DENALI_PHY_548__PHY_WDQLVL_DM_DLY_STEP_2_WIDTH                4U
#define LPDDR4__PHY_WDQLVL_DM_DLY_STEP_2__REG DENALI_PHY_548
#define LPDDR4__PHY_WDQLVL_DM_DLY_STEP_2__FLD LPDDR4__DENALI_PHY_548__PHY_WDQLVL_DM_DLY_STEP_2

#define LPDDR4__DENALI_PHY_548__SC_PHY_WDQLVL_CLR_PREV_RESULTS_2_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_548__SC_PHY_WDQLVL_CLR_PREV_RESULTS_2_SHIFT       24U
#define LPDDR4__DENALI_PHY_548__SC_PHY_WDQLVL_CLR_PREV_RESULTS_2_WIDTH        1U
#define LPDDR4__DENALI_PHY_548__SC_PHY_WDQLVL_CLR_PREV_RESULTS_2_WOCLR        0U
#define LPDDR4__DENALI_PHY_548__SC_PHY_WDQLVL_CLR_PREV_RESULTS_2_WOSET        0U
#define LPDDR4__SC_PHY_WDQLVL_CLR_PREV_RESULTS_2__REG DENALI_PHY_548
#define LPDDR4__SC_PHY_WDQLVL_CLR_PREV_RESULTS_2__FLD LPDDR4__DENALI_PHY_548__SC_PHY_WDQLVL_CLR_PREV_RESULTS_2

#define LPDDR4__DENALI_PHY_549_READ_MASK                             0x000001FFU
#define LPDDR4__DENALI_PHY_549_WRITE_MASK                            0x000001FFU
#define LPDDR4__DENALI_PHY_549__PHY_WDQLVL_DATADM_MASK_2_MASK        0x000001FFU
#define LPDDR4__DENALI_PHY_549__PHY_WDQLVL_DATADM_MASK_2_SHIFT                0U
#define LPDDR4__DENALI_PHY_549__PHY_WDQLVL_DATADM_MASK_2_WIDTH                9U
#define LPDDR4__PHY_WDQLVL_DATADM_MASK_2__REG DENALI_PHY_549
#define LPDDR4__PHY_WDQLVL_DATADM_MASK_2__FLD LPDDR4__DENALI_PHY_549__PHY_WDQLVL_DATADM_MASK_2

#define LPDDR4__DENALI_PHY_550_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_550_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_550__PHY_USER_PATT0_2_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_550__PHY_USER_PATT0_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_550__PHY_USER_PATT0_2_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT0_2__REG DENALI_PHY_550
#define LPDDR4__PHY_USER_PATT0_2__FLD LPDDR4__DENALI_PHY_550__PHY_USER_PATT0_2

#define LPDDR4__DENALI_PHY_551_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_551_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_551__PHY_USER_PATT1_2_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_551__PHY_USER_PATT1_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_551__PHY_USER_PATT1_2_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT1_2__REG DENALI_PHY_551
#define LPDDR4__PHY_USER_PATT1_2__FLD LPDDR4__DENALI_PHY_551__PHY_USER_PATT1_2

#define LPDDR4__DENALI_PHY_552_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_552_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_552__PHY_USER_PATT2_2_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_552__PHY_USER_PATT2_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_552__PHY_USER_PATT2_2_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT2_2__REG DENALI_PHY_552
#define LPDDR4__PHY_USER_PATT2_2__FLD LPDDR4__DENALI_PHY_552__PHY_USER_PATT2_2

#define LPDDR4__DENALI_PHY_553_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_553_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_553__PHY_USER_PATT3_2_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_553__PHY_USER_PATT3_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_553__PHY_USER_PATT3_2_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT3_2__REG DENALI_PHY_553
#define LPDDR4__PHY_USER_PATT3_2__FLD LPDDR4__DENALI_PHY_553__PHY_USER_PATT3_2

#define LPDDR4__DENALI_PHY_554_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PHY_554_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_554__PHY_USER_PATT4_2_MASK                0x0000FFFFU
#define LPDDR4__DENALI_PHY_554__PHY_USER_PATT4_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_554__PHY_USER_PATT4_2_WIDTH                       16U
#define LPDDR4__PHY_USER_PATT4_2__REG DENALI_PHY_554
#define LPDDR4__PHY_USER_PATT4_2__FLD LPDDR4__DENALI_PHY_554__PHY_USER_PATT4_2

#define LPDDR4__DENALI_PHY_554__PHY_NTP_MULT_TRAIN_2_MASK            0x00010000U
#define LPDDR4__DENALI_PHY_554__PHY_NTP_MULT_TRAIN_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_554__PHY_NTP_MULT_TRAIN_2_WIDTH                    1U
#define LPDDR4__DENALI_PHY_554__PHY_NTP_MULT_TRAIN_2_WOCLR                    0U
#define LPDDR4__DENALI_PHY_554__PHY_NTP_MULT_TRAIN_2_WOSET                    0U
#define LPDDR4__PHY_NTP_MULT_TRAIN_2__REG DENALI_PHY_554
#define LPDDR4__PHY_NTP_MULT_TRAIN_2__FLD LPDDR4__DENALI_PHY_554__PHY_NTP_MULT_TRAIN_2

#define LPDDR4__DENALI_PHY_555_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_555_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_555__PHY_NTP_EARLY_THRESHOLD_2_MASK       0x000003FFU
#define LPDDR4__DENALI_PHY_555__PHY_NTP_EARLY_THRESHOLD_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_555__PHY_NTP_EARLY_THRESHOLD_2_WIDTH              10U
#define LPDDR4__PHY_NTP_EARLY_THRESHOLD_2__REG DENALI_PHY_555
#define LPDDR4__PHY_NTP_EARLY_THRESHOLD_2__FLD LPDDR4__DENALI_PHY_555__PHY_NTP_EARLY_THRESHOLD_2

#define LPDDR4__DENALI_PHY_555__PHY_NTP_PERIOD_THRESHOLD_2_MASK      0x03FF0000U
#define LPDDR4__DENALI_PHY_555__PHY_NTP_PERIOD_THRESHOLD_2_SHIFT             16U
#define LPDDR4__DENALI_PHY_555__PHY_NTP_PERIOD_THRESHOLD_2_WIDTH             10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_2__REG DENALI_PHY_555
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_2__FLD LPDDR4__DENALI_PHY_555__PHY_NTP_PERIOD_THRESHOLD_2

#define LPDDR4__DENALI_PHY_556_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_556_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_556__PHY_NTP_PERIOD_THRESHOLD_MIN_2_MASK  0x000003FFU
#define LPDDR4__DENALI_PHY_556__PHY_NTP_PERIOD_THRESHOLD_MIN_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_556__PHY_NTP_PERIOD_THRESHOLD_MIN_2_WIDTH         10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MIN_2__REG DENALI_PHY_556
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MIN_2__FLD LPDDR4__DENALI_PHY_556__PHY_NTP_PERIOD_THRESHOLD_MIN_2

#define LPDDR4__DENALI_PHY_556__PHY_NTP_PERIOD_THRESHOLD_MAX_2_MASK  0x03FF0000U
#define LPDDR4__DENALI_PHY_556__PHY_NTP_PERIOD_THRESHOLD_MAX_2_SHIFT         16U
#define LPDDR4__DENALI_PHY_556__PHY_NTP_PERIOD_THRESHOLD_MAX_2_WIDTH         10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MAX_2__REG DENALI_PHY_556
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MAX_2__FLD LPDDR4__DENALI_PHY_556__PHY_NTP_PERIOD_THRESHOLD_MAX_2

#define LPDDR4__DENALI_PHY_557_READ_MASK                             0x00FF0001U
#define LPDDR4__DENALI_PHY_557_WRITE_MASK                            0x00FF0001U
#define LPDDR4__DENALI_PHY_557__PHY_CALVL_VREF_DRIVING_SLICE_2_MASK  0x00000001U
#define LPDDR4__DENALI_PHY_557__PHY_CALVL_VREF_DRIVING_SLICE_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_557__PHY_CALVL_VREF_DRIVING_SLICE_2_WIDTH          1U
#define LPDDR4__DENALI_PHY_557__PHY_CALVL_VREF_DRIVING_SLICE_2_WOCLR          0U
#define LPDDR4__DENALI_PHY_557__PHY_CALVL_VREF_DRIVING_SLICE_2_WOSET          0U
#define LPDDR4__PHY_CALVL_VREF_DRIVING_SLICE_2__REG DENALI_PHY_557
#define LPDDR4__PHY_CALVL_VREF_DRIVING_SLICE_2__FLD LPDDR4__DENALI_PHY_557__PHY_CALVL_VREF_DRIVING_SLICE_2

#define LPDDR4__DENALI_PHY_557__SC_PHY_MANUAL_CLEAR_2_MASK           0x00003F00U
#define LPDDR4__DENALI_PHY_557__SC_PHY_MANUAL_CLEAR_2_SHIFT                   8U
#define LPDDR4__DENALI_PHY_557__SC_PHY_MANUAL_CLEAR_2_WIDTH                   6U
#define LPDDR4__SC_PHY_MANUAL_CLEAR_2__REG DENALI_PHY_557
#define LPDDR4__SC_PHY_MANUAL_CLEAR_2__FLD LPDDR4__DENALI_PHY_557__SC_PHY_MANUAL_CLEAR_2

#define LPDDR4__DENALI_PHY_557__PHY_FIFO_PTR_OBS_2_MASK              0x00FF0000U
#define LPDDR4__DENALI_PHY_557__PHY_FIFO_PTR_OBS_2_SHIFT                     16U
#define LPDDR4__DENALI_PHY_557__PHY_FIFO_PTR_OBS_2_WIDTH                      8U
#define LPDDR4__PHY_FIFO_PTR_OBS_2__REG DENALI_PHY_557
#define LPDDR4__PHY_FIFO_PTR_OBS_2__FLD LPDDR4__DENALI_PHY_557__PHY_FIFO_PTR_OBS_2

#define LPDDR4__DENALI_PHY_558_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_558_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_558__PHY_LPBK_RESULT_OBS_2_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_558__PHY_LPBK_RESULT_OBS_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_558__PHY_LPBK_RESULT_OBS_2_WIDTH                  32U
#define LPDDR4__PHY_LPBK_RESULT_OBS_2__REG DENALI_PHY_558
#define LPDDR4__PHY_LPBK_RESULT_OBS_2__FLD LPDDR4__DENALI_PHY_558__PHY_LPBK_RESULT_OBS_2

#define LPDDR4__DENALI_PHY_559_READ_MASK                             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_559_WRITE_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_559__PHY_LPBK_ERROR_COUNT_OBS_2_MASK      0x0000FFFFU
#define LPDDR4__DENALI_PHY_559__PHY_LPBK_ERROR_COUNT_OBS_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_559__PHY_LPBK_ERROR_COUNT_OBS_2_WIDTH             16U
#define LPDDR4__PHY_LPBK_ERROR_COUNT_OBS_2__REG DENALI_PHY_559
#define LPDDR4__PHY_LPBK_ERROR_COUNT_OBS_2__FLD LPDDR4__DENALI_PHY_559__PHY_LPBK_ERROR_COUNT_OBS_2

#define LPDDR4__DENALI_PHY_559__PHY_MASTER_DLY_LOCK_OBS_2_MASK       0x07FF0000U
#define LPDDR4__DENALI_PHY_559__PHY_MASTER_DLY_LOCK_OBS_2_SHIFT              16U
#define LPDDR4__DENALI_PHY_559__PHY_MASTER_DLY_LOCK_OBS_2_WIDTH              11U
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_2__REG DENALI_PHY_559
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_2__FLD LPDDR4__DENALI_PHY_559__PHY_MASTER_DLY_LOCK_OBS_2

#define LPDDR4__DENALI_PHY_560_READ_MASK                             0xFFFF7F7FU
#define LPDDR4__DENALI_PHY_560_WRITE_MASK                            0xFFFF7F7FU
#define LPDDR4__DENALI_PHY_560__PHY_RDDQ_SLV_DLY_ENC_OBS_2_MASK      0x0000007FU
#define LPDDR4__DENALI_PHY_560__PHY_RDDQ_SLV_DLY_ENC_OBS_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_560__PHY_RDDQ_SLV_DLY_ENC_OBS_2_WIDTH              7U
#define LPDDR4__PHY_RDDQ_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_560
#define LPDDR4__PHY_RDDQ_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_560__PHY_RDDQ_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_560__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_2_MASK 0x00007F00U
#define LPDDR4__DENALI_PHY_560__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_2_SHIFT        8U
#define LPDDR4__DENALI_PHY_560__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_2_WIDTH        7U
#define LPDDR4__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_560
#define LPDDR4__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_560__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_560__PHY_MEAS_DLY_STEP_VALUE_2_MASK       0x00FF0000U
#define LPDDR4__DENALI_PHY_560__PHY_MEAS_DLY_STEP_VALUE_2_SHIFT              16U
#define LPDDR4__DENALI_PHY_560__PHY_MEAS_DLY_STEP_VALUE_2_WIDTH               8U
#define LPDDR4__PHY_MEAS_DLY_STEP_VALUE_2__REG DENALI_PHY_560
#define LPDDR4__PHY_MEAS_DLY_STEP_VALUE_2__FLD LPDDR4__DENALI_PHY_560__PHY_MEAS_DLY_STEP_VALUE_2

#define LPDDR4__DENALI_PHY_560__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_2_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_560__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_2_SHIFT 24U
#define LPDDR4__DENALI_PHY_560__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_2_WIDTH 8U
#define LPDDR4__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_560
#define LPDDR4__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_560__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_561_READ_MASK                             0x7F07FFFFU
#define LPDDR4__DENALI_PHY_561_WRITE_MASK                            0x7F07FFFFU
#define LPDDR4__DENALI_PHY_561__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_2_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_561__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_2_SHIFT 0U
#define LPDDR4__DENALI_PHY_561__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_2_WIDTH 8U
#define LPDDR4__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_561
#define LPDDR4__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_561__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_561__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_2_MASK 0x0007FF00U
#define LPDDR4__DENALI_PHY_561__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_2_SHIFT        8U
#define LPDDR4__DENALI_PHY_561__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_2_WIDTH       11U
#define LPDDR4__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_561
#define LPDDR4__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_561__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_561__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_2_MASK 0x7F000000U
#define LPDDR4__DENALI_PHY_561__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_2_SHIFT       24U
#define LPDDR4__DENALI_PHY_561__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_2_WIDTH        7U
#define LPDDR4__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_561
#define LPDDR4__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_561__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_562_READ_MASK                             0x0007FFFFU
#define LPDDR4__DENALI_PHY_562_WRITE_MASK                            0x0007FFFFU
#define LPDDR4__DENALI_PHY_562__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_2_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_562__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_562__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_2_WIDTH         8U
#define LPDDR4__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_562
#define LPDDR4__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_562__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_562__PHY_WR_ADDER_SLV_DLY_ENC_OBS_2_MASK  0x0000FF00U
#define LPDDR4__DENALI_PHY_562__PHY_WR_ADDER_SLV_DLY_ENC_OBS_2_SHIFT          8U
#define LPDDR4__DENALI_PHY_562__PHY_WR_ADDER_SLV_DLY_ENC_OBS_2_WIDTH          8U
#define LPDDR4__PHY_WR_ADDER_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_562
#define LPDDR4__PHY_WR_ADDER_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_562__PHY_WR_ADDER_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_562__PHY_WR_SHIFT_OBS_2_MASK              0x00070000U
#define LPDDR4__DENALI_PHY_562__PHY_WR_SHIFT_OBS_2_SHIFT                     16U
#define LPDDR4__DENALI_PHY_562__PHY_WR_SHIFT_OBS_2_WIDTH                      3U
#define LPDDR4__PHY_WR_SHIFT_OBS_2__REG DENALI_PHY_562
#define LPDDR4__PHY_WR_SHIFT_OBS_2__FLD LPDDR4__DENALI_PHY_562__PHY_WR_SHIFT_OBS_2

#define LPDDR4__DENALI_PHY_563_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_563_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_563__PHY_WRLVL_HARD0_DELAY_OBS_2_MASK     0x000003FFU
#define LPDDR4__DENALI_PHY_563__PHY_WRLVL_HARD0_DELAY_OBS_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_563__PHY_WRLVL_HARD0_DELAY_OBS_2_WIDTH            10U
#define LPDDR4__PHY_WRLVL_HARD0_DELAY_OBS_2__REG DENALI_PHY_563
#define LPDDR4__PHY_WRLVL_HARD0_DELAY_OBS_2__FLD LPDDR4__DENALI_PHY_563__PHY_WRLVL_HARD0_DELAY_OBS_2

#define LPDDR4__DENALI_PHY_563__PHY_WRLVL_HARD1_DELAY_OBS_2_MASK     0x03FF0000U
#define LPDDR4__DENALI_PHY_563__PHY_WRLVL_HARD1_DELAY_OBS_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_563__PHY_WRLVL_HARD1_DELAY_OBS_2_WIDTH            10U
#define LPDDR4__PHY_WRLVL_HARD1_DELAY_OBS_2__REG DENALI_PHY_563
#define LPDDR4__PHY_WRLVL_HARD1_DELAY_OBS_2__FLD LPDDR4__DENALI_PHY_563__PHY_WRLVL_HARD1_DELAY_OBS_2

#define LPDDR4__DENALI_PHY_564_READ_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_PHY_564_WRITE_MASK                            0x001FFFFFU
#define LPDDR4__DENALI_PHY_564__PHY_WRLVL_STATUS_OBS_2_MASK          0x001FFFFFU
#define LPDDR4__DENALI_PHY_564__PHY_WRLVL_STATUS_OBS_2_SHIFT                  0U
#define LPDDR4__DENALI_PHY_564__PHY_WRLVL_STATUS_OBS_2_WIDTH                 21U
#define LPDDR4__PHY_WRLVL_STATUS_OBS_2__REG DENALI_PHY_564
#define LPDDR4__PHY_WRLVL_STATUS_OBS_2__FLD LPDDR4__DENALI_PHY_564__PHY_WRLVL_STATUS_OBS_2

#define LPDDR4__DENALI_PHY_565_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_565_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_565__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_565__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_565__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_2_WIDTH       10U
#define LPDDR4__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_565
#define LPDDR4__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_565__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_565__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_565__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_565__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_2_WIDTH       10U
#define LPDDR4__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_565
#define LPDDR4__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_565__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_566_READ_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_566_WRITE_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_566__PHY_WRLVL_ERROR_OBS_2_MASK           0x0000FFFFU
#define LPDDR4__DENALI_PHY_566__PHY_WRLVL_ERROR_OBS_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_566__PHY_WRLVL_ERROR_OBS_2_WIDTH                  16U
#define LPDDR4__PHY_WRLVL_ERROR_OBS_2__REG DENALI_PHY_566
#define LPDDR4__PHY_WRLVL_ERROR_OBS_2__FLD LPDDR4__DENALI_PHY_566__PHY_WRLVL_ERROR_OBS_2

#define LPDDR4__DENALI_PHY_566__PHY_GTLVL_HARD0_DELAY_OBS_2_MASK     0x3FFF0000U
#define LPDDR4__DENALI_PHY_566__PHY_GTLVL_HARD0_DELAY_OBS_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_566__PHY_GTLVL_HARD0_DELAY_OBS_2_WIDTH            14U
#define LPDDR4__PHY_GTLVL_HARD0_DELAY_OBS_2__REG DENALI_PHY_566
#define LPDDR4__PHY_GTLVL_HARD0_DELAY_OBS_2__FLD LPDDR4__DENALI_PHY_566__PHY_GTLVL_HARD0_DELAY_OBS_2

#define LPDDR4__DENALI_PHY_567_READ_MASK                             0x00003FFFU
#define LPDDR4__DENALI_PHY_567_WRITE_MASK                            0x00003FFFU
#define LPDDR4__DENALI_PHY_567__PHY_GTLVL_HARD1_DELAY_OBS_2_MASK     0x00003FFFU
#define LPDDR4__DENALI_PHY_567__PHY_GTLVL_HARD1_DELAY_OBS_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_567__PHY_GTLVL_HARD1_DELAY_OBS_2_WIDTH            14U
#define LPDDR4__PHY_GTLVL_HARD1_DELAY_OBS_2__REG DENALI_PHY_567
#define LPDDR4__PHY_GTLVL_HARD1_DELAY_OBS_2__FLD LPDDR4__DENALI_PHY_567__PHY_GTLVL_HARD1_DELAY_OBS_2

#define LPDDR4__DENALI_PHY_568_READ_MASK                             0x0003FFFFU
#define LPDDR4__DENALI_PHY_568_WRITE_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_568__PHY_GTLVL_STATUS_OBS_2_MASK          0x0003FFFFU
#define LPDDR4__DENALI_PHY_568__PHY_GTLVL_STATUS_OBS_2_SHIFT                  0U
#define LPDDR4__DENALI_PHY_568__PHY_GTLVL_STATUS_OBS_2_WIDTH                 18U
#define LPDDR4__PHY_GTLVL_STATUS_OBS_2__REG DENALI_PHY_568
#define LPDDR4__PHY_GTLVL_STATUS_OBS_2__FLD LPDDR4__DENALI_PHY_568__PHY_GTLVL_STATUS_OBS_2

#define LPDDR4__DENALI_PHY_569_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_569_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_569__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_569__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_569__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_2_WIDTH        10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_2__REG DENALI_PHY_569
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_2__FLD LPDDR4__DENALI_PHY_569__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_2

#define LPDDR4__DENALI_PHY_569__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_569__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_2_SHIFT        16U
#define LPDDR4__DENALI_PHY_569__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_2_WIDTH        10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_2__REG DENALI_PHY_569
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_2__FLD LPDDR4__DENALI_PHY_569__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_2

#define LPDDR4__DENALI_PHY_570_READ_MASK                             0x00000003U
#define LPDDR4__DENALI_PHY_570_WRITE_MASK                            0x00000003U
#define LPDDR4__DENALI_PHY_570__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_2_MASK 0x00000003U
#define LPDDR4__DENALI_PHY_570__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_2_SHIFT    0U
#define LPDDR4__DENALI_PHY_570__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_2_WIDTH    2U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_2__REG DENALI_PHY_570
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_2__FLD LPDDR4__DENALI_PHY_570__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_2

#define LPDDR4__DENALI_PHY_571_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_571_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_571__PHY_RDLVL_STATUS_OBS_2_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_571__PHY_RDLVL_STATUS_OBS_2_SHIFT                  0U
#define LPDDR4__DENALI_PHY_571__PHY_RDLVL_STATUS_OBS_2_WIDTH                 32U
#define LPDDR4__PHY_RDLVL_STATUS_OBS_2__REG DENALI_PHY_571
#define LPDDR4__PHY_RDLVL_STATUS_OBS_2__FLD LPDDR4__DENALI_PHY_571__PHY_RDLVL_STATUS_OBS_2

#define LPDDR4__DENALI_PHY_572_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_572_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_572__PHY_WDQLVL_DQDM_LE_DLY_OBS_2_MASK    0x000007FFU
#define LPDDR4__DENALI_PHY_572__PHY_WDQLVL_DQDM_LE_DLY_OBS_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_572__PHY_WDQLVL_DQDM_LE_DLY_OBS_2_WIDTH           11U
#define LPDDR4__PHY_WDQLVL_DQDM_LE_DLY_OBS_2__REG DENALI_PHY_572
#define LPDDR4__PHY_WDQLVL_DQDM_LE_DLY_OBS_2__FLD LPDDR4__DENALI_PHY_572__PHY_WDQLVL_DQDM_LE_DLY_OBS_2

#define LPDDR4__DENALI_PHY_572__PHY_WDQLVL_DQDM_TE_DLY_OBS_2_MASK    0x07FF0000U
#define LPDDR4__DENALI_PHY_572__PHY_WDQLVL_DQDM_TE_DLY_OBS_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_572__PHY_WDQLVL_DQDM_TE_DLY_OBS_2_WIDTH           11U
#define LPDDR4__PHY_WDQLVL_DQDM_TE_DLY_OBS_2__REG DENALI_PHY_572
#define LPDDR4__PHY_WDQLVL_DQDM_TE_DLY_OBS_2__FLD LPDDR4__DENALI_PHY_572__PHY_WDQLVL_DQDM_TE_DLY_OBS_2

#define LPDDR4__DENALI_PHY_573_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_573_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_573__PHY_WDQLVL_STATUS_OBS_2_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_573__PHY_WDQLVL_STATUS_OBS_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_573__PHY_WDQLVL_STATUS_OBS_2_WIDTH                32U
#define LPDDR4__PHY_WDQLVL_STATUS_OBS_2__REG DENALI_PHY_573
#define LPDDR4__PHY_WDQLVL_STATUS_OBS_2__FLD LPDDR4__DENALI_PHY_573__PHY_WDQLVL_STATUS_OBS_2

#define LPDDR4__DENALI_PHY_574_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_574_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_574__PHY_WDQLVL_PERIODIC_OBS_2_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_574__PHY_WDQLVL_PERIODIC_OBS_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_574__PHY_WDQLVL_PERIODIC_OBS_2_WIDTH              32U
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_2__REG DENALI_PHY_574
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_2__FLD LPDDR4__DENALI_PHY_574__PHY_WDQLVL_PERIODIC_OBS_2

#define LPDDR4__DENALI_PHY_575_READ_MASK                             0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_575_WRITE_MASK                            0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_575__PHY_DDL_MODE_2_MASK                  0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_575__PHY_DDL_MODE_2_SHIFT                          0U
#define LPDDR4__DENALI_PHY_575__PHY_DDL_MODE_2_WIDTH                         31U
#define LPDDR4__PHY_DDL_MODE_2__REG DENALI_PHY_575
#define LPDDR4__PHY_DDL_MODE_2__FLD LPDDR4__DENALI_PHY_575__PHY_DDL_MODE_2

#define LPDDR4__DENALI_PHY_576_READ_MASK                             0x0000003FU
#define LPDDR4__DENALI_PHY_576_WRITE_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_576__PHY_DDL_MASK_2_MASK                  0x0000003FU
#define LPDDR4__DENALI_PHY_576__PHY_DDL_MASK_2_SHIFT                          0U
#define LPDDR4__DENALI_PHY_576__PHY_DDL_MASK_2_WIDTH                          6U
#define LPDDR4__PHY_DDL_MASK_2__REG DENALI_PHY_576
#define LPDDR4__PHY_DDL_MASK_2__FLD LPDDR4__DENALI_PHY_576__PHY_DDL_MASK_2

#define LPDDR4__DENALI_PHY_577_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_577_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_577__PHY_DDL_TEST_OBS_2_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_577__PHY_DDL_TEST_OBS_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_577__PHY_DDL_TEST_OBS_2_WIDTH                     32U
#define LPDDR4__PHY_DDL_TEST_OBS_2__REG DENALI_PHY_577
#define LPDDR4__PHY_DDL_TEST_OBS_2__FLD LPDDR4__DENALI_PHY_577__PHY_DDL_TEST_OBS_2

#define LPDDR4__DENALI_PHY_578_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_578_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_578__PHY_DDL_TEST_MSTR_DLY_OBS_2_MASK     0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_578__PHY_DDL_TEST_MSTR_DLY_OBS_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_578__PHY_DDL_TEST_MSTR_DLY_OBS_2_WIDTH            32U
#define LPDDR4__PHY_DDL_TEST_MSTR_DLY_OBS_2__REG DENALI_PHY_578
#define LPDDR4__PHY_DDL_TEST_MSTR_DLY_OBS_2__FLD LPDDR4__DENALI_PHY_578__PHY_DDL_TEST_MSTR_DLY_OBS_2

#define LPDDR4__DENALI_PHY_579_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_579_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_579__PHY_DDL_TRACK_UPD_THRESHOLD_2_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_579__PHY_DDL_TRACK_UPD_THRESHOLD_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_579__PHY_DDL_TRACK_UPD_THRESHOLD_2_WIDTH           8U
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_2__REG DENALI_PHY_579
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_2__FLD LPDDR4__DENALI_PHY_579__PHY_DDL_TRACK_UPD_THRESHOLD_2

#define LPDDR4__DENALI_PHY_579__PHY_LP4_WDQS_OE_EXTEND_2_MASK        0x00000100U
#define LPDDR4__DENALI_PHY_579__PHY_LP4_WDQS_OE_EXTEND_2_SHIFT                8U
#define LPDDR4__DENALI_PHY_579__PHY_LP4_WDQS_OE_EXTEND_2_WIDTH                1U
#define LPDDR4__DENALI_PHY_579__PHY_LP4_WDQS_OE_EXTEND_2_WOCLR                0U
#define LPDDR4__DENALI_PHY_579__PHY_LP4_WDQS_OE_EXTEND_2_WOSET                0U
#define LPDDR4__PHY_LP4_WDQS_OE_EXTEND_2__REG DENALI_PHY_579
#define LPDDR4__PHY_LP4_WDQS_OE_EXTEND_2__FLD LPDDR4__DENALI_PHY_579__PHY_LP4_WDQS_OE_EXTEND_2

#define LPDDR4__DENALI_PHY_579__PHY_RX_CAL_DQ0_2_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_579__PHY_RX_CAL_DQ0_2_SHIFT                       16U
#define LPDDR4__DENALI_PHY_579__PHY_RX_CAL_DQ0_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ0_2__REG DENALI_PHY_579
#define LPDDR4__PHY_RX_CAL_DQ0_2__FLD LPDDR4__DENALI_PHY_579__PHY_RX_CAL_DQ0_2

#define LPDDR4__DENALI_PHY_580_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_580_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_580__PHY_RX_CAL_DQ1_2_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_580__PHY_RX_CAL_DQ1_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_580__PHY_RX_CAL_DQ1_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ1_2__REG DENALI_PHY_580
#define LPDDR4__PHY_RX_CAL_DQ1_2__FLD LPDDR4__DENALI_PHY_580__PHY_RX_CAL_DQ1_2

#define LPDDR4__DENALI_PHY_580__PHY_RX_CAL_DQ2_2_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_580__PHY_RX_CAL_DQ2_2_SHIFT                       16U
#define LPDDR4__DENALI_PHY_580__PHY_RX_CAL_DQ2_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ2_2__REG DENALI_PHY_580
#define LPDDR4__PHY_RX_CAL_DQ2_2__FLD LPDDR4__DENALI_PHY_580__PHY_RX_CAL_DQ2_2

#define LPDDR4__DENALI_PHY_581_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_581_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_581__PHY_RX_CAL_DQ3_2_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_581__PHY_RX_CAL_DQ3_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_581__PHY_RX_CAL_DQ3_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ3_2__REG DENALI_PHY_581
#define LPDDR4__PHY_RX_CAL_DQ3_2__FLD LPDDR4__DENALI_PHY_581__PHY_RX_CAL_DQ3_2

#define LPDDR4__DENALI_PHY_581__PHY_RX_CAL_DQ4_2_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_581__PHY_RX_CAL_DQ4_2_SHIFT                       16U
#define LPDDR4__DENALI_PHY_581__PHY_RX_CAL_DQ4_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ4_2__REG DENALI_PHY_581
#define LPDDR4__PHY_RX_CAL_DQ4_2__FLD LPDDR4__DENALI_PHY_581__PHY_RX_CAL_DQ4_2

#define LPDDR4__DENALI_PHY_582_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_582_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_582__PHY_RX_CAL_DQ5_2_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_582__PHY_RX_CAL_DQ5_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_582__PHY_RX_CAL_DQ5_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ5_2__REG DENALI_PHY_582
#define LPDDR4__PHY_RX_CAL_DQ5_2__FLD LPDDR4__DENALI_PHY_582__PHY_RX_CAL_DQ5_2

#define LPDDR4__DENALI_PHY_582__PHY_RX_CAL_DQ6_2_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_582__PHY_RX_CAL_DQ6_2_SHIFT                       16U
#define LPDDR4__DENALI_PHY_582__PHY_RX_CAL_DQ6_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ6_2__REG DENALI_PHY_582
#define LPDDR4__PHY_RX_CAL_DQ6_2__FLD LPDDR4__DENALI_PHY_582__PHY_RX_CAL_DQ6_2

#define LPDDR4__DENALI_PHY_583_READ_MASK                             0x000001FFU
#define LPDDR4__DENALI_PHY_583_WRITE_MASK                            0x000001FFU
#define LPDDR4__DENALI_PHY_583__PHY_RX_CAL_DQ7_2_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_583__PHY_RX_CAL_DQ7_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_583__PHY_RX_CAL_DQ7_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ7_2__REG DENALI_PHY_583
#define LPDDR4__PHY_RX_CAL_DQ7_2__FLD LPDDR4__DENALI_PHY_583__PHY_RX_CAL_DQ7_2

#define LPDDR4__DENALI_PHY_584_READ_MASK                             0x0003FFFFU
#define LPDDR4__DENALI_PHY_584_WRITE_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_584__PHY_RX_CAL_DM_2_MASK                 0x0003FFFFU
#define LPDDR4__DENALI_PHY_584__PHY_RX_CAL_DM_2_SHIFT                         0U
#define LPDDR4__DENALI_PHY_584__PHY_RX_CAL_DM_2_WIDTH                        18U
#define LPDDR4__PHY_RX_CAL_DM_2__REG DENALI_PHY_584
#define LPDDR4__PHY_RX_CAL_DM_2__FLD LPDDR4__DENALI_PHY_584__PHY_RX_CAL_DM_2

#define LPDDR4__DENALI_PHY_585_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_585_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_585__PHY_RX_CAL_DQS_2_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_585__PHY_RX_CAL_DQS_2_SHIFT                        0U
#define LPDDR4__DENALI_PHY_585__PHY_RX_CAL_DQS_2_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQS_2__REG DENALI_PHY_585
#define LPDDR4__PHY_RX_CAL_DQS_2__FLD LPDDR4__DENALI_PHY_585__PHY_RX_CAL_DQS_2

#define LPDDR4__DENALI_PHY_585__PHY_RX_CAL_FDBK_2_MASK               0x01FF0000U
#define LPDDR4__DENALI_PHY_585__PHY_RX_CAL_FDBK_2_SHIFT                      16U
#define LPDDR4__DENALI_PHY_585__PHY_RX_CAL_FDBK_2_WIDTH                       9U
#define LPDDR4__PHY_RX_CAL_FDBK_2__REG DENALI_PHY_585
#define LPDDR4__PHY_RX_CAL_FDBK_2__FLD LPDDR4__DENALI_PHY_585__PHY_RX_CAL_FDBK_2

#define LPDDR4__DENALI_PHY_586_READ_MASK                             0xFF1F07FFU
#define LPDDR4__DENALI_PHY_586_WRITE_MASK                            0xFF1F07FFU
#define LPDDR4__DENALI_PHY_586__PHY_PAD_RX_BIAS_EN_2_MASK            0x000007FFU
#define LPDDR4__DENALI_PHY_586__PHY_PAD_RX_BIAS_EN_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_586__PHY_PAD_RX_BIAS_EN_2_WIDTH                   11U
#define LPDDR4__PHY_PAD_RX_BIAS_EN_2__REG DENALI_PHY_586
#define LPDDR4__PHY_PAD_RX_BIAS_EN_2__FLD LPDDR4__DENALI_PHY_586__PHY_PAD_RX_BIAS_EN_2

#define LPDDR4__DENALI_PHY_586__PHY_STATIC_TOG_DISABLE_2_MASK        0x001F0000U
#define LPDDR4__DENALI_PHY_586__PHY_STATIC_TOG_DISABLE_2_SHIFT               16U
#define LPDDR4__DENALI_PHY_586__PHY_STATIC_TOG_DISABLE_2_WIDTH                5U
#define LPDDR4__PHY_STATIC_TOG_DISABLE_2__REG DENALI_PHY_586
#define LPDDR4__PHY_STATIC_TOG_DISABLE_2__FLD LPDDR4__DENALI_PHY_586__PHY_STATIC_TOG_DISABLE_2

#define LPDDR4__DENALI_PHY_586__PHY_DATA_DC_CAL_SAMPLE_WAIT_2_MASK   0xFF000000U
#define LPDDR4__DENALI_PHY_586__PHY_DATA_DC_CAL_SAMPLE_WAIT_2_SHIFT          24U
#define LPDDR4__DENALI_PHY_586__PHY_DATA_DC_CAL_SAMPLE_WAIT_2_WIDTH           8U
#define LPDDR4__PHY_DATA_DC_CAL_SAMPLE_WAIT_2__REG DENALI_PHY_586
#define LPDDR4__PHY_DATA_DC_CAL_SAMPLE_WAIT_2__FLD LPDDR4__DENALI_PHY_586__PHY_DATA_DC_CAL_SAMPLE_WAIT_2

#define LPDDR4__DENALI_PHY_587_READ_MASK                             0xFF3F03FFU
#define LPDDR4__DENALI_PHY_587_WRITE_MASK                            0xFF3F03FFU
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_CAL_TIMEOUT_2_MASK       0x000000FFU
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_CAL_TIMEOUT_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_CAL_TIMEOUT_2_WIDTH               8U
#define LPDDR4__PHY_DATA_DC_CAL_TIMEOUT_2__REG DENALI_PHY_587
#define LPDDR4__PHY_DATA_DC_CAL_TIMEOUT_2__FLD LPDDR4__DENALI_PHY_587__PHY_DATA_DC_CAL_TIMEOUT_2

#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_WEIGHT_2_MASK            0x00000300U
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_WEIGHT_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_WEIGHT_2_WIDTH                    2U
#define LPDDR4__PHY_DATA_DC_WEIGHT_2__REG DENALI_PHY_587
#define LPDDR4__PHY_DATA_DC_WEIGHT_2__FLD LPDDR4__DENALI_PHY_587__PHY_DATA_DC_WEIGHT_2

#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_ADJUST_START_2_MASK      0x003F0000U
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_ADJUST_START_2_SHIFT             16U
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_ADJUST_START_2_WIDTH              6U
#define LPDDR4__PHY_DATA_DC_ADJUST_START_2__REG DENALI_PHY_587
#define LPDDR4__PHY_DATA_DC_ADJUST_START_2__FLD LPDDR4__DENALI_PHY_587__PHY_DATA_DC_ADJUST_START_2

#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_ADJUST_SAMPLE_CNT_2_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_ADJUST_SAMPLE_CNT_2_SHIFT        24U
#define LPDDR4__DENALI_PHY_587__PHY_DATA_DC_ADJUST_SAMPLE_CNT_2_WIDTH         8U
#define LPDDR4__PHY_DATA_DC_ADJUST_SAMPLE_CNT_2__REG DENALI_PHY_587
#define LPDDR4__PHY_DATA_DC_ADJUST_SAMPLE_CNT_2__FLD LPDDR4__DENALI_PHY_587__PHY_DATA_DC_ADJUST_SAMPLE_CNT_2

#define LPDDR4__DENALI_PHY_588_READ_MASK                             0x010101FFU
#define LPDDR4__DENALI_PHY_588_WRITE_MASK                            0x010101FFU
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_THRSHLD_2_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_THRSHLD_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_THRSHLD_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_ADJUST_THRSHLD_2__REG DENALI_PHY_588
#define LPDDR4__PHY_DATA_DC_ADJUST_THRSHLD_2__FLD LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_THRSHLD_2

#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_DIRECT_2_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_DIRECT_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_DIRECT_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_DIRECT_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_DIRECT_2_WOSET             0U
#define LPDDR4__PHY_DATA_DC_ADJUST_DIRECT_2__REG DENALI_PHY_588
#define LPDDR4__PHY_DATA_DC_ADJUST_DIRECT_2__FLD LPDDR4__DENALI_PHY_588__PHY_DATA_DC_ADJUST_DIRECT_2

#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_POLARITY_2_MASK      0x00010000U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_POLARITY_2_SHIFT             16U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_POLARITY_2_WIDTH              1U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_POLARITY_2_WOCLR              0U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_POLARITY_2_WOSET              0U
#define LPDDR4__PHY_DATA_DC_CAL_POLARITY_2__REG DENALI_PHY_588
#define LPDDR4__PHY_DATA_DC_CAL_POLARITY_2__FLD LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_POLARITY_2

#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_START_2_MASK         0x01000000U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_START_2_SHIFT                24U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_START_2_WIDTH                 1U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_START_2_WOCLR                 0U
#define LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_START_2_WOSET                 0U
#define LPDDR4__PHY_DATA_DC_CAL_START_2__REG DENALI_PHY_588
#define LPDDR4__PHY_DATA_DC_CAL_START_2__FLD LPDDR4__DENALI_PHY_588__PHY_DATA_DC_CAL_START_2

#define LPDDR4__DENALI_PHY_589_READ_MASK                             0x01010703U
#define LPDDR4__DENALI_PHY_589_WRITE_MASK                            0x01010703U
#define LPDDR4__DENALI_PHY_589__PHY_DATA_DC_SW_RANK_2_MASK           0x00000003U
#define LPDDR4__DENALI_PHY_589__PHY_DATA_DC_SW_RANK_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_589__PHY_DATA_DC_SW_RANK_2_WIDTH                   2U
#define LPDDR4__PHY_DATA_DC_SW_RANK_2__REG DENALI_PHY_589
#define LPDDR4__PHY_DATA_DC_SW_RANK_2__FLD LPDDR4__DENALI_PHY_589__PHY_DATA_DC_SW_RANK_2

#define LPDDR4__DENALI_PHY_589__PHY_FDBK_PWR_CTRL_2_MASK             0x00000700U
#define LPDDR4__DENALI_PHY_589__PHY_FDBK_PWR_CTRL_2_SHIFT                     8U
#define LPDDR4__DENALI_PHY_589__PHY_FDBK_PWR_CTRL_2_WIDTH                     3U
#define LPDDR4__PHY_FDBK_PWR_CTRL_2__REG DENALI_PHY_589
#define LPDDR4__PHY_FDBK_PWR_CTRL_2__FLD LPDDR4__DENALI_PHY_589__PHY_FDBK_PWR_CTRL_2

#define LPDDR4__DENALI_PHY_589__PHY_SLV_DLY_CTRL_GATE_DISABLE_2_MASK 0x00010000U
#define LPDDR4__DENALI_PHY_589__PHY_SLV_DLY_CTRL_GATE_DISABLE_2_SHIFT        16U
#define LPDDR4__DENALI_PHY_589__PHY_SLV_DLY_CTRL_GATE_DISABLE_2_WIDTH         1U
#define LPDDR4__DENALI_PHY_589__PHY_SLV_DLY_CTRL_GATE_DISABLE_2_WOCLR         0U
#define LPDDR4__DENALI_PHY_589__PHY_SLV_DLY_CTRL_GATE_DISABLE_2_WOSET         0U
#define LPDDR4__PHY_SLV_DLY_CTRL_GATE_DISABLE_2__REG DENALI_PHY_589
#define LPDDR4__PHY_SLV_DLY_CTRL_GATE_DISABLE_2__FLD LPDDR4__DENALI_PHY_589__PHY_SLV_DLY_CTRL_GATE_DISABLE_2

#define LPDDR4__DENALI_PHY_589__PHY_RDPATH_GATE_DISABLE_2_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_589__PHY_RDPATH_GATE_DISABLE_2_SHIFT              24U
#define LPDDR4__DENALI_PHY_589__PHY_RDPATH_GATE_DISABLE_2_WIDTH               1U
#define LPDDR4__DENALI_PHY_589__PHY_RDPATH_GATE_DISABLE_2_WOCLR               0U
#define LPDDR4__DENALI_PHY_589__PHY_RDPATH_GATE_DISABLE_2_WOSET               0U
#define LPDDR4__PHY_RDPATH_GATE_DISABLE_2__REG DENALI_PHY_589
#define LPDDR4__PHY_RDPATH_GATE_DISABLE_2__FLD LPDDR4__DENALI_PHY_589__PHY_RDPATH_GATE_DISABLE_2

#define LPDDR4__DENALI_PHY_590_READ_MASK                             0x00000101U
#define LPDDR4__DENALI_PHY_590_WRITE_MASK                            0x00000101U
#define LPDDR4__DENALI_PHY_590__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_2_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_590__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_2_SHIFT       0U
#define LPDDR4__DENALI_PHY_590__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_2_WIDTH       1U
#define LPDDR4__DENALI_PHY_590__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_2_WOCLR       0U
#define LPDDR4__DENALI_PHY_590__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_2_WOSET       0U
#define LPDDR4__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_2__REG DENALI_PHY_590
#define LPDDR4__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_2__FLD LPDDR4__DENALI_PHY_590__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_2

#define LPDDR4__DENALI_PHY_590__PHY_SLICE_PWR_RDC_DISABLE_2_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_590__PHY_SLICE_PWR_RDC_DISABLE_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_590__PHY_SLICE_PWR_RDC_DISABLE_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_590__PHY_SLICE_PWR_RDC_DISABLE_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_590__PHY_SLICE_PWR_RDC_DISABLE_2_WOSET             0U
#define LPDDR4__PHY_SLICE_PWR_RDC_DISABLE_2__REG DENALI_PHY_590
#define LPDDR4__PHY_SLICE_PWR_RDC_DISABLE_2__FLD LPDDR4__DENALI_PHY_590__PHY_SLICE_PWR_RDC_DISABLE_2

#define LPDDR4__DENALI_PHY_591_READ_MASK                             0x07FFFF07U
#define LPDDR4__DENALI_PHY_591_WRITE_MASK                            0x07FFFF07U
#define LPDDR4__DENALI_PHY_591__PHY_DQ_TSEL_ENABLE_2_MASK            0x00000007U
#define LPDDR4__DENALI_PHY_591__PHY_DQ_TSEL_ENABLE_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_591__PHY_DQ_TSEL_ENABLE_2_WIDTH                    3U
#define LPDDR4__PHY_DQ_TSEL_ENABLE_2__REG DENALI_PHY_591
#define LPDDR4__PHY_DQ_TSEL_ENABLE_2__FLD LPDDR4__DENALI_PHY_591__PHY_DQ_TSEL_ENABLE_2

#define LPDDR4__DENALI_PHY_591__PHY_DQ_TSEL_SELECT_2_MASK            0x00FFFF00U
#define LPDDR4__DENALI_PHY_591__PHY_DQ_TSEL_SELECT_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_591__PHY_DQ_TSEL_SELECT_2_WIDTH                   16U
#define LPDDR4__PHY_DQ_TSEL_SELECT_2__REG DENALI_PHY_591
#define LPDDR4__PHY_DQ_TSEL_SELECT_2__FLD LPDDR4__DENALI_PHY_591__PHY_DQ_TSEL_SELECT_2

#define LPDDR4__DENALI_PHY_591__PHY_DQS_TSEL_ENABLE_2_MASK           0x07000000U
#define LPDDR4__DENALI_PHY_591__PHY_DQS_TSEL_ENABLE_2_SHIFT                  24U
#define LPDDR4__DENALI_PHY_591__PHY_DQS_TSEL_ENABLE_2_WIDTH                   3U
#define LPDDR4__PHY_DQS_TSEL_ENABLE_2__REG DENALI_PHY_591
#define LPDDR4__PHY_DQS_TSEL_ENABLE_2__FLD LPDDR4__DENALI_PHY_591__PHY_DQS_TSEL_ENABLE_2

#define LPDDR4__DENALI_PHY_592_READ_MASK                             0x7F03FFFFU
#define LPDDR4__DENALI_PHY_592_WRITE_MASK                            0x7F03FFFFU
#define LPDDR4__DENALI_PHY_592__PHY_DQS_TSEL_SELECT_2_MASK           0x0000FFFFU
#define LPDDR4__DENALI_PHY_592__PHY_DQS_TSEL_SELECT_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_592__PHY_DQS_TSEL_SELECT_2_WIDTH                  16U
#define LPDDR4__PHY_DQS_TSEL_SELECT_2__REG DENALI_PHY_592
#define LPDDR4__PHY_DQS_TSEL_SELECT_2__FLD LPDDR4__DENALI_PHY_592__PHY_DQS_TSEL_SELECT_2

#define LPDDR4__DENALI_PHY_592__PHY_TWO_CYC_PREAMBLE_2_MASK          0x00030000U
#define LPDDR4__DENALI_PHY_592__PHY_TWO_CYC_PREAMBLE_2_SHIFT                 16U
#define LPDDR4__DENALI_PHY_592__PHY_TWO_CYC_PREAMBLE_2_WIDTH                  2U
#define LPDDR4__PHY_TWO_CYC_PREAMBLE_2__REG DENALI_PHY_592
#define LPDDR4__PHY_TWO_CYC_PREAMBLE_2__FLD LPDDR4__DENALI_PHY_592__PHY_TWO_CYC_PREAMBLE_2

#define LPDDR4__DENALI_PHY_592__PHY_VREF_INITIAL_START_POINT_2_MASK  0x7F000000U
#define LPDDR4__DENALI_PHY_592__PHY_VREF_INITIAL_START_POINT_2_SHIFT         24U
#define LPDDR4__DENALI_PHY_592__PHY_VREF_INITIAL_START_POINT_2_WIDTH          7U
#define LPDDR4__PHY_VREF_INITIAL_START_POINT_2__REG DENALI_PHY_592
#define LPDDR4__PHY_VREF_INITIAL_START_POINT_2__FLD LPDDR4__DENALI_PHY_592__PHY_VREF_INITIAL_START_POINT_2

#define LPDDR4__DENALI_PHY_593_READ_MASK                             0xFF01037FU
#define LPDDR4__DENALI_PHY_593_WRITE_MASK                            0xFF01037FU
#define LPDDR4__DENALI_PHY_593__PHY_VREF_INITIAL_STOP_POINT_2_MASK   0x0000007FU
#define LPDDR4__DENALI_PHY_593__PHY_VREF_INITIAL_STOP_POINT_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_593__PHY_VREF_INITIAL_STOP_POINT_2_WIDTH           7U
#define LPDDR4__PHY_VREF_INITIAL_STOP_POINT_2__REG DENALI_PHY_593
#define LPDDR4__PHY_VREF_INITIAL_STOP_POINT_2__FLD LPDDR4__DENALI_PHY_593__PHY_VREF_INITIAL_STOP_POINT_2

#define LPDDR4__DENALI_PHY_593__PHY_VREF_TRAINING_CTRL_2_MASK        0x00000300U
#define LPDDR4__DENALI_PHY_593__PHY_VREF_TRAINING_CTRL_2_SHIFT                8U
#define LPDDR4__DENALI_PHY_593__PHY_VREF_TRAINING_CTRL_2_WIDTH                2U
#define LPDDR4__PHY_VREF_TRAINING_CTRL_2__REG DENALI_PHY_593
#define LPDDR4__PHY_VREF_TRAINING_CTRL_2__FLD LPDDR4__DENALI_PHY_593__PHY_VREF_TRAINING_CTRL_2

#define LPDDR4__DENALI_PHY_593__PHY_NTP_TRAIN_EN_2_MASK              0x00010000U
#define LPDDR4__DENALI_PHY_593__PHY_NTP_TRAIN_EN_2_SHIFT                     16U
#define LPDDR4__DENALI_PHY_593__PHY_NTP_TRAIN_EN_2_WIDTH                      1U
#define LPDDR4__DENALI_PHY_593__PHY_NTP_TRAIN_EN_2_WOCLR                      0U
#define LPDDR4__DENALI_PHY_593__PHY_NTP_TRAIN_EN_2_WOSET                      0U
#define LPDDR4__PHY_NTP_TRAIN_EN_2__REG DENALI_PHY_593
#define LPDDR4__PHY_NTP_TRAIN_EN_2__FLD LPDDR4__DENALI_PHY_593__PHY_NTP_TRAIN_EN_2

#define LPDDR4__DENALI_PHY_593__PHY_NTP_WDQ_STEP_SIZE_2_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_593__PHY_NTP_WDQ_STEP_SIZE_2_SHIFT                24U
#define LPDDR4__DENALI_PHY_593__PHY_NTP_WDQ_STEP_SIZE_2_WIDTH                 8U
#define LPDDR4__PHY_NTP_WDQ_STEP_SIZE_2__REG DENALI_PHY_593
#define LPDDR4__PHY_NTP_WDQ_STEP_SIZE_2__FLD LPDDR4__DENALI_PHY_593__PHY_NTP_WDQ_STEP_SIZE_2

#define LPDDR4__DENALI_PHY_594_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_594_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_594__PHY_NTP_WDQ_START_2_MASK             0x000007FFU
#define LPDDR4__DENALI_PHY_594__PHY_NTP_WDQ_START_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_594__PHY_NTP_WDQ_START_2_WIDTH                    11U
#define LPDDR4__PHY_NTP_WDQ_START_2__REG DENALI_PHY_594
#define LPDDR4__PHY_NTP_WDQ_START_2__FLD LPDDR4__DENALI_PHY_594__PHY_NTP_WDQ_START_2

#define LPDDR4__DENALI_PHY_594__PHY_NTP_WDQ_STOP_2_MASK              0x07FF0000U
#define LPDDR4__DENALI_PHY_594__PHY_NTP_WDQ_STOP_2_SHIFT                     16U
#define LPDDR4__DENALI_PHY_594__PHY_NTP_WDQ_STOP_2_WIDTH                     11U
#define LPDDR4__PHY_NTP_WDQ_STOP_2__REG DENALI_PHY_594
#define LPDDR4__PHY_NTP_WDQ_STOP_2__FLD LPDDR4__DENALI_PHY_594__PHY_NTP_WDQ_STOP_2

#define LPDDR4__DENALI_PHY_595_READ_MASK                             0x0103FFFFU
#define LPDDR4__DENALI_PHY_595_WRITE_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_595__PHY_NTP_WDQ_BIT_EN_2_MASK            0x000000FFU
#define LPDDR4__DENALI_PHY_595__PHY_NTP_WDQ_BIT_EN_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_595__PHY_NTP_WDQ_BIT_EN_2_WIDTH                    8U
#define LPDDR4__PHY_NTP_WDQ_BIT_EN_2__REG DENALI_PHY_595
#define LPDDR4__PHY_NTP_WDQ_BIT_EN_2__FLD LPDDR4__DENALI_PHY_595__PHY_NTP_WDQ_BIT_EN_2

#define LPDDR4__DENALI_PHY_595__PHY_WDQLVL_DVW_MIN_2_MASK            0x0003FF00U
#define LPDDR4__DENALI_PHY_595__PHY_WDQLVL_DVW_MIN_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_595__PHY_WDQLVL_DVW_MIN_2_WIDTH                   10U
#define LPDDR4__PHY_WDQLVL_DVW_MIN_2__REG DENALI_PHY_595
#define LPDDR4__PHY_WDQLVL_DVW_MIN_2__FLD LPDDR4__DENALI_PHY_595__PHY_WDQLVL_DVW_MIN_2

#define LPDDR4__DENALI_PHY_595__PHY_SW_WDQLVL_DVW_MIN_EN_2_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_595__PHY_SW_WDQLVL_DVW_MIN_EN_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_595__PHY_SW_WDQLVL_DVW_MIN_EN_2_WIDTH              1U
#define LPDDR4__DENALI_PHY_595__PHY_SW_WDQLVL_DVW_MIN_EN_2_WOCLR              0U
#define LPDDR4__DENALI_PHY_595__PHY_SW_WDQLVL_DVW_MIN_EN_2_WOSET              0U
#define LPDDR4__PHY_SW_WDQLVL_DVW_MIN_EN_2__REG DENALI_PHY_595
#define LPDDR4__PHY_SW_WDQLVL_DVW_MIN_EN_2__FLD LPDDR4__DENALI_PHY_595__PHY_SW_WDQLVL_DVW_MIN_EN_2

#define LPDDR4__DENALI_PHY_596_READ_MASK                             0x1F1F0F3FU
#define LPDDR4__DENALI_PHY_596_WRITE_MASK                            0x1F1F0F3FU
#define LPDDR4__DENALI_PHY_596__PHY_WDQLVL_PER_START_OFFSET_2_MASK   0x0000003FU
#define LPDDR4__DENALI_PHY_596__PHY_WDQLVL_PER_START_OFFSET_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_596__PHY_WDQLVL_PER_START_OFFSET_2_WIDTH           6U
#define LPDDR4__PHY_WDQLVL_PER_START_OFFSET_2__REG DENALI_PHY_596
#define LPDDR4__PHY_WDQLVL_PER_START_OFFSET_2__FLD LPDDR4__DENALI_PHY_596__PHY_WDQLVL_PER_START_OFFSET_2

#define LPDDR4__DENALI_PHY_596__PHY_FAST_LVL_EN_2_MASK               0x00000F00U
#define LPDDR4__DENALI_PHY_596__PHY_FAST_LVL_EN_2_SHIFT                       8U
#define LPDDR4__DENALI_PHY_596__PHY_FAST_LVL_EN_2_WIDTH                       4U
#define LPDDR4__PHY_FAST_LVL_EN_2__REG DENALI_PHY_596
#define LPDDR4__PHY_FAST_LVL_EN_2__FLD LPDDR4__DENALI_PHY_596__PHY_FAST_LVL_EN_2

#define LPDDR4__DENALI_PHY_596__PHY_PAD_TX_DCD_2_MASK                0x001F0000U
#define LPDDR4__DENALI_PHY_596__PHY_PAD_TX_DCD_2_SHIFT                       16U
#define LPDDR4__DENALI_PHY_596__PHY_PAD_TX_DCD_2_WIDTH                        5U
#define LPDDR4__PHY_PAD_TX_DCD_2__REG DENALI_PHY_596
#define LPDDR4__PHY_PAD_TX_DCD_2__FLD LPDDR4__DENALI_PHY_596__PHY_PAD_TX_DCD_2

#define LPDDR4__DENALI_PHY_596__PHY_PAD_RX_DCD_0_2_MASK              0x1F000000U
#define LPDDR4__DENALI_PHY_596__PHY_PAD_RX_DCD_0_2_SHIFT                     24U
#define LPDDR4__DENALI_PHY_596__PHY_PAD_RX_DCD_0_2_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_0_2__REG DENALI_PHY_596
#define LPDDR4__PHY_PAD_RX_DCD_0_2__FLD LPDDR4__DENALI_PHY_596__PHY_PAD_RX_DCD_0_2

#define LPDDR4__DENALI_PHY_597_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_597_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_1_2_MASK              0x0000001FU
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_1_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_1_2_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_1_2__REG DENALI_PHY_597
#define LPDDR4__PHY_PAD_RX_DCD_1_2__FLD LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_1_2

#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_2_2_MASK              0x00001F00U
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_2_2_SHIFT                      8U
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_2_2_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_2_2__REG DENALI_PHY_597
#define LPDDR4__PHY_PAD_RX_DCD_2_2__FLD LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_2_2

#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_3_2_MASK              0x001F0000U
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_3_2_SHIFT                     16U
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_3_2_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_3_2__REG DENALI_PHY_597
#define LPDDR4__PHY_PAD_RX_DCD_3_2__FLD LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_3_2

#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_4_2_MASK              0x1F000000U
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_4_2_SHIFT                     24U
#define LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_4_2_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_4_2__REG DENALI_PHY_597
#define LPDDR4__PHY_PAD_RX_DCD_4_2__FLD LPDDR4__DENALI_PHY_597__PHY_PAD_RX_DCD_4_2

#define LPDDR4__DENALI_PHY_598_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_598_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_5_2_MASK              0x0000001FU
#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_5_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_5_2_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_5_2__REG DENALI_PHY_598
#define LPDDR4__PHY_PAD_RX_DCD_5_2__FLD LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_5_2

#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_6_2_MASK              0x00001F00U
#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_6_2_SHIFT                      8U
#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_6_2_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_6_2__REG DENALI_PHY_598
#define LPDDR4__PHY_PAD_RX_DCD_6_2__FLD LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_6_2

#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_7_2_MASK              0x001F0000U
#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_7_2_SHIFT                     16U
#define LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_7_2_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_7_2__REG DENALI_PHY_598
#define LPDDR4__PHY_PAD_RX_DCD_7_2__FLD LPDDR4__DENALI_PHY_598__PHY_PAD_RX_DCD_7_2

#define LPDDR4__DENALI_PHY_598__PHY_PAD_DM_RX_DCD_2_MASK             0x1F000000U
#define LPDDR4__DENALI_PHY_598__PHY_PAD_DM_RX_DCD_2_SHIFT                    24U
#define LPDDR4__DENALI_PHY_598__PHY_PAD_DM_RX_DCD_2_WIDTH                     5U
#define LPDDR4__PHY_PAD_DM_RX_DCD_2__REG DENALI_PHY_598
#define LPDDR4__PHY_PAD_DM_RX_DCD_2__FLD LPDDR4__DENALI_PHY_598__PHY_PAD_DM_RX_DCD_2

#define LPDDR4__DENALI_PHY_599_READ_MASK                             0x007F1F1FU
#define LPDDR4__DENALI_PHY_599_WRITE_MASK                            0x007F1F1FU
#define LPDDR4__DENALI_PHY_599__PHY_PAD_DQS_RX_DCD_2_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_599__PHY_PAD_DQS_RX_DCD_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_599__PHY_PAD_DQS_RX_DCD_2_WIDTH                    5U
#define LPDDR4__PHY_PAD_DQS_RX_DCD_2__REG DENALI_PHY_599
#define LPDDR4__PHY_PAD_DQS_RX_DCD_2__FLD LPDDR4__DENALI_PHY_599__PHY_PAD_DQS_RX_DCD_2

#define LPDDR4__DENALI_PHY_599__PHY_PAD_FDBK_RX_DCD_2_MASK           0x00001F00U
#define LPDDR4__DENALI_PHY_599__PHY_PAD_FDBK_RX_DCD_2_SHIFT                   8U
#define LPDDR4__DENALI_PHY_599__PHY_PAD_FDBK_RX_DCD_2_WIDTH                   5U
#define LPDDR4__PHY_PAD_FDBK_RX_DCD_2__REG DENALI_PHY_599
#define LPDDR4__PHY_PAD_FDBK_RX_DCD_2__FLD LPDDR4__DENALI_PHY_599__PHY_PAD_FDBK_RX_DCD_2

#define LPDDR4__DENALI_PHY_599__PHY_PAD_DSLICE_IO_CFG_2_MASK         0x007F0000U
#define LPDDR4__DENALI_PHY_599__PHY_PAD_DSLICE_IO_CFG_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_599__PHY_PAD_DSLICE_IO_CFG_2_WIDTH                 7U
#define LPDDR4__PHY_PAD_DSLICE_IO_CFG_2__REG DENALI_PHY_599
#define LPDDR4__PHY_PAD_DSLICE_IO_CFG_2__FLD LPDDR4__DENALI_PHY_599__PHY_PAD_DSLICE_IO_CFG_2

#define LPDDR4__DENALI_PHY_600_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_600_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_600__PHY_RDDQ0_SLAVE_DELAY_2_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_600__PHY_RDDQ0_SLAVE_DELAY_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_600__PHY_RDDQ0_SLAVE_DELAY_2_WIDTH                10U
#define LPDDR4__PHY_RDDQ0_SLAVE_DELAY_2__REG DENALI_PHY_600
#define LPDDR4__PHY_RDDQ0_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_600__PHY_RDDQ0_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_600__PHY_RDDQ1_SLAVE_DELAY_2_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_600__PHY_RDDQ1_SLAVE_DELAY_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_600__PHY_RDDQ1_SLAVE_DELAY_2_WIDTH                10U
#define LPDDR4__PHY_RDDQ1_SLAVE_DELAY_2__REG DENALI_PHY_600
#define LPDDR4__PHY_RDDQ1_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_600__PHY_RDDQ1_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_601_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_601_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_601__PHY_RDDQ2_SLAVE_DELAY_2_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_601__PHY_RDDQ2_SLAVE_DELAY_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_601__PHY_RDDQ2_SLAVE_DELAY_2_WIDTH                10U
#define LPDDR4__PHY_RDDQ2_SLAVE_DELAY_2__REG DENALI_PHY_601
#define LPDDR4__PHY_RDDQ2_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_601__PHY_RDDQ2_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_601__PHY_RDDQ3_SLAVE_DELAY_2_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_601__PHY_RDDQ3_SLAVE_DELAY_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_601__PHY_RDDQ3_SLAVE_DELAY_2_WIDTH                10U
#define LPDDR4__PHY_RDDQ3_SLAVE_DELAY_2__REG DENALI_PHY_601
#define LPDDR4__PHY_RDDQ3_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_601__PHY_RDDQ3_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_602_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_602_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_602__PHY_RDDQ4_SLAVE_DELAY_2_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_602__PHY_RDDQ4_SLAVE_DELAY_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_602__PHY_RDDQ4_SLAVE_DELAY_2_WIDTH                10U
#define LPDDR4__PHY_RDDQ4_SLAVE_DELAY_2__REG DENALI_PHY_602
#define LPDDR4__PHY_RDDQ4_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_602__PHY_RDDQ4_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_602__PHY_RDDQ5_SLAVE_DELAY_2_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_602__PHY_RDDQ5_SLAVE_DELAY_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_602__PHY_RDDQ5_SLAVE_DELAY_2_WIDTH                10U
#define LPDDR4__PHY_RDDQ5_SLAVE_DELAY_2__REG DENALI_PHY_602
#define LPDDR4__PHY_RDDQ5_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_602__PHY_RDDQ5_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_603_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_603_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_603__PHY_RDDQ6_SLAVE_DELAY_2_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_603__PHY_RDDQ6_SLAVE_DELAY_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_603__PHY_RDDQ6_SLAVE_DELAY_2_WIDTH                10U
#define LPDDR4__PHY_RDDQ6_SLAVE_DELAY_2__REG DENALI_PHY_603
#define LPDDR4__PHY_RDDQ6_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_603__PHY_RDDQ6_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_603__PHY_RDDQ7_SLAVE_DELAY_2_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_603__PHY_RDDQ7_SLAVE_DELAY_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_603__PHY_RDDQ7_SLAVE_DELAY_2_WIDTH                10U
#define LPDDR4__PHY_RDDQ7_SLAVE_DELAY_2__REG DENALI_PHY_603
#define LPDDR4__PHY_RDDQ7_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_603__PHY_RDDQ7_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_604_READ_MASK                             0x1F0703FFU
#define LPDDR4__DENALI_PHY_604_WRITE_MASK                            0x1F0703FFU
#define LPDDR4__DENALI_PHY_604__PHY_RDDM_SLAVE_DELAY_2_MASK          0x000003FFU
#define LPDDR4__DENALI_PHY_604__PHY_RDDM_SLAVE_DELAY_2_SHIFT                  0U
#define LPDDR4__DENALI_PHY_604__PHY_RDDM_SLAVE_DELAY_2_WIDTH                 10U
#define LPDDR4__PHY_RDDM_SLAVE_DELAY_2__REG DENALI_PHY_604
#define LPDDR4__PHY_RDDM_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_604__PHY_RDDM_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_604__PHY_RX_PCLK_CLK_SEL_2_MASK           0x00070000U
#define LPDDR4__DENALI_PHY_604__PHY_RX_PCLK_CLK_SEL_2_SHIFT                  16U
#define LPDDR4__DENALI_PHY_604__PHY_RX_PCLK_CLK_SEL_2_WIDTH                   3U
#define LPDDR4__PHY_RX_PCLK_CLK_SEL_2__REG DENALI_PHY_604
#define LPDDR4__PHY_RX_PCLK_CLK_SEL_2__FLD LPDDR4__DENALI_PHY_604__PHY_RX_PCLK_CLK_SEL_2

#define LPDDR4__DENALI_PHY_604__PHY_RX_CAL_ALL_DLY_2_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_604__PHY_RX_CAL_ALL_DLY_2_SHIFT                   24U
#define LPDDR4__DENALI_PHY_604__PHY_RX_CAL_ALL_DLY_2_WIDTH                    5U
#define LPDDR4__PHY_RX_CAL_ALL_DLY_2__REG DENALI_PHY_604
#define LPDDR4__PHY_RX_CAL_ALL_DLY_2__FLD LPDDR4__DENALI_PHY_604__PHY_RX_CAL_ALL_DLY_2

#define LPDDR4__DENALI_PHY_605_READ_MASK                             0x00000007U
#define LPDDR4__DENALI_PHY_605_WRITE_MASK                            0x00000007U
#define LPDDR4__DENALI_PHY_605__PHY_DATA_DC_CAL_CLK_SEL_2_MASK       0x00000007U
#define LPDDR4__DENALI_PHY_605__PHY_DATA_DC_CAL_CLK_SEL_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_605__PHY_DATA_DC_CAL_CLK_SEL_2_WIDTH               3U
#define LPDDR4__PHY_DATA_DC_CAL_CLK_SEL_2__REG DENALI_PHY_605
#define LPDDR4__PHY_DATA_DC_CAL_CLK_SEL_2__FLD LPDDR4__DENALI_PHY_605__PHY_DATA_DC_CAL_CLK_SEL_2

#define LPDDR4__DENALI_PHY_606_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_606_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_606__PHY_DQ_OE_TIMING_2_MASK              0x000000FFU
#define LPDDR4__DENALI_PHY_606__PHY_DQ_OE_TIMING_2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_606__PHY_DQ_OE_TIMING_2_WIDTH                      8U
#define LPDDR4__PHY_DQ_OE_TIMING_2__REG DENALI_PHY_606
#define LPDDR4__PHY_DQ_OE_TIMING_2__FLD LPDDR4__DENALI_PHY_606__PHY_DQ_OE_TIMING_2

#define LPDDR4__DENALI_PHY_606__PHY_DQ_TSEL_RD_TIMING_2_MASK         0x0000FF00U
#define LPDDR4__DENALI_PHY_606__PHY_DQ_TSEL_RD_TIMING_2_SHIFT                 8U
#define LPDDR4__DENALI_PHY_606__PHY_DQ_TSEL_RD_TIMING_2_WIDTH                 8U
#define LPDDR4__PHY_DQ_TSEL_RD_TIMING_2__REG DENALI_PHY_606
#define LPDDR4__PHY_DQ_TSEL_RD_TIMING_2__FLD LPDDR4__DENALI_PHY_606__PHY_DQ_TSEL_RD_TIMING_2

#define LPDDR4__DENALI_PHY_606__PHY_DQ_TSEL_WR_TIMING_2_MASK         0x00FF0000U
#define LPDDR4__DENALI_PHY_606__PHY_DQ_TSEL_WR_TIMING_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_606__PHY_DQ_TSEL_WR_TIMING_2_WIDTH                 8U
#define LPDDR4__PHY_DQ_TSEL_WR_TIMING_2__REG DENALI_PHY_606
#define LPDDR4__PHY_DQ_TSEL_WR_TIMING_2__FLD LPDDR4__DENALI_PHY_606__PHY_DQ_TSEL_WR_TIMING_2

#define LPDDR4__DENALI_PHY_606__PHY_DQS_OE_TIMING_2_MASK             0xFF000000U
#define LPDDR4__DENALI_PHY_606__PHY_DQS_OE_TIMING_2_SHIFT                    24U
#define LPDDR4__DENALI_PHY_606__PHY_DQS_OE_TIMING_2_WIDTH                     8U
#define LPDDR4__PHY_DQS_OE_TIMING_2__REG DENALI_PHY_606
#define LPDDR4__PHY_DQS_OE_TIMING_2__FLD LPDDR4__DENALI_PHY_606__PHY_DQS_OE_TIMING_2

#define LPDDR4__DENALI_PHY_607_READ_MASK                             0xFFFFFF0FU
#define LPDDR4__DENALI_PHY_607_WRITE_MASK                            0xFFFFFF0FU
#define LPDDR4__DENALI_PHY_607__PHY_IO_PAD_DELAY_TIMING_2_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_607__PHY_IO_PAD_DELAY_TIMING_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_607__PHY_IO_PAD_DELAY_TIMING_2_WIDTH               4U
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_2__REG DENALI_PHY_607
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_2__FLD LPDDR4__DENALI_PHY_607__PHY_IO_PAD_DELAY_TIMING_2

#define LPDDR4__DENALI_PHY_607__PHY_DQS_TSEL_RD_TIMING_2_MASK        0x0000FF00U
#define LPDDR4__DENALI_PHY_607__PHY_DQS_TSEL_RD_TIMING_2_SHIFT                8U
#define LPDDR4__DENALI_PHY_607__PHY_DQS_TSEL_RD_TIMING_2_WIDTH                8U
#define LPDDR4__PHY_DQS_TSEL_RD_TIMING_2__REG DENALI_PHY_607
#define LPDDR4__PHY_DQS_TSEL_RD_TIMING_2__FLD LPDDR4__DENALI_PHY_607__PHY_DQS_TSEL_RD_TIMING_2

#define LPDDR4__DENALI_PHY_607__PHY_DQS_OE_RD_TIMING_2_MASK          0x00FF0000U
#define LPDDR4__DENALI_PHY_607__PHY_DQS_OE_RD_TIMING_2_SHIFT                 16U
#define LPDDR4__DENALI_PHY_607__PHY_DQS_OE_RD_TIMING_2_WIDTH                  8U
#define LPDDR4__PHY_DQS_OE_RD_TIMING_2__REG DENALI_PHY_607
#define LPDDR4__PHY_DQS_OE_RD_TIMING_2__FLD LPDDR4__DENALI_PHY_607__PHY_DQS_OE_RD_TIMING_2

#define LPDDR4__DENALI_PHY_607__PHY_DQS_TSEL_WR_TIMING_2_MASK        0xFF000000U
#define LPDDR4__DENALI_PHY_607__PHY_DQS_TSEL_WR_TIMING_2_SHIFT               24U
#define LPDDR4__DENALI_PHY_607__PHY_DQS_TSEL_WR_TIMING_2_WIDTH                8U
#define LPDDR4__PHY_DQS_TSEL_WR_TIMING_2__REG DENALI_PHY_607
#define LPDDR4__PHY_DQS_TSEL_WR_TIMING_2__FLD LPDDR4__DENALI_PHY_607__PHY_DQS_TSEL_WR_TIMING_2

#define LPDDR4__DENALI_PHY_608_READ_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_608_WRITE_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_608__PHY_VREF_SETTING_TIME_2_MASK         0x0000FFFFU
#define LPDDR4__DENALI_PHY_608__PHY_VREF_SETTING_TIME_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_608__PHY_VREF_SETTING_TIME_2_WIDTH                16U
#define LPDDR4__PHY_VREF_SETTING_TIME_2__REG DENALI_PHY_608
#define LPDDR4__PHY_VREF_SETTING_TIME_2__FLD LPDDR4__DENALI_PHY_608__PHY_VREF_SETTING_TIME_2

#define LPDDR4__DENALI_PHY_608__PHY_PAD_VREF_CTRL_DQ_2_MASK          0x0FFF0000U
#define LPDDR4__DENALI_PHY_608__PHY_PAD_VREF_CTRL_DQ_2_SHIFT                 16U
#define LPDDR4__DENALI_PHY_608__PHY_PAD_VREF_CTRL_DQ_2_WIDTH                 12U
#define LPDDR4__PHY_PAD_VREF_CTRL_DQ_2__REG DENALI_PHY_608
#define LPDDR4__PHY_PAD_VREF_CTRL_DQ_2__FLD LPDDR4__DENALI_PHY_608__PHY_PAD_VREF_CTRL_DQ_2

#define LPDDR4__DENALI_PHY_609_READ_MASK                             0x03FFFF01U
#define LPDDR4__DENALI_PHY_609_WRITE_MASK                            0x03FFFF01U
#define LPDDR4__DENALI_PHY_609__PHY_PER_CS_TRAINING_EN_2_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_609__PHY_PER_CS_TRAINING_EN_2_SHIFT                0U
#define LPDDR4__DENALI_PHY_609__PHY_PER_CS_TRAINING_EN_2_WIDTH                1U
#define LPDDR4__DENALI_PHY_609__PHY_PER_CS_TRAINING_EN_2_WOCLR                0U
#define LPDDR4__DENALI_PHY_609__PHY_PER_CS_TRAINING_EN_2_WOSET                0U
#define LPDDR4__PHY_PER_CS_TRAINING_EN_2__REG DENALI_PHY_609
#define LPDDR4__PHY_PER_CS_TRAINING_EN_2__FLD LPDDR4__DENALI_PHY_609__PHY_PER_CS_TRAINING_EN_2

#define LPDDR4__DENALI_PHY_609__PHY_DQ_IE_TIMING_2_MASK              0x0000FF00U
#define LPDDR4__DENALI_PHY_609__PHY_DQ_IE_TIMING_2_SHIFT                      8U
#define LPDDR4__DENALI_PHY_609__PHY_DQ_IE_TIMING_2_WIDTH                      8U
#define LPDDR4__PHY_DQ_IE_TIMING_2__REG DENALI_PHY_609
#define LPDDR4__PHY_DQ_IE_TIMING_2__FLD LPDDR4__DENALI_PHY_609__PHY_DQ_IE_TIMING_2

#define LPDDR4__DENALI_PHY_609__PHY_DQS_IE_TIMING_2_MASK             0x00FF0000U
#define LPDDR4__DENALI_PHY_609__PHY_DQS_IE_TIMING_2_SHIFT                    16U
#define LPDDR4__DENALI_PHY_609__PHY_DQS_IE_TIMING_2_WIDTH                     8U
#define LPDDR4__PHY_DQS_IE_TIMING_2__REG DENALI_PHY_609
#define LPDDR4__PHY_DQS_IE_TIMING_2__FLD LPDDR4__DENALI_PHY_609__PHY_DQS_IE_TIMING_2

#define LPDDR4__DENALI_PHY_609__PHY_RDDATA_EN_IE_DLY_2_MASK          0x03000000U
#define LPDDR4__DENALI_PHY_609__PHY_RDDATA_EN_IE_DLY_2_SHIFT                 24U
#define LPDDR4__DENALI_PHY_609__PHY_RDDATA_EN_IE_DLY_2_WIDTH                  2U
#define LPDDR4__PHY_RDDATA_EN_IE_DLY_2__REG DENALI_PHY_609
#define LPDDR4__PHY_RDDATA_EN_IE_DLY_2__FLD LPDDR4__DENALI_PHY_609__PHY_RDDATA_EN_IE_DLY_2

#define LPDDR4__DENALI_PHY_610_READ_MASK                             0x1F010303U
#define LPDDR4__DENALI_PHY_610_WRITE_MASK                            0x1F010303U
#define LPDDR4__DENALI_PHY_610__PHY_IE_MODE_2_MASK                   0x00000003U
#define LPDDR4__DENALI_PHY_610__PHY_IE_MODE_2_SHIFT                           0U
#define LPDDR4__DENALI_PHY_610__PHY_IE_MODE_2_WIDTH                           2U
#define LPDDR4__PHY_IE_MODE_2__REG DENALI_PHY_610
#define LPDDR4__PHY_IE_MODE_2__FLD LPDDR4__DENALI_PHY_610__PHY_IE_MODE_2

#define LPDDR4__DENALI_PHY_610__PHY_DBI_MODE_2_MASK                  0x00000300U
#define LPDDR4__DENALI_PHY_610__PHY_DBI_MODE_2_SHIFT                          8U
#define LPDDR4__DENALI_PHY_610__PHY_DBI_MODE_2_WIDTH                          2U
#define LPDDR4__PHY_DBI_MODE_2__REG DENALI_PHY_610
#define LPDDR4__PHY_DBI_MODE_2__FLD LPDDR4__DENALI_PHY_610__PHY_DBI_MODE_2

#define LPDDR4__DENALI_PHY_610__PHY_WDQLVL_IE_ON_2_MASK              0x00010000U
#define LPDDR4__DENALI_PHY_610__PHY_WDQLVL_IE_ON_2_SHIFT                     16U
#define LPDDR4__DENALI_PHY_610__PHY_WDQLVL_IE_ON_2_WIDTH                      1U
#define LPDDR4__DENALI_PHY_610__PHY_WDQLVL_IE_ON_2_WOCLR                      0U
#define LPDDR4__DENALI_PHY_610__PHY_WDQLVL_IE_ON_2_WOSET                      0U
#define LPDDR4__PHY_WDQLVL_IE_ON_2__REG DENALI_PHY_610
#define LPDDR4__PHY_WDQLVL_IE_ON_2__FLD LPDDR4__DENALI_PHY_610__PHY_WDQLVL_IE_ON_2

#define LPDDR4__DENALI_PHY_610__PHY_WDQLVL_RDDATA_EN_DLY_2_MASK      0x1F000000U
#define LPDDR4__DENALI_PHY_610__PHY_WDQLVL_RDDATA_EN_DLY_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_610__PHY_WDQLVL_RDDATA_EN_DLY_2_WIDTH              5U
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_DLY_2__REG DENALI_PHY_610
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_DLY_2__FLD LPDDR4__DENALI_PHY_610__PHY_WDQLVL_RDDATA_EN_DLY_2

#define LPDDR4__DENALI_PHY_611_READ_MASK                             0x0F1F1F1FU
#define LPDDR4__DENALI_PHY_611_WRITE_MASK                            0x0F1F1F1FU
#define LPDDR4__DENALI_PHY_611__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_2_MASK 0x0000001FU
#define LPDDR4__DENALI_PHY_611__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_611__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_2_WIDTH         5U
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_2__REG DENALI_PHY_611
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_2__FLD LPDDR4__DENALI_PHY_611__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_2

#define LPDDR4__DENALI_PHY_611__PHY_RDDATA_EN_TSEL_DLY_2_MASK        0x00001F00U
#define LPDDR4__DENALI_PHY_611__PHY_RDDATA_EN_TSEL_DLY_2_SHIFT                8U
#define LPDDR4__DENALI_PHY_611__PHY_RDDATA_EN_TSEL_DLY_2_WIDTH                5U
#define LPDDR4__PHY_RDDATA_EN_TSEL_DLY_2__REG DENALI_PHY_611
#define LPDDR4__PHY_RDDATA_EN_TSEL_DLY_2__FLD LPDDR4__DENALI_PHY_611__PHY_RDDATA_EN_TSEL_DLY_2

#define LPDDR4__DENALI_PHY_611__PHY_RDDATA_EN_OE_DLY_2_MASK          0x001F0000U
#define LPDDR4__DENALI_PHY_611__PHY_RDDATA_EN_OE_DLY_2_SHIFT                 16U
#define LPDDR4__DENALI_PHY_611__PHY_RDDATA_EN_OE_DLY_2_WIDTH                  5U
#define LPDDR4__PHY_RDDATA_EN_OE_DLY_2__REG DENALI_PHY_611
#define LPDDR4__PHY_RDDATA_EN_OE_DLY_2__FLD LPDDR4__DENALI_PHY_611__PHY_RDDATA_EN_OE_DLY_2

#define LPDDR4__DENALI_PHY_611__PHY_SW_MASTER_MODE_2_MASK            0x0F000000U
#define LPDDR4__DENALI_PHY_611__PHY_SW_MASTER_MODE_2_SHIFT                   24U
#define LPDDR4__DENALI_PHY_611__PHY_SW_MASTER_MODE_2_WIDTH                    4U
#define LPDDR4__PHY_SW_MASTER_MODE_2__REG DENALI_PHY_611
#define LPDDR4__PHY_SW_MASTER_MODE_2__FLD LPDDR4__DENALI_PHY_611__PHY_SW_MASTER_MODE_2

#define LPDDR4__DENALI_PHY_612_READ_MASK                             0xFF3F07FFU
#define LPDDR4__DENALI_PHY_612_WRITE_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_START_2_MASK        0x000007FFU
#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_START_2_SHIFT                0U
#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_START_2_WIDTH               11U
#define LPDDR4__PHY_MASTER_DELAY_START_2__REG DENALI_PHY_612
#define LPDDR4__PHY_MASTER_DELAY_START_2__FLD LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_START_2

#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_STEP_2_MASK         0x003F0000U
#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_STEP_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_STEP_2_WIDTH                 6U
#define LPDDR4__PHY_MASTER_DELAY_STEP_2__REG DENALI_PHY_612
#define LPDDR4__PHY_MASTER_DELAY_STEP_2__FLD LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_STEP_2

#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_WAIT_2_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_WAIT_2_SHIFT                24U
#define LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_WAIT_2_WIDTH                 8U
#define LPDDR4__PHY_MASTER_DELAY_WAIT_2__REG DENALI_PHY_612
#define LPDDR4__PHY_MASTER_DELAY_WAIT_2__FLD LPDDR4__DENALI_PHY_612__PHY_MASTER_DELAY_WAIT_2

#define LPDDR4__DENALI_PHY_613_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_613_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_613__PHY_MASTER_DELAY_HALF_MEASURE_2_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_613__PHY_MASTER_DELAY_HALF_MEASURE_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_613__PHY_MASTER_DELAY_HALF_MEASURE_2_WIDTH         8U
#define LPDDR4__PHY_MASTER_DELAY_HALF_MEASURE_2__REG DENALI_PHY_613
#define LPDDR4__PHY_MASTER_DELAY_HALF_MEASURE_2__FLD LPDDR4__DENALI_PHY_613__PHY_MASTER_DELAY_HALF_MEASURE_2

#define LPDDR4__DENALI_PHY_613__PHY_RPTR_UPDATE_2_MASK               0x00000F00U
#define LPDDR4__DENALI_PHY_613__PHY_RPTR_UPDATE_2_SHIFT                       8U
#define LPDDR4__DENALI_PHY_613__PHY_RPTR_UPDATE_2_WIDTH                       4U
#define LPDDR4__PHY_RPTR_UPDATE_2__REG DENALI_PHY_613
#define LPDDR4__PHY_RPTR_UPDATE_2__FLD LPDDR4__DENALI_PHY_613__PHY_RPTR_UPDATE_2

#define LPDDR4__DENALI_PHY_613__PHY_WRLVL_DLY_STEP_2_MASK            0x00FF0000U
#define LPDDR4__DENALI_PHY_613__PHY_WRLVL_DLY_STEP_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_613__PHY_WRLVL_DLY_STEP_2_WIDTH                    8U
#define LPDDR4__PHY_WRLVL_DLY_STEP_2__REG DENALI_PHY_613
#define LPDDR4__PHY_WRLVL_DLY_STEP_2__FLD LPDDR4__DENALI_PHY_613__PHY_WRLVL_DLY_STEP_2

#define LPDDR4__DENALI_PHY_613__PHY_WRLVL_DLY_FINE_STEP_2_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_613__PHY_WRLVL_DLY_FINE_STEP_2_SHIFT              24U
#define LPDDR4__DENALI_PHY_613__PHY_WRLVL_DLY_FINE_STEP_2_WIDTH               4U
#define LPDDR4__PHY_WRLVL_DLY_FINE_STEP_2__REG DENALI_PHY_613
#define LPDDR4__PHY_WRLVL_DLY_FINE_STEP_2__FLD LPDDR4__DENALI_PHY_613__PHY_WRLVL_DLY_FINE_STEP_2

#define LPDDR4__DENALI_PHY_614_READ_MASK                             0x001F0F3FU
#define LPDDR4__DENALI_PHY_614_WRITE_MASK                            0x001F0F3FU
#define LPDDR4__DENALI_PHY_614__PHY_WRLVL_RESP_WAIT_CNT_2_MASK       0x0000003FU
#define LPDDR4__DENALI_PHY_614__PHY_WRLVL_RESP_WAIT_CNT_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_614__PHY_WRLVL_RESP_WAIT_CNT_2_WIDTH               6U
#define LPDDR4__PHY_WRLVL_RESP_WAIT_CNT_2__REG DENALI_PHY_614
#define LPDDR4__PHY_WRLVL_RESP_WAIT_CNT_2__FLD LPDDR4__DENALI_PHY_614__PHY_WRLVL_RESP_WAIT_CNT_2

#define LPDDR4__DENALI_PHY_614__PHY_GTLVL_DLY_STEP_2_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_614__PHY_GTLVL_DLY_STEP_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_614__PHY_GTLVL_DLY_STEP_2_WIDTH                    4U
#define LPDDR4__PHY_GTLVL_DLY_STEP_2__REG DENALI_PHY_614
#define LPDDR4__PHY_GTLVL_DLY_STEP_2__FLD LPDDR4__DENALI_PHY_614__PHY_GTLVL_DLY_STEP_2

#define LPDDR4__DENALI_PHY_614__PHY_GTLVL_RESP_WAIT_CNT_2_MASK       0x001F0000U
#define LPDDR4__DENALI_PHY_614__PHY_GTLVL_RESP_WAIT_CNT_2_SHIFT              16U
#define LPDDR4__DENALI_PHY_614__PHY_GTLVL_RESP_WAIT_CNT_2_WIDTH               5U
#define LPDDR4__PHY_GTLVL_RESP_WAIT_CNT_2__REG DENALI_PHY_614
#define LPDDR4__PHY_GTLVL_RESP_WAIT_CNT_2__FLD LPDDR4__DENALI_PHY_614__PHY_GTLVL_RESP_WAIT_CNT_2

#define LPDDR4__DENALI_PHY_615_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_615_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_615__PHY_GTLVL_BACK_STEP_2_MASK           0x000003FFU
#define LPDDR4__DENALI_PHY_615__PHY_GTLVL_BACK_STEP_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_615__PHY_GTLVL_BACK_STEP_2_WIDTH                  10U
#define LPDDR4__PHY_GTLVL_BACK_STEP_2__REG DENALI_PHY_615
#define LPDDR4__PHY_GTLVL_BACK_STEP_2__FLD LPDDR4__DENALI_PHY_615__PHY_GTLVL_BACK_STEP_2

#define LPDDR4__DENALI_PHY_615__PHY_GTLVL_FINAL_STEP_2_MASK          0x03FF0000U
#define LPDDR4__DENALI_PHY_615__PHY_GTLVL_FINAL_STEP_2_SHIFT                 16U
#define LPDDR4__DENALI_PHY_615__PHY_GTLVL_FINAL_STEP_2_WIDTH                 10U
#define LPDDR4__PHY_GTLVL_FINAL_STEP_2__REG DENALI_PHY_615
#define LPDDR4__PHY_GTLVL_FINAL_STEP_2__FLD LPDDR4__DENALI_PHY_615__PHY_GTLVL_FINAL_STEP_2

#define LPDDR4__DENALI_PHY_616_READ_MASK                             0x01FF0FFFU
#define LPDDR4__DENALI_PHY_616_WRITE_MASK                            0x01FF0FFFU
#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_DLY_STEP_2_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_DLY_STEP_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_DLY_STEP_2_WIDTH                   8U
#define LPDDR4__PHY_WDQLVL_DLY_STEP_2__REG DENALI_PHY_616
#define LPDDR4__PHY_WDQLVL_DLY_STEP_2__FLD LPDDR4__DENALI_PHY_616__PHY_WDQLVL_DLY_STEP_2

#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_QTR_DLY_STEP_2_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_QTR_DLY_STEP_2_SHIFT               8U
#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_QTR_DLY_STEP_2_WIDTH               4U
#define LPDDR4__PHY_WDQLVL_QTR_DLY_STEP_2__REG DENALI_PHY_616
#define LPDDR4__PHY_WDQLVL_QTR_DLY_STEP_2__FLD LPDDR4__DENALI_PHY_616__PHY_WDQLVL_QTR_DLY_STEP_2

#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_DM_SEARCH_RANGE_2_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_DM_SEARCH_RANGE_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_616__PHY_WDQLVL_DM_SEARCH_RANGE_2_WIDTH            9U
#define LPDDR4__PHY_WDQLVL_DM_SEARCH_RANGE_2__REG DENALI_PHY_616
#define LPDDR4__PHY_WDQLVL_DM_SEARCH_RANGE_2__FLD LPDDR4__DENALI_PHY_616__PHY_WDQLVL_DM_SEARCH_RANGE_2

#define LPDDR4__DENALI_PHY_617_READ_MASK                             0x00000F01U
#define LPDDR4__DENALI_PHY_617_WRITE_MASK                            0x00000F01U
#define LPDDR4__DENALI_PHY_617__PHY_TOGGLE_PRE_SUPPORT_2_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_617__PHY_TOGGLE_PRE_SUPPORT_2_SHIFT                0U
#define LPDDR4__DENALI_PHY_617__PHY_TOGGLE_PRE_SUPPORT_2_WIDTH                1U
#define LPDDR4__DENALI_PHY_617__PHY_TOGGLE_PRE_SUPPORT_2_WOCLR                0U
#define LPDDR4__DENALI_PHY_617__PHY_TOGGLE_PRE_SUPPORT_2_WOSET                0U
#define LPDDR4__PHY_TOGGLE_PRE_SUPPORT_2__REG DENALI_PHY_617
#define LPDDR4__PHY_TOGGLE_PRE_SUPPORT_2__FLD LPDDR4__DENALI_PHY_617__PHY_TOGGLE_PRE_SUPPORT_2

#define LPDDR4__DENALI_PHY_617__PHY_RDLVL_DLY_STEP_2_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_617__PHY_RDLVL_DLY_STEP_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_617__PHY_RDLVL_DLY_STEP_2_WIDTH                    4U
#define LPDDR4__PHY_RDLVL_DLY_STEP_2__REG DENALI_PHY_617
#define LPDDR4__PHY_RDLVL_DLY_STEP_2__FLD LPDDR4__DENALI_PHY_617__PHY_RDLVL_DLY_STEP_2

#define LPDDR4__DENALI_PHY_618_READ_MASK                             0x000003FFU
#define LPDDR4__DENALI_PHY_618_WRITE_MASK                            0x000003FFU
#define LPDDR4__DENALI_PHY_618__PHY_RDLVL_MAX_EDGE_2_MASK            0x000003FFU
#define LPDDR4__DENALI_PHY_618__PHY_RDLVL_MAX_EDGE_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_618__PHY_RDLVL_MAX_EDGE_2_WIDTH                   10U
#define LPDDR4__PHY_RDLVL_MAX_EDGE_2__REG DENALI_PHY_618
#define LPDDR4__PHY_RDLVL_MAX_EDGE_2__FLD LPDDR4__DENALI_PHY_618__PHY_RDLVL_MAX_EDGE_2

#define LPDDR4__DENALI_PHY_619_READ_MASK                             0x00030703U
#define LPDDR4__DENALI_PHY_619_WRITE_MASK                            0x00030703U
#define LPDDR4__DENALI_PHY_619__PHY_WRPATH_GATE_DISABLE_2_MASK       0x00000003U
#define LPDDR4__DENALI_PHY_619__PHY_WRPATH_GATE_DISABLE_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_619__PHY_WRPATH_GATE_DISABLE_2_WIDTH               2U
#define LPDDR4__PHY_WRPATH_GATE_DISABLE_2__REG DENALI_PHY_619
#define LPDDR4__PHY_WRPATH_GATE_DISABLE_2__FLD LPDDR4__DENALI_PHY_619__PHY_WRPATH_GATE_DISABLE_2

#define LPDDR4__DENALI_PHY_619__PHY_WRPATH_GATE_TIMING_2_MASK        0x00000700U
#define LPDDR4__DENALI_PHY_619__PHY_WRPATH_GATE_TIMING_2_SHIFT                8U
#define LPDDR4__DENALI_PHY_619__PHY_WRPATH_GATE_TIMING_2_WIDTH                3U
#define LPDDR4__PHY_WRPATH_GATE_TIMING_2__REG DENALI_PHY_619
#define LPDDR4__PHY_WRPATH_GATE_TIMING_2__FLD LPDDR4__DENALI_PHY_619__PHY_WRPATH_GATE_TIMING_2

#define LPDDR4__DENALI_PHY_619__PHY_DATA_DC_INIT_DISABLE_2_MASK      0x00030000U
#define LPDDR4__DENALI_PHY_619__PHY_DATA_DC_INIT_DISABLE_2_SHIFT             16U
#define LPDDR4__DENALI_PHY_619__PHY_DATA_DC_INIT_DISABLE_2_WIDTH              2U
#define LPDDR4__PHY_DATA_DC_INIT_DISABLE_2__REG DENALI_PHY_619
#define LPDDR4__PHY_DATA_DC_INIT_DISABLE_2__FLD LPDDR4__DENALI_PHY_619__PHY_DATA_DC_INIT_DISABLE_2

#define LPDDR4__DENALI_PHY_620_READ_MASK                             0x07FF03FFU
#define LPDDR4__DENALI_PHY_620_WRITE_MASK                            0x07FF03FFU
#define LPDDR4__DENALI_PHY_620__PHY_DATA_DC_DQS_INIT_SLV_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_620__PHY_DATA_DC_DQS_INIT_SLV_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_620__PHY_DATA_DC_DQS_INIT_SLV_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_DATA_DC_DQS_INIT_SLV_DELAY_2__REG DENALI_PHY_620
#define LPDDR4__PHY_DATA_DC_DQS_INIT_SLV_DELAY_2__FLD LPDDR4__DENALI_PHY_620__PHY_DATA_DC_DQS_INIT_SLV_DELAY_2

#define LPDDR4__DENALI_PHY_620__PHY_DATA_DC_DQ_INIT_SLV_DELAY_2_MASK 0x07FF0000U
#define LPDDR4__DENALI_PHY_620__PHY_DATA_DC_DQ_INIT_SLV_DELAY_2_SHIFT        16U
#define LPDDR4__DENALI_PHY_620__PHY_DATA_DC_DQ_INIT_SLV_DELAY_2_WIDTH        11U
#define LPDDR4__PHY_DATA_DC_DQ_INIT_SLV_DELAY_2__REG DENALI_PHY_620
#define LPDDR4__PHY_DATA_DC_DQ_INIT_SLV_DELAY_2__FLD LPDDR4__DENALI_PHY_620__PHY_DATA_DC_DQ_INIT_SLV_DELAY_2

#define LPDDR4__DENALI_PHY_621_READ_MASK                             0xFFFF0101U
#define LPDDR4__DENALI_PHY_621_WRITE_MASK                            0xFFFF0101U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WRLVL_ENABLE_2_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WRLVL_ENABLE_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WRLVL_ENABLE_2_WIDTH              1U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WRLVL_ENABLE_2_WOCLR              0U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WRLVL_ENABLE_2_WOSET              0U
#define LPDDR4__PHY_DATA_DC_WRLVL_ENABLE_2__REG DENALI_PHY_621
#define LPDDR4__PHY_DATA_DC_WRLVL_ENABLE_2__FLD LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WRLVL_ENABLE_2

#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WDQLVL_ENABLE_2_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WDQLVL_ENABLE_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WDQLVL_ENABLE_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WDQLVL_ENABLE_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WDQLVL_ENABLE_2_WOSET             0U
#define LPDDR4__PHY_DATA_DC_WDQLVL_ENABLE_2__REG DENALI_PHY_621
#define LPDDR4__PHY_DATA_DC_WDQLVL_ENABLE_2__FLD LPDDR4__DENALI_PHY_621__PHY_DATA_DC_WDQLVL_ENABLE_2

#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_DM_CLK_SE_THRSHLD_2_MASK 0x00FF0000U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_DM_CLK_SE_THRSHLD_2_SHIFT        16U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_DM_CLK_SE_THRSHLD_2_WIDTH         8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_SE_THRSHLD_2__REG DENALI_PHY_621
#define LPDDR4__PHY_DATA_DC_DM_CLK_SE_THRSHLD_2__FLD LPDDR4__DENALI_PHY_621__PHY_DATA_DC_DM_CLK_SE_THRSHLD_2

#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_2_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_2_SHIFT      24U
#define LPDDR4__DENALI_PHY_621__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_2_WIDTH       8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_2__REG DENALI_PHY_621
#define LPDDR4__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_2__FLD LPDDR4__DENALI_PHY_621__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_2

#define LPDDR4__DENALI_PHY_622_READ_MASK                             0x001F7F7FU
#define LPDDR4__DENALI_PHY_622_WRITE_MASK                            0x001F7F7FU
#define LPDDR4__DENALI_PHY_622__PHY_WDQ_OSC_DELTA_2_MASK             0x0000007FU
#define LPDDR4__DENALI_PHY_622__PHY_WDQ_OSC_DELTA_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_622__PHY_WDQ_OSC_DELTA_2_WIDTH                     7U
#define LPDDR4__PHY_WDQ_OSC_DELTA_2__REG DENALI_PHY_622
#define LPDDR4__PHY_WDQ_OSC_DELTA_2__FLD LPDDR4__DENALI_PHY_622__PHY_WDQ_OSC_DELTA_2

#define LPDDR4__DENALI_PHY_622__PHY_MEAS_DLY_STEP_ENABLE_2_MASK      0x00007F00U
#define LPDDR4__DENALI_PHY_622__PHY_MEAS_DLY_STEP_ENABLE_2_SHIFT              8U
#define LPDDR4__DENALI_PHY_622__PHY_MEAS_DLY_STEP_ENABLE_2_WIDTH              7U
#define LPDDR4__PHY_MEAS_DLY_STEP_ENABLE_2__REG DENALI_PHY_622
#define LPDDR4__PHY_MEAS_DLY_STEP_ENABLE_2__FLD LPDDR4__DENALI_PHY_622__PHY_MEAS_DLY_STEP_ENABLE_2

#define LPDDR4__DENALI_PHY_622__PHY_RDDATA_EN_DLY_2_MASK             0x001F0000U
#define LPDDR4__DENALI_PHY_622__PHY_RDDATA_EN_DLY_2_SHIFT                    16U
#define LPDDR4__DENALI_PHY_622__PHY_RDDATA_EN_DLY_2_WIDTH                     5U
#define LPDDR4__PHY_RDDATA_EN_DLY_2__REG DENALI_PHY_622
#define LPDDR4__PHY_RDDATA_EN_DLY_2__FLD LPDDR4__DENALI_PHY_622__PHY_RDDATA_EN_DLY_2

#define LPDDR4__DENALI_PHY_623_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_623_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_623__PHY_DQ_DM_SWIZZLE0_2_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_623__PHY_DQ_DM_SWIZZLE0_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_623__PHY_DQ_DM_SWIZZLE0_2_WIDTH                   32U
#define LPDDR4__PHY_DQ_DM_SWIZZLE0_2__REG DENALI_PHY_623
#define LPDDR4__PHY_DQ_DM_SWIZZLE0_2__FLD LPDDR4__DENALI_PHY_623__PHY_DQ_DM_SWIZZLE0_2

#define LPDDR4__DENALI_PHY_624_READ_MASK                             0x0000000FU
#define LPDDR4__DENALI_PHY_624_WRITE_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_624__PHY_DQ_DM_SWIZZLE1_2_MASK            0x0000000FU
#define LPDDR4__DENALI_PHY_624__PHY_DQ_DM_SWIZZLE1_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_624__PHY_DQ_DM_SWIZZLE1_2_WIDTH                    4U
#define LPDDR4__PHY_DQ_DM_SWIZZLE1_2__REG DENALI_PHY_624
#define LPDDR4__PHY_DQ_DM_SWIZZLE1_2__FLD LPDDR4__DENALI_PHY_624__PHY_DQ_DM_SWIZZLE1_2

#define LPDDR4__DENALI_PHY_625_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_625_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_625__PHY_CLK_WRDQ0_SLAVE_DELAY_2_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_625__PHY_CLK_WRDQ0_SLAVE_DELAY_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_625__PHY_CLK_WRDQ0_SLAVE_DELAY_2_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ0_SLAVE_DELAY_2__REG DENALI_PHY_625
#define LPDDR4__PHY_CLK_WRDQ0_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_625__PHY_CLK_WRDQ0_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_625__PHY_CLK_WRDQ1_SLAVE_DELAY_2_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_625__PHY_CLK_WRDQ1_SLAVE_DELAY_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_625__PHY_CLK_WRDQ1_SLAVE_DELAY_2_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ1_SLAVE_DELAY_2__REG DENALI_PHY_625
#define LPDDR4__PHY_CLK_WRDQ1_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_625__PHY_CLK_WRDQ1_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_626_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_626_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_626__PHY_CLK_WRDQ2_SLAVE_DELAY_2_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_626__PHY_CLK_WRDQ2_SLAVE_DELAY_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_626__PHY_CLK_WRDQ2_SLAVE_DELAY_2_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ2_SLAVE_DELAY_2__REG DENALI_PHY_626
#define LPDDR4__PHY_CLK_WRDQ2_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_626__PHY_CLK_WRDQ2_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_626__PHY_CLK_WRDQ3_SLAVE_DELAY_2_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_626__PHY_CLK_WRDQ3_SLAVE_DELAY_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_626__PHY_CLK_WRDQ3_SLAVE_DELAY_2_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ3_SLAVE_DELAY_2__REG DENALI_PHY_626
#define LPDDR4__PHY_CLK_WRDQ3_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_626__PHY_CLK_WRDQ3_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_627_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_627_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_627__PHY_CLK_WRDQ4_SLAVE_DELAY_2_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_627__PHY_CLK_WRDQ4_SLAVE_DELAY_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_627__PHY_CLK_WRDQ4_SLAVE_DELAY_2_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ4_SLAVE_DELAY_2__REG DENALI_PHY_627
#define LPDDR4__PHY_CLK_WRDQ4_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_627__PHY_CLK_WRDQ4_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_627__PHY_CLK_WRDQ5_SLAVE_DELAY_2_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_627__PHY_CLK_WRDQ5_SLAVE_DELAY_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_627__PHY_CLK_WRDQ5_SLAVE_DELAY_2_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ5_SLAVE_DELAY_2__REG DENALI_PHY_627
#define LPDDR4__PHY_CLK_WRDQ5_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_627__PHY_CLK_WRDQ5_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_628_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_628_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_628__PHY_CLK_WRDQ6_SLAVE_DELAY_2_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_628__PHY_CLK_WRDQ6_SLAVE_DELAY_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_628__PHY_CLK_WRDQ6_SLAVE_DELAY_2_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ6_SLAVE_DELAY_2__REG DENALI_PHY_628
#define LPDDR4__PHY_CLK_WRDQ6_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_628__PHY_CLK_WRDQ6_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_628__PHY_CLK_WRDQ7_SLAVE_DELAY_2_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_628__PHY_CLK_WRDQ7_SLAVE_DELAY_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_628__PHY_CLK_WRDQ7_SLAVE_DELAY_2_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ7_SLAVE_DELAY_2__REG DENALI_PHY_628
#define LPDDR4__PHY_CLK_WRDQ7_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_628__PHY_CLK_WRDQ7_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_629_READ_MASK                             0x03FF07FFU
#define LPDDR4__DENALI_PHY_629_WRITE_MASK                            0x03FF07FFU
#define LPDDR4__DENALI_PHY_629__PHY_CLK_WRDM_SLAVE_DELAY_2_MASK      0x000007FFU
#define LPDDR4__DENALI_PHY_629__PHY_CLK_WRDM_SLAVE_DELAY_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_629__PHY_CLK_WRDM_SLAVE_DELAY_2_WIDTH             11U
#define LPDDR4__PHY_CLK_WRDM_SLAVE_DELAY_2__REG DENALI_PHY_629
#define LPDDR4__PHY_CLK_WRDM_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_629__PHY_CLK_WRDM_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_629__PHY_CLK_WRDQS_SLAVE_DELAY_2_MASK     0x03FF0000U
#define LPDDR4__DENALI_PHY_629__PHY_CLK_WRDQS_SLAVE_DELAY_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_629__PHY_CLK_WRDQS_SLAVE_DELAY_2_WIDTH            10U
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_2__REG DENALI_PHY_629
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_629__PHY_CLK_WRDQS_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_630_READ_MASK                             0x0003FF03U
#define LPDDR4__DENALI_PHY_630_WRITE_MASK                            0x0003FF03U
#define LPDDR4__DENALI_PHY_630__PHY_WRLVL_THRESHOLD_ADJUST_2_MASK    0x00000003U
#define LPDDR4__DENALI_PHY_630__PHY_WRLVL_THRESHOLD_ADJUST_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_630__PHY_WRLVL_THRESHOLD_ADJUST_2_WIDTH            2U
#define LPDDR4__PHY_WRLVL_THRESHOLD_ADJUST_2__REG DENALI_PHY_630
#define LPDDR4__PHY_WRLVL_THRESHOLD_ADJUST_2__FLD LPDDR4__DENALI_PHY_630__PHY_WRLVL_THRESHOLD_ADJUST_2

#define LPDDR4__DENALI_PHY_630__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_2_MASK 0x0003FF00U
#define LPDDR4__DENALI_PHY_630__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_2_SHIFT        8U
#define LPDDR4__DENALI_PHY_630__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_2__REG DENALI_PHY_630
#define LPDDR4__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_630__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_631_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_631_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_631__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_631__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_631__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_2__REG DENALI_PHY_631
#define LPDDR4__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_631__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_631__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_631__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_631__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_2__REG DENALI_PHY_631
#define LPDDR4__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_631__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_632_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_632_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_632__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_632__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_632__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_2__REG DENALI_PHY_632
#define LPDDR4__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_632__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_632__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_632__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_632__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_2__REG DENALI_PHY_632
#define LPDDR4__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_632__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_633_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_633_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_633__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_633__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_633__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_2__REG DENALI_PHY_633
#define LPDDR4__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_633__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_633__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_633__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_633__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_2__REG DENALI_PHY_633
#define LPDDR4__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_633__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_634_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_634_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_634__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_634__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_634__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_2__REG DENALI_PHY_634
#define LPDDR4__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_634__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_634__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_634__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_634__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_2__REG DENALI_PHY_634
#define LPDDR4__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_634__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_635_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_635_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_635__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_635__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_635__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_2__REG DENALI_PHY_635
#define LPDDR4__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_635__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_635__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_635__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_635__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_2__REG DENALI_PHY_635
#define LPDDR4__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_635__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_636_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_636_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_636__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_636__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_636__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_2__REG DENALI_PHY_636
#define LPDDR4__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_636__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_636__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_636__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_636__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_2__REG DENALI_PHY_636
#define LPDDR4__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_636__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_637_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_637_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_637__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_637__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_637__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_2__REG DENALI_PHY_637
#define LPDDR4__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_637__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_637__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_637__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_637__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_2__REG DENALI_PHY_637
#define LPDDR4__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_637__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_638_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_638_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_638__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_638__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_638__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_2_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_2__REG DENALI_PHY_638
#define LPDDR4__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_638__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_638__PHY_RDDQS_DM_RISE_SLAVE_DELAY_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_638__PHY_RDDQS_DM_RISE_SLAVE_DELAY_2_SHIFT        16U
#define LPDDR4__DENALI_PHY_638__PHY_RDDQS_DM_RISE_SLAVE_DELAY_2_WIDTH        10U
#define LPDDR4__PHY_RDDQS_DM_RISE_SLAVE_DELAY_2__REG DENALI_PHY_638
#define LPDDR4__PHY_RDDQS_DM_RISE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_638__PHY_RDDQS_DM_RISE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_639_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_639_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_639__PHY_RDDQS_DM_FALL_SLAVE_DELAY_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_639__PHY_RDDQS_DM_FALL_SLAVE_DELAY_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_639__PHY_RDDQS_DM_FALL_SLAVE_DELAY_2_WIDTH        10U
#define LPDDR4__PHY_RDDQS_DM_FALL_SLAVE_DELAY_2__REG DENALI_PHY_639
#define LPDDR4__PHY_RDDQS_DM_FALL_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_639__PHY_RDDQS_DM_FALL_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_639__PHY_RDDQS_GATE_SLAVE_DELAY_2_MASK    0x03FF0000U
#define LPDDR4__DENALI_PHY_639__PHY_RDDQS_GATE_SLAVE_DELAY_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_639__PHY_RDDQS_GATE_SLAVE_DELAY_2_WIDTH           10U
#define LPDDR4__PHY_RDDQS_GATE_SLAVE_DELAY_2__REG DENALI_PHY_639
#define LPDDR4__PHY_RDDQS_GATE_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_639__PHY_RDDQS_GATE_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_640_READ_MASK                             0x03FF070FU
#define LPDDR4__DENALI_PHY_640_WRITE_MASK                            0x03FF070FU
#define LPDDR4__DENALI_PHY_640__PHY_RDDQS_LATENCY_ADJUST_2_MASK      0x0000000FU
#define LPDDR4__DENALI_PHY_640__PHY_RDDQS_LATENCY_ADJUST_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_640__PHY_RDDQS_LATENCY_ADJUST_2_WIDTH              4U
#define LPDDR4__PHY_RDDQS_LATENCY_ADJUST_2__REG DENALI_PHY_640
#define LPDDR4__PHY_RDDQS_LATENCY_ADJUST_2__FLD LPDDR4__DENALI_PHY_640__PHY_RDDQS_LATENCY_ADJUST_2

#define LPDDR4__DENALI_PHY_640__PHY_WRITE_PATH_LAT_ADD_2_MASK        0x00000700U
#define LPDDR4__DENALI_PHY_640__PHY_WRITE_PATH_LAT_ADD_2_SHIFT                8U
#define LPDDR4__DENALI_PHY_640__PHY_WRITE_PATH_LAT_ADD_2_WIDTH                3U
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_2__REG DENALI_PHY_640
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_2__FLD LPDDR4__DENALI_PHY_640__PHY_WRITE_PATH_LAT_ADD_2

#define LPDDR4__DENALI_PHY_640__PHY_WRLVL_DELAY_EARLY_THRESHOLD_2_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_640__PHY_WRLVL_DELAY_EARLY_THRESHOLD_2_SHIFT      16U
#define LPDDR4__DENALI_PHY_640__PHY_WRLVL_DELAY_EARLY_THRESHOLD_2_WIDTH      10U
#define LPDDR4__PHY_WRLVL_DELAY_EARLY_THRESHOLD_2__REG DENALI_PHY_640
#define LPDDR4__PHY_WRLVL_DELAY_EARLY_THRESHOLD_2__FLD LPDDR4__DENALI_PHY_640__PHY_WRLVL_DELAY_EARLY_THRESHOLD_2

#define LPDDR4__DENALI_PHY_641_READ_MASK                             0x000103FFU
#define LPDDR4__DENALI_PHY_641_WRITE_MASK                            0x000103FFU
#define LPDDR4__DENALI_PHY_641__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_641__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_2_SHIFT      0U
#define LPDDR4__DENALI_PHY_641__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_2_WIDTH     10U
#define LPDDR4__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_2__REG DENALI_PHY_641
#define LPDDR4__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_2__FLD LPDDR4__DENALI_PHY_641__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_2

#define LPDDR4__DENALI_PHY_641__PHY_WRLVL_EARLY_FORCE_ZERO_2_MASK    0x00010000U
#define LPDDR4__DENALI_PHY_641__PHY_WRLVL_EARLY_FORCE_ZERO_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_641__PHY_WRLVL_EARLY_FORCE_ZERO_2_WIDTH            1U
#define LPDDR4__DENALI_PHY_641__PHY_WRLVL_EARLY_FORCE_ZERO_2_WOCLR            0U
#define LPDDR4__DENALI_PHY_641__PHY_WRLVL_EARLY_FORCE_ZERO_2_WOSET            0U
#define LPDDR4__PHY_WRLVL_EARLY_FORCE_ZERO_2__REG DENALI_PHY_641
#define LPDDR4__PHY_WRLVL_EARLY_FORCE_ZERO_2__FLD LPDDR4__DENALI_PHY_641__PHY_WRLVL_EARLY_FORCE_ZERO_2

#define LPDDR4__DENALI_PHY_642_READ_MASK                             0x000F03FFU
#define LPDDR4__DENALI_PHY_642_WRITE_MASK                            0x000F03FFU
#define LPDDR4__DENALI_PHY_642__PHY_GTLVL_RDDQS_SLV_DLY_START_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_642__PHY_GTLVL_RDDQS_SLV_DLY_START_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_642__PHY_GTLVL_RDDQS_SLV_DLY_START_2_WIDTH        10U
#define LPDDR4__PHY_GTLVL_RDDQS_SLV_DLY_START_2__REG DENALI_PHY_642
#define LPDDR4__PHY_GTLVL_RDDQS_SLV_DLY_START_2__FLD LPDDR4__DENALI_PHY_642__PHY_GTLVL_RDDQS_SLV_DLY_START_2

#define LPDDR4__DENALI_PHY_642__PHY_GTLVL_LAT_ADJ_START_2_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_642__PHY_GTLVL_LAT_ADJ_START_2_SHIFT              16U
#define LPDDR4__DENALI_PHY_642__PHY_GTLVL_LAT_ADJ_START_2_WIDTH               4U
#define LPDDR4__PHY_GTLVL_LAT_ADJ_START_2__REG DENALI_PHY_642
#define LPDDR4__PHY_GTLVL_LAT_ADJ_START_2__FLD LPDDR4__DENALI_PHY_642__PHY_GTLVL_LAT_ADJ_START_2

#define LPDDR4__DENALI_PHY_643_READ_MASK                             0x010F07FFU
#define LPDDR4__DENALI_PHY_643_WRITE_MASK                            0x010F07FFU
#define LPDDR4__DENALI_PHY_643__PHY_WDQLVL_DQDM_SLV_DLY_START_2_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_643__PHY_WDQLVL_DQDM_SLV_DLY_START_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_643__PHY_WDQLVL_DQDM_SLV_DLY_START_2_WIDTH        11U
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_START_2__REG DENALI_PHY_643
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_START_2__FLD LPDDR4__DENALI_PHY_643__PHY_WDQLVL_DQDM_SLV_DLY_START_2

#define LPDDR4__DENALI_PHY_643__PHY_NTP_WRLAT_START_2_MASK           0x000F0000U
#define LPDDR4__DENALI_PHY_643__PHY_NTP_WRLAT_START_2_SHIFT                  16U
#define LPDDR4__DENALI_PHY_643__PHY_NTP_WRLAT_START_2_WIDTH                   4U
#define LPDDR4__PHY_NTP_WRLAT_START_2__REG DENALI_PHY_643
#define LPDDR4__PHY_NTP_WRLAT_START_2__FLD LPDDR4__DENALI_PHY_643__PHY_NTP_WRLAT_START_2

#define LPDDR4__DENALI_PHY_643__PHY_NTP_PASS_2_MASK                  0x01000000U
#define LPDDR4__DENALI_PHY_643__PHY_NTP_PASS_2_SHIFT                         24U
#define LPDDR4__DENALI_PHY_643__PHY_NTP_PASS_2_WIDTH                          1U
#define LPDDR4__DENALI_PHY_643__PHY_NTP_PASS_2_WOCLR                          0U
#define LPDDR4__DENALI_PHY_643__PHY_NTP_PASS_2_WOSET                          0U
#define LPDDR4__PHY_NTP_PASS_2__REG DENALI_PHY_643
#define LPDDR4__PHY_NTP_PASS_2__FLD LPDDR4__DENALI_PHY_643__PHY_NTP_PASS_2

#define LPDDR4__DENALI_PHY_644_READ_MASK                             0x000003FFU
#define LPDDR4__DENALI_PHY_644_WRITE_MASK                            0x000003FFU
#define LPDDR4__DENALI_PHY_644__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_2_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_644__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_2_SHIFT      0U
#define LPDDR4__DENALI_PHY_644__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_2_WIDTH     10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_2__REG DENALI_PHY_644
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_2__FLD LPDDR4__DENALI_PHY_644__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_2

#define LPDDR4__DENALI_PHY_645_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_645_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQS_CLK_ADJUST_2_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQS_CLK_ADJUST_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQS_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQS_CLK_ADJUST_2__REG DENALI_PHY_645
#define LPDDR4__PHY_DATA_DC_DQS_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQS_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ0_CLK_ADJUST_2_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ0_CLK_ADJUST_2_SHIFT            8U
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ0_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ0_CLK_ADJUST_2__REG DENALI_PHY_645
#define LPDDR4__PHY_DATA_DC_DQ0_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ0_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ1_CLK_ADJUST_2_MASK    0x00FF0000U
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ1_CLK_ADJUST_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ1_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ1_CLK_ADJUST_2__REG DENALI_PHY_645
#define LPDDR4__PHY_DATA_DC_DQ1_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ1_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ2_CLK_ADJUST_2_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ2_CLK_ADJUST_2_SHIFT           24U
#define LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ2_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ2_CLK_ADJUST_2__REG DENALI_PHY_645
#define LPDDR4__PHY_DATA_DC_DQ2_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_645__PHY_DATA_DC_DQ2_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_646_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_646_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ3_CLK_ADJUST_2_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ3_CLK_ADJUST_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ3_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ3_CLK_ADJUST_2__REG DENALI_PHY_646
#define LPDDR4__PHY_DATA_DC_DQ3_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ3_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ4_CLK_ADJUST_2_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ4_CLK_ADJUST_2_SHIFT            8U
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ4_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ4_CLK_ADJUST_2__REG DENALI_PHY_646
#define LPDDR4__PHY_DATA_DC_DQ4_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ4_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ5_CLK_ADJUST_2_MASK    0x00FF0000U
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ5_CLK_ADJUST_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ5_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ5_CLK_ADJUST_2__REG DENALI_PHY_646
#define LPDDR4__PHY_DATA_DC_DQ5_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ5_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ6_CLK_ADJUST_2_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ6_CLK_ADJUST_2_SHIFT           24U
#define LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ6_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ6_CLK_ADJUST_2__REG DENALI_PHY_646
#define LPDDR4__PHY_DATA_DC_DQ6_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_646__PHY_DATA_DC_DQ6_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_647_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_647_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_647__PHY_DATA_DC_DQ7_CLK_ADJUST_2_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_647__PHY_DATA_DC_DQ7_CLK_ADJUST_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_647__PHY_DATA_DC_DQ7_CLK_ADJUST_2_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ7_CLK_ADJUST_2__REG DENALI_PHY_647
#define LPDDR4__PHY_DATA_DC_DQ7_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_647__PHY_DATA_DC_DQ7_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_647__PHY_DATA_DC_DM_CLK_ADJUST_2_MASK     0x0000FF00U
#define LPDDR4__DENALI_PHY_647__PHY_DATA_DC_DM_CLK_ADJUST_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_647__PHY_DATA_DC_DM_CLK_ADJUST_2_WIDTH             8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_ADJUST_2__REG DENALI_PHY_647
#define LPDDR4__PHY_DATA_DC_DM_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_647__PHY_DATA_DC_DM_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_647__PHY_DSLICE_PAD_BOOSTPN_SETTING_2_MASK 0xFFFF0000U
#define LPDDR4__DENALI_PHY_647__PHY_DSLICE_PAD_BOOSTPN_SETTING_2_SHIFT       16U
#define LPDDR4__DENALI_PHY_647__PHY_DSLICE_PAD_BOOSTPN_SETTING_2_WIDTH       16U
#define LPDDR4__PHY_DSLICE_PAD_BOOSTPN_SETTING_2__REG DENALI_PHY_647
#define LPDDR4__PHY_DSLICE_PAD_BOOSTPN_SETTING_2__FLD LPDDR4__DENALI_PHY_647__PHY_DSLICE_PAD_BOOSTPN_SETTING_2

#define LPDDR4__DENALI_PHY_648_READ_MASK                             0x0003033FU
#define LPDDR4__DENALI_PHY_648_WRITE_MASK                            0x0003033FU
#define LPDDR4__DENALI_PHY_648__PHY_DSLICE_PAD_RX_CTLE_SETTING_2_MASK 0x0000003FU
#define LPDDR4__DENALI_PHY_648__PHY_DSLICE_PAD_RX_CTLE_SETTING_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_648__PHY_DSLICE_PAD_RX_CTLE_SETTING_2_WIDTH        6U
#define LPDDR4__PHY_DSLICE_PAD_RX_CTLE_SETTING_2__REG DENALI_PHY_648
#define LPDDR4__PHY_DSLICE_PAD_RX_CTLE_SETTING_2__FLD LPDDR4__DENALI_PHY_648__PHY_DSLICE_PAD_RX_CTLE_SETTING_2

#define LPDDR4__DENALI_PHY_648__PHY_DQ_FFE_2_MASK                    0x00000300U
#define LPDDR4__DENALI_PHY_648__PHY_DQ_FFE_2_SHIFT                            8U
#define LPDDR4__DENALI_PHY_648__PHY_DQ_FFE_2_WIDTH                            2U
#define LPDDR4__PHY_DQ_FFE_2__REG DENALI_PHY_648
#define LPDDR4__PHY_DQ_FFE_2__FLD LPDDR4__DENALI_PHY_648__PHY_DQ_FFE_2

#define LPDDR4__DENALI_PHY_648__PHY_DQS_FFE_2_MASK                   0x00030000U
#define LPDDR4__DENALI_PHY_648__PHY_DQS_FFE_2_SHIFT                          16U
#define LPDDR4__DENALI_PHY_648__PHY_DQS_FFE_2_WIDTH                           2U
#define LPDDR4__PHY_DQS_FFE_2__REG DENALI_PHY_648
#define LPDDR4__PHY_DQS_FFE_2__FLD LPDDR4__DENALI_PHY_648__PHY_DQS_FFE_2

#endif /* REG_LPDDR4_DATA_SLICE_2_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

