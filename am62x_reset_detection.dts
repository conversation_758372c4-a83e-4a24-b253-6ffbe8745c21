/*
 * AM62x Reset Detection Device Tree Binding
 * 
 * 这个设备树片段展示如何在AM62x平台上配置复位检测驱动
 */

/ {
    /* AM62x复位检测节点 */
    am62x_reset_detection: reset-detection@43000000 {
        compatible = "ti,am62x-reset-detection";
        reg = <0x0 0x43000000 0x0 0x20000>;  /* WKUP_CTRL_MMR0_CFG0 基地址和大小 */
        status = "okay";
        
        /* 可选属性 */
        ti,enable-proc-interface;  /* 启用proc文件系统接口 */
        ti,enable-sysfs-interface; /* 启用sysfs接口 */
    };
    
    /* 
     * 或者，如果需要访问多个控制寄存器区域：
     */
    am62x_reset_detection_full: reset-detection-full {
        compatible = "ti,am62x-reset-detection-full";
        reg = <0x0 0x43000000 0x0 0x20000>,  /* WKUP_CTRL_MMR0_CFG0 */
              <0x0 0x04500000 0x0 0x20000>;  /* MCU_CTRL_MMR0_CFG0 */
        reg-names = "wkup-ctrl", "mcu-ctrl";
        status = "disabled";  /* 默认禁用，根据需要启用 */
    };
};

/*
 * 完整的AM62x设备树示例片段
 * 展示如何集成到现有的AM62x设备树中
 */
&main_pmx0 {
    /* 如果需要特定的引脚配置 */
    reset_detection_pins: reset-detection-pins {
        pinctrl-single,pins = <
            /* 这里可以添加相关的引脚配置，如果需要的话 */
        >;
    };
};

/* 
 * 在主SoC节点中添加复位检测
 */
&cbass_main {
    am62x_reset_detection: reset-detection@43000000 {
        compatible = "ti,am62x-reset-detection";
        reg = <0x00 0x43000000 0x00 0x20000>;
        
        /* 中断配置（如果需要） */
        /* interrupts = <GIC_SPI xxx IRQ_TYPE_LEVEL_HIGH>; */
        
        /* 时钟配置（如果需要） */
        /* clocks = <&k3_clks xxx x>; */
        /* clock-names = "fck"; */
        
        /* 电源域配置（如果需要） */
        /* power-domains = <&k3_pds xxx TI_SCI_PD_EXCLUSIVE>; */
        
        status = "okay";
    };
};

/*
 * 用于用户空间访问的示例配置
 * 这允许用户空间程序通过标准的Linux接口访问复位信息
 */
&{/} {
    /* 创建一个符号链接以便于访问 */
    aliases {
        reset-detection = &am62x_reset_detection;
    };
    
    /* 
     * 如果需要在启动时执行特定操作，可以添加启动脚本节点
     */
    chosen {
        /* 可以在这里添加启动参数来控制复位检测行为 */
        bootargs-append = " am62x_reset.debug=1";
    };
};
