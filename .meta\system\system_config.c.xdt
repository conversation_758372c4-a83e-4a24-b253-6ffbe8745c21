/*
 *  Copyright (C) 2021 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
%%{
    let sciclientInitTemplate = '/drivers/sciclient/sciclient_init.c.xdt'
    let sciclientDeInitTemplate = '/drivers/sciclient/sciclient_deinit.c.xdt'
    let common = system.getScript("/common");
    let cpuName = system.getScript('/common').getSelfSysCfgCoreName();
    let socName = system.getScript("/common").getSocName();
%%}
/*
 * Auto generated file
 */

#include "ti_drivers_config.h"
% if (common.isSciClientSupported()) {
    % if((socName.match(/am62x/) && cpuName.match(/r5/)) ||
    %    (socName.match(/am62ax/) && cpuName.match(/^r5/))||
    %    (socName.match(/am62px/) && cpuName.match(/wkup-r5/))){
#include <drivers/device_manager/sciclient.h>
    % } else {
#include <drivers/sciclient.h>
    % }
% }

% for(let subTemplate of args) {
    % if (subTemplate.driver_config) {
`system.getTemplate(subTemplate.driver_config)(subTemplate.moduleName)`
    % }
% }

void Pinmux_init();
void PowerClock_init(void);
void PowerClock_deinit(void);

/*
 * Common Functions
 */
void System_init(void)
{
    /* DPL init sets up address transalation unit, on some CPUs this is needed
     * to access SCICLIENT services, hence this needs to happen first
     */
    Dpl_init();
% if(cpuName.match(/a53/)) {
    if(0 == Armv8_getCoreId())
    {
% }
% if (common.isSciClientSupported()) {
    /* We should do sciclient init before we enable power and clock to the peripherals */
`system.getTemplate(sciclientInitTemplate)()`
% }

   `system.getTemplate("/kernel/dpl/pmu_init.c.xdt")()`

    PowerClock_init();
    /* Now we can do pinmux */
    Pinmux_init();
    /* finally we initialize all peripheral drivers */
% for(let subTemplate of args) {
    % if (subTemplate.driver_init) {
`system.getTemplate(subTemplate.driver_init)()`
    % }
% }
% if(cpuName.match(/a53/)) {
    }

% }
}

void System_deinit(void)
{
% if(cpuName.match(/a53/)) {
    if(0 == Armv8_getCoreId())
    {
% }
% for(let subTemplate of args) {
    % if (subTemplate.driver_deinit) {
`system.getTemplate(subTemplate.driver_deinit)()`
    % }
% }
    PowerClock_deinit();
% if (common.isSciClientSupported()) {
`system.getTemplate(sciclientDeInitTemplate)()`
% }
% if(cpuName.match(/a53/)) {
    }

% }
    Dpl_deinit();
}
