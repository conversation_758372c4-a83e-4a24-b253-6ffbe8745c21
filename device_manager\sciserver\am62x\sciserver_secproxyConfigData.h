/*
 * System Firmware Source File
 *
 * SoC defines for secure proxy configs for AM62X device
 *
 * Data version: 220420_070512
 *
 * Copyright (C) 2021-2022 Texas Instruments Incorporated - http://www.ti.com/
 * ALL RIGHTS RESERVED
 */
#ifndef SOC_AM62X_SPROXY_CONFIG_DATA_H
#define SOC_AM62X_SPROXY_CONFIG_DATA_H

/** Number of Secure Proxy Transmit (Tx) thread configurations */
#define SOC_MAX_SPT_RX_CONFIG_INSTANCES (0x10U)

#define AM62X_DMASS0_SEC_PROXY_0_RX_TIFS_SEC_LOW_PRIORITY_RX_THR075_CONF000 (0x00U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_DM_NONSEC_LOW_PRIORITY_RX_THR069_CONF001 (0x01U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_TIFS_SEC_DM2TIFS_LOW_PRIORITY_RX_THR060_CONF002 (0x02U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_MAIN_0_R5_0_RESPONSE_THR000_CONF003 (0x03U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_MAIN_0_R5_1_RESPONSE_THR002_CONF004 (0x04U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_MAIN_0_R5_2_RESPONSE_THR004_CONF005 (0x05U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_MAIN_0_R5_3_RESPONSE_THR006_CONF006 (0x06U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_A53_0_RESPONSE_THR008_CONF007 (0x07U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_A53_1_RESPONSE_THR010_CONF008 (0x08U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_A53_2_RESPONSE_THR012_CONF009 (0x09U)
#define AM62X_DMASS0_SEC_PROXY_0_RX_A53_3_RESPONSE_THR014_CONF010 (0x0AU)
#define AM62X_DMASS0_SEC_PROXY_0_RX_M4_0_RESPONSE_THR016_CONF011 (0x0BU)
#define AM62X_DMASS0_SEC_PROXY_0_RX_GPU_RESPONSE_THR018_CONF012 (0x0CU)
#define AM62X_DMASS0_SEC_PROXY_0_RX_A53_4_RESPONSE_THR020_CONF013 (0x0DU)
#define AM62X_DMASS0_SEC_PROXY_0_RX_DM2TIFS_RESPONSE_THR022_CONF014 (0x0EU)
#define AM62X_DMASS0_SEC_PROXY_0_RX_TIFS2DM_RESPONSE_THR024_CONF015 (0x0FU)

/** Number of Secure Proxy Transmit (Rx) thread configurations */
#define SOC_MAX_SPT_TX_CONFIG_INSTANCES (0x1AU)

#define AM62X_DMASS0_SEC_PROXY_0_TX_TIFS_SEC_MAIN_0_R5_0_RESPONSE_TX_THR074_CONF000 (0x00U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_TIFS_SEC_MAIN_0_R5_2_RESPONSE_TX_THR073_CONF001 (0x01U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_TIFS_SEC_A53_0_RESPONSE_TX_THR072_CONF002 (0x02U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_TIFS_SEC_A53_1_RESPONSE_TX_THR071_CONF003 (0x03U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_TIFS_SEC_DM2TIFS_RESPONSE_TX_THR070_CONF004 (0x04U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM_NONSEC_MAIN_0_R5_1_RESPONSE_TX_THR068_CONF005 (0x05U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM_NONSEC_MAIN_0_R5_3_RESPONSE_TX_THR067_CONF006 (0x06U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM_NONSEC_A53_2_RESPONSE_TX_THR066_CONF007 (0x07U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM_NONSEC_A53_3_RESPONSE_TX_THR065_CONF008 (0x08U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM_NONSEC_M4_0_RESPONSE_TX_THR064_CONF009 (0x09U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM_NONSEC_GPU_RESPONSE_TX_THR063_CONF010 (0x0AU)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM_NONSEC_A53_4_RESPONSE_TX_THR062_CONF011 (0x0BU)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM_NONSEC_TIFS2DM_RESPONSE_TX_THR061_CONF012 (0x0CU)
#define AM62X_DMASS0_SEC_PROXY_0_TX_MAIN_0_R5_0_LOW_PRIORITY_THR001_CONF013 (0x0DU)
#define AM62X_DMASS0_SEC_PROXY_0_TX_MAIN_0_R5_1_LOW_PRIORITY_THR003_CONF014 (0x0EU)
#define AM62X_DMASS0_SEC_PROXY_0_TX_MAIN_0_R5_2_LOW_PRIORITY_THR005_CONF015 (0x0FU)
#define AM62X_DMASS0_SEC_PROXY_0_TX_MAIN_0_R5_3_LOW_PRIORITY_THR007_CONF016 (0x10U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_A53_0_LOW_PRIORITY_THR009_CONF017 (0x11U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_A53_1_LOW_PRIORITY_THR011_CONF018 (0x12U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_A53_2_LOW_PRIORITY_THR013_CONF019 (0x13U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_A53_3_LOW_PRIORITY_THR015_CONF020 (0x14U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_M4_0_LOW_PRIORITY_THR017_CONF021 (0x15U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_GPU_LOW_PRIORITY_THR019_CONF022 (0x16U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_A53_4_LOW_PRIORITY_THR021_CONF023 (0x17U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_DM2TIFS_LOW_PRIORITY_THR023_CONF024 (0x18U)
#define AM62X_DMASS0_SEC_PROXY_0_TX_TIFS2DM_LOW_PRIORITY_THR025_CONF025 (0x19U)

#endif /* SOC_AM62X_SPROXY_CONFIG_DATA_H */