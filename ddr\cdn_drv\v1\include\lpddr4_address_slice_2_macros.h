/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_ADDRESS_SLICE_2_MACROS_H_
#define REG_LPDDR4_ADDRESS_SLICE_2_MACROS_H_

#define LPDDR4__DENALI_PHY_1536_READ_MASK                            0x000107FFU
#define LPDDR4__DENALI_PHY_1536_WRITE_MASK                           0x000107FFU
#define LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2_SHIFT    0U
#define LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2_WIDTH   11U
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2__REG DENALI_PHY_1536
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_BYPASS_OVERRIDE_2_MASK  0x00010000U
#define LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_BYPASS_OVERRIDE_2_SHIFT         16U
#define LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_BYPASS_OVERRIDE_2_WIDTH          1U
#define LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_BYPASS_OVERRIDE_2_WOCLR          0U
#define LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_BYPASS_OVERRIDE_2_WOSET          0U
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_2__REG DENALI_PHY_1536
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_2__FLD LPDDR4__DENALI_PHY_1536__PHY_ADR_CLK_BYPASS_OVERRIDE_2

#define LPDDR4__DENALI_PHY_1536__SC_PHY_ADR_MANUAL_CLEAR_2_MASK      0x07000000U
#define LPDDR4__DENALI_PHY_1536__SC_PHY_ADR_MANUAL_CLEAR_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_1536__SC_PHY_ADR_MANUAL_CLEAR_2_WIDTH              3U
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_2__REG DENALI_PHY_1536
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_2__FLD LPDDR4__DENALI_PHY_1536__SC_PHY_ADR_MANUAL_CLEAR_2

#define LPDDR4__DENALI_PHY_1537_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1537_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1537__PHY_ADR_LPBK_RESULT_OBS_2_MASK      0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1537__PHY_ADR_LPBK_RESULT_OBS_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_1537__PHY_ADR_LPBK_RESULT_OBS_2_WIDTH             32U
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_2__REG DENALI_PHY_1537
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_2__FLD LPDDR4__DENALI_PHY_1537__PHY_ADR_LPBK_RESULT_OBS_2

#define LPDDR4__DENALI_PHY_1538_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1538_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1538__PHY_ADR_LPBK_ERROR_COUNT_OBS_2_MASK 0x0000FFFFU
#define LPDDR4__DENALI_PHY_1538__PHY_ADR_LPBK_ERROR_COUNT_OBS_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_1538__PHY_ADR_LPBK_ERROR_COUNT_OBS_2_WIDTH        16U
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_2__REG DENALI_PHY_1538
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_2__FLD LPDDR4__DENALI_PHY_1538__PHY_ADR_LPBK_ERROR_COUNT_OBS_2

#define LPDDR4__DENALI_PHY_1538__PHY_ADR_MEAS_DLY_STEP_VALUE_2_MASK  0x00FF0000U
#define LPDDR4__DENALI_PHY_1538__PHY_ADR_MEAS_DLY_STEP_VALUE_2_SHIFT         16U
#define LPDDR4__DENALI_PHY_1538__PHY_ADR_MEAS_DLY_STEP_VALUE_2_WIDTH          8U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_2__REG DENALI_PHY_1538
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_2__FLD LPDDR4__DENALI_PHY_1538__PHY_ADR_MEAS_DLY_STEP_VALUE_2

#define LPDDR4__DENALI_PHY_1538__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2_MASK 0x0F000000U
#define LPDDR4__DENALI_PHY_1538__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2_SHIFT  24U
#define LPDDR4__DENALI_PHY_1538__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2_WIDTH   4U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2__REG DENALI_PHY_1538
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_1538__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_1539_READ_MASK                            0xFF7F07FFU
#define LPDDR4__DENALI_PHY_1539_WRITE_MASK                           0xFF7F07FFU
#define LPDDR4__DENALI_PHY_1539__PHY_ADR_MASTER_DLY_LOCK_OBS_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1539__PHY_ADR_MASTER_DLY_LOCK_OBS_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1539__PHY_ADR_MASTER_DLY_LOCK_OBS_2_WIDTH         11U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_2__REG DENALI_PHY_1539
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_2__FLD LPDDR4__DENALI_PHY_1539__PHY_ADR_MASTER_DLY_LOCK_OBS_2

#define LPDDR4__DENALI_PHY_1539__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2_MASK 0x007F0000U
#define LPDDR4__DENALI_PHY_1539__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2_SHIFT        16U
#define LPDDR4__DENALI_PHY_1539__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2_WIDTH         7U
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_1539
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_1539__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_1539__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_1539__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2_SHIFT       24U
#define LPDDR4__DENALI_PHY_1539__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2_WIDTH        8U
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_1539
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_1539__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_1540_READ_MASK                            0x01000707U
#define LPDDR4__DENALI_PHY_1540_WRITE_MASK                           0x01000707U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2_MASK 0x00000007U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2_WIDTH        3U
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2__REG DENALI_PHY_1540
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2__FLD LPDDR4__DENALI_PHY_1540__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2

#define LPDDR4__DENALI_PHY_1540__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2_MASK 0x00000700U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2_SHIFT       8U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2_WIDTH       3U
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2__REG DENALI_PHY_1540
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_1540__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_1540__SC_PHY_ADR_SNAP_OBS_REGS_2_MASK     0x00010000U
#define LPDDR4__DENALI_PHY_1540__SC_PHY_ADR_SNAP_OBS_REGS_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1540__SC_PHY_ADR_SNAP_OBS_REGS_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_1540__SC_PHY_ADR_SNAP_OBS_REGS_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_1540__SC_PHY_ADR_SNAP_OBS_REGS_2_WOSET             0U
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_2__REG DENALI_PHY_1540
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_2__FLD LPDDR4__DENALI_PHY_1540__SC_PHY_ADR_SNAP_OBS_REGS_2

#define LPDDR4__DENALI_PHY_1540__PHY_ADR_TSEL_ENABLE_2_MASK          0x01000000U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_TSEL_ENABLE_2_SHIFT                 24U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_TSEL_ENABLE_2_WIDTH                  1U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_TSEL_ENABLE_2_WOCLR                  0U
#define LPDDR4__DENALI_PHY_1540__PHY_ADR_TSEL_ENABLE_2_WOSET                  0U
#define LPDDR4__PHY_ADR_TSEL_ENABLE_2__REG DENALI_PHY_1540
#define LPDDR4__PHY_ADR_TSEL_ENABLE_2__FLD LPDDR4__DENALI_PHY_1540__PHY_ADR_TSEL_ENABLE_2

#define LPDDR4__DENALI_PHY_1541_READ_MASK                            0x011F7F7FU
#define LPDDR4__DENALI_PHY_1541_WRITE_MASK                           0x011F7F7FU
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_LPBK_CONTROL_2_MASK         0x0000007FU
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_LPBK_CONTROL_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_LPBK_CONTROL_2_WIDTH                 7U
#define LPDDR4__PHY_ADR_LPBK_CONTROL_2__REG DENALI_PHY_1541
#define LPDDR4__PHY_ADR_LPBK_CONTROL_2__FLD LPDDR4__DENALI_PHY_1541__PHY_ADR_LPBK_CONTROL_2

#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PRBS_PATTERN_START_2_MASK   0x00007F00U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PRBS_PATTERN_START_2_SHIFT           8U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PRBS_PATTERN_START_2_WIDTH           7U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_2__REG DENALI_PHY_1541
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_2__FLD LPDDR4__DENALI_PHY_1541__PHY_ADR_PRBS_PATTERN_START_2

#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PRBS_PATTERN_MASK_2_MASK    0x001F0000U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PRBS_PATTERN_MASK_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PRBS_PATTERN_MASK_2_WIDTH            5U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_2__REG DENALI_PHY_1541
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_2__FLD LPDDR4__DENALI_PHY_1541__PHY_ADR_PRBS_PATTERN_MASK_2

#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PWR_RDC_DISABLE_2_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PWR_RDC_DISABLE_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PWR_RDC_DISABLE_2_WIDTH              1U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PWR_RDC_DISABLE_2_WOCLR              0U
#define LPDDR4__DENALI_PHY_1541__PHY_ADR_PWR_RDC_DISABLE_2_WOSET              0U
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_2__REG DENALI_PHY_1541
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_2__FLD LPDDR4__DENALI_PHY_1541__PHY_ADR_PWR_RDC_DISABLE_2

#define LPDDR4__DENALI_PHY_1542_READ_MASK                            0x01070301U
#define LPDDR4__DENALI_PHY_1542_WRITE_MASK                           0x01070301U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_SHIFT    0U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_WIDTH    1U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_WOCLR    0U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_WOSET    0U
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2__REG DENALI_PHY_1542
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2__FLD LPDDR4__DENALI_PHY_1542__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2

#define LPDDR4__DENALI_PHY_1542__PHY_ADR_TYPE_2_MASK                 0x00000300U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_TYPE_2_SHIFT                         8U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_TYPE_2_WIDTH                         2U
#define LPDDR4__PHY_ADR_TYPE_2__REG DENALI_PHY_1542
#define LPDDR4__PHY_ADR_TYPE_2__FLD LPDDR4__DENALI_PHY_1542__PHY_ADR_TYPE_2

#define LPDDR4__DENALI_PHY_1542__PHY_ADR_WRADDR_SHIFT_OBS_2_MASK     0x00070000U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_WRADDR_SHIFT_OBS_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_WRADDR_SHIFT_OBS_2_WIDTH             3U
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_2__REG DENALI_PHY_1542
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_2__FLD LPDDR4__DENALI_PHY_1542__PHY_ADR_WRADDR_SHIFT_OBS_2

#define LPDDR4__DENALI_PHY_1542__PHY_ADR_IE_MODE_2_MASK              0x01000000U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_IE_MODE_2_SHIFT                     24U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_IE_MODE_2_WIDTH                      1U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_IE_MODE_2_WOCLR                      0U
#define LPDDR4__DENALI_PHY_1542__PHY_ADR_IE_MODE_2_WOSET                      0U
#define LPDDR4__PHY_ADR_IE_MODE_2__REG DENALI_PHY_1542
#define LPDDR4__PHY_ADR_IE_MODE_2__FLD LPDDR4__DENALI_PHY_1542__PHY_ADR_IE_MODE_2

#define LPDDR4__DENALI_PHY_1543_READ_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1543_WRITE_MASK                           0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1543__PHY_ADR_DDL_MODE_2_MASK             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1543__PHY_ADR_DDL_MODE_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1543__PHY_ADR_DDL_MODE_2_WIDTH                    27U
#define LPDDR4__PHY_ADR_DDL_MODE_2__REG DENALI_PHY_1543
#define LPDDR4__PHY_ADR_DDL_MODE_2__FLD LPDDR4__DENALI_PHY_1543__PHY_ADR_DDL_MODE_2

#define LPDDR4__DENALI_PHY_1544_READ_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_1544_WRITE_MASK                           0x0000003FU
#define LPDDR4__DENALI_PHY_1544__PHY_ADR_DDL_MASK_2_MASK             0x0000003FU
#define LPDDR4__DENALI_PHY_1544__PHY_ADR_DDL_MASK_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1544__PHY_ADR_DDL_MASK_2_WIDTH                     6U
#define LPDDR4__PHY_ADR_DDL_MASK_2__REG DENALI_PHY_1544
#define LPDDR4__PHY_ADR_DDL_MASK_2__FLD LPDDR4__DENALI_PHY_1544__PHY_ADR_DDL_MASK_2

#define LPDDR4__DENALI_PHY_1545_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1545_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1545__PHY_ADR_DDL_TEST_OBS_2_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1545__PHY_ADR_DDL_TEST_OBS_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1545__PHY_ADR_DDL_TEST_OBS_2_WIDTH                32U
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_2__REG DENALI_PHY_1545
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_2__FLD LPDDR4__DENALI_PHY_1545__PHY_ADR_DDL_TEST_OBS_2

#define LPDDR4__DENALI_PHY_1546_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1546_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1546__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1546__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_1546__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2_WIDTH       32U
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2__REG DENALI_PHY_1546
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2__FLD LPDDR4__DENALI_PHY_1546__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2

#define LPDDR4__DENALI_PHY_1547_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1547_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1547__PHY_ADR_CALVL_START_2_MASK          0x000007FFU
#define LPDDR4__DENALI_PHY_1547__PHY_ADR_CALVL_START_2_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1547__PHY_ADR_CALVL_START_2_WIDTH                 11U
#define LPDDR4__PHY_ADR_CALVL_START_2__REG DENALI_PHY_1547
#define LPDDR4__PHY_ADR_CALVL_START_2__FLD LPDDR4__DENALI_PHY_1547__PHY_ADR_CALVL_START_2

#define LPDDR4__DENALI_PHY_1547__PHY_ADR_CALVL_COARSE_DLY_2_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_1547__PHY_ADR_CALVL_COARSE_DLY_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1547__PHY_ADR_CALVL_COARSE_DLY_2_WIDTH            11U
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_2__REG DENALI_PHY_1547
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_2__FLD LPDDR4__DENALI_PHY_1547__PHY_ADR_CALVL_COARSE_DLY_2

#define LPDDR4__DENALI_PHY_1548_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1548_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1548__PHY_ADR_CALVL_QTR_2_MASK            0x000007FFU
#define LPDDR4__DENALI_PHY_1548__PHY_ADR_CALVL_QTR_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1548__PHY_ADR_CALVL_QTR_2_WIDTH                   11U
#define LPDDR4__PHY_ADR_CALVL_QTR_2__REG DENALI_PHY_1548
#define LPDDR4__PHY_ADR_CALVL_QTR_2__FLD LPDDR4__DENALI_PHY_1548__PHY_ADR_CALVL_QTR_2

#define LPDDR4__DENALI_PHY_1549_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1549_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1549__PHY_ADR_CALVL_SWIZZLE0_2_MASK       0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1549__PHY_ADR_CALVL_SWIZZLE0_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_1549__PHY_ADR_CALVL_SWIZZLE0_2_WIDTH              24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_2__REG DENALI_PHY_1549
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_2__FLD LPDDR4__DENALI_PHY_1549__PHY_ADR_CALVL_SWIZZLE0_2

#define LPDDR4__DENALI_PHY_1550_READ_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1550_WRITE_MASK                           0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1550__PHY_ADR_CALVL_SWIZZLE1_2_MASK       0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1550__PHY_ADR_CALVL_SWIZZLE1_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_1550__PHY_ADR_CALVL_SWIZZLE1_2_WIDTH              24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_2__REG DENALI_PHY_1550
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_2__FLD LPDDR4__DENALI_PHY_1550__PHY_ADR_CALVL_SWIZZLE1_2

#define LPDDR4__DENALI_PHY_1550__PHY_ADR_CALVL_RANK_CTRL_2_MASK      0x03000000U
#define LPDDR4__DENALI_PHY_1550__PHY_ADR_CALVL_RANK_CTRL_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_1550__PHY_ADR_CALVL_RANK_CTRL_2_WIDTH              2U
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_2__REG DENALI_PHY_1550
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_2__FLD LPDDR4__DENALI_PHY_1550__PHY_ADR_CALVL_RANK_CTRL_2

#define LPDDR4__DENALI_PHY_1551_READ_MASK                            0x01FF0F03U
#define LPDDR4__DENALI_PHY_1551_WRITE_MASK                           0x01FF0F03U
#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_NUM_PATTERNS_2_MASK   0x00000003U
#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_NUM_PATTERNS_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_NUM_PATTERNS_2_WIDTH           2U
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_2__REG DENALI_PHY_1551
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_2__FLD LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_NUM_PATTERNS_2

#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_RESP_WAIT_CNT_2_MASK  0x00000F00U
#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_RESP_WAIT_CNT_2_SHIFT          8U
#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_RESP_WAIT_CNT_2_WIDTH          4U
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_2__REG DENALI_PHY_1551
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_2__FLD LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_RESP_WAIT_CNT_2

#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2_MASK 0x01FF0000U
#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2_SHIFT 16U
#define LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2_WIDTH  9U
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2__REG DENALI_PHY_1551
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2__FLD LPDDR4__DENALI_PHY_1551__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2

#define LPDDR4__DENALI_PHY_1552_READ_MASK                            0x07000001U
#define LPDDR4__DENALI_PHY_1552_WRITE_MASK                           0x07000001U
#define LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_DEBUG_MODE_2_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_DEBUG_MODE_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_DEBUG_MODE_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_DEBUG_MODE_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_DEBUG_MODE_2_WOSET             0U
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_2__REG DENALI_PHY_1552
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_2__FLD LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_DEBUG_MODE_2

#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_DEBUG_CONT_2_MASK  0x00000100U
#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_DEBUG_CONT_2_SHIFT          8U
#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_DEBUG_CONT_2_WIDTH          1U
#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_DEBUG_CONT_2_WOCLR          0U
#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_DEBUG_CONT_2_WOSET          0U
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_2__REG DENALI_PHY_1552
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_2__FLD LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_DEBUG_CONT_2

#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_ERROR_CLR_2_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_ERROR_CLR_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_ERROR_CLR_2_WIDTH           1U
#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_ERROR_CLR_2_WOCLR           0U
#define LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_ERROR_CLR_2_WOSET           0U
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_2__REG DENALI_PHY_1552
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_2__FLD LPDDR4__DENALI_PHY_1552__SC_PHY_ADR_CALVL_ERROR_CLR_2

#define LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_OBS_SELECT_2_MASK     0x07000000U
#define LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_OBS_SELECT_2_SHIFT            24U
#define LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_OBS_SELECT_2_WIDTH             3U
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_2__REG DENALI_PHY_1552
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_1552__PHY_ADR_CALVL_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_1553_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1553_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1553__PHY_ADR_CALVL_CH0_OBS0_2_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1553__PHY_ADR_CALVL_CH0_OBS0_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_1553__PHY_ADR_CALVL_CH0_OBS0_2_WIDTH              32U
#define LPDDR4__PHY_ADR_CALVL_CH0_OBS0_2__REG DENALI_PHY_1553
#define LPDDR4__PHY_ADR_CALVL_CH0_OBS0_2__FLD LPDDR4__DENALI_PHY_1553__PHY_ADR_CALVL_CH0_OBS0_2

#define LPDDR4__DENALI_PHY_1554_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1554_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1554__PHY_ADR_CALVL_CH1_OBS0_2_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1554__PHY_ADR_CALVL_CH1_OBS0_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_1554__PHY_ADR_CALVL_CH1_OBS0_2_WIDTH              32U
#define LPDDR4__PHY_ADR_CALVL_CH1_OBS0_2__REG DENALI_PHY_1554
#define LPDDR4__PHY_ADR_CALVL_CH1_OBS0_2__FLD LPDDR4__DENALI_PHY_1554__PHY_ADR_CALVL_CH1_OBS0_2

#define LPDDR4__DENALI_PHY_1555_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1555_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1555__PHY_ADR_CALVL_OBS1_2_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1555__PHY_ADR_CALVL_OBS1_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1555__PHY_ADR_CALVL_OBS1_2_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS1_2__REG DENALI_PHY_1555
#define LPDDR4__PHY_ADR_CALVL_OBS1_2__FLD LPDDR4__DENALI_PHY_1555__PHY_ADR_CALVL_OBS1_2

#define LPDDR4__DENALI_PHY_1556_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1556_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1556__PHY_ADR_CALVL_OBS2_2_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1556__PHY_ADR_CALVL_OBS2_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1556__PHY_ADR_CALVL_OBS2_2_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS2_2__REG DENALI_PHY_1556
#define LPDDR4__PHY_ADR_CALVL_OBS2_2__FLD LPDDR4__DENALI_PHY_1556__PHY_ADR_CALVL_OBS2_2

#define LPDDR4__DENALI_PHY_1557_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1557_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1557__PHY_ADR_CALVL_FG_0_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1557__PHY_ADR_CALVL_FG_0_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1557__PHY_ADR_CALVL_FG_0_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_0_2__REG DENALI_PHY_1557
#define LPDDR4__PHY_ADR_CALVL_FG_0_2__FLD LPDDR4__DENALI_PHY_1557__PHY_ADR_CALVL_FG_0_2

#define LPDDR4__DENALI_PHY_1558_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1558_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1558__PHY_ADR_CALVL_BG_0_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1558__PHY_ADR_CALVL_BG_0_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1558__PHY_ADR_CALVL_BG_0_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_0_2__REG DENALI_PHY_1558
#define LPDDR4__PHY_ADR_CALVL_BG_0_2__FLD LPDDR4__DENALI_PHY_1558__PHY_ADR_CALVL_BG_0_2

#define LPDDR4__DENALI_PHY_1559_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1559_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1559__PHY_ADR_CALVL_FG_1_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1559__PHY_ADR_CALVL_FG_1_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1559__PHY_ADR_CALVL_FG_1_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_1_2__REG DENALI_PHY_1559
#define LPDDR4__PHY_ADR_CALVL_FG_1_2__FLD LPDDR4__DENALI_PHY_1559__PHY_ADR_CALVL_FG_1_2

#define LPDDR4__DENALI_PHY_1560_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1560_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1560__PHY_ADR_CALVL_BG_1_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1560__PHY_ADR_CALVL_BG_1_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1560__PHY_ADR_CALVL_BG_1_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_1_2__REG DENALI_PHY_1560
#define LPDDR4__PHY_ADR_CALVL_BG_1_2__FLD LPDDR4__DENALI_PHY_1560__PHY_ADR_CALVL_BG_1_2

#define LPDDR4__DENALI_PHY_1561_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1561_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1561__PHY_ADR_CALVL_FG_2_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1561__PHY_ADR_CALVL_FG_2_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1561__PHY_ADR_CALVL_FG_2_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_2_2__REG DENALI_PHY_1561
#define LPDDR4__PHY_ADR_CALVL_FG_2_2__FLD LPDDR4__DENALI_PHY_1561__PHY_ADR_CALVL_FG_2_2

#define LPDDR4__DENALI_PHY_1562_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1562_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1562__PHY_ADR_CALVL_BG_2_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1562__PHY_ADR_CALVL_BG_2_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1562__PHY_ADR_CALVL_BG_2_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_2_2__REG DENALI_PHY_1562
#define LPDDR4__PHY_ADR_CALVL_BG_2_2__FLD LPDDR4__DENALI_PHY_1562__PHY_ADR_CALVL_BG_2_2

#define LPDDR4__DENALI_PHY_1563_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1563_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1563__PHY_ADR_CALVL_FG_3_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1563__PHY_ADR_CALVL_FG_3_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1563__PHY_ADR_CALVL_FG_3_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_3_2__REG DENALI_PHY_1563
#define LPDDR4__PHY_ADR_CALVL_FG_3_2__FLD LPDDR4__DENALI_PHY_1563__PHY_ADR_CALVL_FG_3_2

#define LPDDR4__DENALI_PHY_1564_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1564_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1564__PHY_ADR_CALVL_BG_3_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1564__PHY_ADR_CALVL_BG_3_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1564__PHY_ADR_CALVL_BG_3_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_3_2__REG DENALI_PHY_1564
#define LPDDR4__PHY_ADR_CALVL_BG_3_2__FLD LPDDR4__DENALI_PHY_1564__PHY_ADR_CALVL_BG_3_2

#define LPDDR4__DENALI_PHY_1565_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1565_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1565__PHY_ADR_ADDR_SEL_2_MASK             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1565__PHY_ADR_ADDR_SEL_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1565__PHY_ADR_ADDR_SEL_2_WIDTH                    30U
#define LPDDR4__PHY_ADR_ADDR_SEL_2__REG DENALI_PHY_1565
#define LPDDR4__PHY_ADR_ADDR_SEL_2__FLD LPDDR4__DENALI_PHY_1565__PHY_ADR_ADDR_SEL_2

#define LPDDR4__DENALI_PHY_1566_READ_MASK                            0x3F3F03FFU
#define LPDDR4__DENALI_PHY_1566_WRITE_MASK                           0x3F3F03FFU
#define LPDDR4__DENALI_PHY_1566__PHY_ADR_LP4_BOOT_SLV_DELAY_2_MASK   0x000003FFU
#define LPDDR4__DENALI_PHY_1566__PHY_ADR_LP4_BOOT_SLV_DELAY_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1566__PHY_ADR_LP4_BOOT_SLV_DELAY_2_WIDTH          10U
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_2__REG DENALI_PHY_1566
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_2__FLD LPDDR4__DENALI_PHY_1566__PHY_ADR_LP4_BOOT_SLV_DELAY_2

#define LPDDR4__DENALI_PHY_1566__PHY_ADR_BIT_MASK_2_MASK             0x003F0000U
#define LPDDR4__DENALI_PHY_1566__PHY_ADR_BIT_MASK_2_SHIFT                    16U
#define LPDDR4__DENALI_PHY_1566__PHY_ADR_BIT_MASK_2_WIDTH                     6U
#define LPDDR4__PHY_ADR_BIT_MASK_2__REG DENALI_PHY_1566
#define LPDDR4__PHY_ADR_BIT_MASK_2__FLD LPDDR4__DENALI_PHY_1566__PHY_ADR_BIT_MASK_2

#define LPDDR4__DENALI_PHY_1566__PHY_ADR_SEG_MASK_2_MASK             0x3F000000U
#define LPDDR4__DENALI_PHY_1566__PHY_ADR_SEG_MASK_2_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1566__PHY_ADR_SEG_MASK_2_WIDTH                     6U
#define LPDDR4__PHY_ADR_SEG_MASK_2__REG DENALI_PHY_1566
#define LPDDR4__PHY_ADR_SEG_MASK_2__FLD LPDDR4__DENALI_PHY_1566__PHY_ADR_SEG_MASK_2

#define LPDDR4__DENALI_PHY_1567_READ_MASK                            0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_1567_WRITE_MASK                           0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_CALVL_TRAIN_MASK_2_MASK     0x0000003FU
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_CALVL_TRAIN_MASK_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_CALVL_TRAIN_MASK_2_WIDTH             6U
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_2__REG DENALI_PHY_1567
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_2__FLD LPDDR4__DENALI_PHY_1567__PHY_ADR_CALVL_TRAIN_MASK_2

#define LPDDR4__DENALI_PHY_1567__PHY_ADR_CSLVL_TRAIN_MASK_2_MASK     0x00003F00U
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_CSLVL_TRAIN_MASK_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_CSLVL_TRAIN_MASK_2_WIDTH             6U
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_2__REG DENALI_PHY_1567
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_2__FLD LPDDR4__DENALI_PHY_1567__PHY_ADR_CSLVL_TRAIN_MASK_2

#define LPDDR4__DENALI_PHY_1567__PHY_ADR_STATIC_TOG_DISABLE_2_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_STATIC_TOG_DISABLE_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_STATIC_TOG_DISABLE_2_WIDTH           4U
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_2__REG DENALI_PHY_1567
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_2__FLD LPDDR4__DENALI_PHY_1567__PHY_ADR_STATIC_TOG_DISABLE_2

#define LPDDR4__DENALI_PHY_1567__PHY_ADR_SW_TXIO_CTRL_2_MASK         0x3F000000U
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_SW_TXIO_CTRL_2_SHIFT                24U
#define LPDDR4__DENALI_PHY_1567__PHY_ADR_SW_TXIO_CTRL_2_WIDTH                 6U
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_2__REG DENALI_PHY_1567
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_2__FLD LPDDR4__DENALI_PHY_1567__PHY_ADR_SW_TXIO_CTRL_2

#define LPDDR4__DENALI_PHY_1568_READ_MASK                            0xFFFFFF03U
#define LPDDR4__DENALI_PHY_1568_WRITE_MASK                           0xFFFFFF03U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_INIT_DISABLE_2_MASK      0x00000003U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_INIT_DISABLE_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_INIT_DISABLE_2_WIDTH              2U
#define LPDDR4__PHY_ADR_DC_INIT_DISABLE_2__REG DENALI_PHY_1568
#define LPDDR4__PHY_ADR_DC_INIT_DISABLE_2__FLD LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_INIT_DISABLE_2

#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR0_CLK_ADJUST_2_MASK   0x0000FF00U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR0_CLK_ADJUST_2_SHIFT           8U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR0_CLK_ADJUST_2_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR0_CLK_ADJUST_2__REG DENALI_PHY_1568
#define LPDDR4__PHY_ADR_DC_ADR0_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR0_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR1_CLK_ADJUST_2_MASK   0x00FF0000U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR1_CLK_ADJUST_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR1_CLK_ADJUST_2_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR1_CLK_ADJUST_2__REG DENALI_PHY_1568
#define LPDDR4__PHY_ADR_DC_ADR1_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR1_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR2_CLK_ADJUST_2_MASK   0xFF000000U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR2_CLK_ADJUST_2_SHIFT          24U
#define LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR2_CLK_ADJUST_2_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR2_CLK_ADJUST_2__REG DENALI_PHY_1568
#define LPDDR4__PHY_ADR_DC_ADR2_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_1568__PHY_ADR_DC_ADR2_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_1569_READ_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1569_WRITE_MASK                           0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR3_CLK_ADJUST_2_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR3_CLK_ADJUST_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR3_CLK_ADJUST_2_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR3_CLK_ADJUST_2__REG DENALI_PHY_1569
#define LPDDR4__PHY_ADR_DC_ADR3_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR3_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR4_CLK_ADJUST_2_MASK   0x0000FF00U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR4_CLK_ADJUST_2_SHIFT           8U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR4_CLK_ADJUST_2_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR4_CLK_ADJUST_2__REG DENALI_PHY_1569
#define LPDDR4__PHY_ADR_DC_ADR4_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR4_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR5_CLK_ADJUST_2_MASK   0x00FF0000U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR5_CLK_ADJUST_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR5_CLK_ADJUST_2_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR5_CLK_ADJUST_2__REG DENALI_PHY_1569
#define LPDDR4__PHY_ADR_DC_ADR5_CLK_ADJUST_2__FLD LPDDR4__DENALI_PHY_1569__PHY_ADR_DC_ADR5_CLK_ADJUST_2

#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_2_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_2_SHIFT 24U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_2_WIDTH  1U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_2_WOCLR  0U
#define LPDDR4__DENALI_PHY_1569__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_2_WOSET  0U
#define LPDDR4__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_2__REG DENALI_PHY_1569
#define LPDDR4__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_2__FLD LPDDR4__DENALI_PHY_1569__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_2

#define LPDDR4__DENALI_PHY_1570_READ_MASK                            0x3F03FFFFU
#define LPDDR4__DENALI_PHY_1570_WRITE_MASK                           0x3F03FFFFU
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_CAL_SAMPLE_WAIT_2_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_CAL_SAMPLE_WAIT_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_CAL_SAMPLE_WAIT_2_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_CAL_SAMPLE_WAIT_2__REG DENALI_PHY_1570
#define LPDDR4__PHY_ADR_DC_CAL_SAMPLE_WAIT_2__FLD LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_CAL_SAMPLE_WAIT_2

#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_CAL_TIMEOUT_2_MASK       0x0000FF00U
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_CAL_TIMEOUT_2_SHIFT               8U
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_CAL_TIMEOUT_2_WIDTH               8U
#define LPDDR4__PHY_ADR_DC_CAL_TIMEOUT_2__REG DENALI_PHY_1570
#define LPDDR4__PHY_ADR_DC_CAL_TIMEOUT_2__FLD LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_CAL_TIMEOUT_2

#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_WEIGHT_2_MASK            0x00030000U
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_WEIGHT_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_WEIGHT_2_WIDTH                    2U
#define LPDDR4__PHY_ADR_DC_WEIGHT_2__REG DENALI_PHY_1570
#define LPDDR4__PHY_ADR_DC_WEIGHT_2__FLD LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_WEIGHT_2

#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_ADJUST_START_2_MASK      0x3F000000U
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_ADJUST_START_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_ADJUST_START_2_WIDTH              6U
#define LPDDR4__PHY_ADR_DC_ADJUST_START_2__REG DENALI_PHY_1570
#define LPDDR4__PHY_ADR_DC_ADJUST_START_2__FLD LPDDR4__DENALI_PHY_1570__PHY_ADR_DC_ADJUST_START_2

#define LPDDR4__DENALI_PHY_1571_READ_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_PHY_1571_WRITE_MASK                           0x0101FFFFU
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_SAMPLE_CNT_2_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_SAMPLE_CNT_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_SAMPLE_CNT_2_WIDTH         8U
#define LPDDR4__PHY_ADR_DC_ADJUST_SAMPLE_CNT_2__REG DENALI_PHY_1571
#define LPDDR4__PHY_ADR_DC_ADJUST_SAMPLE_CNT_2__FLD LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_SAMPLE_CNT_2

#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_THRSHLD_2_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_THRSHLD_2_SHIFT            8U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_THRSHLD_2_WIDTH            8U
#define LPDDR4__PHY_ADR_DC_ADJUST_THRSHLD_2__REG DENALI_PHY_1571
#define LPDDR4__PHY_ADR_DC_ADJUST_THRSHLD_2__FLD LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_THRSHLD_2

#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_DIRECT_2_MASK     0x00010000U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_DIRECT_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_DIRECT_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_DIRECT_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_DIRECT_2_WOSET             0U
#define LPDDR4__PHY_ADR_DC_ADJUST_DIRECT_2__REG DENALI_PHY_1571
#define LPDDR4__PHY_ADR_DC_ADJUST_DIRECT_2__FLD LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_ADJUST_DIRECT_2

#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_CAL_POLARITY_2_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_CAL_POLARITY_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_CAL_POLARITY_2_WIDTH              1U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_CAL_POLARITY_2_WOCLR              0U
#define LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_CAL_POLARITY_2_WOSET              0U
#define LPDDR4__PHY_ADR_DC_CAL_POLARITY_2__REG DENALI_PHY_1571
#define LPDDR4__PHY_ADR_DC_CAL_POLARITY_2__FLD LPDDR4__DENALI_PHY_1571__PHY_ADR_DC_CAL_POLARITY_2

#define LPDDR4__DENALI_PHY_1572_READ_MASK                            0x00003F01U
#define LPDDR4__DENALI_PHY_1572_WRITE_MASK                           0x00003F01U
#define LPDDR4__DENALI_PHY_1572__PHY_ADR_DC_CAL_START_2_MASK         0x00000001U
#define LPDDR4__DENALI_PHY_1572__PHY_ADR_DC_CAL_START_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1572__PHY_ADR_DC_CAL_START_2_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1572__PHY_ADR_DC_CAL_START_2_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1572__PHY_ADR_DC_CAL_START_2_WOSET                 0U
#define LPDDR4__PHY_ADR_DC_CAL_START_2__REG DENALI_PHY_1572
#define LPDDR4__PHY_ADR_DC_CAL_START_2__FLD LPDDR4__DENALI_PHY_1572__PHY_ADR_DC_CAL_START_2

#define LPDDR4__DENALI_PHY_1572__PHY_ADR_SW_TXPWR_CTRL_2_MASK        0x00003F00U
#define LPDDR4__DENALI_PHY_1572__PHY_ADR_SW_TXPWR_CTRL_2_SHIFT                8U
#define LPDDR4__DENALI_PHY_1572__PHY_ADR_SW_TXPWR_CTRL_2_WIDTH                6U
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_2__REG DENALI_PHY_1572
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_2__FLD LPDDR4__DENALI_PHY_1572__PHY_ADR_SW_TXPWR_CTRL_2

#define LPDDR4__DENALI_PHY_1573_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1573_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1573__PHY_ADR_TSEL_SELECT_2_MASK          0x000000FFU
#define LPDDR4__DENALI_PHY_1573__PHY_ADR_TSEL_SELECT_2_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1573__PHY_ADR_TSEL_SELECT_2_WIDTH                  8U
#define LPDDR4__PHY_ADR_TSEL_SELECT_2__REG DENALI_PHY_1573
#define LPDDR4__PHY_ADR_TSEL_SELECT_2__FLD LPDDR4__DENALI_PHY_1573__PHY_ADR_TSEL_SELECT_2

#define LPDDR4__DENALI_PHY_1573__PHY_ADR_DC_CAL_CLK_SEL_2_MASK       0x00000700U
#define LPDDR4__DENALI_PHY_1573__PHY_ADR_DC_CAL_CLK_SEL_2_SHIFT               8U
#define LPDDR4__DENALI_PHY_1573__PHY_ADR_DC_CAL_CLK_SEL_2_WIDTH               3U
#define LPDDR4__PHY_ADR_DC_CAL_CLK_SEL_2__REG DENALI_PHY_1573
#define LPDDR4__PHY_ADR_DC_CAL_CLK_SEL_2__FLD LPDDR4__DENALI_PHY_1573__PHY_ADR_DC_CAL_CLK_SEL_2

#define LPDDR4__DENALI_PHY_1573__PHY_PAD_ADR_IO_CFG_2_MASK           0x07FF0000U
#define LPDDR4__DENALI_PHY_1573__PHY_PAD_ADR_IO_CFG_2_SHIFT                  16U
#define LPDDR4__DENALI_PHY_1573__PHY_PAD_ADR_IO_CFG_2_WIDTH                  11U
#define LPDDR4__PHY_PAD_ADR_IO_CFG_2__REG DENALI_PHY_1573
#define LPDDR4__PHY_PAD_ADR_IO_CFG_2__FLD LPDDR4__DENALI_PHY_1573__PHY_PAD_ADR_IO_CFG_2

#define LPDDR4__DENALI_PHY_1574_READ_MASK                            0x07FF1F07U
#define LPDDR4__DENALI_PHY_1574_WRITE_MASK                           0x07FF1F07U
#define LPDDR4__DENALI_PHY_1574__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2_MASK  0x00000007U
#define LPDDR4__DENALI_PHY_1574__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1574__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2_WIDTH          3U
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2__REG DENALI_PHY_1574
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2__FLD LPDDR4__DENALI_PHY_1574__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2

#define LPDDR4__DENALI_PHY_1574__PHY_ADR0_SW_WRADDR_SHIFT_2_MASK     0x00001F00U
#define LPDDR4__DENALI_PHY_1574__PHY_ADR0_SW_WRADDR_SHIFT_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_1574__PHY_ADR0_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1574
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1574__PHY_ADR0_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1574__PHY_ADR0_CLK_WR_SLAVE_DELAY_2_MASK  0x07FF0000U
#define LPDDR4__DENALI_PHY_1574__PHY_ADR0_CLK_WR_SLAVE_DELAY_2_SHIFT         16U
#define LPDDR4__DENALI_PHY_1574__PHY_ADR0_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1574
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1574__PHY_ADR0_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1575_READ_MASK                            0x1F07FF1FU
#define LPDDR4__DENALI_PHY_1575_WRITE_MASK                           0x1F07FF1FU
#define LPDDR4__DENALI_PHY_1575__PHY_ADR1_SW_WRADDR_SHIFT_2_MASK     0x0000001FU
#define LPDDR4__DENALI_PHY_1575__PHY_ADR1_SW_WRADDR_SHIFT_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_1575__PHY_ADR1_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1575
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1575__PHY_ADR1_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1575__PHY_ADR1_CLK_WR_SLAVE_DELAY_2_MASK  0x0007FF00U
#define LPDDR4__DENALI_PHY_1575__PHY_ADR1_CLK_WR_SLAVE_DELAY_2_SHIFT          8U
#define LPDDR4__DENALI_PHY_1575__PHY_ADR1_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1575
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1575__PHY_ADR1_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1575__PHY_ADR2_SW_WRADDR_SHIFT_2_MASK     0x1F000000U
#define LPDDR4__DENALI_PHY_1575__PHY_ADR2_SW_WRADDR_SHIFT_2_SHIFT            24U
#define LPDDR4__DENALI_PHY_1575__PHY_ADR2_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1575
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1575__PHY_ADR2_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1576_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1576_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1576__PHY_ADR2_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1576__PHY_ADR2_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1576__PHY_ADR2_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1576
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1576__PHY_ADR2_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1576__PHY_ADR3_SW_WRADDR_SHIFT_2_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1576__PHY_ADR3_SW_WRADDR_SHIFT_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1576__PHY_ADR3_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1576
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1576__PHY_ADR3_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1577_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1577_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1577__PHY_ADR3_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1577__PHY_ADR3_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1577__PHY_ADR3_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1577
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1577__PHY_ADR3_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1577__PHY_ADR4_SW_WRADDR_SHIFT_2_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1577__PHY_ADR4_SW_WRADDR_SHIFT_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1577__PHY_ADR4_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1577
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1577__PHY_ADR4_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1578_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1578_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1578__PHY_ADR4_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1578__PHY_ADR4_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1578__PHY_ADR4_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1578
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1578__PHY_ADR4_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1578__PHY_ADR5_SW_WRADDR_SHIFT_2_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1578__PHY_ADR5_SW_WRADDR_SHIFT_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1578__PHY_ADR5_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1578
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1578__PHY_ADR5_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1579_READ_MASK                            0x000F07FFU
#define LPDDR4__DENALI_PHY_1579_WRITE_MASK                           0x000F07FFU
#define LPDDR4__DENALI_PHY_1579__PHY_ADR5_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1579__PHY_ADR5_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1579__PHY_ADR5_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1579
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1579__PHY_ADR5_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1579__PHY_ADR_SW_MASTER_MODE_2_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_1579__PHY_ADR_SW_MASTER_MODE_2_SHIFT              16U
#define LPDDR4__DENALI_PHY_1579__PHY_ADR_SW_MASTER_MODE_2_WIDTH               4U
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_2__REG DENALI_PHY_1579
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_2__FLD LPDDR4__DENALI_PHY_1579__PHY_ADR_SW_MASTER_MODE_2

#define LPDDR4__DENALI_PHY_1580_READ_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_1580_WRITE_MASK                           0xFF3F07FFU
#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_START_2_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_START_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_START_2_WIDTH          11U
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_2__REG DENALI_PHY_1580
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_2__FLD LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_START_2

#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_STEP_2_MASK    0x003F0000U
#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_STEP_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_STEP_2_WIDTH            6U
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_2__REG DENALI_PHY_1580
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_2__FLD LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_STEP_2

#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_WAIT_2_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_WAIT_2_SHIFT           24U
#define LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_WAIT_2_WIDTH            8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_2__REG DENALI_PHY_1580
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_2__FLD LPDDR4__DENALI_PHY_1580__PHY_ADR_MASTER_DELAY_WAIT_2

#define LPDDR4__DENALI_PHY_1581_READ_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_1581_WRITE_MASK                           0x0103FFFFU
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2_SHIFT    0U
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2_WIDTH    8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2__REG DENALI_PHY_1581
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2__FLD LPDDR4__DENALI_PHY_1581__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2

#define LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_2_MASK     0x0003FF00U
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_2_WIDTH            10U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_2__REG DENALI_PHY_1581
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_2__FLD LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_2

#define LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_MASK  0x01000000U
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_SHIFT         24U
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_WIDTH          1U
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_WOCLR          0U
#define LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_WOSET          0U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_2__REG DENALI_PHY_1581
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_2__FLD LPDDR4__DENALI_PHY_1581__PHY_ADR_SW_CALVL_DVW_MIN_EN_2

#define LPDDR4__DENALI_PHY_1582_READ_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_1582_WRITE_MASK                           0x0000000FU
#define LPDDR4__DENALI_PHY_1582__PHY_ADR_CALVL_DLY_STEP_2_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_1582__PHY_ADR_CALVL_DLY_STEP_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_1582__PHY_ADR_CALVL_DLY_STEP_2_WIDTH               4U
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_2__REG DENALI_PHY_1582
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_2__FLD LPDDR4__DENALI_PHY_1582__PHY_ADR_CALVL_DLY_STEP_2

#define LPDDR4__DENALI_PHY_1583_READ_MASK                            0x03FF010FU
#define LPDDR4__DENALI_PHY_1583_WRITE_MASK                           0x03FF010FU
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_CALVL_CAPTURE_CNT_2_MASK    0x0000000FU
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_CALVL_CAPTURE_CNT_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_CALVL_CAPTURE_CNT_2_WIDTH            4U
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_2__REG DENALI_PHY_1583
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_2__FLD LPDDR4__DENALI_PHY_1583__PHY_ADR_CALVL_CAPTURE_CNT_2

#define LPDDR4__DENALI_PHY_1583__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_MASK 0x00000100U
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_SHIFT         8U
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_WIDTH         1U
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_WOCLR         0U
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_WOSET         0U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_2__REG DENALI_PHY_1583
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_2__FLD LPDDR4__DENALI_PHY_1583__PHY_ADR_MEAS_DLY_STEP_ENABLE_2

#define LPDDR4__DENALI_PHY_1583__PHY_ADR_DC_INIT_SLV_DELAY_2_MASK    0x03FF0000U
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_DC_INIT_SLV_DELAY_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_1583__PHY_ADR_DC_INIT_SLV_DELAY_2_WIDTH           10U
#define LPDDR4__PHY_ADR_DC_INIT_SLV_DELAY_2__REG DENALI_PHY_1583
#define LPDDR4__PHY_ADR_DC_INIT_SLV_DELAY_2__FLD LPDDR4__DENALI_PHY_1583__PHY_ADR_DC_INIT_SLV_DELAY_2

#define LPDDR4__DENALI_PHY_1584_READ_MASK                            0x0000FF01U
#define LPDDR4__DENALI_PHY_1584_WRITE_MASK                           0x0000FF01U
#define LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_CALVL_ENABLE_2_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_CALVL_ENABLE_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_CALVL_ENABLE_2_WIDTH              1U
#define LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_CALVL_ENABLE_2_WOCLR              0U
#define LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_CALVL_ENABLE_2_WOSET              0U
#define LPDDR4__PHY_ADR_DC_CALVL_ENABLE_2__REG DENALI_PHY_1584
#define LPDDR4__PHY_ADR_DC_CALVL_ENABLE_2__FLD LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_CALVL_ENABLE_2

#define LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_DM_CLK_THRSHLD_2_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_DM_CLK_THRSHLD_2_SHIFT            8U
#define LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_DM_CLK_THRSHLD_2_WIDTH            8U
#define LPDDR4__PHY_ADR_DC_DM_CLK_THRSHLD_2__REG DENALI_PHY_1584
#define LPDDR4__PHY_ADR_DC_DM_CLK_THRSHLD_2__FLD LPDDR4__DENALI_PHY_1584__PHY_ADR_DC_DM_CLK_THRSHLD_2

#endif /* REG_LPDDR4_ADDRESS_SLICE_2_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

