/*
 *  Copyright (C) 2021 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
%%{
    let common = system.getScript("/common");
    let clockEnableTemplate = '/drivers/system/power_clock_module_enable.c.xdt'
    let clockSetFrequencyTemplate = '/drivers/system/power_clock_module_set_frequency.c.xdt'
    let sblClockEnableTemplate = '/drivers/system/power_clock_sbl_module_enable.c.xdt'
    let sblClockSetFrequencyTemplate = '/drivers/system/power_clock_sbl_module_set_frequency.c.xdt'
%%}
/*
 * Auto generated file
 */
#include <kernel/dpl/DebugP.h>
#include <drivers/soc.h>

#define SOC_MODULES_END     (0xFFFFFFFFu)

typedef struct {

    uint32_t moduleId;
    uint32_t clkId;
    uint32_t clkRate;

} SOC_ModuleClockFrequency;

uint32_t gSocModules[] = {
% for(let subTemplate of args) {
    % if (subTemplate.moduleName) {
`system.getTemplate(clockEnableTemplate)(subTemplate.moduleName)`
    % }
% }
    SOC_MODULES_END,
};

SOC_ModuleClockFrequency gSocModulesClockFrequency[] = {
% for(let subTemplate of args) {
    % if (subTemplate.moduleName) {
`system.getTemplate(clockSetFrequencyTemplate)(subTemplate.moduleName)`
    % }
% }
    { SOC_MODULES_END, SOC_MODULES_END, SOC_MODULES_END },
};

% if (common.isDMWithBootSupported()) {
uint32_t gSocSBLModules[] = {
% for(let subTemplate of args) {
    % if (subTemplate.moduleName) {
`system.getTemplate(sblClockEnableTemplate)(subTemplate.moduleName)`
    % }
% }
    SOC_MODULES_END,
};

SOC_ModuleClockFrequency gSocSBLModulesClockFrequency[] = {
% for(let subTemplate of args) {
    % if (subTemplate.moduleName) {
`system.getTemplate(sblClockSetFrequencyTemplate)(subTemplate.moduleName)`
    % }
% }
    { SOC_MODULES_END, SOC_MODULES_END, SOC_MODULES_END },
};
% }

void Module_clockEnable(void)
{
    int32_t status;
    uint32_t i = 0;

    while(gSocModules[i]!=SOC_MODULES_END)
    {
        status = SOC_moduleClockEnable(gSocModules[i], 1);
        DebugP_assertNoLog(status == SystemP_SUCCESS);
        i++;
    }
}

void Module_clockDisable(void)
{
    int32_t status;
    uint32_t i = 0;

    while(gSocModules[i]!=SOC_MODULES_END)
    {
        status = SOC_moduleClockEnable(gSocModules[i], 0);
        DebugP_assertNoLog(status == SystemP_SUCCESS);
        i++;
    }
}

void Module_clockSetFrequency(void)
{
    int32_t status;
    uint32_t i = 0;

    while(gSocModulesClockFrequency[i].moduleId!=SOC_MODULES_END)
    {
        status = SOC_moduleSetClockFrequency(
                    gSocModulesClockFrequency[i].moduleId,
                    gSocModulesClockFrequency[i].clkId,
                    gSocModulesClockFrequency[i].clkRate
                    );
        DebugP_assertNoLog(status == SystemP_SUCCESS);
        i++;
    }
}

% if (common.isDMWithBootSupported()) {
void Module_clockSBLEnable(void)
{
    int32_t status;
    uint32_t i = 0;

    while(gSocSBLModules[i]!=SOC_MODULES_END)
    {
        status = SOC_moduleClockEnable(gSocSBLModules[i], 1);
        DebugP_assertNoLog(status == SystemP_SUCCESS);
        i++;
    }
}

void Module_clockSBLDisable(void)
{
    int32_t status;
    uint32_t i = 0;

    while(gSocSBLModules[i]!=SOC_MODULES_END)
    {
        status = SOC_moduleClockEnable(gSocSBLModules[i], 0);
        DebugP_assertNoLog(status == SystemP_SUCCESS);
        i++;
    }
}

void Module_clockSBLSetFrequency(void)
{
    int32_t status;
    uint32_t i = 0;

    while(gSocSBLModulesClockFrequency[i].moduleId!=SOC_MODULES_END)
    {
        status = SOC_moduleSetClockFrequency(
                    gSocSBLModulesClockFrequency[i].moduleId,
                    gSocSBLModulesClockFrequency[i].clkId,
                    gSocSBLModulesClockFrequency[i].clkRate
                    );
        DebugP_assertNoLog(status == SystemP_SUCCESS);
        i++;
    }
}
%}

void PowerClock_init(void)
{
    Module_clockEnable();
    Module_clockSetFrequency();
}

void PowerClock_deinit()
{
    Module_clockDisable();
}
