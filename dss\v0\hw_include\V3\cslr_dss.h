/********************************************************************
 * Copyright (C) 2023 Texas Instruments Incorporated.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *  Name        : cslr_dss.h
*/
#ifndef CSLR_DSS_H_
#define CSLR_DSS_H_

#ifdef __cplusplus
extern "C"
{
#endif
#include <drivers/hw_include/cslr.h>
#include <stdint.h>

/**************************************************************************
* Hardware Region  : COMMON Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint8_t  Resv_4[4];
    volatile uint32_t DSS_REVISION;              /* DSS_REVISION */
    volatile uint32_t DSS_SYSCONFIG;             /* DSS_SYSCONFIG */
    volatile uint8_t  Resv_32[20];
    volatile uint32_t DSS_SYSSTATUS;             /* DSS_SYSSTATUS */
    volatile uint32_t DISPC_IRQ_EOI;             /* DISPC_IRQ_EOI */
    volatile uint32_t DISPC_IRQSTATUS_RAW;       /* DISPC_IRQSTATUS_RAW */
    volatile uint32_t DISPC_IRQSTATUS;           /* DISPC_IRQSTATUS */
    volatile uint32_t DISPC_IRQENABLE_SET;       /* DISPC_IRQENABLE_SET */
    volatile uint8_t  Resv_64[12];
    volatile uint32_t DISPC_IRQENABLE_CLR;       /* DISPC_IRQENABLE_CLR */
    volatile uint32_t VID_IRQENABLE_0;           /* VID_IRQENABLE_0 */
    volatile uint32_t VID_IRQENABLE_1;           /* VID_IRQENABLE_1 */
    volatile uint8_t  Resv_88[12];
    volatile uint32_t VID_IRQSTATUS_0;           /* VID_IRQSTATUS_0 */
    volatile uint32_t VID_IRQSTATUS_1;           /* VID_IRQSTATUS_1 */
    volatile uint8_t  Resv_112[16];
    volatile uint32_t VP_IRQENABLE_0;            /* VP_IRQENABLE_0 */
    volatile uint32_t VP_IRQENABLE_1;            /* VP_IRQENABLE_1 */
    volatile uint8_t  Resv_124[4];
    volatile uint32_t VP_IRQSTATUS_0;            /* VP_IRQSTATUS_0 */
    volatile uint32_t VP_IRQSTATUS_1;            /* VP_IRQSTATUS_1 */
    volatile uint8_t  Resv_144[12];
    volatile uint32_t DISPC_GLOBAL_MFLAG_ATTRIBUTE;   /* DISPC_GLOBAL_MFLAG_ATTRIBUTE */
    volatile uint32_t DISPC_GLOBAL_OUTPUT_ENABLE;   /* DISPC_GLOBAL_OUTPUT_ENABLE */
    volatile uint32_t DISPC_GLOBAL_BUFFER;       /* DISPC_GLOBAL_BUFFER */
    volatile uint32_t DSS_CBA_CFG;               /* DSS_CBA_CFG */
    volatile uint32_t DISPC_DBG_CONTROL;         /* DISPC_DBG_CONTROL */
    volatile uint32_t DISPC_DBG_STATUS;          /* DISPC_DBG_STATUS */
    volatile uint32_t DISPC_CLKGATING_DISABLE;   /* DISPC_CLKGATING_DISABLE */
    volatile uint32_t DISPC_SECURE_DISABLE;      /* DISPC_SECURE_DISABLE */
} CSL_dss_commonRegs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_DSS_COMMON_DSS_REVISION                                            (0x00000004U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG                                           (0x00000008U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS                                           (0x00000020U)
#define CSL_DSS_COMMON_DISPC_IRQ_EOI                                           (0x00000024U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW                                     (0x00000028U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS                                         (0x0000002CU)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET                                     (0x00000030U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR                                     (0x00000040U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0                                         (0x00000044U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1                                         (0x00000048U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0                                         (0x00000058U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1                                         (0x0000005CU)
#define CSL_DSS_COMMON_VP_IRQENABLE_0                                          (0x00000070U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1                                          (0x00000074U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0                                          (0x0000007CU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1                                          (0x00000080U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE                            (0x00000090U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE                              (0x00000094U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER                                     (0x00000098U)
#define CSL_DSS_COMMON_DSS_CBA_CFG                                             (0x0000009CU)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL                                       (0x000000A0U)
#define CSL_DSS_COMMON_DISPC_DBG_STATUS                                        (0x000000A4U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE                                 (0x000000A8U)
#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE                                    (0x000000ACU)

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* DSS_REVISION */

#define CSL_DSS_COMMON_DSS_REVISION_REVMIN_MASK                                (0x0000003FU)
#define CSL_DSS_COMMON_DSS_REVISION_REVMIN_SHIFT                               (0x00000000U)
#define CSL_DSS_COMMON_DSS_REVISION_REVMIN_MAX                                 (0x0000003FU)

#define CSL_DSS_COMMON_DSS_REVISION_CUSTOM_MASK                                (0x000000C0U)
#define CSL_DSS_COMMON_DSS_REVISION_CUSTOM_SHIFT                               (0x00000006U)
#define CSL_DSS_COMMON_DSS_REVISION_CUSTOM_MAX                                 (0x00000003U)

#define CSL_DSS_COMMON_DSS_REVISION_REVMAJOR_MASK                              (0x00000700U)
#define CSL_DSS_COMMON_DSS_REVISION_REVMAJOR_SHIFT                             (0x00000008U)
#define CSL_DSS_COMMON_DSS_REVISION_REVMAJOR_MAX                               (0x00000007U)

#define CSL_DSS_COMMON_DSS_REVISION_REVRTL_MASK                                (0x0000F800U)
#define CSL_DSS_COMMON_DSS_REVISION_REVRTL_SHIFT                               (0x0000000BU)
#define CSL_DSS_COMMON_DSS_REVISION_REVRTL_MAX                                 (0x0000001FU)

#define CSL_DSS_COMMON_DSS_REVISION_MODID_MASK                                 (0xFFFF0000U)
#define CSL_DSS_COMMON_DSS_REVISION_MODID_SHIFT                                (0x00000010U)
#define CSL_DSS_COMMON_DSS_REVISION_MODID_MAX                                  (0x0000FFFFU)

/* DSS_SYSCONFIG */

#define CSL_DSS_COMMON_DSS_SYSCONFIG_AUTOCLKGATING_MASK                        (0x00000001U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_AUTOCLKGATING_SHIFT                       (0x00000000U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_AUTOCLKGATING_MAX                         (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSCONFIG_AUTOCLKGATING_VAL_CLKFREE                 (0x0U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_AUTOCLKGATING_VAL_CLKGATED                (0x1U)

#define CSL_DSS_COMMON_DSS_SYSCONFIG_SOFTRESET_MASK                            (0x00000002U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_SOFTRESET_SHIFT                           (0x00000001U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_SOFTRESET_MAX                             (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED1_MASK                            (0x00000004U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED1_SHIFT                           (0x00000002U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED1_MAX                             (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSCONFIG_IDLEMODE_MASK                             (0x00000018U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_IDLEMODE_SHIFT                            (0x00000003U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_IDLEMODE_MAX                              (0x00000003U)

#define CSL_DSS_COMMON_DSS_SYSCONFIG_WARMRESET_MASK                            (0x00000020U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_WARMRESET_SHIFT                           (0x00000005U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_WARMRESET_MAX                             (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED2_MASK                            (0x000000C0U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED2_SHIFT                           (0x00000006U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED2_MAX                             (0x00000003U)

#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED3_MASK                            (0x00003F00U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED3_SHIFT                           (0x00000008U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED3_MAX                             (0x0000003FU)

#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED4_MASK                            (0xFFFFC000U)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED4_SHIFT                           (0x0000000EU)
#define CSL_DSS_COMMON_DSS_SYSCONFIG_RESERVED4_MAX                             (0x0003FFFFU)

/* DSS_SYSSTATUS */

#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_FUNC_RESETDONE_MASK                 (0x00000001U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_FUNC_RESETDONE_SHIFT                (0x00000000U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_FUNC_RESETDONE_MAX                  (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_FUNC_RESETDONE_VAL_RSTONGOING       (0x0U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_FUNC_RESETDONE_VAL_RSTCOMP          (0x1U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_VP_RESETDONE_MASK                   (0x00000006U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_VP_RESETDONE_SHIFT                  (0x00000001U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_VP_RESETDONE_MAX                    (0x00000003U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_VP_RESETDONE_VAL_RSTONGOING         (0x0U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_VP_RESETDONE_VAL_RSTCOMP            (0x1U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED1_MASK                            (0x00000010U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED1_SHIFT                           (0x00000004U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED1_MAX                             (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_OLDI_RESETDONE_MASK                       (0x00000020U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_OLDI_RESETDONE_SHIFT                      (0x00000005U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_OLDI_RESETDONE_MAX                        (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_OLDI_RESETDONE_VAL_RSTONGOING             (0x0U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_OLDI_RESETDONE_VAL_RSTCOMP                (0x1U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED2_MASK                            (0x00000100U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED2_SHIFT                           (0x00000008U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED2_MAX                             (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_IDLE_STATUS_MASK                    (0x00000200U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_IDLE_STATUS_SHIFT                   (0x00000009U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_IDLE_STATUS_MAX                     (0x00000001U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_IDLE_STATUS_VAL_NOTIDLE             (0x0U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_DISPC_IDLE_STATUS_VAL_IDLE                (0x1U)

#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED_MASK                             (0xFFFFFC00U)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED_SHIFT                            (0x0000000AU)
#define CSL_DSS_COMMON_DSS_SYSSTATUS_RESERVED_MAX                              (0x003FFFFFU)

/* DISPC_IRQ_EOI */

#define CSL_DSS_COMMON_DISPC_IRQ_EOI_EOI_MASK                                  (0x00000001U)
#define CSL_DSS_COMMON_DISPC_IRQ_EOI_EOI_SHIFT                                 (0x00000000U)
#define CSL_DSS_COMMON_DISPC_IRQ_EOI_EOI_MAX                                   (0x00000001U)

#define CSL_DSS_COMMON_DISPC_IRQ_EOI_EOI_VAL_NOACTION                          (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQ_EOI_EOI_VAL_EOI                               (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQ_EOI_RESERVED_MASK                             (0xFFFFFFFEU)
#define CSL_DSS_COMMON_DISPC_IRQ_EOI_RESERVED_SHIFT                            (0x00000001U)
#define CSL_DSS_COMMON_DISPC_IRQ_EOI_RESERVED_MAX                              (0x7FFFFFFFU)

/* DISPC_IRQSTATUS_RAW */

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VP_IRQ_MASK                         (0x00000003U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VP_IRQ_SHIFT                        (0x00000000U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VP_IRQ_MAX                          (0x00000003U)

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VP_IRQ_VAL_NOACTION                 (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VP_IRQ_VAL_SET_EVENT                (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VID_IRQ_MASK                        (0x00000030U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VID_IRQ_SHIFT                       (0x00000004U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VID_IRQ_MAX                         (0x00000003U)

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VID_IRQ_VAL_NOACTION                (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_VID_IRQ_VAL_SET_EVENT               (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_RESERVED_MASK                       (0xFFFF8000U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_RESERVED_SHIFT                      (0x0000000FU)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RAW_RESERVED_MAX                        (0x0001FFFFU)

/* DISPC_IRQSTATUS */

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VP_IRQ_MASK                             (0x00000003U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VP_IRQ_SHIFT                            (0x00000000U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VP_IRQ_MAX                              (0x00000003U)

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VP_IRQ_VAL_NOACTION                     (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VP_IRQ_VAL_CLEAR                        (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VID_IRQ_MASK                            (0x00000030U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VID_IRQ_SHIFT                           (0x00000004U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VID_IRQ_MAX                             (0x00000003U)

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VID_IRQ_VAL_NOACTION                    (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_VID_IRQ_VAL_CLEAR                       (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RESERVED_MASK                           (0xFFFF8000U)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RESERVED_SHIFT                          (0x0000000FU)
#define CSL_DSS_COMMON_DISPC_IRQSTATUS_RESERVED_MAX                            (0x0001FFFFU)

/* DISPC_IRQENABLE_SET */

#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VP_IRQ_MASK                     (0x00000003U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VP_IRQ_SHIFT                    (0x00000000U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VP_IRQ_MAX                      (0x00000003U)

#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VP_IRQ_VAL_NOACTION             (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VP_IRQ_VAL_ENABLE               (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VID_IRQ_MASK                    (0x00000030U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VID_IRQ_SHIFT                   (0x00000004U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VID_IRQ_MAX                     (0x00000003U)

#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VID_IRQ_VAL_NOACTION            (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_SET_VID_IRQ_VAL_ENABLE              (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_RESERVED_MASK                       (0xFFFF8000U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_RESERVED_SHIFT                      (0x0000000FU)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_SET_RESERVED_MAX                        (0x0001FFFFU)

/* DISPC_IRQENABLE_CLR */

#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_MASK                     (0x00000003U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_SHIFT                    (0x00000000U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_MAX                      (0x00000003U)

#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_VAL_NOACTION             (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_VAL_CLEAR                (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_MASK                    (0x00000030U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_SHIFT                   (0x00000004U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_MAX                     (0x00000003U)

#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_VAL_NOACTION            (0x0U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_VAL_CLEAR               (0x1U)

#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_RESERVED_MASK                       (0xFFFF8000U)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_RESERVED_SHIFT                      (0x0000000FU)
#define CSL_DSS_COMMON_DISPC_IRQENABLE_CLR_RESERVED_MAX                        (0x0001FFFFU)

/* VID_IRQENABLE_0 */

#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_MASK              (0x00000001U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_SHIFT             (0x00000000U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_MAX               (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_VAL_MASKED        (0x0U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_VAL_GENINT        (0x1U)

#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDENDWINDOW_EN_MASK                    (0x00000002U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDENDWINDOW_EN_SHIFT                   (0x00000001U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDENDWINDOW_EN_MAX                     (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDENDWINDOW_EN_VAL_MASKED              (0x0U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_VIDENDWINDOW_EN_VAL_GENINT              (0x1U)

#define CSL_DSS_COMMON_VID_IRQENABLE_0_SAFETYREGION_EN_MASK                    (0x00000004U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_SAFETYREGION_EN_SHIFT                   (0x00000002U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_SAFETYREGION_EN_MAX                     (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQENABLE_0_SAFETYREGION_EN_VAL_MASKED              (0x0U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_SAFETYREGION_EN_VAL_GENINT              (0x1U)

#define CSL_DSS_COMMON_VID_IRQENABLE_0_RESERVED_MASK                           (0xFFFFFFF8U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_RESERVED_SHIFT                          (0x00000003U)
#define CSL_DSS_COMMON_VID_IRQENABLE_0_RESERVED_MAX                            (0x1FFFFFFFU)

/* VID_IRQENABLE_1 */

#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_MASK              (0x00000001U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_SHIFT             (0x00000000U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_MAX               (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_VAL_MASKED        (0x0U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_VAL_GENINT        (0x1U)

#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDENDWINDOW_EN_MASK                    (0x00000002U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDENDWINDOW_EN_SHIFT                   (0x00000001U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDENDWINDOW_EN_MAX                     (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDENDWINDOW_EN_VAL_MASKED              (0x0U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_VIDENDWINDOW_EN_VAL_GENINT              (0x1U)

#define CSL_DSS_COMMON_VID_IRQENABLE_1_SAFETYREGION_EN_MASK                    (0x00000004U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_SAFETYREGION_EN_SHIFT                   (0x00000002U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_SAFETYREGION_EN_MAX                     (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQENABLE_1_SAFETYREGION_EN_VAL_MASKED              (0x0U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_SAFETYREGION_EN_VAL_GENINT              (0x1U)

#define CSL_DSS_COMMON_VID_IRQENABLE_1_RESERVED_MASK                           (0xFFFFFFF8U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_RESERVED_SHIFT                          (0x00000003U)
#define CSL_DSS_COMMON_VID_IRQENABLE_1_RESERVED_MAX                            (0x1FFFFFFFU)

/* VID_IRQSTATUS_0 */

#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_MASK             (0x00000001U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_SHIFT            (0x00000000U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_MAX              (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_VAL_NOPEND       (0x0U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_VAL_PEND         (0x1U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_MASK                   (0x00000002U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_SHIFT                  (0x00000001U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_MAX                    (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_VAL_NOPEND             (0x0U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_VAL_PEND               (0x1U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_0_SAFETYREGION_IRQ_MASK                   (0x00000004U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_SAFETYREGION_IRQ_SHIFT                  (0x00000002U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_SAFETYREGION_IRQ_MAX                    (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_0_SAFETYREGION_IRQ_VAL_NOPEND             (0x0U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_SAFETYREGION_IRQ_VAL_PEND               (0x1U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_0_RESERVED_MASK                           (0xFFFFFFF8U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_RESERVED_SHIFT                          (0x00000003U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_0_RESERVED_MAX                            (0x1FFFFFFFU)

/* VID_IRQSTATUS_1 */

#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_MASK             (0x00000001U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_SHIFT            (0x00000000U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_MAX              (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_VAL_NOPEND       (0x0U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_VAL_PEND         (0x1U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_MASK                   (0x00000002U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_SHIFT                  (0x00000001U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_MAX                    (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_VAL_NOPEND             (0x0U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_VAL_PEND               (0x1U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_1_SAFETYREGION_IRQ_MASK                   (0x00000004U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_SAFETYREGION_IRQ_SHIFT                  (0x00000002U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_SAFETYREGION_IRQ_MAX                    (0x00000001U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_1_SAFETYREGION_IRQ_VAL_NOPEND             (0x0U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_SAFETYREGION_IRQ_VAL_PEND               (0x1U)

#define CSL_DSS_COMMON_VID_IRQSTATUS_1_RESERVED_MASK                           (0xFFFFFFF8U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_RESERVED_SHIFT                          (0x00000003U)
#define CSL_DSS_COMMON_VID_IRQSTATUS_1_RESERVED_MAX                            (0x1FFFFFFFU)

/* VP_IRQENABLE_0 */

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPFRAMEDONE_EN_MASK                      (0x00000001U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPFRAMEDONE_EN_SHIFT                     (0x00000000U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPFRAMEDONE_EN_MAX                       (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPFRAMEDONE_EN_VAL_MASKED                (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPFRAMEDONE_EN_VAL_GENINT                (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_EN_MASK                          (0x00000002U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_EN_SHIFT                         (0x00000001U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_EN_MAX                           (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_EN_VAL_MASKED                    (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_EN_VAL_GENINT                    (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_ODD_EN_MASK                      (0x00000004U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_ODD_EN_SHIFT                     (0x00000002U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_ODD_EN_MAX                       (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_ODD_EN_VAL_MASKED                (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPVSYNC_ODD_EN_VAL_GENINT                (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_MASK           (0x00000008U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_SHIFT          (0x00000003U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_MAX            (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_VAL_MASKED     (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_VAL_GENINT     (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNCLOST_EN_MASK                       (0x00000010U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNCLOST_EN_SHIFT                      (0x00000004U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNCLOST_EN_MAX                        (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNCLOST_EN_VAL_MASKED                 (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNCLOST_EN_VAL_GENINT                 (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_MASK                (0x00000020U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_SHIFT               (0x00000005U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_MAX                 (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_VAL_MASKED          (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_VAL_GENINT          (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_SAFETYREGION_EN_MASK                     (0x000003C0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_SAFETYREGION_EN_SHIFT                    (0x00000006U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_SAFETYREGION_EN_MAX                      (0x0000000FU)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_SAFETYREGION_EN_VAL_MASKED               (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_SAFETYREGION_EN_VAL_GENINT               (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_SECURITYVIOLATION_EN_MASK                (0x00000400U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_SECURITYVIOLATION_EN_SHIFT               (0x0000000AU)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_SECURITYVIOLATION_EN_MAX                 (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_SECURITYVIOLATION_EN_VAL_MASKED          (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_SECURITYVIOLATION_EN_VAL_GENINT          (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNC_EN_MASK                           (0x00000800U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNC_EN_SHIFT                          (0x0000000BU)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNC_EN_MAX                            (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNC_EN_VAL_MASKED                     (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_VPSYNC_EN_VAL_GENINT                     (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_DUMMY_EN_MASK                            (0x00001000U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_DUMMY_EN_SHIFT                           (0x0000000CU)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_DUMMY_EN_MAX                             (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_DUMMY_EN_VAL_MASKED                      (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_DUMMY_EN_VAL_GENINT                      (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_0_RESERVED_MASK                            (0xFFFFE000U)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_RESERVED_SHIFT                           (0x0000000DU)
#define CSL_DSS_COMMON_VP_IRQENABLE_0_RESERVED_MAX                             (0x0007FFFFU)

/* VP_IRQENABLE_1 */

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPFRAMEDONE_EN_MASK                      (0x00000001U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPFRAMEDONE_EN_SHIFT                     (0x00000000U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPFRAMEDONE_EN_MAX                       (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPFRAMEDONE_EN_VAL_MASKED                (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPFRAMEDONE_EN_VAL_GENINT                (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_EN_MASK                          (0x00000002U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_EN_SHIFT                         (0x00000001U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_EN_MAX                           (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_EN_VAL_MASKED                    (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_EN_VAL_GENINT                    (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_ODD_EN_MASK                      (0x00000004U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_ODD_EN_SHIFT                     (0x00000002U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_ODD_EN_MAX                       (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_ODD_EN_VAL_MASKED                (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPVSYNC_ODD_EN_VAL_GENINT                (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_MASK           (0x00000008U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_SHIFT          (0x00000003U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_MAX            (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_VAL_MASKED     (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_VAL_GENINT     (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNCLOST_EN_MASK                       (0x00000010U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNCLOST_EN_SHIFT                      (0x00000004U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNCLOST_EN_MAX                        (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNCLOST_EN_VAL_MASKED                 (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNCLOST_EN_VAL_GENINT                 (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_MASK                (0x00000020U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_SHIFT               (0x00000005U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_MAX                 (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_VAL_MASKED          (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_VAL_GENINT          (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_SAFETYREGION_EN_MASK                     (0x000003C0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_SAFETYREGION_EN_SHIFT                    (0x00000006U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_SAFETYREGION_EN_MAX                      (0x0000000FU)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_SAFETYREGION_EN_VAL_MASKED               (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_SAFETYREGION_EN_VAL_GENINT               (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_SECURITYVIOLATION_EN_MASK                (0x00000400U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_SECURITYVIOLATION_EN_SHIFT               (0x0000000AU)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_SECURITYVIOLATION_EN_MAX                 (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_SECURITYVIOLATION_EN_VAL_MASKED          (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_SECURITYVIOLATION_EN_VAL_GENINT          (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNC_EN_MASK                           (0x00000800U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNC_EN_SHIFT                          (0x0000000BU)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNC_EN_MAX                            (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNC_EN_VAL_MASKED                     (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_VPSYNC_EN_VAL_GENINT                     (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_DUMMY_EN_MASK                            (0x00001000U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_DUMMY_EN_SHIFT                           (0x0000000CU)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_DUMMY_EN_MAX                             (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_DUMMY_EN_VAL_MASKED                      (0x0U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_DUMMY_EN_VAL_GENINT                      (0x1U)

#define CSL_DSS_COMMON_VP_IRQENABLE_1_RESERVED_MASK                            (0xFFFFE000U)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_RESERVED_SHIFT                           (0x0000000DU)
#define CSL_DSS_COMMON_VP_IRQENABLE_1_RESERVED_MAX                             (0x0007FFFFU)

/* VP_IRQSTATUS_0 */

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_MASK                     (0x00000001U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_SHIFT                    (0x00000000U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_MAX                      (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_VAL_NOPEND               (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_VAL_PEND                 (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_IRQ_MASK                         (0x00000002U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_IRQ_SHIFT                        (0x00000001U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_IRQ_MAX                          (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_IRQ_VAL_NOPEND                   (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_IRQ_VAL_PEND                     (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_MASK                     (0x00000004U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_SHIFT                    (0x00000002U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_MAX                      (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_VAL_NOPEND               (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_VAL_PEND                 (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_MASK          (0x00000008U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_SHIFT         (0x00000003U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_MAX           (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_VAL_NOPEND    (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_VAL_PEND      (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_MASK                      (0x00000010U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_SHIFT                     (0x00000004U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_MAX                       (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_VAL_NOPEND                (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_VAL_PEND                  (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_MASK               (0x00000020U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_SHIFT              (0x00000005U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_MAX                (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_VAL_NOPEND         (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_VAL_PEND           (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SAFETYREGION_IRQ_MASK                    (0x000003C0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SAFETYREGION_IRQ_SHIFT                   (0x00000006U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SAFETYREGION_IRQ_MAX                     (0x0000000FU)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SAFETYREGION_IRQ_VAL_NOPEND              (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SAFETYREGION_IRQ_VAL_PEND                (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_MASK               (0x00000400U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_SHIFT              (0x0000000AU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_MAX                (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_VAL_NOPEND         (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_VAL_PEND           (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNC_IRQ_MASK                          (0x00000800U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNC_IRQ_SHIFT                         (0x0000000BU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNC_IRQ_MAX                           (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNC_IRQ_VAL_NOPEND                    (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_VPSYNC_IRQ_VAL_PEND                      (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_DUMMY_IRQ_MASK                           (0x00001000U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_DUMMY_IRQ_SHIFT                          (0x0000000CU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_DUMMY_IRQ_MAX                            (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_DUMMY_IRQ_VAL_NOPEND                     (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_DUMMY_IRQ_VAL_PEND                       (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_0_RESERVED_MASK                            (0xFFFFE000U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_RESERVED_SHIFT                           (0x0000000DU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_0_RESERVED_MAX                             (0x0007FFFFU)

/* VP_IRQSTATUS_1 */

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_MASK                     (0x00000001U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_SHIFT                    (0x00000000U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_MAX                      (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_VAL_NOPEND               (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_VAL_PEND                 (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_IRQ_MASK                         (0x00000002U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_IRQ_SHIFT                        (0x00000001U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_IRQ_MAX                          (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_IRQ_VAL_NOPEND                   (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_IRQ_VAL_PEND                     (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_MASK                     (0x00000004U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_SHIFT                    (0x00000002U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_MAX                      (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_VAL_NOPEND               (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_VAL_PEND                 (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_MASK          (0x00000008U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_SHIFT         (0x00000003U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_MAX           (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_VAL_NOPEND    (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_VAL_PEND      (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_MASK                      (0x00000010U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_SHIFT                     (0x00000004U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_MAX                       (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_VAL_NOPEND                (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_VAL_PEND                  (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_MASK               (0x00000020U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_SHIFT              (0x00000005U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_MAX                (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_VAL_NOPEND         (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_VAL_PEND           (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SAFETYREGION_IRQ_MASK                    (0x000003C0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SAFETYREGION_IRQ_SHIFT                   (0x00000006U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SAFETYREGION_IRQ_MAX                     (0x0000000FU)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SAFETYREGION_IRQ_VAL_NOPEND              (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SAFETYREGION_IRQ_VAL_PEND                (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_MASK               (0x00000400U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_SHIFT              (0x0000000AU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_MAX                (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_VAL_NOPEND         (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_VAL_PEND           (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNC_IRQ_MASK                          (0x00000800U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNC_IRQ_SHIFT                         (0x0000000BU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNC_IRQ_MAX                           (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNC_IRQ_VAL_NOPEND                    (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_VPSYNC_IRQ_VAL_PEND                      (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_DUMMY_IRQ_MASK                           (0x00001000U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_DUMMY_IRQ_SHIFT                          (0x0000000CU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_DUMMY_IRQ_MAX                            (0x00000001U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_DUMMY_IRQ_VAL_NOPEND                     (0x0U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_DUMMY_IRQ_VAL_PEND                       (0x1U)

#define CSL_DSS_COMMON_VP_IRQSTATUS_1_RESERVED_MASK                            (0xFFFFE000U)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_RESERVED_SHIFT                           (0x0000000DU)
#define CSL_DSS_COMMON_VP_IRQSTATUS_1_RESERVED_MAX                             (0x0007FFFFU)

/* DISPC_GLOBAL_MFLAG_ATTRIBUTE */

#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_CTRL_MASK            (0x00000003U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_CTRL_SHIFT           (0x00000000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_CTRL_MAX             (0x00000003U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_CTRL_VAL_MFLAGDIS    (0x0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_CTRL_VAL_MFLAGFORCE  (0x1U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_CTRL_VAL_MFLAGEN     (0x2U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED1_MASK             (0x0000003CU)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED1_SHIFT            (0x00000002U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED1_MAX              (0x0000000FU)

#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_START_MASK           (0x00000040U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_START_SHIFT          (0x00000006U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_START_MAX            (0x00000001U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_START_VAL_MFLAGNORMALSTARTMODE (0x0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_MFLAG_START_VAL_MFLAGFORCESTARTMODE (0x1U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED2_MASK             (0x00000180U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED2_SHIFT            (0x00000007U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED2_MAX              (0x00000003U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED_MASK              (0xFFFFFE00U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED_SHIFT             (0x00000009U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_MFLAG_ATTRIBUTE_RESERVED_MAX               (0x007FFFFFU)

/* DISPC_GLOBAL_OUTPUT_ENABLE */

#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_ENABLE_MASK               (0x00000007U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_ENABLE_SHIFT              (0x00000000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_ENABLE_MAX                (0x00000007U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_ENABLE_VAL_DISABLE        (0x0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_ENABLE_VAL_ENABLE         (0x1U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED3_MASK               (0x00000008U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED3_SHIFT              (0x00000003U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED3_MAX                (0x00000001U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED1_MASK               (0x0000FFF0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED1_SHIFT              (0x00000004U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED1_MAX                (0x00000FFFU)

#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_GO_MASK                   (0x00070000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_GO_SHIFT                  (0x00000010U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_GO_MAX                    (0x00000007U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_GO_VAL_HFUISR             (0x0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_VP_GO_VAL_UFPSR              (0x1U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED4_MASK               (0x00080000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED4_SHIFT              (0x00000013U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED4_MAX                (0x00000001U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED2_MASK               (0xFFF00000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED2_SHIFT              (0x00000014U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_OUTPUT_ENABLE_RESERVED2_MAX                (0x00000FFFU)

/* DISPC_GLOBAL_BUFFER */

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VID_BUFFER_MASK                     (0x00000007U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VID_BUFFER_SHIFT                    (0x00000000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VID_BUFFER_MAX                      (0x00000007U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VID_BUFFER_VAL_VID                  (0x0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VID_BUFFER_VAL_VIDL1                (0x1U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VIDL1_BUFFER_MASK                   (0x00000038U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VIDL1_BUFFER_SHIFT                  (0x00000003U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VIDL1_BUFFER_MAX                    (0x00000007U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VIDL1_BUFFER_VAL_VID                (0x0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_VIDL1_BUFFER_VAL_VIDL1              (0x1U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED1_MASK                      (0x000001C0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED1_SHIFT                     (0x00000006U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED1_MAX                       (0x00000007U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED2_MASK                      (0x00000E00U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED2_SHIFT                     (0x00000009U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED2_MAX                       (0x00000007U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED3_MASK                      (0x00007000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED3_SHIFT                     (0x0000000CU)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED3_MAX                       (0x00000007U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED_MASK                       (0x7FFF8000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED_SHIFT                      (0x0000000FU)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_RESERVED_MAX                        (0x0000FFFFU)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_BUFFERFILLING_MASK                  (0x80000000U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_BUFFERFILLING_SHIFT                 (0x0000001FU)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_BUFFERFILLING_MAX                   (0x00000001U)

#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_BUFFERFILLING_VAL_INDIVIDUALPIPE    (0x0U)
#define CSL_DSS_COMMON_DISPC_GLOBAL_BUFFER_BUFFERFILLING_VAL_ALLPIPES          (0x1U)

/* DSS_CBA_CFG */

#define CSL_DSS_COMMON_DSS_CBA_CFG_PRI_LO_MASK                                 (0x00000007U)
#define CSL_DSS_COMMON_DSS_CBA_CFG_PRI_LO_SHIFT                                (0x00000000U)
#define CSL_DSS_COMMON_DSS_CBA_CFG_PRI_LO_MAX                                  (0x00000007U)

#define CSL_DSS_COMMON_DSS_CBA_CFG_PRI_HI_MASK                                 (0x00000038U)
#define CSL_DSS_COMMON_DSS_CBA_CFG_PRI_HI_SHIFT                                (0x00000003U)
#define CSL_DSS_COMMON_DSS_CBA_CFG_PRI_HI_MAX                                  (0x00000007U)

#define CSL_DSS_COMMON_DSS_CBA_CFG_RESERVED1_MASK                              (0x000001C0U)
#define CSL_DSS_COMMON_DSS_CBA_CFG_RESERVED1_SHIFT                             (0x00000006U)
#define CSL_DSS_COMMON_DSS_CBA_CFG_RESERVED1_MAX                               (0x00000007U)

#define CSL_DSS_COMMON_DSS_CBA_CFG_RESERVED_MASK                               (0xFFFFFE00U)
#define CSL_DSS_COMMON_DSS_CBA_CFG_RESERVED_SHIFT                              (0x00000009U)
#define CSL_DSS_COMMON_DSS_CBA_CFG_RESERVED_MAX                                (0x007FFFFFU)

/* DISPC_DBG_CONTROL */

#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGEN_MASK                            (0x00000001U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGEN_SHIFT                           (0x00000000U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGEN_MAX                             (0x00000001U)

#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGEN_VAL_DBGDIS                      (0x0U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGEN_VAL_DBGEN                       (0x1U)

#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_MASK                        (0x000001FEU)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_SHIFT                       (0x00000001U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_MAX                         (0x000000FFU)

#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_VAL_VIDSEL                  (0x0U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_VAL_VIDL1SEL                (0x8U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_VAL_OVR1SEL                 (0x11U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_VAL_OVR2SEL                 (0x12U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_VAL_VP1SEL                  (0x13U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_VAL_VP2SEL                  (0x15U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_DBGMUXSEL_VAL_MISCSEL                 (0x17U)

#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_RESERVED_MASK                         (0xFFFFFE00U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_RESERVED_SHIFT                        (0x00000009U)
#define CSL_DSS_COMMON_DISPC_DBG_CONTROL_RESERVED_MAX                          (0x007FFFFFU)

/* DISPC_DBG_STATUS */

#define CSL_DSS_COMMON_DISPC_DBG_STATUS_DBGOUT_MASK                            (0xFFFFFFFFU)
#define CSL_DSS_COMMON_DISPC_DBG_STATUS_DBGOUT_SHIFT                           (0x00000000U)
#define CSL_DSS_COMMON_DISPC_DBG_STATUS_DBGOUT_MAX                             (0xFFFFFFFFU)

/* DISPC_CLKGATING_DISABLE */

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_DMA_MASK                        (0x00000001U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_DMA_SHIFT                       (0x00000000U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_DMA_MAX                         (0x00000001U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_DMA_VAL_CLKGATINGEN             (0x0U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_DMA_VAL_CLKGATINGDIS            (0x1U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED1_MASK                  (0x00000006U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED1_SHIFT                 (0x00000001U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED1_MAX                   (0x00000003U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VID_MASK                        (0x00000018U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VID_SHIFT                       (0x00000003U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VID_MAX                         (0x00000003U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VID_VAL_CLKGATINGEN             (0x0U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VID_VAL_CLKGATINGDIS            (0x1U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED2_MASK                  (0x00000F80U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED2_SHIFT                 (0x00000007U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED2_MAX                   (0x0000001FU)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED3_MASK                  (0x00001000U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED3_SHIFT                 (0x0000000CU)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED3_MAX                   (0x00000001U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED3_VAL_CLKGATINGEN       (0x0U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED3_VAL_CLKGATINGDIS      (0x1U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_OVR_MASK                        (0x0000C000U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_OVR_SHIFT                       (0x0000000EU)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_OVR_MAX                         (0x00000003U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_OVR_VAL_CLKGATINGEN             (0x0U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_OVR_VAL_CLKGATINGDIS            (0x1U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED4_MASK                  (0x00020000U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED4_SHIFT                 (0x00000011U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED4_MAX                   (0x00000001U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VP_MASK                         (0x000C0000U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VP_SHIFT                        (0x00000012U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VP_MAX                          (0x00000003U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VP_VAL_CLKGATINGEN              (0x0U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_VP_VAL_CLKGATINGDIS             (0x1U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED5_MASK                  (0x00200000U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED5_SHIFT                 (0x00000015U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED5_MAX                   (0x00000001U)

#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED_MASK                   (0xFFC00000U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED_SHIFT                  (0x00000016U)
#define CSL_DSS_COMMON_DISPC_CLKGATING_DISABLE_RESERVED_MAX                    (0x000003FFU)

/* DISPC_SECURE_DISABLE */

#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE_SECURE_DISABLE_MASK                (0x00000001U)
#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE_SECURE_DISABLE_SHIFT               (0x00000000U)
#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE_SECURE_DISABLE_MAX                 (0x00000001U)

#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE_SECURE_DISABLE_VAL_SECUREEN        (0x0U)
#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE_SECURE_DISABLE_VAL_SECUREDIS       (0x1U)

#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE_RESERVED_MASK                      (0xFFFFFFFEU)
#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE_RESERVED_SHIFT                     (0x00000001U)
#define CSL_DSS_COMMON_DISPC_SECURE_DISABLE_RESERVED_MAX                       (0x7FFFFFFFU)

/**************************************************************************
* Hardware Region  : COMMON1 Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint8_t  Resv_36[36];
    volatile uint32_t DISPC_IRQ_EOI;             /* DISPC_IRQ_EOI */
    volatile uint32_t DISPC_IRQSTATUS_RAW;       /* DISPC_IRQSTATUS_RAW */
    volatile uint32_t DISPC_IRQSTATUS;           /* DISPC_IRQSTATUS */
    volatile uint32_t DISPC_IRQENABLE_SET;       /* DISPC_IRQENABLE_SET */
    volatile uint8_t  Resv_64[12];
    volatile uint32_t DISPC_IRQENABLE_CLR;       /* DISPC_IRQENABLE_CLR */
    volatile uint32_t VID_IRQENABLE_0;           /* VID_IRQENABLE_0 */
    volatile uint32_t VID_IRQENABLE_1;           /* VID_IRQENABLE_1 */
    volatile uint8_t  Resv_84[8];
    volatile uint32_t DISPC_SECURE;              /* DISPC_SECURE */
    volatile uint32_t VID_IRQSTATUS_0;           /* VID_IRQSTATUS_0 */
    volatile uint32_t VID_IRQSTATUS_1;           /* VID_IRQSTATUS_1 */
    volatile uint8_t  Resv_112[16];
    volatile uint32_t VP_IRQENABLE_0;            /* VP_IRQENABLE_0 */
    volatile uint32_t VP_IRQENABLE_1;            /* VP_IRQENABLE_1 */
    volatile uint8_t  Resv_124[4];
    volatile uint32_t VP_IRQSTATUS_0;            /* VP_IRQSTATUS_0 */
    volatile uint32_t VP_IRQSTATUS_1;            /* VP_IRQSTATUS_1 */
} CSL_dss_common1Regs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_DSS_COMMON1_DISPC_IRQ_EOI                                          (0x00000024U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW                                    (0x00000028U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS                                        (0x0000002CU)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET                                    (0x00000030U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR                                    (0x00000040U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0                                        (0x00000044U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1                                        (0x00000048U)
#define CSL_DSS_COMMON1_DISPC_SECURE                                           (0x00000054U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0                                        (0x00000058U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1                                        (0x0000005CU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0                                         (0x00000070U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1                                         (0x00000074U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0                                         (0x0000007CU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1                                         (0x00000080U)

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* DISPC_IRQ_EOI */

#define CSL_DSS_COMMON1_DISPC_IRQ_EOI_EOI_MASK                                 (0x00000001U)
#define CSL_DSS_COMMON1_DISPC_IRQ_EOI_EOI_SHIFT                                (0x00000000U)
#define CSL_DSS_COMMON1_DISPC_IRQ_EOI_EOI_MAX                                  (0x00000001U)

#define CSL_DSS_COMMON1_DISPC_IRQ_EOI_EOI_VAL_NOACTION                         (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQ_EOI_EOI_VAL_EOI                              (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQ_EOI_RESERVED_MASK                            (0xFFFFFFFEU)
#define CSL_DSS_COMMON1_DISPC_IRQ_EOI_RESERVED_SHIFT                           (0x00000001U)
#define CSL_DSS_COMMON1_DISPC_IRQ_EOI_RESERVED_MAX                             (0x7FFFFFFFU)

/* DISPC_IRQSTATUS_RAW */

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VP_IRQ_MASK                        (0x00000003U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VP_IRQ_SHIFT                       (0x00000000U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VP_IRQ_MAX                         (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VP_IRQ_VAL_NOACTION                (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VP_IRQ_VAL_SET_EVENT               (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VID_IRQ_MASK                       (0x00000030U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VID_IRQ_SHIFT                      (0x00000004U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VID_IRQ_MAX                        (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VID_IRQ_VAL_NOACTION               (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_VID_IRQ_VAL_SET_EVENT              (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_RESERVED_MASK                      (0xFFFF8000U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_RESERVED_SHIFT                     (0x0000000FU)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RAW_RESERVED_MAX                       (0x0001FFFFU)

/* DISPC_IRQSTATUS */

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VP_IRQ_MASK                            (0x00000003U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VP_IRQ_SHIFT                           (0x00000000U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VP_IRQ_MAX                             (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VP_IRQ_VAL_NOACTION                    (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VP_IRQ_VAL_CLEAR                       (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VID_IRQ_MASK                           (0x00000030U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VID_IRQ_SHIFT                          (0x00000004U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VID_IRQ_MAX                            (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VID_IRQ_VAL_NOACTION                   (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_VID_IRQ_VAL_CLEAR                      (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RESERVED_MASK                          (0xFFFF8000U)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RESERVED_SHIFT                         (0x0000000FU)
#define CSL_DSS_COMMON1_DISPC_IRQSTATUS_RESERVED_MAX                           (0x0001FFFFU)

/* DISPC_IRQENABLE_SET */

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VP_IRQ_MASK                    (0x00000003U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VP_IRQ_SHIFT                   (0x00000000U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VP_IRQ_MAX                     (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VP_IRQ_VAL_NOACTION            (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VP_IRQ_VAL_ENABLE              (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VID_IRQ_MASK                   (0x00000030U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VID_IRQ_SHIFT                  (0x00000004U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VID_IRQ_MAX                    (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VID_IRQ_VAL_NOACTION           (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_SET_VID_IRQ_VAL_ENABLE             (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_RESERVED_MASK                      (0xFFFF8000U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_RESERVED_SHIFT                     (0x0000000FU)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_SET_RESERVED_MAX                       (0x0001FFFFU)

/* DISPC_IRQENABLE_CLR */

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_MASK                    (0x00000003U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_SHIFT                   (0x00000000U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_MAX                     (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_VAL_NOACTION            (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VP_IRQ_VAL_CLEAR               (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_MASK                   (0x00000030U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_SHIFT                  (0x00000004U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_MAX                    (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_VAL_NOACTION           (0x0U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_CLR_VID_IRQ_VAL_CLEAR              (0x1U)

#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_RESERVED_MASK                      (0xFFFF8000U)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_RESERVED_SHIFT                     (0x0000000FU)
#define CSL_DSS_COMMON1_DISPC_IRQENABLE_CLR_RESERVED_MAX                       (0x0001FFFFU)

/* VID_IRQENABLE_0 */

#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_MASK             (0x00000001U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_SHIFT            (0x00000000U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_MAX              (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_VAL_MASKED       (0x0U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDBUFFERUNDERFLOW_EN_VAL_GENINT       (0x1U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDENDWINDOW_EN_MASK                   (0x00000002U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDENDWINDOW_EN_SHIFT                  (0x00000001U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDENDWINDOW_EN_MAX                    (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDENDWINDOW_EN_VAL_MASKED             (0x0U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_VIDENDWINDOW_EN_VAL_GENINT             (0x1U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_0_SAFETYREGION_EN_MASK                   (0x00000004U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_SAFETYREGION_EN_SHIFT                  (0x00000002U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_SAFETYREGION_EN_MAX                    (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_0_SAFETYREGION_EN_VAL_MASKED             (0x0U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_SAFETYREGION_EN_VAL_GENINT             (0x1U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_0_RESERVED_MASK                          (0xFFFFFFF8U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_RESERVED_SHIFT                         (0x00000003U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_0_RESERVED_MAX                           (0x1FFFFFFFU)

/* VID_IRQENABLE_1 */

#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_MASK             (0x00000001U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_SHIFT            (0x00000000U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_MAX              (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_VAL_MASKED       (0x0U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDBUFFERUNDERFLOW_EN_VAL_GENINT       (0x1U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDENDWINDOW_EN_MASK                   (0x00000002U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDENDWINDOW_EN_SHIFT                  (0x00000001U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDENDWINDOW_EN_MAX                    (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDENDWINDOW_EN_VAL_MASKED             (0x0U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_VIDENDWINDOW_EN_VAL_GENINT             (0x1U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_1_SAFETYREGION_EN_MASK                   (0x00000004U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_SAFETYREGION_EN_SHIFT                  (0x00000002U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_SAFETYREGION_EN_MAX                    (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_1_SAFETYREGION_EN_VAL_MASKED             (0x0U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_SAFETYREGION_EN_VAL_GENINT             (0x1U)

#define CSL_DSS_COMMON1_VID_IRQENABLE_1_RESERVED_MASK                          (0xFFFFFFF8U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_RESERVED_SHIFT                         (0x00000003U)
#define CSL_DSS_COMMON1_VID_IRQENABLE_1_RESERVED_MAX                           (0x1FFFFFFFU)

/* DISPC_SECURE */

#define CSL_DSS_COMMON1_DISPC_SECURE_VP_SECURE_MASK                            (0x00000003U)
#define CSL_DSS_COMMON1_DISPC_SECURE_VP_SECURE_SHIFT                           (0x00000000U)
#define CSL_DSS_COMMON1_DISPC_SECURE_VP_SECURE_MAX                             (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_SECURE_VP_SECURE_VAL_SECUREDIS                   (0x0U)
#define CSL_DSS_COMMON1_DISPC_SECURE_VP_SECURE_VAL_SECUREEN                    (0x1U)

#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED1_MASK                            (0x00000008U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED1_SHIFT                           (0x00000003U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED1_MAX                             (0x00000001U)

#define CSL_DSS_COMMON1_DISPC_SECURE_VID_SECURE_MASK                           (0x00000030U)
#define CSL_DSS_COMMON1_DISPC_SECURE_VID_SECURE_SHIFT                          (0x00000004U)
#define CSL_DSS_COMMON1_DISPC_SECURE_VID_SECURE_MAX                            (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_SECURE_VID_SECURE_VAL_SECUREDIS                  (0x0U)
#define CSL_DSS_COMMON1_DISPC_SECURE_VID_SECURE_VAL_SECUREEN                   (0x1U)

#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED2_MASK                            (0x00001F00U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED2_SHIFT                           (0x00000008U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED2_MAX                             (0x0000001FU)

#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED3_MASK                            (0x00002000U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED3_SHIFT                           (0x0000000DU)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED3_MAX                             (0x00000001U)

#define CSL_DSS_COMMON1_DISPC_SECURE_OVR_SECURE_MASK                           (0x00018000U)
#define CSL_DSS_COMMON1_DISPC_SECURE_OVR_SECURE_SHIFT                          (0x0000000FU)
#define CSL_DSS_COMMON1_DISPC_SECURE_OVR_SECURE_MAX                            (0x00000003U)

#define CSL_DSS_COMMON1_DISPC_SECURE_OVR_SECURE_VAL_SECUREDIS                  (0x0U)
#define CSL_DSS_COMMON1_DISPC_SECURE_OVR_SECURE_VAL_SECUREEN                   (0x1U)

#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED5_MASK                            (0x00040000U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED5_SHIFT                           (0x00000012U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED5_MAX                             (0x00000001U)

#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED_MASK                             (0xFFF80000U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED_SHIFT                            (0x00000013U)
#define CSL_DSS_COMMON1_DISPC_SECURE_RESERVED_MAX                              (0x00001FFFU)

/* VID_IRQSTATUS_0 */

#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_MASK            (0x00000001U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_SHIFT           (0x00000000U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_MAX             (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_VAL_NOPEND      (0x0U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDBUFFERUNDERFLOW_IRQ_VAL_PEND        (0x1U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_MASK                  (0x00000002U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_SHIFT                 (0x00000001U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_MAX                   (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_VAL_NOPEND            (0x0U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_VIDENDWINDOW_IRQ_VAL_PEND              (0x1U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_SAFETYREGION_IRQ_MASK                  (0x00000004U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_SAFETYREGION_IRQ_SHIFT                 (0x00000002U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_SAFETYREGION_IRQ_MAX                   (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_SAFETYREGION_IRQ_VAL_NOPEND            (0x0U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_SAFETYREGION_IRQ_VAL_PEND              (0x1U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_RESERVED_MASK                          (0xFFFFFFF8U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_RESERVED_SHIFT                         (0x00000003U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_0_RESERVED_MAX                           (0x1FFFFFFFU)

/* VID_IRQSTATUS_1 */

#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_MASK            (0x00000001U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_SHIFT           (0x00000000U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_MAX             (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_VAL_NOPEND      (0x0U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDBUFFERUNDERFLOW_IRQ_VAL_PEND        (0x1U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_MASK                  (0x00000002U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_SHIFT                 (0x00000001U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_MAX                   (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_VAL_NOPEND            (0x0U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_VIDENDWINDOW_IRQ_VAL_PEND              (0x1U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_SAFETYREGION_IRQ_MASK                  (0x00000004U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_SAFETYREGION_IRQ_SHIFT                 (0x00000002U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_SAFETYREGION_IRQ_MAX                   (0x00000001U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_SAFETYREGION_IRQ_VAL_NOPEND            (0x0U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_SAFETYREGION_IRQ_VAL_PEND              (0x1U)

#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_RESERVED_MASK                          (0xFFFFFFF8U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_RESERVED_SHIFT                         (0x00000003U)
#define CSL_DSS_COMMON1_VID_IRQSTATUS_1_RESERVED_MAX                           (0x1FFFFFFFU)

/* VP_IRQENABLE_0 */

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPFRAMEDONE_EN_MASK                     (0x00000001U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPFRAMEDONE_EN_SHIFT                    (0x00000000U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPFRAMEDONE_EN_MAX                      (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPFRAMEDONE_EN_VAL_MASKED               (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPFRAMEDONE_EN_VAL_GENINT               (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_EN_MASK                         (0x00000002U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_EN_SHIFT                        (0x00000001U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_EN_MAX                          (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_EN_VAL_MASKED                   (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_EN_VAL_GENINT                   (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_ODD_EN_MASK                     (0x00000004U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_ODD_EN_SHIFT                    (0x00000002U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_ODD_EN_MAX                      (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_ODD_EN_VAL_MASKED               (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPVSYNC_ODD_EN_VAL_GENINT               (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_MASK          (0x00000008U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_SHIFT         (0x00000003U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_MAX           (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_VAL_MASKED    (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPPROGRAMMEDLINENUMBER_EN_VAL_GENINT    (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNCLOST_EN_MASK                      (0x00000010U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNCLOST_EN_SHIFT                     (0x00000004U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNCLOST_EN_MAX                       (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNCLOST_EN_VAL_MASKED                (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNCLOST_EN_VAL_GENINT                (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_MASK               (0x00000020U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_SHIFT              (0x00000005U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_MAX                (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_VAL_MASKED         (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_ACBIASCOUNTSTATUS_EN_VAL_GENINT         (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SAFETYREGION_EN_MASK                    (0x000003C0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SAFETYREGION_EN_SHIFT                   (0x00000006U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SAFETYREGION_EN_MAX                     (0x0000000FU)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SAFETYREGION_EN_VAL_MASKED              (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SAFETYREGION_EN_VAL_GENINT              (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SECURITYVIOLATION_EN_MASK               (0x00000400U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SECURITYVIOLATION_EN_SHIFT              (0x0000000AU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SECURITYVIOLATION_EN_MAX                (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SECURITYVIOLATION_EN_VAL_MASKED         (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_SECURITYVIOLATION_EN_VAL_GENINT         (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNC_EN_MASK                          (0x00000800U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNC_EN_SHIFT                         (0x0000000BU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNC_EN_MAX                           (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNC_EN_VAL_MASKED                    (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_VPSYNC_EN_VAL_GENINT                    (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_DUMMY_EN_MASK                           (0x00001000U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_DUMMY_EN_SHIFT                          (0x0000000CU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_DUMMY_EN_MAX                            (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_DUMMY_EN_VAL_MASKED                     (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_DUMMY_EN_VAL_GENINT                     (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_0_RESERVED_MASK                           (0xFFFFE000U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_RESERVED_SHIFT                          (0x0000000DU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_0_RESERVED_MAX                            (0x0007FFFFU)

/* VP_IRQENABLE_1 */

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPFRAMEDONE_EN_MASK                     (0x00000001U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPFRAMEDONE_EN_SHIFT                    (0x00000000U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPFRAMEDONE_EN_MAX                      (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPFRAMEDONE_EN_VAL_MASKED               (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPFRAMEDONE_EN_VAL_GENINT               (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_EN_MASK                         (0x00000002U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_EN_SHIFT                        (0x00000001U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_EN_MAX                          (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_EN_VAL_MASKED                   (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_EN_VAL_GENINT                   (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_ODD_EN_MASK                     (0x00000004U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_ODD_EN_SHIFT                    (0x00000002U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_ODD_EN_MAX                      (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_ODD_EN_VAL_MASKED               (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPVSYNC_ODD_EN_VAL_GENINT               (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_MASK          (0x00000008U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_SHIFT         (0x00000003U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_MAX           (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_VAL_MASKED    (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPPROGRAMMEDLINENUMBER_EN_VAL_GENINT    (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNCLOST_EN_MASK                      (0x00000010U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNCLOST_EN_SHIFT                     (0x00000004U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNCLOST_EN_MAX                       (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNCLOST_EN_VAL_MASKED                (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNCLOST_EN_VAL_GENINT                (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_MASK               (0x00000020U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_SHIFT              (0x00000005U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_MAX                (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_VAL_MASKED         (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_ACBIASCOUNTSTATUS_EN_VAL_GENINT         (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SAFETYREGION_EN_MASK                    (0x000003C0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SAFETYREGION_EN_SHIFT                   (0x00000006U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SAFETYREGION_EN_MAX                     (0x0000000FU)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SAFETYREGION_EN_VAL_MASKED              (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SAFETYREGION_EN_VAL_GENINT              (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SECURITYVIOLATION_EN_MASK               (0x00000400U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SECURITYVIOLATION_EN_SHIFT              (0x0000000AU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SECURITYVIOLATION_EN_MAX                (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SECURITYVIOLATION_EN_VAL_MASKED         (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_SECURITYVIOLATION_EN_VAL_GENINT         (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNC_EN_MASK                          (0x00000800U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNC_EN_SHIFT                         (0x0000000BU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNC_EN_MAX                           (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNC_EN_VAL_MASKED                    (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_VPSYNC_EN_VAL_GENINT                    (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_DUMMY_EN_MASK                           (0x00001000U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_DUMMY_EN_SHIFT                          (0x0000000CU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_DUMMY_EN_MAX                            (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_DUMMY_EN_VAL_MASKED                     (0x0U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_DUMMY_EN_VAL_GENINT                     (0x1U)

#define CSL_DSS_COMMON1_VP_IRQENABLE_1_RESERVED_MASK                           (0xFFFFE000U)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_RESERVED_SHIFT                          (0x0000000DU)
#define CSL_DSS_COMMON1_VP_IRQENABLE_1_RESERVED_MAX                            (0x0007FFFFU)

/* VP_IRQSTATUS_0 */

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_MASK                    (0x00000001U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_SHIFT                   (0x00000000U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_MAX                     (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_VAL_NOPEND              (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPFRAMEDONE_IRQ_VAL_PEND                (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_IRQ_MASK                        (0x00000002U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_IRQ_SHIFT                       (0x00000001U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_IRQ_MAX                         (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_IRQ_VAL_NOPEND                  (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_IRQ_VAL_PEND                    (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_MASK                    (0x00000004U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_SHIFT                   (0x00000002U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_MAX                     (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_VAL_NOPEND              (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPVSYNC_ODD_IRQ_VAL_PEND                (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_MASK         (0x00000008U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_SHIFT        (0x00000003U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_MAX          (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_VAL_NOPEND   (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPPROGRAMMEDLINENUMBER_IRQ_VAL_PEND     (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_MASK                     (0x00000010U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_SHIFT                    (0x00000004U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_MAX                      (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_VAL_NOPEND               (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNCLOST_IRQ_VAL_PEND                 (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_MASK              (0x00000020U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_SHIFT             (0x00000005U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_MAX               (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_VAL_NOPEND        (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_ACBIASCOUNTSTATUS_IRQ_VAL_PEND          (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SAFETYREGION_IRQ_MASK                   (0x000003C0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SAFETYREGION_IRQ_SHIFT                  (0x00000006U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SAFETYREGION_IRQ_MAX                    (0x0000000FU)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SAFETYREGION_IRQ_VAL_NOPEND             (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SAFETYREGION_IRQ_VAL_PEND               (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_MASK              (0x00000400U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_SHIFT             (0x0000000AU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_MAX               (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_VAL_NOPEND        (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_SECURITYVIOLATION_IRQ_VAL_PEND          (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNC_IRQ_MASK                         (0x00000800U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNC_IRQ_SHIFT                        (0x0000000BU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNC_IRQ_MAX                          (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNC_IRQ_VAL_NOPEND                   (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_VPSYNC_IRQ_VAL_PEND                     (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_DUMMY_IRQ_MASK                          (0x00001000U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_DUMMY_IRQ_SHIFT                         (0x0000000CU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_DUMMY_IRQ_MAX                           (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_DUMMY_IRQ_VAL_NOPEND                    (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_DUMMY_IRQ_VAL_PEND                      (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_RESERVED_MASK                           (0xFFFFE000U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_RESERVED_SHIFT                          (0x0000000DU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_0_RESERVED_MAX                            (0x0007FFFFU)

/* VP_IRQSTATUS_1 */

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_MASK                    (0x00000001U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_SHIFT                   (0x00000000U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_MAX                     (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_VAL_NOPEND              (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPFRAMEDONE_IRQ_VAL_PEND                (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_IRQ_MASK                        (0x00000002U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_IRQ_SHIFT                       (0x00000001U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_IRQ_MAX                         (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_IRQ_VAL_NOPEND                  (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_IRQ_VAL_PEND                    (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_MASK                    (0x00000004U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_SHIFT                   (0x00000002U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_MAX                     (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_VAL_NOPEND              (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPVSYNC_ODD_IRQ_VAL_PEND                (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_MASK         (0x00000008U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_SHIFT        (0x00000003U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_MAX          (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_VAL_NOPEND   (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPPROGRAMMEDLINENUMBER_IRQ_VAL_PEND     (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_MASK                     (0x00000010U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_SHIFT                    (0x00000004U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_MAX                      (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_VAL_NOPEND               (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNCLOST_IRQ_VAL_PEND                 (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_MASK              (0x00000020U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_SHIFT             (0x00000005U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_MAX               (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_VAL_NOPEND        (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_ACBIASCOUNTSTATUS_IRQ_VAL_PEND          (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SAFETYREGION_IRQ_MASK                   (0x000003C0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SAFETYREGION_IRQ_SHIFT                  (0x00000006U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SAFETYREGION_IRQ_MAX                    (0x0000000FU)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SAFETYREGION_IRQ_VAL_NOPEND             (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SAFETYREGION_IRQ_VAL_PEND               (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_MASK              (0x00000400U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_SHIFT             (0x0000000AU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_MAX               (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_VAL_NOPEND        (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_SECURITYVIOLATION_IRQ_VAL_PEND          (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNC_IRQ_MASK                         (0x00000800U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNC_IRQ_SHIFT                        (0x0000000BU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNC_IRQ_MAX                          (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNC_IRQ_VAL_NOPEND                   (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_VPSYNC_IRQ_VAL_PEND                     (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_DUMMY_IRQ_MASK                          (0x00001000U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_DUMMY_IRQ_SHIFT                         (0x0000000CU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_DUMMY_IRQ_MAX                           (0x00000001U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_DUMMY_IRQ_VAL_NOPEND                    (0x0U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_DUMMY_IRQ_VAL_PEND                      (0x1U)

#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_RESERVED_MASK                           (0xFFFFE000U)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_RESERVED_SHIFT                          (0x0000000DU)
#define CSL_DSS_COMMON1_VP_IRQSTATUS_1_RESERVED_MAX                            (0x0007FFFFU)

/**************************************************************************
* Hardware Region  : VIDL1 Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint8_t  Resv_32[32];
    volatile uint32_t ATTRIBUTES;                /* ATTRIBUTES */
    volatile uint32_t ATTRIBUTES2;               /* ATTRIBUTES2 */
    volatile uint32_t BA_0;                      /* BA_0 */
    volatile uint32_t BA_1;                      /* BA_1 */
    volatile uint32_t BA_UV_0;                   /* BA_UV_0 */
    volatile uint32_t BA_UV_1;                   /* BA_UV_1 */
    volatile uint32_t BUF_SIZE_STATUS;           /* BUF_SIZE_STATUS */
    volatile uint32_t BUF_THRESHOLD;             /* BUF_THRESHOLD */
    volatile uint32_t CSC_COEF0;                 /* CSC_COEF0 */
    volatile uint32_t CSC_COEF1;                 /* CSC_COEF1 */
    volatile uint32_t CSC_COEF2;                 /* CSC_COEF2 */
    volatile uint32_t CSC_COEF3;                 /* CSC_COEF3 */
    volatile uint32_t CSC_COEF4;                 /* CSC_COEF4 */
    volatile uint32_t CSC_COEF5;                 /* CSC_COEF5 */
    volatile uint32_t CSC_COEF6;                 /* CSC_COEF6 */
    volatile uint8_t  Resv_508[416];
    volatile uint32_t GLOBAL_ALPHA;              /* GLOBAL_ALPHA */
    volatile uint8_t  Resv_520[8];
    volatile uint32_t MFLAG_THRESHOLD;           /* MFLAG_THRESHOLD */
    volatile uint32_t PICTURE_SIZE;              /* PICTURE_SIZE */
    volatile uint32_t PIXEL_INC;                 /* PIXEL_INC */
    volatile uint8_t  Resv_536[4];
    volatile uint32_t PRELOAD;                   /* PRELOAD */
    volatile uint32_t ROW_INC;                   /* ROW_INC */
    volatile uint8_t  Resv_556[12];
    volatile uint32_t BA_EXT_0;                  /* BA_EXT_0 */
    volatile uint32_t BA_EXT_1;                  /* BA_EXT_1 */
    volatile uint32_t BA_UV_EXT_0;               /* BA_UV_EXT_0 */
    volatile uint32_t BA_UV_EXT_1;               /* BA_UV_EXT_1 */
    volatile uint32_t CSC_COEF7;                 /* CSC_COEF7 */
    volatile uint8_t  Resv_584[8];
    volatile uint32_t ROW_INC_UV;                /* ROW_INC_UV */
    volatile uint8_t  Resv_608[20];
    volatile uint32_t CLUT_0;                    /* CLUT_0 */
    volatile uint32_t CLUT_1;                    /* CLUT_1 */
    volatile uint32_t CLUT_2;                    /* CLUT_2 */
    volatile uint32_t CLUT_3;                    /* CLUT_3 */
    volatile uint32_t CLUT_4;                    /* CLUT_4 */
    volatile uint32_t CLUT_5;                    /* CLUT_5 */
    volatile uint32_t CLUT_6;                    /* CLUT_6 */
    volatile uint32_t CLUT_7;                    /* CLUT_7 */
    volatile uint32_t CLUT_8;                    /* CLUT_8 */
    volatile uint32_t CLUT_9;                    /* CLUT_9 */
    volatile uint32_t CLUT_10;                   /* CLUT_10 */
    volatile uint32_t CLUT_11;                   /* CLUT_11 */
    volatile uint32_t CLUT_12;                   /* CLUT_12 */
    volatile uint32_t CLUT_13;                   /* CLUT_13 */
    volatile uint32_t CLUT_14;                   /* CLUT_14 */
    volatile uint32_t CLUT_15;                   /* CLUT_15 */
    volatile uint32_t SAFETY_ATTRIBUTES;         /* SAFETY_ATTRIBUTES */
    volatile uint32_t SAFETY_CAPT_SIGNATURE;     /* SAFETY_CAPT_SIGNATURE */
    volatile uint32_t SAFETY_POSITION;           /* SAFETY_POSITION */
    volatile uint32_t SAFETY_REF_SIGNATURE;      /* SAFETY_REF_SIGNATURE */
    volatile uint32_t SAFETY_SIZE;               /* SAFETY_SIZE */
    volatile uint32_t SAFETY_LFSR_SEED;          /* SAFETY_LFSR_SEED */
    volatile uint32_t LUMAKEY;                   /* LUMAKEY */
} CSL_dss_vidl1Regs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_DSS_VIDL1_ATTRIBUTES                                               (0x00000020U)
#define CSL_DSS_VIDL1_ATTRIBUTES2                                              (0x00000024U)
#define CSL_DSS_VIDL1_BA_0                                                     (0x00000028U)
#define CSL_DSS_VIDL1_BA_1                                                     (0x0000002CU)
#define CSL_DSS_VIDL1_BA_UV_0                                                  (0x00000030U)
#define CSL_DSS_VIDL1_BA_UV_1                                                  (0x00000034U)
#define CSL_DSS_VIDL1_BUF_SIZE_STATUS                                          (0x00000038U)
#define CSL_DSS_VIDL1_BUF_THRESHOLD                                            (0x0000003CU)
#define CSL_DSS_VIDL1_CSC_COEF0                                                (0x00000040U)
#define CSL_DSS_VIDL1_CSC_COEF1                                                (0x00000044U)
#define CSL_DSS_VIDL1_CSC_COEF2                                                (0x00000048U)
#define CSL_DSS_VIDL1_CSC_COEF3                                                (0x0000004CU)
#define CSL_DSS_VIDL1_CSC_COEF4                                                (0x00000050U)
#define CSL_DSS_VIDL1_CSC_COEF5                                                (0x00000054U)
#define CSL_DSS_VIDL1_CSC_COEF6                                                (0x00000058U)
#define CSL_DSS_VIDL1_GLOBAL_ALPHA                                             (0x000001FCU)
#define CSL_DSS_VIDL1_MFLAG_THRESHOLD                                          (0x00000208U)
#define CSL_DSS_VIDL1_PICTURE_SIZE                                             (0x0000020CU)
#define CSL_DSS_VIDL1_PIXEL_INC                                                (0x00000210U)
#define CSL_DSS_VIDL1_PRELOAD                                                  (0x00000218U)
#define CSL_DSS_VIDL1_ROW_INC                                                  (0x0000021CU)
#define CSL_DSS_VIDL1_BA_EXT_0                                                 (0x0000022CU)
#define CSL_DSS_VIDL1_BA_EXT_1                                                 (0x00000230U)
#define CSL_DSS_VIDL1_BA_UV_EXT_0                                              (0x00000234U)
#define CSL_DSS_VIDL1_BA_UV_EXT_1                                              (0x00000238U)
#define CSL_DSS_VIDL1_CSC_COEF7                                                (0x0000023CU)
#define CSL_DSS_VIDL1_ROW_INC_UV                                               (0x00000248U)
#define CSL_DSS_VIDL1_CLUT_0                                                   (0x00000260U)
#define CSL_DSS_VIDL1_CLUT_1                                                   (0x00000264U)
#define CSL_DSS_VIDL1_CLUT_2                                                   (0x00000268U)
#define CSL_DSS_VIDL1_CLUT_3                                                   (0x0000026CU)
#define CSL_DSS_VIDL1_CLUT_4                                                   (0x00000270U)
#define CSL_DSS_VIDL1_CLUT_5                                                   (0x00000274U)
#define CSL_DSS_VIDL1_CLUT_6                                                   (0x00000278U)
#define CSL_DSS_VIDL1_CLUT_7                                                   (0x0000027CU)
#define CSL_DSS_VIDL1_CLUT_8                                                   (0x00000280U)
#define CSL_DSS_VIDL1_CLUT_9                                                   (0x00000284U)
#define CSL_DSS_VIDL1_CLUT_10                                                  (0x00000288U)
#define CSL_DSS_VIDL1_CLUT_11                                                  (0x0000028CU)
#define CSL_DSS_VIDL1_CLUT_12                                                  (0x00000290U)
#define CSL_DSS_VIDL1_CLUT_13                                                  (0x00000294U)
#define CSL_DSS_VIDL1_CLUT_14                                                  (0x00000298U)
#define CSL_DSS_VIDL1_CLUT_15                                                  (0x0000029CU)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES                                        (0x000002A0U)
#define CSL_DSS_VIDL1_SAFETY_CAPT_SIGNATURE                                    (0x000002A4U)
#define CSL_DSS_VIDL1_SAFETY_POSITION                                          (0x000002A8U)
#define CSL_DSS_VIDL1_SAFETY_REF_SIGNATURE                                     (0x000002ACU)
#define CSL_DSS_VIDL1_SAFETY_SIZE                                              (0x000002B0U)
#define CSL_DSS_VIDL1_SAFETY_LFSR_SEED                                         (0x000002B4U)
#define CSL_DSS_VIDL1_LUMAKEY                                                  (0x000002B8U)

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* ATTRIBUTES */

#define CSL_DSS_VIDL1_ATTRIBUTES_ENABLE_MASK                                   (0x00000001U)
#define CSL_DSS_VIDL1_ATTRIBUTES_ENABLE_SHIFT                                  (0x00000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_ENABLE_MAX                                    (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_ENABLE_VAL_VIDEOENB                           (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_ENABLE_VAL_VIDEODIS                           (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_MASK                                   (0x0000007EU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_SHIFT                                  (0x00000001U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_MAX                                    (0x0000003FU)

#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ARGB16_4444                        (0x0U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ABGR16_4444                        (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGBA16_4444                        (0x2U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGB16_565                          (0x3U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BGR16_565                          (0x4U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ARGB16_1555                        (0x5U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ABGR16_1555                        (0x6U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ARGB32_8888                        (0x7U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ABGR32_8888                        (0x8U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGBA32_8888                        (0x9U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BGRA32_8888                        (0xAU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGB24P_888                         (0xBU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BGR24P_888                         (0xCU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ARGB32_2101010                     (0xEU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ABGR32_2101010                     (0xFU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_ARGB64_16161616                    (0x10U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGBA64_16161616                    (0x11U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BITMAP1                            (0x12U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BITMAP2                            (0x13U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BITMAP4                            (0x14U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BITMAP8                            (0x15U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGB565A8                           (0x16U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BGR565A8                           (0x17U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XRGB16_4444                        (0x20U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XBGR16_4444                        (0x21U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGBX16_4444                        (0x22U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XRGB16_1555                        (0x25U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XBGR16_1555                        (0x26U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XRGB32_8888                        (0x27U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XBGR32_8888                        (0x28U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGBX32_8888                        (0x29U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_BGRX32_8888                        (0x2AU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XRGB32_2101010                     (0x2EU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XBGR32_2101010                     (0x2FU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_XRGB64_16161616                    (0x30U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_RGBX64_16161616                    (0x31U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_YUV420_NV12                        (0x3DU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_YUV422_YUV2                        (0x3EU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FORMAT_VAL_YUV422_UYVY                        (0x3FU)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED8_MASK                                (0x00000180U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED8_SHIFT                               (0x00000007U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED8_MAX                                 (0x00000003U)

#define CSL_DSS_VIDL1_ATTRIBUTES_COLORCONVENABLE_MASK                          (0x00000200U)
#define CSL_DSS_VIDL1_ATTRIBUTES_COLORCONVENABLE_SHIFT                         (0x00000009U)
#define CSL_DSS_VIDL1_ATTRIBUTES_COLORCONVENABLE_MAX                           (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_COLORCONVENABLE_VAL_COLSPCENB                 (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_COLORCONVENABLE_VAL_COLSPCDIS                 (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_NIBBLEMODE_MASK                               (0x00000400U)
#define CSL_DSS_VIDL1_ATTRIBUTES_NIBBLEMODE_SHIFT                              (0x0000000AU)
#define CSL_DSS_VIDL1_ATTRIBUTES_NIBBLEMODE_MAX                                (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_NIBBLEMODE_VAL_NIBBLEMODEEN                   (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_NIBBLEMODE_VAL_NIBBLEMODEDIS                  (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_FULLRANGE_MASK                                (0x00000800U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FULLRANGE_SHIFT                               (0x0000000BU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FULLRANGE_MAX                                 (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_FULLRANGE_VAL_FULLRANGE                       (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FULLRANGE_VAL_LIMRANGE                        (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_FLIP_MASK                                     (0x00001000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FLIP_SHIFT                                    (0x0000000CU)
#define CSL_DSS_VIDL1_ATTRIBUTES_FLIP_MAX                                      (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_FLIP_VAL_FLIP                                 (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_FLIP_VAL_NOFLIP                               (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED1_MASK                                (0x00002000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED1_SHIFT                               (0x0000000DU)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED1_MAX                                 (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED9_MASK                                (0x0001C000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED9_SHIFT                               (0x0000000EU)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED9_MAX                                 (0x00000007U)

#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESHAUTO_MASK                          (0x00020000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESHAUTO_SHIFT                         (0x00000011U)
#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESHAUTO_MAX                           (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESHAUTO_VAL_SELFREFRESHAUTOEN         (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESHAUTO_VAL_SELFREFRESHAUTODIS        (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED7_MASK                                (0x00040000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED7_SHIFT                               (0x00000012U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED7_MAX                                 (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_BUFPRELOAD_MASK                               (0x00080000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_BUFPRELOAD_SHIFT                              (0x00000013U)
#define CSL_DSS_VIDL1_ATTRIBUTES_BUFPRELOAD_MAX                                (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_BUFPRELOAD_VAL_HIGHTHRES                      (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_BUFPRELOAD_VAL_DEFVAL                         (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED2_MASK                                (0x00100000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED2_SHIFT                               (0x00000014U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED2_MAX                                 (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED3_MASK                                (0x00200000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED3_SHIFT                               (0x00000015U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED3_MAX                                 (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED6_MASK                                (0x00400000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED6_SHIFT                               (0x00000016U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED6_MAX                                 (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_ARBITRATION_MASK                              (0x00800000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_ARBITRATION_SHIFT                             (0x00000017U)
#define CSL_DSS_VIDL1_ATTRIBUTES_ARBITRATION_MAX                               (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_ARBITRATION_VAL_HIGHPRIO                      (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_ARBITRATION_VAL_NORMALPRIO                    (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESH_MASK                              (0x01000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESH_SHIFT                             (0x00000018U)
#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESH_MAX                               (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESH_VAL_SELFREFRESHENB                (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_SELFREFRESH_VAL_SELFREFRESHDIS                (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED5_MASK                                (0x0E000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED5_SHIFT                               (0x00000019U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED5_MAX                                 (0x00000007U)

#define CSL_DSS_VIDL1_ATTRIBUTES_PREMULTIPLYALPHA_MASK                         (0x10000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_PREMULTIPLYALPHA_SHIFT                        (0x0000001CU)
#define CSL_DSS_VIDL1_ATTRIBUTES_PREMULTIPLYALPHA_MAX                          (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_PREMULTIPLYALPHA_VAL_PREMULTIPLIEDALPHA       (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_PREMULTIPLYALPHA_VAL_NONPREMULTIPLIEDALPHA    (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED4_MASK                                (0x20000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED4_SHIFT                               (0x0000001DU)
#define CSL_DSS_VIDL1_ATTRIBUTES_RESERVED4_MAX                                 (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_GAMMAINVERSION_MASK                           (0x40000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_GAMMAINVERSION_SHIFT                          (0x0000001EU)
#define CSL_DSS_VIDL1_ATTRIBUTES_GAMMAINVERSION_MAX                            (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_GAMMAINVERSION_VAL_INVGAMMAEN                 (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_GAMMAINVERSION_VAL_INVGAMMADIS                (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES_LUMAKEYENABLE_MASK                            (0x80000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES_LUMAKEYENABLE_SHIFT                           (0x0000001FU)
#define CSL_DSS_VIDL1_ATTRIBUTES_LUMAKEYENABLE_MAX                             (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES_LUMAKEYENABLE_VAL_LUMAKEYEN                   (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES_LUMAKEYENABLE_VAL_LUMAKEYDIS                  (0x0U)

/* ATTRIBUTES2 */

#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1ENABLE_MASK                               (0x00000001U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1ENABLE_SHIFT                              (0x00000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1ENABLE_MAX                                (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1ENABLE_VAL_VC1ENB                         (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1ENABLE_VAL_VC1DIS                         (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1_RANGE_Y_MASK                             (0x0000000EU)
#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1_RANGE_Y_SHIFT                            (0x00000001U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1_RANGE_Y_MAX                              (0x00000007U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1_RANGE_CBCR_MASK                          (0x00000070U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1_RANGE_CBCR_SHIFT                         (0x00000004U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_VC1_RANGE_CBCR_MAX                           (0x00000007U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_SIZE_MASK                                (0x00000180U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_SIZE_SHIFT                               (0x00000007U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_SIZE_MAX                                 (0x00000003U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_SIZE_VAL_8B                              (0x0U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_SIZE_VAL_10B                             (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_SIZE_VAL_12B                             (0x2U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_MODE_MASK                                (0x00000200U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_MODE_SHIFT                               (0x00000009U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_MODE_MAX                                 (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_MODE_VAL_PACKED                          (0x0U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_MODE_VAL_UNPACKED                        (0x1U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_ALIGN_MASK                               (0x00000400U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_ALIGN_SHIFT                              (0x0000000AU)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_ALIGN_MAX                                (0x00000001U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_ALIGN_VAL_MSB                            (0x1U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_YUV_ALIGN_VAL_LSB                            (0x0U)

#define CSL_DSS_VIDL1_ATTRIBUTES2_RESERVED_MASK                                (0x03FFF800U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_RESERVED_SHIFT                               (0x0000000BU)
#define CSL_DSS_VIDL1_ATTRIBUTES2_RESERVED_MAX                                 (0x00007FFFU)

#define CSL_DSS_VIDL1_ATTRIBUTES2_TAGS_MASK                                    (0x7C000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_TAGS_SHIFT                                   (0x0000001AU)
#define CSL_DSS_VIDL1_ATTRIBUTES2_TAGS_MAX                                     (0x0000001FU)

#define CSL_DSS_VIDL1_ATTRIBUTES2_RESERVED3_MASK                               (0x80000000U)
#define CSL_DSS_VIDL1_ATTRIBUTES2_RESERVED3_SHIFT                              (0x0000001FU)
#define CSL_DSS_VIDL1_ATTRIBUTES2_RESERVED3_MAX                                (0x00000001U)

/* BA_0 */

#define CSL_DSS_VIDL1_BA_0_BA_MASK                                             (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_BA_0_BA_SHIFT                                            (0x00000000U)
#define CSL_DSS_VIDL1_BA_0_BA_MAX                                              (0xFFFFFFFFU)

/* BA_1 */

#define CSL_DSS_VIDL1_BA_1_BA_MASK                                             (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_BA_1_BA_SHIFT                                            (0x00000000U)
#define CSL_DSS_VIDL1_BA_1_BA_MAX                                              (0xFFFFFFFFU)

/* BA_UV_0 */

#define CSL_DSS_VIDL1_BA_UV_0_BA_MASK                                          (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_BA_UV_0_BA_SHIFT                                         (0x00000000U)
#define CSL_DSS_VIDL1_BA_UV_0_BA_MAX                                           (0xFFFFFFFFU)

/* BA_UV_1 */

#define CSL_DSS_VIDL1_BA_UV_1_BA_MASK                                          (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_BA_UV_1_BA_SHIFT                                         (0x00000000U)
#define CSL_DSS_VIDL1_BA_UV_1_BA_MAX                                           (0xFFFFFFFFU)

/* BUF_SIZE_STATUS */

#define CSL_DSS_VIDL1_BUF_SIZE_STATUS_BUFSIZE_MASK                             (0x0000FFFFU)
#define CSL_DSS_VIDL1_BUF_SIZE_STATUS_BUFSIZE_SHIFT                            (0x00000000U)
#define CSL_DSS_VIDL1_BUF_SIZE_STATUS_BUFSIZE_MAX                              (0x0000FFFFU)

#define CSL_DSS_VIDL1_BUF_SIZE_STATUS_RESERVED_61_MASK                         (0xFFFF0000U)
#define CSL_DSS_VIDL1_BUF_SIZE_STATUS_RESERVED_61_SHIFT                        (0x00000010U)
#define CSL_DSS_VIDL1_BUF_SIZE_STATUS_RESERVED_61_MAX                          (0x0000FFFFU)

/* BUF_THRESHOLD */

#define CSL_DSS_VIDL1_BUF_THRESHOLD_BUFLOWTHRESHOLD_MASK                       (0x0000FFFFU)
#define CSL_DSS_VIDL1_BUF_THRESHOLD_BUFLOWTHRESHOLD_SHIFT                      (0x00000000U)
#define CSL_DSS_VIDL1_BUF_THRESHOLD_BUFLOWTHRESHOLD_MAX                        (0x0000FFFFU)

#define CSL_DSS_VIDL1_BUF_THRESHOLD_BUFHIGHTHRESHOLD_MASK                      (0xFFFF0000U)
#define CSL_DSS_VIDL1_BUF_THRESHOLD_BUFHIGHTHRESHOLD_SHIFT                     (0x00000010U)
#define CSL_DSS_VIDL1_BUF_THRESHOLD_BUFHIGHTHRESHOLD_MAX                       (0x0000FFFFU)

/* CSC_COEF0 */

#define CSL_DSS_VIDL1_CSC_COEF0_C00_MASK                                       (0x000007FFU)
#define CSL_DSS_VIDL1_CSC_COEF0_C00_SHIFT                                      (0x00000000U)
#define CSL_DSS_VIDL1_CSC_COEF0_C00_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF0_RESERVED_53_MASK                               (0x0000F800U)
#define CSL_DSS_VIDL1_CSC_COEF0_RESERVED_53_SHIFT                              (0x0000000BU)
#define CSL_DSS_VIDL1_CSC_COEF0_RESERVED_53_MAX                                (0x0000001FU)

#define CSL_DSS_VIDL1_CSC_COEF0_C01_MASK                                       (0x07FF0000U)
#define CSL_DSS_VIDL1_CSC_COEF0_C01_SHIFT                                      (0x00000010U)
#define CSL_DSS_VIDL1_CSC_COEF0_C01_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF0_RESERVED_52_MASK                               (0xF8000000U)
#define CSL_DSS_VIDL1_CSC_COEF0_RESERVED_52_SHIFT                              (0x0000001BU)
#define CSL_DSS_VIDL1_CSC_COEF0_RESERVED_52_MAX                                (0x0000001FU)

/* CSC_COEF1 */

#define CSL_DSS_VIDL1_CSC_COEF1_C02_MASK                                       (0x000007FFU)
#define CSL_DSS_VIDL1_CSC_COEF1_C02_SHIFT                                      (0x00000000U)
#define CSL_DSS_VIDL1_CSC_COEF1_C02_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF1_RESERVED_55_MASK                               (0x0000F800U)
#define CSL_DSS_VIDL1_CSC_COEF1_RESERVED_55_SHIFT                              (0x0000000BU)
#define CSL_DSS_VIDL1_CSC_COEF1_RESERVED_55_MAX                                (0x0000001FU)

#define CSL_DSS_VIDL1_CSC_COEF1_C10_MASK                                       (0x07FF0000U)
#define CSL_DSS_VIDL1_CSC_COEF1_C10_SHIFT                                      (0x00000010U)
#define CSL_DSS_VIDL1_CSC_COEF1_C10_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF1_RESERVED_54_MASK                               (0xF8000000U)
#define CSL_DSS_VIDL1_CSC_COEF1_RESERVED_54_SHIFT                              (0x0000001BU)
#define CSL_DSS_VIDL1_CSC_COEF1_RESERVED_54_MAX                                (0x0000001FU)

/* CSC_COEF2 */

#define CSL_DSS_VIDL1_CSC_COEF2_C11_MASK                                       (0x000007FFU)
#define CSL_DSS_VIDL1_CSC_COEF2_C11_SHIFT                                      (0x00000000U)
#define CSL_DSS_VIDL1_CSC_COEF2_C11_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF2_RESERVED_57_MASK                               (0x0000F800U)
#define CSL_DSS_VIDL1_CSC_COEF2_RESERVED_57_SHIFT                              (0x0000000BU)
#define CSL_DSS_VIDL1_CSC_COEF2_RESERVED_57_MAX                                (0x0000001FU)

#define CSL_DSS_VIDL1_CSC_COEF2_C12_MASK                                       (0x07FF0000U)
#define CSL_DSS_VIDL1_CSC_COEF2_C12_SHIFT                                      (0x00000010U)
#define CSL_DSS_VIDL1_CSC_COEF2_C12_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF2_RESERVED_56_MASK                               (0xF8000000U)
#define CSL_DSS_VIDL1_CSC_COEF2_RESERVED_56_SHIFT                              (0x0000001BU)
#define CSL_DSS_VIDL1_CSC_COEF2_RESERVED_56_MAX                                (0x0000001FU)

/* CSC_COEF3 */

#define CSL_DSS_VIDL1_CSC_COEF3_C20_MASK                                       (0x000007FFU)
#define CSL_DSS_VIDL1_CSC_COEF3_C20_SHIFT                                      (0x00000000U)
#define CSL_DSS_VIDL1_CSC_COEF3_C20_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF3_RESERVED_59_MASK                               (0x0000F800U)
#define CSL_DSS_VIDL1_CSC_COEF3_RESERVED_59_SHIFT                              (0x0000000BU)
#define CSL_DSS_VIDL1_CSC_COEF3_RESERVED_59_MAX                                (0x0000001FU)

#define CSL_DSS_VIDL1_CSC_COEF3_C21_MASK                                       (0x07FF0000U)
#define CSL_DSS_VIDL1_CSC_COEF3_C21_SHIFT                                      (0x00000010U)
#define CSL_DSS_VIDL1_CSC_COEF3_C21_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF3_RESERVED_58_MASK                               (0xF8000000U)
#define CSL_DSS_VIDL1_CSC_COEF3_RESERVED_58_SHIFT                              (0x0000001BU)
#define CSL_DSS_VIDL1_CSC_COEF3_RESERVED_58_MAX                                (0x0000001FU)

/* CSC_COEF4 */

#define CSL_DSS_VIDL1_CSC_COEF4_C22_MASK                                       (0x000007FFU)
#define CSL_DSS_VIDL1_CSC_COEF4_C22_SHIFT                                      (0x00000000U)
#define CSL_DSS_VIDL1_CSC_COEF4_C22_MAX                                        (0x000007FFU)

#define CSL_DSS_VIDL1_CSC_COEF4_RESERVED_60_MASK                               (0xFFFFF800U)
#define CSL_DSS_VIDL1_CSC_COEF4_RESERVED_60_SHIFT                              (0x0000000BU)
#define CSL_DSS_VIDL1_CSC_COEF4_RESERVED_60_MAX                                (0x001FFFFFU)

/* CSC_COEF5 */

#define CSL_DSS_VIDL1_CSC_COEF5_RESERVED_MASK                                  (0x00000007U)
#define CSL_DSS_VIDL1_CSC_COEF5_RESERVED_SHIFT                                 (0x00000000U)
#define CSL_DSS_VIDL1_CSC_COEF5_RESERVED_MAX                                   (0x00000007U)

#define CSL_DSS_VIDL1_CSC_COEF5_PREOFFSET1_MASK                                (0x0000FFF8U)
#define CSL_DSS_VIDL1_CSC_COEF5_PREOFFSET1_SHIFT                               (0x00000003U)
#define CSL_DSS_VIDL1_CSC_COEF5_PREOFFSET1_MAX                                 (0x00001FFFU)

#define CSL_DSS_VIDL1_CSC_COEF5_RESERVED1_MASK                                 (0x00070000U)
#define CSL_DSS_VIDL1_CSC_COEF5_RESERVED1_SHIFT                                (0x00000010U)
#define CSL_DSS_VIDL1_CSC_COEF5_RESERVED1_MAX                                  (0x00000007U)

#define CSL_DSS_VIDL1_CSC_COEF5_PREOFFSET2_MASK                                (0xFFF80000U)
#define CSL_DSS_VIDL1_CSC_COEF5_PREOFFSET2_SHIFT                               (0x00000013U)
#define CSL_DSS_VIDL1_CSC_COEF5_PREOFFSET2_MAX                                 (0x00001FFFU)

/* CSC_COEF6 */

#define CSL_DSS_VIDL1_CSC_COEF6_RESERVED_MASK                                  (0x00000007U)
#define CSL_DSS_VIDL1_CSC_COEF6_RESERVED_SHIFT                                 (0x00000000U)
#define CSL_DSS_VIDL1_CSC_COEF6_RESERVED_MAX                                   (0x00000007U)

#define CSL_DSS_VIDL1_CSC_COEF6_PREOFFSET3_MASK                                (0x0000FFF8U)
#define CSL_DSS_VIDL1_CSC_COEF6_PREOFFSET3_SHIFT                               (0x00000003U)
#define CSL_DSS_VIDL1_CSC_COEF6_PREOFFSET3_MAX                                 (0x00001FFFU)

#define CSL_DSS_VIDL1_CSC_COEF6_RESERVED1_MASK                                 (0x00070000U)
#define CSL_DSS_VIDL1_CSC_COEF6_RESERVED1_SHIFT                                (0x00000010U)
#define CSL_DSS_VIDL1_CSC_COEF6_RESERVED1_MAX                                  (0x00000007U)

#define CSL_DSS_VIDL1_CSC_COEF6_POSTOFFSET1_MASK                               (0xFFF80000U)
#define CSL_DSS_VIDL1_CSC_COEF6_POSTOFFSET1_SHIFT                              (0x00000013U)
#define CSL_DSS_VIDL1_CSC_COEF6_POSTOFFSET1_MAX                                (0x00001FFFU)

/* GLOBAL_ALPHA */

#define CSL_DSS_VIDL1_GLOBAL_ALPHA_GLOBALALPHA_MASK                            (0x000000FFU)
#define CSL_DSS_VIDL1_GLOBAL_ALPHA_GLOBALALPHA_SHIFT                           (0x00000000U)
#define CSL_DSS_VIDL1_GLOBAL_ALPHA_GLOBALALPHA_MAX                             (0x000000FFU)

#define CSL_DSS_VIDL1_GLOBAL_ALPHA_RESERVED_MASK                               (0xFFFFFF00U)
#define CSL_DSS_VIDL1_GLOBAL_ALPHA_RESERVED_SHIFT                              (0x00000008U)
#define CSL_DSS_VIDL1_GLOBAL_ALPHA_RESERVED_MAX                                (0x00FFFFFFU)

/* MFLAG_THRESHOLD */

#define CSL_DSS_VIDL1_MFLAG_THRESHOLD_LT_MFLAG_MASK                            (0x0000FFFFU)
#define CSL_DSS_VIDL1_MFLAG_THRESHOLD_LT_MFLAG_SHIFT                           (0x00000000U)
#define CSL_DSS_VIDL1_MFLAG_THRESHOLD_LT_MFLAG_MAX                             (0x0000FFFFU)

#define CSL_DSS_VIDL1_MFLAG_THRESHOLD_HT_MFLAG_MASK                            (0xFFFF0000U)
#define CSL_DSS_VIDL1_MFLAG_THRESHOLD_HT_MFLAG_SHIFT                           (0x00000010U)
#define CSL_DSS_VIDL1_MFLAG_THRESHOLD_HT_MFLAG_MAX                             (0x0000FFFFU)

/* PICTURE_SIZE */

#define CSL_DSS_VIDL1_PICTURE_SIZE_MEMSIZEX_MASK                               (0x00000FFFU)
#define CSL_DSS_VIDL1_PICTURE_SIZE_MEMSIZEX_SHIFT                              (0x00000000U)
#define CSL_DSS_VIDL1_PICTURE_SIZE_MEMSIZEX_MAX                                (0x00000FFFU)

#define CSL_DSS_VIDL1_PICTURE_SIZE_RESERVED1_MASK                              (0x0000F000U)
#define CSL_DSS_VIDL1_PICTURE_SIZE_RESERVED1_SHIFT                             (0x0000000CU)
#define CSL_DSS_VIDL1_PICTURE_SIZE_RESERVED1_MAX                               (0x0000000FU)

#define CSL_DSS_VIDL1_PICTURE_SIZE_MEMSIZEY_MASK                               (0x0FFF0000U)
#define CSL_DSS_VIDL1_PICTURE_SIZE_MEMSIZEY_SHIFT                              (0x00000010U)
#define CSL_DSS_VIDL1_PICTURE_SIZE_MEMSIZEY_MAX                                (0x00000FFFU)

#define CSL_DSS_VIDL1_PICTURE_SIZE_RESERVED_MASK                               (0xF0000000U)
#define CSL_DSS_VIDL1_PICTURE_SIZE_RESERVED_SHIFT                              (0x0000001CU)
#define CSL_DSS_VIDL1_PICTURE_SIZE_RESERVED_MAX                                (0x0000000FU)

/* PIXEL_INC */

#define CSL_DSS_VIDL1_PIXEL_INC_PIXELINC_MASK                                  (0x000000FFU)
#define CSL_DSS_VIDL1_PIXEL_INC_PIXELINC_SHIFT                                 (0x00000000U)
#define CSL_DSS_VIDL1_PIXEL_INC_PIXELINC_MAX                                   (0x000000FFU)

#define CSL_DSS_VIDL1_PIXEL_INC_RESERVED_68_MASK                               (0xFFFFFF00U)
#define CSL_DSS_VIDL1_PIXEL_INC_RESERVED_68_SHIFT                              (0x00000008U)
#define CSL_DSS_VIDL1_PIXEL_INC_RESERVED_68_MAX                                (0x00FFFFFFU)

/* PRELOAD */

#define CSL_DSS_VIDL1_PRELOAD_PRELOAD_MASK                                     (0x00000FFFU)
#define CSL_DSS_VIDL1_PRELOAD_PRELOAD_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_PRELOAD_PRELOAD_MAX                                      (0x00000FFFU)

#define CSL_DSS_VIDL1_PRELOAD_RESERVED_212_MASK                                (0xFFFFF000U)
#define CSL_DSS_VIDL1_PRELOAD_RESERVED_212_SHIFT                               (0x0000000CU)
#define CSL_DSS_VIDL1_PRELOAD_RESERVED_212_MAX                                 (0x000FFFFFU)

/* ROW_INC */

#define CSL_DSS_VIDL1_ROW_INC_ROWINC_MASK                                      (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_ROW_INC_ROWINC_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_ROW_INC_ROWINC_MAX                                       (0xFFFFFFFFU)

/* BA_EXT_0 */

#define CSL_DSS_VIDL1_BA_EXT_0_BA_EXT_MASK                                     (0x0000FFFFU)
#define CSL_DSS_VIDL1_BA_EXT_0_BA_EXT_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_BA_EXT_0_BA_EXT_MAX                                      (0x0000FFFFU)

#define CSL_DSS_VIDL1_BA_EXT_0_RESERVED_MASK                                   (0xFFFF0000U)
#define CSL_DSS_VIDL1_BA_EXT_0_RESERVED_SHIFT                                  (0x00000010U)
#define CSL_DSS_VIDL1_BA_EXT_0_RESERVED_MAX                                    (0x0000FFFFU)

/* BA_EXT_1 */

#define CSL_DSS_VIDL1_BA_EXT_1_BA_EXT_MASK                                     (0x0000FFFFU)
#define CSL_DSS_VIDL1_BA_EXT_1_BA_EXT_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_BA_EXT_1_BA_EXT_MAX                                      (0x0000FFFFU)

#define CSL_DSS_VIDL1_BA_EXT_1_RESERVED_MASK                                   (0xFFFF0000U)
#define CSL_DSS_VIDL1_BA_EXT_1_RESERVED_SHIFT                                  (0x00000010U)
#define CSL_DSS_VIDL1_BA_EXT_1_RESERVED_MAX                                    (0x0000FFFFU)

/* BA_UV_EXT_0 */

#define CSL_DSS_VIDL1_BA_UV_EXT_0_BA_UV_EXT_MASK                               (0x0000FFFFU)
#define CSL_DSS_VIDL1_BA_UV_EXT_0_BA_UV_EXT_SHIFT                              (0x00000000U)
#define CSL_DSS_VIDL1_BA_UV_EXT_0_BA_UV_EXT_MAX                                (0x0000FFFFU)

#define CSL_DSS_VIDL1_BA_UV_EXT_0_RESERVED_MASK                                (0xFFFF0000U)
#define CSL_DSS_VIDL1_BA_UV_EXT_0_RESERVED_SHIFT                               (0x00000010U)
#define CSL_DSS_VIDL1_BA_UV_EXT_0_RESERVED_MAX                                 (0x0000FFFFU)

/* BA_UV_EXT_1 */

#define CSL_DSS_VIDL1_BA_UV_EXT_1_BA_UV_EXT_MASK                               (0x0000FFFFU)
#define CSL_DSS_VIDL1_BA_UV_EXT_1_BA_UV_EXT_SHIFT                              (0x00000000U)
#define CSL_DSS_VIDL1_BA_UV_EXT_1_BA_UV_EXT_MAX                                (0x0000FFFFU)

#define CSL_DSS_VIDL1_BA_UV_EXT_1_RESERVED_MASK                                (0xFFFF0000U)
#define CSL_DSS_VIDL1_BA_UV_EXT_1_RESERVED_SHIFT                               (0x00000010U)
#define CSL_DSS_VIDL1_BA_UV_EXT_1_RESERVED_MAX                                 (0x0000FFFFU)

/* CSC_COEF7 */

#define CSL_DSS_VIDL1_CSC_COEF7_RESERVED_MASK                                  (0x00000007U)
#define CSL_DSS_VIDL1_CSC_COEF7_RESERVED_SHIFT                                 (0x00000000U)
#define CSL_DSS_VIDL1_CSC_COEF7_RESERVED_MAX                                   (0x00000007U)

#define CSL_DSS_VIDL1_CSC_COEF7_POSTOFFSET2_MASK                               (0x0000FFF8U)
#define CSL_DSS_VIDL1_CSC_COEF7_POSTOFFSET2_SHIFT                              (0x00000003U)
#define CSL_DSS_VIDL1_CSC_COEF7_POSTOFFSET2_MAX                                (0x00001FFFU)

#define CSL_DSS_VIDL1_CSC_COEF7_RESERVED1_MASK                                 (0x00070000U)
#define CSL_DSS_VIDL1_CSC_COEF7_RESERVED1_SHIFT                                (0x00000010U)
#define CSL_DSS_VIDL1_CSC_COEF7_RESERVED1_MAX                                  (0x00000007U)

#define CSL_DSS_VIDL1_CSC_COEF7_POSTOFFSET3_MASK                               (0xFFF80000U)
#define CSL_DSS_VIDL1_CSC_COEF7_POSTOFFSET3_SHIFT                              (0x00000013U)
#define CSL_DSS_VIDL1_CSC_COEF7_POSTOFFSET3_MAX                                (0x00001FFFU)

/* ROW_INC_UV */

#define CSL_DSS_VIDL1_ROW_INC_UV_ROWINC_MASK                                   (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_ROW_INC_UV_ROWINC_SHIFT                                  (0x00000000U)
#define CSL_DSS_VIDL1_ROW_INC_UV_ROWINC_MAX                                    (0xFFFFFFFFU)

/* CLUT_0 */

#define CSL_DSS_VIDL1_CLUT_0_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_0_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_0_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_0_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_0_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_0_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_0_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_0_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_0_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_0_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_0_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_0_INDEX_MAX                                         (0x000000FFU)

/* CLUT_1 */

#define CSL_DSS_VIDL1_CLUT_1_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_1_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_1_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_1_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_1_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_1_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_1_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_1_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_1_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_1_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_1_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_1_INDEX_MAX                                         (0x000000FFU)

/* CLUT_2 */

#define CSL_DSS_VIDL1_CLUT_2_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_2_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_2_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_2_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_2_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_2_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_2_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_2_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_2_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_2_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_2_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_2_INDEX_MAX                                         (0x000000FFU)

/* CLUT_3 */

#define CSL_DSS_VIDL1_CLUT_3_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_3_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_3_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_3_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_3_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_3_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_3_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_3_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_3_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_3_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_3_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_3_INDEX_MAX                                         (0x000000FFU)

/* CLUT_4 */

#define CSL_DSS_VIDL1_CLUT_4_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_4_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_4_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_4_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_4_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_4_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_4_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_4_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_4_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_4_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_4_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_4_INDEX_MAX                                         (0x000000FFU)

/* CLUT_5 */

#define CSL_DSS_VIDL1_CLUT_5_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_5_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_5_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_5_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_5_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_5_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_5_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_5_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_5_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_5_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_5_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_5_INDEX_MAX                                         (0x000000FFU)

/* CLUT_6 */

#define CSL_DSS_VIDL1_CLUT_6_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_6_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_6_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_6_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_6_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_6_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_6_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_6_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_6_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_6_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_6_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_6_INDEX_MAX                                         (0x000000FFU)

/* CLUT_7 */

#define CSL_DSS_VIDL1_CLUT_7_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_7_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_7_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_7_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_7_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_7_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_7_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_7_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_7_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_7_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_7_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_7_INDEX_MAX                                         (0x000000FFU)

/* CLUT_8 */

#define CSL_DSS_VIDL1_CLUT_8_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_8_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_8_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_8_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_8_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_8_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_8_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_8_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_8_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_8_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_8_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_8_INDEX_MAX                                         (0x000000FFU)

/* CLUT_9 */

#define CSL_DSS_VIDL1_CLUT_9_VALUE_B_MASK                                      (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_9_VALUE_B_SHIFT                                     (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_9_VALUE_B_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_9_VALUE_G_MASK                                      (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_9_VALUE_G_SHIFT                                     (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_9_VALUE_G_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_9_VALUE_R_MASK                                      (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_9_VALUE_R_SHIFT                                     (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_9_VALUE_R_MAX                                       (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_9_INDEX_MASK                                        (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_9_INDEX_SHIFT                                       (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_9_INDEX_MAX                                         (0x000000FFU)

/* CLUT_10 */

#define CSL_DSS_VIDL1_CLUT_10_VALUE_B_MASK                                     (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_10_VALUE_B_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_10_VALUE_B_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_10_VALUE_G_MASK                                     (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_10_VALUE_G_SHIFT                                    (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_10_VALUE_G_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_10_VALUE_R_MASK                                     (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_10_VALUE_R_SHIFT                                    (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_10_VALUE_R_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_10_INDEX_MASK                                       (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_10_INDEX_SHIFT                                      (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_10_INDEX_MAX                                        (0x000000FFU)

/* CLUT_11 */

#define CSL_DSS_VIDL1_CLUT_11_VALUE_B_MASK                                     (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_11_VALUE_B_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_11_VALUE_B_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_11_VALUE_G_MASK                                     (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_11_VALUE_G_SHIFT                                    (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_11_VALUE_G_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_11_VALUE_R_MASK                                     (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_11_VALUE_R_SHIFT                                    (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_11_VALUE_R_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_11_INDEX_MASK                                       (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_11_INDEX_SHIFT                                      (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_11_INDEX_MAX                                        (0x000000FFU)

/* CLUT_12 */

#define CSL_DSS_VIDL1_CLUT_12_VALUE_B_MASK                                     (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_12_VALUE_B_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_12_VALUE_B_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_12_VALUE_G_MASK                                     (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_12_VALUE_G_SHIFT                                    (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_12_VALUE_G_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_12_VALUE_R_MASK                                     (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_12_VALUE_R_SHIFT                                    (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_12_VALUE_R_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_12_INDEX_MASK                                       (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_12_INDEX_SHIFT                                      (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_12_INDEX_MAX                                        (0x000000FFU)

/* CLUT_13 */

#define CSL_DSS_VIDL1_CLUT_13_VALUE_B_MASK                                     (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_13_VALUE_B_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_13_VALUE_B_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_13_VALUE_G_MASK                                     (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_13_VALUE_G_SHIFT                                    (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_13_VALUE_G_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_13_VALUE_R_MASK                                     (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_13_VALUE_R_SHIFT                                    (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_13_VALUE_R_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_13_INDEX_MASK                                       (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_13_INDEX_SHIFT                                      (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_13_INDEX_MAX                                        (0x000000FFU)

/* CLUT_14 */

#define CSL_DSS_VIDL1_CLUT_14_VALUE_B_MASK                                     (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_14_VALUE_B_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_14_VALUE_B_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_14_VALUE_G_MASK                                     (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_14_VALUE_G_SHIFT                                    (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_14_VALUE_G_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_14_VALUE_R_MASK                                     (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_14_VALUE_R_SHIFT                                    (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_14_VALUE_R_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_14_INDEX_MASK                                       (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_14_INDEX_SHIFT                                      (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_14_INDEX_MAX                                        (0x000000FFU)

/* CLUT_15 */

#define CSL_DSS_VIDL1_CLUT_15_VALUE_B_MASK                                     (0x000000FFU)
#define CSL_DSS_VIDL1_CLUT_15_VALUE_B_SHIFT                                    (0x00000000U)
#define CSL_DSS_VIDL1_CLUT_15_VALUE_B_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_15_VALUE_G_MASK                                     (0x0000FF00U)
#define CSL_DSS_VIDL1_CLUT_15_VALUE_G_SHIFT                                    (0x00000008U)
#define CSL_DSS_VIDL1_CLUT_15_VALUE_G_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_15_VALUE_R_MASK                                     (0x00FF0000U)
#define CSL_DSS_VIDL1_CLUT_15_VALUE_R_SHIFT                                    (0x00000010U)
#define CSL_DSS_VIDL1_CLUT_15_VALUE_R_MAX                                      (0x000000FFU)

#define CSL_DSS_VIDL1_CLUT_15_INDEX_MASK                                       (0xFF000000U)
#define CSL_DSS_VIDL1_CLUT_15_INDEX_SHIFT                                      (0x00000018U)
#define CSL_DSS_VIDL1_CLUT_15_INDEX_MAX                                        (0x000000FFU)

/* SAFETY_ATTRIBUTES */

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_ENABLE_MASK                            (0x00000001U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_ENABLE_SHIFT                           (0x00000000U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_ENABLE_MAX                             (0x00000001U)

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_CAPTUREMODE_MASK                       (0x00000002U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_CAPTUREMODE_SHIFT                      (0x00000001U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_CAPTUREMODE_MAX                        (0x00000001U)

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_CAPTUREMODE_VAL_DATACHECK              (0x1U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_CAPTUREMODE_VAL_FRAMEFREEZE            (0x0U)

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_SEEDSELECT_MASK                        (0x00000004U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_SEEDSELECT_SHIFT                       (0x00000002U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_SEEDSELECT_MAX                         (0x00000001U)

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_SEEDSELECT_VAL_ENABLE                  (0x1U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_SEEDSELECT_VAL_DISABLE                 (0x0U)

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_THRESHOLD_MASK                         (0x000007F8U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_THRESHOLD_SHIFT                        (0x00000003U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_THRESHOLD_MAX                          (0x000000FFU)

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_FRAMESKIP_MASK                         (0x00001800U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_FRAMESKIP_SHIFT                        (0x0000000BU)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_FRAMESKIP_MAX                          (0x00000003U)

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_DISABLE                  (0x0U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_EVEN                     (0x1U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_ODD                      (0x2U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_RESERVED                 (0x3U)

#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_RESERVED_MASK                          (0xFFFFE000U)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_RESERVED_SHIFT                         (0x0000000DU)
#define CSL_DSS_VIDL1_SAFETY_ATTRIBUTES_RESERVED_MAX                           (0x0007FFFFU)

/* SAFETY_CAPT_SIGNATURE */

#define CSL_DSS_VIDL1_SAFETY_CAPT_SIGNATURE_SIGNATURE_MASK                     (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_SAFETY_CAPT_SIGNATURE_SIGNATURE_SHIFT                    (0x00000000U)
#define CSL_DSS_VIDL1_SAFETY_CAPT_SIGNATURE_SIGNATURE_MAX                      (0xFFFFFFFFU)

/* SAFETY_POSITION */

#define CSL_DSS_VIDL1_SAFETY_POSITION_POSX_MASK                                (0x00000FFFU)
#define CSL_DSS_VIDL1_SAFETY_POSITION_POSX_SHIFT                               (0x00000000U)
#define CSL_DSS_VIDL1_SAFETY_POSITION_POSX_MAX                                 (0x00000FFFU)

#define CSL_DSS_VIDL1_SAFETY_POSITION_RESERVED1_MASK                           (0x0000F000U)
#define CSL_DSS_VIDL1_SAFETY_POSITION_RESERVED1_SHIFT                          (0x0000000CU)
#define CSL_DSS_VIDL1_SAFETY_POSITION_RESERVED1_MAX                            (0x0000000FU)

#define CSL_DSS_VIDL1_SAFETY_POSITION_POSY_MASK                                (0x0FFF0000U)
#define CSL_DSS_VIDL1_SAFETY_POSITION_POSY_SHIFT                               (0x00000010U)
#define CSL_DSS_VIDL1_SAFETY_POSITION_POSY_MAX                                 (0x00000FFFU)

#define CSL_DSS_VIDL1_SAFETY_POSITION_RESERVED_MASK                            (0xF0000000U)
#define CSL_DSS_VIDL1_SAFETY_POSITION_RESERVED_SHIFT                           (0x0000001CU)
#define CSL_DSS_VIDL1_SAFETY_POSITION_RESERVED_MAX                             (0x0000000FU)

/* SAFETY_REF_SIGNATURE */

#define CSL_DSS_VIDL1_SAFETY_REF_SIGNATURE_SIGNATURE_MASK                      (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_SAFETY_REF_SIGNATURE_SIGNATURE_SHIFT                     (0x00000000U)
#define CSL_DSS_VIDL1_SAFETY_REF_SIGNATURE_SIGNATURE_MAX                       (0xFFFFFFFFU)

/* SAFETY_SIZE */

#define CSL_DSS_VIDL1_SAFETY_SIZE_SIZEX_MASK                                   (0x00000FFFU)
#define CSL_DSS_VIDL1_SAFETY_SIZE_SIZEX_SHIFT                                  (0x00000000U)
#define CSL_DSS_VIDL1_SAFETY_SIZE_SIZEX_MAX                                    (0x00000FFFU)

#define CSL_DSS_VIDL1_SAFETY_SIZE_RESERVED1_MASK                               (0x0000F000U)
#define CSL_DSS_VIDL1_SAFETY_SIZE_RESERVED1_SHIFT                              (0x0000000CU)
#define CSL_DSS_VIDL1_SAFETY_SIZE_RESERVED1_MAX                                (0x0000000FU)

#define CSL_DSS_VIDL1_SAFETY_SIZE_SIZEY_MASK                                   (0x0FFF0000U)
#define CSL_DSS_VIDL1_SAFETY_SIZE_SIZEY_SHIFT                                  (0x00000010U)
#define CSL_DSS_VIDL1_SAFETY_SIZE_SIZEY_MAX                                    (0x00000FFFU)

#define CSL_DSS_VIDL1_SAFETY_SIZE_RESERVED_MASK                                (0xF0000000U)
#define CSL_DSS_VIDL1_SAFETY_SIZE_RESERVED_SHIFT                               (0x0000001CU)
#define CSL_DSS_VIDL1_SAFETY_SIZE_RESERVED_MAX                                 (0x0000000FU)

/* SAFETY_LFSR_SEED */

#define CSL_DSS_VIDL1_SAFETY_LFSR_SEED_SEED_MASK                               (0xFFFFFFFFU)
#define CSL_DSS_VIDL1_SAFETY_LFSR_SEED_SEED_SHIFT                              (0x00000000U)
#define CSL_DSS_VIDL1_SAFETY_LFSR_SEED_SEED_MAX                                (0xFFFFFFFFU)

/* LUMAKEY */

#define CSL_DSS_VIDL1_LUMAKEY_RESERVED1_MASK                                   (0xF0000000U)
#define CSL_DSS_VIDL1_LUMAKEY_RESERVED1_SHIFT                                  (0x0000001CU)
#define CSL_DSS_VIDL1_LUMAKEY_RESERVED1_MAX                                    (0x0000000FU)

#define CSL_DSS_VIDL1_LUMAKEY_LUMAKEYMAX_MASK                                  (0x0FFF0000U)
#define CSL_DSS_VIDL1_LUMAKEY_LUMAKEYMAX_SHIFT                                 (0x00000010U)
#define CSL_DSS_VIDL1_LUMAKEY_LUMAKEYMAX_MAX                                   (0x00000FFFU)

#define CSL_DSS_VIDL1_LUMAKEY_RESERVED_MASK                                    (0x0000F000U)
#define CSL_DSS_VIDL1_LUMAKEY_RESERVED_SHIFT                                   (0x0000000CU)
#define CSL_DSS_VIDL1_LUMAKEY_RESERVED_MAX                                     (0x0000000FU)

#define CSL_DSS_VIDL1_LUMAKEY_LUMAKEYMIN_MASK                                  (0x00000FFFU)
#define CSL_DSS_VIDL1_LUMAKEY_LUMAKEYMIN_SHIFT                                 (0x00000000U)
#define CSL_DSS_VIDL1_LUMAKEY_LUMAKEYMIN_MAX                                   (0x00000FFFU)

/**************************************************************************
* Hardware Region  : VID Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t ACCUH_0;                   /* ACCUH_0 */
    volatile uint32_t ACCUH_1;                   /* ACCUH_1 */
    volatile uint32_t ACCUH2_0;                  /* ACCUH2_0 */
    volatile uint32_t ACCUH2_1;                  /* ACCUH2_1 */
    volatile uint32_t ACCUV_0;                   /* ACCUV_0 */
    volatile uint32_t ACCUV_1;                   /* ACCUV_1 */
    volatile uint32_t ACCUV2_0;                  /* ACCUV2_0 */
    volatile uint32_t ACCUV2_1;                  /* ACCUV2_1 */
    volatile uint32_t ATTRIBUTES;                /* ATTRIBUTES */
    volatile uint32_t ATTRIBUTES2;               /* ATTRIBUTES2 */
    volatile uint32_t BA_0;                      /* BA_0 */
    volatile uint32_t BA_1;                      /* BA_1 */
    volatile uint32_t BA_UV_0;                   /* BA_UV_0 */
    volatile uint32_t BA_UV_1;                   /* BA_UV_1 */
    volatile uint32_t BUF_SIZE_STATUS;           /* BUF_SIZE_STATUS */
    volatile uint32_t BUF_THRESHOLD;             /* BUF_THRESHOLD */
    volatile uint32_t CSC_COEF0;                 /* CSC_COEF0 */
    volatile uint32_t CSC_COEF1;                 /* CSC_COEF1 */
    volatile uint32_t CSC_COEF2;                 /* CSC_COEF2 */
    volatile uint32_t CSC_COEF3;                 /* CSC_COEF3 */
    volatile uint32_t CSC_COEF4;                 /* CSC_COEF4 */
    volatile uint32_t CSC_COEF5;                 /* CSC_COEF5 */
    volatile uint32_t CSC_COEF6;                 /* CSC_COEF6 */
    volatile uint32_t FIRH;                      /* FIRH */
    volatile uint32_t FIRH2;                     /* FIRH2 */
    volatile uint32_t FIRV;                      /* FIRV */
    volatile uint32_t FIRV2;                     /* FIRV2 */
    volatile uint32_t FIR_COEF_H0[9U];           /* FIR_COEF_H0 0..8 */
    volatile uint32_t FIR_COEF_H0_C[9U];         /* FIR_COEF_H0_C 0..8 */
    volatile uint32_t FIR_COEF_H12[16U];         /* FIR_COEF_H12 0..15 */
    volatile uint32_t FIR_COEF_H12_C[16U];       /* FIR_COEF_H12_C 0..15 */
    volatile uint32_t FIR_COEF_V0[9U];           /* FIR_COEF_V0 0..8 */
    volatile uint32_t FIR_COEF_V0_C[9U];         /* FIR_COEF_V0_C 0..8 */
    volatile uint32_t FIR_COEF_V12[16U];         /* FIR_COEF_V12 0..15 */
    volatile uint32_t FIR_COEF_V12_C[16U];       /* FIR_COEF_V12_C 0..15 */
    volatile uint32_t GLOBAL_ALPHA;              /* GLOBAL_ALPHA */
    volatile uint8_t  Resv_520[8];
    volatile uint32_t MFLAG_THRESHOLD;           /* MFLAG_THRESHOLD */
    volatile uint32_t PICTURE_SIZE;              /* PICTURE_SIZE */
    volatile uint32_t PIXEL_INC;                 /* PIXEL_INC */
    volatile uint8_t  Resv_536[4];
    volatile uint32_t PRELOAD;                   /* PRELOAD */
    volatile uint32_t ROW_INC;                   /* ROW_INC */
    volatile uint32_t SIZE;                      /* SIZE */
    volatile uint8_t  Resv_556[8];
    volatile uint32_t BA_EXT_0;                  /* BA_EXT_0 */
    volatile uint32_t BA_EXT_1;                  /* BA_EXT_1 */
    volatile uint32_t BA_UV_EXT_0;               /* BA_UV_EXT_0 */
    volatile uint32_t BA_UV_EXT_1;               /* BA_UV_EXT_1 */
    volatile uint32_t CSC_COEF7;                 /* CSC_COEF7 */
    volatile uint8_t  Resv_584[8];
    volatile uint32_t ROW_INC_UV;                /* ROW_INC_UV */
    volatile uint8_t  Resv_608[20];
    volatile uint32_t CLUT_0;                    /* CLUT_0 */
    volatile uint32_t CLUT_1;                    /* CLUT_1 */
    volatile uint32_t CLUT_2;                    /* CLUT_2 */
    volatile uint32_t CLUT_3;                    /* CLUT_3 */
    volatile uint32_t CLUT_4;                    /* CLUT_4 */
    volatile uint32_t CLUT_5;                    /* CLUT_5 */
    volatile uint32_t CLUT_6;                    /* CLUT_6 */
    volatile uint32_t CLUT_7;                    /* CLUT_7 */
    volatile uint32_t CLUT_8;                    /* CLUT_8 */
    volatile uint32_t CLUT_9;                    /* CLUT_9 */
    volatile uint32_t CLUT_10;                   /* CLUT_10 */
    volatile uint32_t CLUT_11;                   /* CLUT_11 */
    volatile uint32_t CLUT_12;                   /* CLUT_12 */
    volatile uint32_t CLUT_13;                   /* CLUT_13 */
    volatile uint32_t CLUT_14;                   /* CLUT_14 */
    volatile uint32_t CLUT_15;                   /* CLUT_15 */
    volatile uint32_t SAFETY_ATTRIBUTES;         /* SAFETY_ATTRIBUTES */
    volatile uint32_t SAFETY_CAPT_SIGNATURE;     /* SAFETY_CAPT_SIGNATURE */
    volatile uint32_t SAFETY_POSITION;           /* SAFETY_POSITION */
    volatile uint32_t SAFETY_REF_SIGNATURE;      /* SAFETY_REF_SIGNATURE */
    volatile uint32_t SAFETY_SIZE;               /* SAFETY_SIZE */
    volatile uint32_t SAFETY_LFSR_SEED;          /* SAFETY_LFSR_SEED */
    volatile uint32_t LUMAKEY;                   /* LUMAKEY */
} CSL_dss_vidRegs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_DSS_VID_ACCUH_0                                                    (0x00000000U)
#define CSL_DSS_VID_ACCUH_1                                                    (0x00000004U)
#define CSL_DSS_VID_ACCUH2_0                                                   (0x00000008U)
#define CSL_DSS_VID_ACCUH2_1                                                   (0x0000000CU)
#define CSL_DSS_VID_ACCUV_0                                                    (0x00000010U)
#define CSL_DSS_VID_ACCUV_1                                                    (0x00000014U)
#define CSL_DSS_VID_ACCUV2_0                                                   (0x00000018U)
#define CSL_DSS_VID_ACCUV2_1                                                   (0x0000001CU)
#define CSL_DSS_VID_ATTRIBUTES                                                 (0x00000020U)
#define CSL_DSS_VID_ATTRIBUTES2                                                (0x00000024U)
#define CSL_DSS_VID_BA_0                                                       (0x00000028U)
#define CSL_DSS_VID_BA_1                                                       (0x0000002CU)
#define CSL_DSS_VID_BA_UV_0                                                    (0x00000030U)
#define CSL_DSS_VID_BA_UV_1                                                    (0x00000034U)
#define CSL_DSS_VID_BUF_SIZE_STATUS                                            (0x00000038U)
#define CSL_DSS_VID_BUF_THRESHOLD                                              (0x0000003CU)
#define CSL_DSS_VID_CSC_COEF0                                                  (0x00000040U)
#define CSL_DSS_VID_CSC_COEF1                                                  (0x00000044U)
#define CSL_DSS_VID_CSC_COEF2                                                  (0x00000048U)
#define CSL_DSS_VID_CSC_COEF3                                                  (0x0000004CU)
#define CSL_DSS_VID_CSC_COEF4                                                  (0x00000050U)
#define CSL_DSS_VID_CSC_COEF5                                                  (0x00000054U)
#define CSL_DSS_VID_CSC_COEF6                                                  (0x00000058U)
#define CSL_DSS_VID_FIRH                                                       (0x0000005CU)
#define CSL_DSS_VID_FIRH2                                                      (0x00000060U)
#define CSL_DSS_VID_FIRV                                                       (0x00000064U)
#define CSL_DSS_VID_FIRV2                                                      (0x00000068U)
#define CSL_DSS_VID_FIR_COEF_H0(index)                                         (0x0000006CU+((uint32_t)(index)*0x4U))
#define CSL_DSS_VID_FIR_COEF_H0_C(index)                                       (0x00000090U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VID_FIR_COEF_H12(index)                                        (0x000000B4U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VID_FIR_COEF_H12_C(index)                                      (0x000000F4U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VID_FIR_COEF_V0(index)                                         (0x00000134U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VID_FIR_COEF_V0_C(index)                                       (0x00000158U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VID_FIR_COEF_V12(index)                                        (0x0000017CU+((uint32_t)(index)*0x4U))
#define CSL_DSS_VID_FIR_COEF_V12_C(index)                                      (0x000001BCU+((uint32_t)(index)*0x4U))
#define CSL_DSS_VID_GLOBAL_ALPHA                                               (0x000001FCU)
#define CSL_DSS_VID_MFLAG_THRESHOLD                                            (0x00000208U)
#define CSL_DSS_VID_PICTURE_SIZE                                               (0x0000020CU)
#define CSL_DSS_VID_PIXEL_INC                                                  (0x00000210U)
#define CSL_DSS_VID_PRELOAD                                                    (0x00000218U)
#define CSL_DSS_VID_ROW_INC                                                    (0x0000021CU)
#define CSL_DSS_VID_SIZE                                                       (0x00000220U)
#define CSL_DSS_VID_BA_EXT_0                                                   (0x0000022CU)
#define CSL_DSS_VID_BA_EXT_1                                                   (0x00000230U)
#define CSL_DSS_VID_BA_UV_EXT_0                                                (0x00000234U)
#define CSL_DSS_VID_BA_UV_EXT_1                                                (0x00000238U)
#define CSL_DSS_VID_CSC_COEF7                                                  (0x0000023CU)
#define CSL_DSS_VID_ROW_INC_UV                                                 (0x00000248U)
#define CSL_DSS_VID_CLUT_0                                                     (0x00000260U)
#define CSL_DSS_VID_CLUT_1                                                     (0x00000264U)
#define CSL_DSS_VID_CLUT_2                                                     (0x00000268U)
#define CSL_DSS_VID_CLUT_3                                                     (0x0000026CU)
#define CSL_DSS_VID_CLUT_4                                                     (0x00000270U)
#define CSL_DSS_VID_CLUT_5                                                     (0x00000274U)
#define CSL_DSS_VID_CLUT_6                                                     (0x00000278U)
#define CSL_DSS_VID_CLUT_7                                                     (0x0000027CU)
#define CSL_DSS_VID_CLUT_8                                                     (0x00000280U)
#define CSL_DSS_VID_CLUT_9                                                     (0x00000284U)
#define CSL_DSS_VID_CLUT_10                                                    (0x00000288U)
#define CSL_DSS_VID_CLUT_11                                                    (0x0000028CU)
#define CSL_DSS_VID_CLUT_12                                                    (0x00000290U)
#define CSL_DSS_VID_CLUT_13                                                    (0x00000294U)
#define CSL_DSS_VID_CLUT_14                                                    (0x00000298U)
#define CSL_DSS_VID_CLUT_15                                                    (0x0000029CU)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES                                          (0x000002A0U)
#define CSL_DSS_VID_SAFETY_CAPT_SIGNATURE                                      (0x000002A4U)
#define CSL_DSS_VID_SAFETY_POSITION                                            (0x000002A8U)
#define CSL_DSS_VID_SAFETY_REF_SIGNATURE                                       (0x000002ACU)
#define CSL_DSS_VID_SAFETY_SIZE                                                (0x000002B0U)
#define CSL_DSS_VID_SAFETY_LFSR_SEED                                           (0x000002B4U)
#define CSL_DSS_VID_LUMAKEY                                                    (0x000002B8U)

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* ACCUH_0 */

#define CSL_DSS_VID_ACCUH_0_HORIZONTALACCU_MASK                                (0x00FFFFFFU)
#define CSL_DSS_VID_ACCUH_0_HORIZONTALACCU_SHIFT                               (0x00000000U)
#define CSL_DSS_VID_ACCUH_0_HORIZONTALACCU_MAX                                 (0x00FFFFFFU)

#define CSL_DSS_VID_ACCUH_0_RESERVED_MASK                                      (0xFF000000U)
#define CSL_DSS_VID_ACCUH_0_RESERVED_SHIFT                                     (0x00000018U)
#define CSL_DSS_VID_ACCUH_0_RESERVED_MAX                                       (0x000000FFU)

/* ACCUH_1 */

#define CSL_DSS_VID_ACCUH_1_HORIZONTALACCU_MASK                                (0x00FFFFFFU)
#define CSL_DSS_VID_ACCUH_1_HORIZONTALACCU_SHIFT                               (0x00000000U)
#define CSL_DSS_VID_ACCUH_1_HORIZONTALACCU_MAX                                 (0x00FFFFFFU)

#define CSL_DSS_VID_ACCUH_1_RESERVED_MASK                                      (0xFF000000U)
#define CSL_DSS_VID_ACCUH_1_RESERVED_SHIFT                                     (0x00000018U)
#define CSL_DSS_VID_ACCUH_1_RESERVED_MAX                                       (0x000000FFU)

/* ACCUH2_0 */

#define CSL_DSS_VID_ACCUH2_0_HORIZONTALACCU_MASK                               (0x00FFFFFFU)
#define CSL_DSS_VID_ACCUH2_0_HORIZONTALACCU_SHIFT                              (0x00000000U)
#define CSL_DSS_VID_ACCUH2_0_HORIZONTALACCU_MAX                                (0x00FFFFFFU)

#define CSL_DSS_VID_ACCUH2_0_RESERVED_MASK                                     (0xFF000000U)
#define CSL_DSS_VID_ACCUH2_0_RESERVED_SHIFT                                    (0x00000018U)
#define CSL_DSS_VID_ACCUH2_0_RESERVED_MAX                                      (0x000000FFU)

/* ACCUH2_1 */

#define CSL_DSS_VID_ACCUH2_1_HORIZONTALACCU_MASK                               (0x00FFFFFFU)
#define CSL_DSS_VID_ACCUH2_1_HORIZONTALACCU_SHIFT                              (0x00000000U)
#define CSL_DSS_VID_ACCUH2_1_HORIZONTALACCU_MAX                                (0x00FFFFFFU)

#define CSL_DSS_VID_ACCUH2_1_RESERVED_MASK                                     (0xFF000000U)
#define CSL_DSS_VID_ACCUH2_1_RESERVED_SHIFT                                    (0x00000018U)
#define CSL_DSS_VID_ACCUH2_1_RESERVED_MAX                                      (0x000000FFU)

/* ACCUV_0 */

#define CSL_DSS_VID_ACCUV_0_VERTICALACCU_MASK                                  (0x00FFFFFFU)
#define CSL_DSS_VID_ACCUV_0_VERTICALACCU_SHIFT                                 (0x00000000U)
#define CSL_DSS_VID_ACCUV_0_VERTICALACCU_MAX                                   (0x00FFFFFFU)

#define CSL_DSS_VID_ACCUV_0_RESERVED_MASK                                      (0xFF000000U)
#define CSL_DSS_VID_ACCUV_0_RESERVED_SHIFT                                     (0x00000018U)
#define CSL_DSS_VID_ACCUV_0_RESERVED_MAX                                       (0x000000FFU)

/* ACCUV_1 */

#define CSL_DSS_VID_ACCUV_1_VERTICALACCU_MASK                                  (0x00FFFFFFU)
#define CSL_DSS_VID_ACCUV_1_VERTICALACCU_SHIFT                                 (0x00000000U)
#define CSL_DSS_VID_ACCUV_1_VERTICALACCU_MAX                                   (0x00FFFFFFU)

#define CSL_DSS_VID_ACCUV_1_RESERVED_MASK                                      (0xFF000000U)
#define CSL_DSS_VID_ACCUV_1_RESERVED_SHIFT                                     (0x00000018U)
#define CSL_DSS_VID_ACCUV_1_RESERVED_MAX                                       (0x000000FFU)

/* ACCUV2_0 */

#define CSL_DSS_VID_ACCUV2_0_VERTICALACCU_MASK                                 (0x00FFFFFFU)
#define CSL_DSS_VID_ACCUV2_0_VERTICALACCU_SHIFT                                (0x00000000U)
#define CSL_DSS_VID_ACCUV2_0_VERTICALACCU_MAX                                  (0x00FFFFFFU)

#define CSL_DSS_VID_ACCUV2_0_RESERVED_MASK                                     (0xFF000000U)
#define CSL_DSS_VID_ACCUV2_0_RESERVED_SHIFT                                    (0x00000018U)
#define CSL_DSS_VID_ACCUV2_0_RESERVED_MAX                                      (0x000000FFU)

/* ACCUV2_1 */

#define CSL_DSS_VID_ACCUV2_1_VERTICALACCU_MASK                                 (0x00FFFFFFU)
#define CSL_DSS_VID_ACCUV2_1_VERTICALACCU_SHIFT                                (0x00000000U)
#define CSL_DSS_VID_ACCUV2_1_VERTICALACCU_MAX                                  (0x00FFFFFFU)

#define CSL_DSS_VID_ACCUV2_1_RESERVED_MASK                                     (0xFF000000U)
#define CSL_DSS_VID_ACCUV2_1_RESERVED_SHIFT                                    (0x00000018U)
#define CSL_DSS_VID_ACCUV2_1_RESERVED_MAX                                      (0x000000FFU)

/* ATTRIBUTES */

#define CSL_DSS_VID_ATTRIBUTES_ENABLE_MASK                                     (0x00000001U)
#define CSL_DSS_VID_ATTRIBUTES_ENABLE_SHIFT                                    (0x00000000U)
#define CSL_DSS_VID_ATTRIBUTES_ENABLE_MAX                                      (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_ENABLE_VAL_VIDEOENB                             (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_ENABLE_VAL_VIDEODIS                             (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_FORMAT_MASK                                     (0x0000007EU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_SHIFT                                    (0x00000001U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_MAX                                      (0x0000003FU)

#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ARGB16_4444                          (0x0U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ABGR16_4444                          (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGBA16_4444                          (0x2U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGB16_565                            (0x3U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BGR16_565                            (0x4U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ARGB16_1555                          (0x5U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ABGR16_1555                          (0x6U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ARGB32_8888                          (0x7U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ABGR32_8888                          (0x8U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGBA32_8888                          (0x9U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BGRA32_8888                          (0xAU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGB24P_888                           (0xBU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BGR24P_888                           (0xCU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ARGB32_2101010                       (0xEU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ABGR32_2101010                       (0xFU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_ARGB64_16161616                      (0x10U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGBA64_16161616                      (0x11U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BITMAP1                              (0x12U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BITMAP2                              (0x13U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BITMAP4                              (0x14U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BITMAP8                              (0x15U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGB565A8                             (0x16U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BGR565A8                             (0x17U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XRGB16_4444                          (0x20U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XBGR16_4444                          (0x21U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGBX16_4444                          (0x22U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XRGB16_1555                          (0x25U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XBGR16_1555                          (0x26U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XRGB32_8888                          (0x27U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XBGR32_8888                          (0x28U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGBX32_8888                          (0x29U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_BGRX32_8888                          (0x2AU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XRGB32_2101010                       (0x2EU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XBGR32_2101010                       (0x2FU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_XRGB64_16161616                      (0x30U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_RGBX64_16161616                      (0x31U)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_YUV420_NV12                          (0x3DU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_YUV422_YUV2                          (0x3EU)
#define CSL_DSS_VID_ATTRIBUTES_FORMAT_VAL_YUV422_UYVY                          (0x3FU)

#define CSL_DSS_VID_ATTRIBUTES_RESIZEENABLE_MASK                               (0x00000180U)
#define CSL_DSS_VID_ATTRIBUTES_RESIZEENABLE_SHIFT                              (0x00000007U)
#define CSL_DSS_VID_ATTRIBUTES_RESIZEENABLE_MAX                                (0x00000003U)

#define CSL_DSS_VID_ATTRIBUTES_RESIZEENABLE_VAL_RESIZEPROC                     (0x0U)
#define CSL_DSS_VID_ATTRIBUTES_RESIZEENABLE_VAL_HRESIZE                        (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_RESIZEENABLE_VAL_VRESIZE                        (0x2U)
#define CSL_DSS_VID_ATTRIBUTES_RESIZEENABLE_VAL_HVRESIZE                       (0x3U)

#define CSL_DSS_VID_ATTRIBUTES_COLORCONVENABLE_MASK                            (0x00000200U)
#define CSL_DSS_VID_ATTRIBUTES_COLORCONVENABLE_SHIFT                           (0x00000009U)
#define CSL_DSS_VID_ATTRIBUTES_COLORCONVENABLE_MAX                             (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_COLORCONVENABLE_VAL_COLSPCENB                   (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_COLORCONVENABLE_VAL_COLSPCDIS                   (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_NIBBLEMODE_MASK                                 (0x00000400U)
#define CSL_DSS_VID_ATTRIBUTES_NIBBLEMODE_SHIFT                                (0x0000000AU)
#define CSL_DSS_VID_ATTRIBUTES_NIBBLEMODE_MAX                                  (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_NIBBLEMODE_VAL_NIBBLEMODEEN                     (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_NIBBLEMODE_VAL_NIBBLEMODEDIS                    (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_FULLRANGE_MASK                                  (0x00000800U)
#define CSL_DSS_VID_ATTRIBUTES_FULLRANGE_SHIFT                                 (0x0000000BU)
#define CSL_DSS_VID_ATTRIBUTES_FULLRANGE_MAX                                   (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_FULLRANGE_VAL_FULLRANGE                         (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_FULLRANGE_VAL_LIMRANGE                          (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_FLIP_MASK                                       (0x00001000U)
#define CSL_DSS_VID_ATTRIBUTES_FLIP_SHIFT                                      (0x0000000CU)
#define CSL_DSS_VID_ATTRIBUTES_FLIP_MAX                                        (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_FLIP_VAL_FLIP                                   (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_FLIP_VAL_NOFLIP                                 (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_RESERVED1_MASK                                  (0x00002000U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED1_SHIFT                                 (0x0000000DU)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED1_MAX                                   (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_RESERVED9_MASK                                  (0x0001C000U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED9_SHIFT                                 (0x0000000EU)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED9_MAX                                   (0x00000007U)

#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESHAUTO_MASK                            (0x00020000U)
#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESHAUTO_SHIFT                           (0x00000011U)
#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESHAUTO_MAX                             (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESHAUTO_VAL_SELFREFRESHAUTOEN           (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESHAUTO_VAL_SELFREFRESHAUTODIS          (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_RESERVED7_MASK                                  (0x00040000U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED7_SHIFT                                 (0x00000012U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED7_MAX                                   (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_BUFPRELOAD_MASK                                 (0x00080000U)
#define CSL_DSS_VID_ATTRIBUTES_BUFPRELOAD_SHIFT                                (0x00000013U)
#define CSL_DSS_VID_ATTRIBUTES_BUFPRELOAD_MAX                                  (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_BUFPRELOAD_VAL_HIGHTHRES                        (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_BUFPRELOAD_VAL_DEFVAL                           (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_RESERVED2_MASK                                  (0x00100000U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED2_SHIFT                                 (0x00000014U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED2_MAX                                   (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_VERTICALTAPS_MASK                               (0x00200000U)
#define CSL_DSS_VID_ATTRIBUTES_VERTICALTAPS_SHIFT                              (0x00000015U)
#define CSL_DSS_VID_ATTRIBUTES_VERTICALTAPS_MAX                                (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_VERTICALTAPS_VAL_TAPS5                          (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_VERTICALTAPS_VAL_TAPS3                          (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_RESERVED6_MASK                                  (0x00400000U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED6_SHIFT                                 (0x00000016U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED6_MAX                                   (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_ARBITRATION_MASK                                (0x00800000U)
#define CSL_DSS_VID_ATTRIBUTES_ARBITRATION_SHIFT                               (0x00000017U)
#define CSL_DSS_VID_ATTRIBUTES_ARBITRATION_MAX                                 (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_ARBITRATION_VAL_HIGHPRIO                        (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_ARBITRATION_VAL_NORMALPRIO                      (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESH_MASK                                (0x01000000U)
#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESH_SHIFT                               (0x00000018U)
#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESH_MAX                                 (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESH_VAL_SELFREFRESHENB                  (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_SELFREFRESH_VAL_SELFREFRESHDIS                  (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_RESERVED5_MASK                                  (0x0E000000U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED5_SHIFT                                 (0x00000019U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED5_MAX                                   (0x00000007U)

#define CSL_DSS_VID_ATTRIBUTES_PREMULTIPLYALPHA_MASK                           (0x10000000U)
#define CSL_DSS_VID_ATTRIBUTES_PREMULTIPLYALPHA_SHIFT                          (0x0000001CU)
#define CSL_DSS_VID_ATTRIBUTES_PREMULTIPLYALPHA_MAX                            (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_PREMULTIPLYALPHA_VAL_PREMULTIPLIEDALPHA         (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_PREMULTIPLYALPHA_VAL_NONPREMULTIPLIEDALPHA      (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_RESERVED4_MASK                                  (0x20000000U)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED4_SHIFT                                 (0x0000001DU)
#define CSL_DSS_VID_ATTRIBUTES_RESERVED4_MAX                                   (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_GAMMAINVERSION_MASK                             (0x40000000U)
#define CSL_DSS_VID_ATTRIBUTES_GAMMAINVERSION_SHIFT                            (0x0000001EU)
#define CSL_DSS_VID_ATTRIBUTES_GAMMAINVERSION_MAX                              (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_GAMMAINVERSION_VAL_INVGAMMAEN                   (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_GAMMAINVERSION_VAL_INVGAMMADIS                  (0x0U)

#define CSL_DSS_VID_ATTRIBUTES_LUMAKEYENABLE_MASK                              (0x80000000U)
#define CSL_DSS_VID_ATTRIBUTES_LUMAKEYENABLE_SHIFT                             (0x0000001FU)
#define CSL_DSS_VID_ATTRIBUTES_LUMAKEYENABLE_MAX                               (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES_LUMAKEYENABLE_VAL_LUMAKEYEN                     (0x1U)
#define CSL_DSS_VID_ATTRIBUTES_LUMAKEYENABLE_VAL_LUMAKEYDIS                    (0x0U)

/* ATTRIBUTES2 */

#define CSL_DSS_VID_ATTRIBUTES2_VC1ENABLE_MASK                                 (0x00000001U)
#define CSL_DSS_VID_ATTRIBUTES2_VC1ENABLE_SHIFT                                (0x00000000U)
#define CSL_DSS_VID_ATTRIBUTES2_VC1ENABLE_MAX                                  (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES2_VC1ENABLE_VAL_VC1ENB                           (0x1U)
#define CSL_DSS_VID_ATTRIBUTES2_VC1ENABLE_VAL_VC1DIS                           (0x0U)

#define CSL_DSS_VID_ATTRIBUTES2_VC1_RANGE_Y_MASK                               (0x0000000EU)
#define CSL_DSS_VID_ATTRIBUTES2_VC1_RANGE_Y_SHIFT                              (0x00000001U)
#define CSL_DSS_VID_ATTRIBUTES2_VC1_RANGE_Y_MAX                                (0x00000007U)

#define CSL_DSS_VID_ATTRIBUTES2_VC1_RANGE_CBCR_MASK                            (0x00000070U)
#define CSL_DSS_VID_ATTRIBUTES2_VC1_RANGE_CBCR_SHIFT                           (0x00000004U)
#define CSL_DSS_VID_ATTRIBUTES2_VC1_RANGE_CBCR_MAX                             (0x00000007U)

#define CSL_DSS_VID_ATTRIBUTES2_YUV_SIZE_MASK                                  (0x00000180U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_SIZE_SHIFT                                 (0x00000007U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_SIZE_MAX                                   (0x00000003U)

#define CSL_DSS_VID_ATTRIBUTES2_YUV_SIZE_VAL_8B                                (0x0U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_SIZE_VAL_10B                               (0x1U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_SIZE_VAL_12B                               (0x2U)

#define CSL_DSS_VID_ATTRIBUTES2_YUV_MODE_MASK                                  (0x00000200U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_MODE_SHIFT                                 (0x00000009U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_MODE_MAX                                   (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES2_YUV_MODE_VAL_PACKED                            (0x0U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_MODE_VAL_UNPACKED                          (0x1U)

#define CSL_DSS_VID_ATTRIBUTES2_YUV_ALIGN_MASK                                 (0x00000400U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_ALIGN_SHIFT                                (0x0000000AU)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_ALIGN_MAX                                  (0x00000001U)

#define CSL_DSS_VID_ATTRIBUTES2_YUV_ALIGN_VAL_MSB                              (0x1U)
#define CSL_DSS_VID_ATTRIBUTES2_YUV_ALIGN_VAL_LSB                              (0x0U)

#define CSL_DSS_VID_ATTRIBUTES2_RESERVED_MASK                                  (0x03FFF800U)
#define CSL_DSS_VID_ATTRIBUTES2_RESERVED_SHIFT                                 (0x0000000BU)
#define CSL_DSS_VID_ATTRIBUTES2_RESERVED_MAX                                   (0x00007FFFU)

#define CSL_DSS_VID_ATTRIBUTES2_TAGS_MASK                                      (0x7C000000U)
#define CSL_DSS_VID_ATTRIBUTES2_TAGS_SHIFT                                     (0x0000001AU)
#define CSL_DSS_VID_ATTRIBUTES2_TAGS_MAX                                       (0x0000001FU)

#define CSL_DSS_VID_ATTRIBUTES2_RESERVED3_MASK                                 (0x80000000U)
#define CSL_DSS_VID_ATTRIBUTES2_RESERVED3_SHIFT                                (0x0000001FU)
#define CSL_DSS_VID_ATTRIBUTES2_RESERVED3_MAX                                  (0x00000001U)

/* BA_0 */

#define CSL_DSS_VID_BA_0_BA_MASK                                               (0xFFFFFFFFU)
#define CSL_DSS_VID_BA_0_BA_SHIFT                                              (0x00000000U)
#define CSL_DSS_VID_BA_0_BA_MAX                                                (0xFFFFFFFFU)

/* BA_1 */

#define CSL_DSS_VID_BA_1_BA_MASK                                               (0xFFFFFFFFU)
#define CSL_DSS_VID_BA_1_BA_SHIFT                                              (0x00000000U)
#define CSL_DSS_VID_BA_1_BA_MAX                                                (0xFFFFFFFFU)

/* BA_UV_0 */

#define CSL_DSS_VID_BA_UV_0_BA_MASK                                            (0xFFFFFFFFU)
#define CSL_DSS_VID_BA_UV_0_BA_SHIFT                                           (0x00000000U)
#define CSL_DSS_VID_BA_UV_0_BA_MAX                                             (0xFFFFFFFFU)

/* BA_UV_1 */

#define CSL_DSS_VID_BA_UV_1_BA_MASK                                            (0xFFFFFFFFU)
#define CSL_DSS_VID_BA_UV_1_BA_SHIFT                                           (0x00000000U)
#define CSL_DSS_VID_BA_UV_1_BA_MAX                                             (0xFFFFFFFFU)

/* BUF_SIZE_STATUS */

#define CSL_DSS_VID_BUF_SIZE_STATUS_BUFSIZE_MASK                               (0x0000FFFFU)
#define CSL_DSS_VID_BUF_SIZE_STATUS_BUFSIZE_SHIFT                              (0x00000000U)
#define CSL_DSS_VID_BUF_SIZE_STATUS_BUFSIZE_MAX                                (0x0000FFFFU)

#define CSL_DSS_VID_BUF_SIZE_STATUS_RESERVED_61_MASK                           (0xFFFF0000U)
#define CSL_DSS_VID_BUF_SIZE_STATUS_RESERVED_61_SHIFT                          (0x00000010U)
#define CSL_DSS_VID_BUF_SIZE_STATUS_RESERVED_61_MAX                            (0x0000FFFFU)

/* BUF_THRESHOLD */

#define CSL_DSS_VID_BUF_THRESHOLD_BUFLOWTHRESHOLD_MASK                         (0x0000FFFFU)
#define CSL_DSS_VID_BUF_THRESHOLD_BUFLOWTHRESHOLD_SHIFT                        (0x00000000U)
#define CSL_DSS_VID_BUF_THRESHOLD_BUFLOWTHRESHOLD_MAX                          (0x0000FFFFU)

#define CSL_DSS_VID_BUF_THRESHOLD_BUFHIGHTHRESHOLD_MASK                        (0xFFFF0000U)
#define CSL_DSS_VID_BUF_THRESHOLD_BUFHIGHTHRESHOLD_SHIFT                       (0x00000010U)
#define CSL_DSS_VID_BUF_THRESHOLD_BUFHIGHTHRESHOLD_MAX                         (0x0000FFFFU)

/* CSC_COEF0 */

#define CSL_DSS_VID_CSC_COEF0_C00_MASK                                         (0x000007FFU)
#define CSL_DSS_VID_CSC_COEF0_C00_SHIFT                                        (0x00000000U)
#define CSL_DSS_VID_CSC_COEF0_C00_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF0_RESERVED_53_MASK                                 (0x0000F800U)
#define CSL_DSS_VID_CSC_COEF0_RESERVED_53_SHIFT                                (0x0000000BU)
#define CSL_DSS_VID_CSC_COEF0_RESERVED_53_MAX                                  (0x0000001FU)

#define CSL_DSS_VID_CSC_COEF0_C01_MASK                                         (0x07FF0000U)
#define CSL_DSS_VID_CSC_COEF0_C01_SHIFT                                        (0x00000010U)
#define CSL_DSS_VID_CSC_COEF0_C01_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF0_RESERVED_52_MASK                                 (0xF8000000U)
#define CSL_DSS_VID_CSC_COEF0_RESERVED_52_SHIFT                                (0x0000001BU)
#define CSL_DSS_VID_CSC_COEF0_RESERVED_52_MAX                                  (0x0000001FU)

/* CSC_COEF1 */

#define CSL_DSS_VID_CSC_COEF1_C02_MASK                                         (0x000007FFU)
#define CSL_DSS_VID_CSC_COEF1_C02_SHIFT                                        (0x00000000U)
#define CSL_DSS_VID_CSC_COEF1_C02_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF1_RESERVED_55_MASK                                 (0x0000F800U)
#define CSL_DSS_VID_CSC_COEF1_RESERVED_55_SHIFT                                (0x0000000BU)
#define CSL_DSS_VID_CSC_COEF1_RESERVED_55_MAX                                  (0x0000001FU)

#define CSL_DSS_VID_CSC_COEF1_C10_MASK                                         (0x07FF0000U)
#define CSL_DSS_VID_CSC_COEF1_C10_SHIFT                                        (0x00000010U)
#define CSL_DSS_VID_CSC_COEF1_C10_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF1_RESERVED_54_MASK                                 (0xF8000000U)
#define CSL_DSS_VID_CSC_COEF1_RESERVED_54_SHIFT                                (0x0000001BU)
#define CSL_DSS_VID_CSC_COEF1_RESERVED_54_MAX                                  (0x0000001FU)

/* CSC_COEF2 */

#define CSL_DSS_VID_CSC_COEF2_C11_MASK                                         (0x000007FFU)
#define CSL_DSS_VID_CSC_COEF2_C11_SHIFT                                        (0x00000000U)
#define CSL_DSS_VID_CSC_COEF2_C11_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF2_RESERVED_57_MASK                                 (0x0000F800U)
#define CSL_DSS_VID_CSC_COEF2_RESERVED_57_SHIFT                                (0x0000000BU)
#define CSL_DSS_VID_CSC_COEF2_RESERVED_57_MAX                                  (0x0000001FU)

#define CSL_DSS_VID_CSC_COEF2_C12_MASK                                         (0x07FF0000U)
#define CSL_DSS_VID_CSC_COEF2_C12_SHIFT                                        (0x00000010U)
#define CSL_DSS_VID_CSC_COEF2_C12_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF2_RESERVED_56_MASK                                 (0xF8000000U)
#define CSL_DSS_VID_CSC_COEF2_RESERVED_56_SHIFT                                (0x0000001BU)
#define CSL_DSS_VID_CSC_COEF2_RESERVED_56_MAX                                  (0x0000001FU)

/* CSC_COEF3 */

#define CSL_DSS_VID_CSC_COEF3_C20_MASK                                         (0x000007FFU)
#define CSL_DSS_VID_CSC_COEF3_C20_SHIFT                                        (0x00000000U)
#define CSL_DSS_VID_CSC_COEF3_C20_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF3_RESERVED_59_MASK                                 (0x0000F800U)
#define CSL_DSS_VID_CSC_COEF3_RESERVED_59_SHIFT                                (0x0000000BU)
#define CSL_DSS_VID_CSC_COEF3_RESERVED_59_MAX                                  (0x0000001FU)

#define CSL_DSS_VID_CSC_COEF3_C21_MASK                                         (0x07FF0000U)
#define CSL_DSS_VID_CSC_COEF3_C21_SHIFT                                        (0x00000010U)
#define CSL_DSS_VID_CSC_COEF3_C21_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF3_RESERVED_58_MASK                                 (0xF8000000U)
#define CSL_DSS_VID_CSC_COEF3_RESERVED_58_SHIFT                                (0x0000001BU)
#define CSL_DSS_VID_CSC_COEF3_RESERVED_58_MAX                                  (0x0000001FU)

/* CSC_COEF4 */

#define CSL_DSS_VID_CSC_COEF4_C22_MASK                                         (0x000007FFU)
#define CSL_DSS_VID_CSC_COEF4_C22_SHIFT                                        (0x00000000U)
#define CSL_DSS_VID_CSC_COEF4_C22_MAX                                          (0x000007FFU)

#define CSL_DSS_VID_CSC_COEF4_RESERVED_60_MASK                                 (0xFFFFF800U)
#define CSL_DSS_VID_CSC_COEF4_RESERVED_60_SHIFT                                (0x0000000BU)
#define CSL_DSS_VID_CSC_COEF4_RESERVED_60_MAX                                  (0x001FFFFFU)

/* CSC_COEF5 */

#define CSL_DSS_VID_CSC_COEF5_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VID_CSC_COEF5_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VID_CSC_COEF5_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VID_CSC_COEF5_PREOFFSET1_MASK                                  (0x0000FFF8U)
#define CSL_DSS_VID_CSC_COEF5_PREOFFSET1_SHIFT                                 (0x00000003U)
#define CSL_DSS_VID_CSC_COEF5_PREOFFSET1_MAX                                   (0x00001FFFU)

#define CSL_DSS_VID_CSC_COEF5_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VID_CSC_COEF5_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VID_CSC_COEF5_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VID_CSC_COEF5_PREOFFSET2_MASK                                  (0xFFF80000U)
#define CSL_DSS_VID_CSC_COEF5_PREOFFSET2_SHIFT                                 (0x00000013U)
#define CSL_DSS_VID_CSC_COEF5_PREOFFSET2_MAX                                   (0x00001FFFU)

/* CSC_COEF6 */

#define CSL_DSS_VID_CSC_COEF6_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VID_CSC_COEF6_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VID_CSC_COEF6_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VID_CSC_COEF6_PREOFFSET3_MASK                                  (0x0000FFF8U)
#define CSL_DSS_VID_CSC_COEF6_PREOFFSET3_SHIFT                                 (0x00000003U)
#define CSL_DSS_VID_CSC_COEF6_PREOFFSET3_MAX                                   (0x00001FFFU)

#define CSL_DSS_VID_CSC_COEF6_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VID_CSC_COEF6_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VID_CSC_COEF6_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VID_CSC_COEF6_POSTOFFSET1_MASK                                 (0xFFF80000U)
#define CSL_DSS_VID_CSC_COEF6_POSTOFFSET1_SHIFT                                (0x00000013U)
#define CSL_DSS_VID_CSC_COEF6_POSTOFFSET1_MAX                                  (0x00001FFFU)

/* FIRH */

#define CSL_DSS_VID_FIRH_FIRHINC_MASK                                          (0x00FFFFFFU)
#define CSL_DSS_VID_FIRH_FIRHINC_SHIFT                                         (0x00000000U)
#define CSL_DSS_VID_FIRH_FIRHINC_MAX                                           (0x00FFFFFFU)

#define CSL_DSS_VID_FIRH_RESERVED_MASK                                         (0xFF000000U)
#define CSL_DSS_VID_FIRH_RESERVED_SHIFT                                        (0x00000018U)
#define CSL_DSS_VID_FIRH_RESERVED_MAX                                          (0x000000FFU)

/* FIRH2 */

#define CSL_DSS_VID_FIRH2_FIRHINC_MASK                                         (0x00FFFFFFU)
#define CSL_DSS_VID_FIRH2_FIRHINC_SHIFT                                        (0x00000000U)
#define CSL_DSS_VID_FIRH2_FIRHINC_MAX                                          (0x00FFFFFFU)

#define CSL_DSS_VID_FIRH2_RESERVED_MASK                                        (0xFF000000U)
#define CSL_DSS_VID_FIRH2_RESERVED_SHIFT                                       (0x00000018U)
#define CSL_DSS_VID_FIRH2_RESERVED_MAX                                         (0x000000FFU)

/* FIRV */

#define CSL_DSS_VID_FIRV_FIRVINC_MASK                                          (0x00FFFFFFU)
#define CSL_DSS_VID_FIRV_FIRVINC_SHIFT                                         (0x00000000U)
#define CSL_DSS_VID_FIRV_FIRVINC_MAX                                           (0x00FFFFFFU)

#define CSL_DSS_VID_FIRV_RESERVED_MASK                                         (0xFF000000U)
#define CSL_DSS_VID_FIRV_RESERVED_SHIFT                                        (0x00000018U)
#define CSL_DSS_VID_FIRV_RESERVED_MAX                                          (0x000000FFU)

/* FIRV2 */

#define CSL_DSS_VID_FIRV2_FIRVINC_MASK                                         (0x00FFFFFFU)
#define CSL_DSS_VID_FIRV2_FIRVINC_SHIFT                                        (0x00000000U)
#define CSL_DSS_VID_FIRV2_FIRVINC_MAX                                          (0x00FFFFFFU)

#define CSL_DSS_VID_FIRV2_RESERVED_MASK                                        (0xFF000000U)
#define CSL_DSS_VID_FIRV2_RESERVED_SHIFT                                       (0x00000018U)
#define CSL_DSS_VID_FIRV2_RESERVED_MAX                                         (0x000000FFU)

/* FIR_COEF_H0 */

#define CSL_DSS_VID_FIR_COEF_H0_FIRHC0_MASK                                    (0x000003FFU)
#define CSL_DSS_VID_FIR_COEF_H0_FIRHC0_SHIFT                                   (0x00000000U)
#define CSL_DSS_VID_FIR_COEF_H0_FIRHC0_MAX                                     (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_H0_RESERVED1_MASK                                 (0x3FFFFC00U)
#define CSL_DSS_VID_FIR_COEF_H0_RESERVED1_SHIFT                                (0x0000000AU)
#define CSL_DSS_VID_FIR_COEF_H0_RESERVED1_MAX                                  (0x000FFFFFU)

#define CSL_DSS_VID_FIR_COEF_H0_RESERVED_MASK                                  (0xC0000000U)
#define CSL_DSS_VID_FIR_COEF_H0_RESERVED_SHIFT                                 (0x0000001EU)
#define CSL_DSS_VID_FIR_COEF_H0_RESERVED_MAX                                   (0x00000003U)

/* FIR_COEF_H0_C */

#define CSL_DSS_VID_FIR_COEF_H0_C_FIRHC0_MASK                                  (0x000003FFU)
#define CSL_DSS_VID_FIR_COEF_H0_C_FIRHC0_SHIFT                                 (0x00000000U)
#define CSL_DSS_VID_FIR_COEF_H0_C_FIRHC0_MAX                                   (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_H0_C_RESERVED1_MASK                               (0x3FFFFC00U)
#define CSL_DSS_VID_FIR_COEF_H0_C_RESERVED1_SHIFT                              (0x0000000AU)
#define CSL_DSS_VID_FIR_COEF_H0_C_RESERVED1_MAX                                (0x000FFFFFU)

#define CSL_DSS_VID_FIR_COEF_H0_C_RESERVED_MASK                                (0xC0000000U)
#define CSL_DSS_VID_FIR_COEF_H0_C_RESERVED_SHIFT                               (0x0000001EU)
#define CSL_DSS_VID_FIR_COEF_H0_C_RESERVED_MAX                                 (0x00000003U)

/* FIR_COEF_H12 */

#define CSL_DSS_VID_FIR_COEF_H12_RESERVED1_MASK                                (0x000003FFU)
#define CSL_DSS_VID_FIR_COEF_H12_RESERVED1_SHIFT                               (0x00000000U)
#define CSL_DSS_VID_FIR_COEF_H12_RESERVED1_MAX                                 (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_H12_FIRHC1_MASK                                   (0x000FFC00U)
#define CSL_DSS_VID_FIR_COEF_H12_FIRHC1_SHIFT                                  (0x0000000AU)
#define CSL_DSS_VID_FIR_COEF_H12_FIRHC1_MAX                                    (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_H12_FIRHC2_MASK                                   (0x3FF00000U)
#define CSL_DSS_VID_FIR_COEF_H12_FIRHC2_SHIFT                                  (0x00000014U)
#define CSL_DSS_VID_FIR_COEF_H12_FIRHC2_MAX                                    (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_H12_RESERVED_MASK                                 (0xC0000000U)
#define CSL_DSS_VID_FIR_COEF_H12_RESERVED_SHIFT                                (0x0000001EU)
#define CSL_DSS_VID_FIR_COEF_H12_RESERVED_MAX                                  (0x00000003U)

/* FIR_COEF_H12_C */

#define CSL_DSS_VID_FIR_COEF_H12_C_RESERVED1_MASK                              (0x000003FFU)
#define CSL_DSS_VID_FIR_COEF_H12_C_RESERVED1_SHIFT                             (0x00000000U)
#define CSL_DSS_VID_FIR_COEF_H12_C_RESERVED1_MAX                               (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_H12_C_FIRHC1_MASK                                 (0x000FFC00U)
#define CSL_DSS_VID_FIR_COEF_H12_C_FIRHC1_SHIFT                                (0x0000000AU)
#define CSL_DSS_VID_FIR_COEF_H12_C_FIRHC1_MAX                                  (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_H12_C_FIRHC2_MASK                                 (0x3FF00000U)
#define CSL_DSS_VID_FIR_COEF_H12_C_FIRHC2_SHIFT                                (0x00000014U)
#define CSL_DSS_VID_FIR_COEF_H12_C_FIRHC2_MAX                                  (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_H12_C_RESERVED_MASK                               (0xC0000000U)
#define CSL_DSS_VID_FIR_COEF_H12_C_RESERVED_SHIFT                              (0x0000001EU)
#define CSL_DSS_VID_FIR_COEF_H12_C_RESERVED_MAX                                (0x00000003U)

/* FIR_COEF_V0 */

#define CSL_DSS_VID_FIR_COEF_V0_FIRVC0_MASK                                    (0x000003FFU)
#define CSL_DSS_VID_FIR_COEF_V0_FIRVC0_SHIFT                                   (0x00000000U)
#define CSL_DSS_VID_FIR_COEF_V0_FIRVC0_MAX                                     (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_V0_RESERVED1_MASK                                 (0x3FFFFC00U)
#define CSL_DSS_VID_FIR_COEF_V0_RESERVED1_SHIFT                                (0x0000000AU)
#define CSL_DSS_VID_FIR_COEF_V0_RESERVED1_MAX                                  (0x000FFFFFU)

#define CSL_DSS_VID_FIR_COEF_V0_RESERVED_MASK                                  (0xC0000000U)
#define CSL_DSS_VID_FIR_COEF_V0_RESERVED_SHIFT                                 (0x0000001EU)
#define CSL_DSS_VID_FIR_COEF_V0_RESERVED_MAX                                   (0x00000003U)

/* FIR_COEF_V0_C */

#define CSL_DSS_VID_FIR_COEF_V0_C_FIRVC0_MASK                                  (0x000003FFU)
#define CSL_DSS_VID_FIR_COEF_V0_C_FIRVC0_SHIFT                                 (0x00000000U)
#define CSL_DSS_VID_FIR_COEF_V0_C_FIRVC0_MAX                                   (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_V0_C_RESERVED1_MASK                               (0x3FFFFC00U)
#define CSL_DSS_VID_FIR_COEF_V0_C_RESERVED1_SHIFT                              (0x0000000AU)
#define CSL_DSS_VID_FIR_COEF_V0_C_RESERVED1_MAX                                (0x000FFFFFU)

#define CSL_DSS_VID_FIR_COEF_V0_C_RESERVED_MASK                                (0xC0000000U)
#define CSL_DSS_VID_FIR_COEF_V0_C_RESERVED_SHIFT                               (0x0000001EU)
#define CSL_DSS_VID_FIR_COEF_V0_C_RESERVED_MAX                                 (0x00000003U)

/* FIR_COEF_V12 */

#define CSL_DSS_VID_FIR_COEF_V12_RESERVED1_MASK                                (0x000003FFU)
#define CSL_DSS_VID_FIR_COEF_V12_RESERVED1_SHIFT                               (0x00000000U)
#define CSL_DSS_VID_FIR_COEF_V12_RESERVED1_MAX                                 (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_V12_FIRVC1_MASK                                   (0x000FFC00U)
#define CSL_DSS_VID_FIR_COEF_V12_FIRVC1_SHIFT                                  (0x0000000AU)
#define CSL_DSS_VID_FIR_COEF_V12_FIRVC1_MAX                                    (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_V12_FIRVC2_MASK                                   (0x3FF00000U)
#define CSL_DSS_VID_FIR_COEF_V12_FIRVC2_SHIFT                                  (0x00000014U)
#define CSL_DSS_VID_FIR_COEF_V12_FIRVC2_MAX                                    (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_V12_RESERVED_MASK                                 (0xC0000000U)
#define CSL_DSS_VID_FIR_COEF_V12_RESERVED_SHIFT                                (0x0000001EU)
#define CSL_DSS_VID_FIR_COEF_V12_RESERVED_MAX                                  (0x00000003U)

/* FIR_COEF_V12_C */

#define CSL_DSS_VID_FIR_COEF_V12_C_RESERVED1_MASK                              (0x000003FFU)
#define CSL_DSS_VID_FIR_COEF_V12_C_RESERVED1_SHIFT                             (0x00000000U)
#define CSL_DSS_VID_FIR_COEF_V12_C_RESERVED1_MAX                               (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_V12_C_FIRVC1_MASK                                 (0x000FFC00U)
#define CSL_DSS_VID_FIR_COEF_V12_C_FIRVC1_SHIFT                                (0x0000000AU)
#define CSL_DSS_VID_FIR_COEF_V12_C_FIRVC1_MAX                                  (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_V12_C_FIRVC2_MASK                                 (0x3FF00000U)
#define CSL_DSS_VID_FIR_COEF_V12_C_FIRVC2_SHIFT                                (0x00000014U)
#define CSL_DSS_VID_FIR_COEF_V12_C_FIRVC2_MAX                                  (0x000003FFU)

#define CSL_DSS_VID_FIR_COEF_V12_C_RESERVED_MASK                               (0xC0000000U)
#define CSL_DSS_VID_FIR_COEF_V12_C_RESERVED_SHIFT                              (0x0000001EU)
#define CSL_DSS_VID_FIR_COEF_V12_C_RESERVED_MAX                                (0x00000003U)

/* GLOBAL_ALPHA */

#define CSL_DSS_VID_GLOBAL_ALPHA_GLOBALALPHA_MASK                              (0x000000FFU)
#define CSL_DSS_VID_GLOBAL_ALPHA_GLOBALALPHA_SHIFT                             (0x00000000U)
#define CSL_DSS_VID_GLOBAL_ALPHA_GLOBALALPHA_MAX                               (0x000000FFU)

#define CSL_DSS_VID_GLOBAL_ALPHA_RESERVED_MASK                                 (0xFFFFFF00U)
#define CSL_DSS_VID_GLOBAL_ALPHA_RESERVED_SHIFT                                (0x00000008U)
#define CSL_DSS_VID_GLOBAL_ALPHA_RESERVED_MAX                                  (0x00FFFFFFU)

/* MFLAG_THRESHOLD */

#define CSL_DSS_VID_MFLAG_THRESHOLD_LT_MFLAG_MASK                              (0x0000FFFFU)
#define CSL_DSS_VID_MFLAG_THRESHOLD_LT_MFLAG_SHIFT                             (0x00000000U)
#define CSL_DSS_VID_MFLAG_THRESHOLD_LT_MFLAG_MAX                               (0x0000FFFFU)

#define CSL_DSS_VID_MFLAG_THRESHOLD_HT_MFLAG_MASK                              (0xFFFF0000U)
#define CSL_DSS_VID_MFLAG_THRESHOLD_HT_MFLAG_SHIFT                             (0x00000010U)
#define CSL_DSS_VID_MFLAG_THRESHOLD_HT_MFLAG_MAX                               (0x0000FFFFU)

/* PICTURE_SIZE */

#define CSL_DSS_VID_PICTURE_SIZE_MEMSIZEX_MASK                                 (0x00000FFFU)
#define CSL_DSS_VID_PICTURE_SIZE_MEMSIZEX_SHIFT                                (0x00000000U)
#define CSL_DSS_VID_PICTURE_SIZE_MEMSIZEX_MAX                                  (0x00000FFFU)

#define CSL_DSS_VID_PICTURE_SIZE_RESERVED1_MASK                                (0x0000F000U)
#define CSL_DSS_VID_PICTURE_SIZE_RESERVED1_SHIFT                               (0x0000000CU)
#define CSL_DSS_VID_PICTURE_SIZE_RESERVED1_MAX                                 (0x0000000FU)

#define CSL_DSS_VID_PICTURE_SIZE_MEMSIZEY_MASK                                 (0x0FFF0000U)
#define CSL_DSS_VID_PICTURE_SIZE_MEMSIZEY_SHIFT                                (0x00000010U)
#define CSL_DSS_VID_PICTURE_SIZE_MEMSIZEY_MAX                                  (0x00000FFFU)

#define CSL_DSS_VID_PICTURE_SIZE_RESERVED_MASK                                 (0xF0000000U)
#define CSL_DSS_VID_PICTURE_SIZE_RESERVED_SHIFT                                (0x0000001CU)
#define CSL_DSS_VID_PICTURE_SIZE_RESERVED_MAX                                  (0x0000000FU)

/* PIXEL_INC */

#define CSL_DSS_VID_PIXEL_INC_PIXELINC_MASK                                    (0x000000FFU)
#define CSL_DSS_VID_PIXEL_INC_PIXELINC_SHIFT                                   (0x00000000U)
#define CSL_DSS_VID_PIXEL_INC_PIXELINC_MAX                                     (0x000000FFU)

#define CSL_DSS_VID_PIXEL_INC_RESERVED_68_MASK                                 (0xFFFFFF00U)
#define CSL_DSS_VID_PIXEL_INC_RESERVED_68_SHIFT                                (0x00000008U)
#define CSL_DSS_VID_PIXEL_INC_RESERVED_68_MAX                                  (0x00FFFFFFU)

/* PRELOAD */

#define CSL_DSS_VID_PRELOAD_PRELOAD_MASK                                       (0x00000FFFU)
#define CSL_DSS_VID_PRELOAD_PRELOAD_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_PRELOAD_PRELOAD_MAX                                        (0x00000FFFU)

#define CSL_DSS_VID_PRELOAD_RESERVED_212_MASK                                  (0xFFFFF000U)
#define CSL_DSS_VID_PRELOAD_RESERVED_212_SHIFT                                 (0x0000000CU)
#define CSL_DSS_VID_PRELOAD_RESERVED_212_MAX                                   (0x000FFFFFU)

/* ROW_INC */

#define CSL_DSS_VID_ROW_INC_ROWINC_MASK                                        (0xFFFFFFFFU)
#define CSL_DSS_VID_ROW_INC_ROWINC_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_ROW_INC_ROWINC_MAX                                         (0xFFFFFFFFU)

/* SIZE */

#define CSL_DSS_VID_SIZE_SIZEX_MASK                                            (0x00000FFFU)
#define CSL_DSS_VID_SIZE_SIZEX_SHIFT                                           (0x00000000U)
#define CSL_DSS_VID_SIZE_SIZEX_MAX                                             (0x00000FFFU)

#define CSL_DSS_VID_SIZE_RESERVED_MASK                                         (0x0000F000U)
#define CSL_DSS_VID_SIZE_RESERVED_SHIFT                                        (0x0000000CU)
#define CSL_DSS_VID_SIZE_RESERVED_MAX                                          (0x0000000FU)

#define CSL_DSS_VID_SIZE_SIZEY_MASK                                            (0x0FFF0000U)
#define CSL_DSS_VID_SIZE_SIZEY_SHIFT                                           (0x00000010U)
#define CSL_DSS_VID_SIZE_SIZEY_MAX                                             (0x00000FFFU)

#define CSL_DSS_VID_SIZE_RESERVED1_MASK                                        (0xF0000000U)
#define CSL_DSS_VID_SIZE_RESERVED1_SHIFT                                       (0x0000001CU)
#define CSL_DSS_VID_SIZE_RESERVED1_MAX                                         (0x0000000FU)

/* BA_EXT_0 */

#define CSL_DSS_VID_BA_EXT_0_BA_EXT_MASK                                       (0x0000FFFFU)
#define CSL_DSS_VID_BA_EXT_0_BA_EXT_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_BA_EXT_0_BA_EXT_MAX                                        (0x0000FFFFU)

#define CSL_DSS_VID_BA_EXT_0_RESERVED_MASK                                     (0xFFFF0000U)
#define CSL_DSS_VID_BA_EXT_0_RESERVED_SHIFT                                    (0x00000010U)
#define CSL_DSS_VID_BA_EXT_0_RESERVED_MAX                                      (0x0000FFFFU)

/* BA_EXT_1 */

#define CSL_DSS_VID_BA_EXT_1_BA_EXT_MASK                                       (0x0000FFFFU)
#define CSL_DSS_VID_BA_EXT_1_BA_EXT_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_BA_EXT_1_BA_EXT_MAX                                        (0x0000FFFFU)

#define CSL_DSS_VID_BA_EXT_1_RESERVED_MASK                                     (0xFFFF0000U)
#define CSL_DSS_VID_BA_EXT_1_RESERVED_SHIFT                                    (0x00000010U)
#define CSL_DSS_VID_BA_EXT_1_RESERVED_MAX                                      (0x0000FFFFU)

/* BA_UV_EXT_0 */

#define CSL_DSS_VID_BA_UV_EXT_0_BA_UV_EXT_MASK                                 (0x0000FFFFU)
#define CSL_DSS_VID_BA_UV_EXT_0_BA_UV_EXT_SHIFT                                (0x00000000U)
#define CSL_DSS_VID_BA_UV_EXT_0_BA_UV_EXT_MAX                                  (0x0000FFFFU)

#define CSL_DSS_VID_BA_UV_EXT_0_RESERVED_MASK                                  (0xFFFF0000U)
#define CSL_DSS_VID_BA_UV_EXT_0_RESERVED_SHIFT                                 (0x00000010U)
#define CSL_DSS_VID_BA_UV_EXT_0_RESERVED_MAX                                   (0x0000FFFFU)

/* BA_UV_EXT_1 */

#define CSL_DSS_VID_BA_UV_EXT_1_BA_UV_EXT_MASK                                 (0x0000FFFFU)
#define CSL_DSS_VID_BA_UV_EXT_1_BA_UV_EXT_SHIFT                                (0x00000000U)
#define CSL_DSS_VID_BA_UV_EXT_1_BA_UV_EXT_MAX                                  (0x0000FFFFU)

#define CSL_DSS_VID_BA_UV_EXT_1_RESERVED_MASK                                  (0xFFFF0000U)
#define CSL_DSS_VID_BA_UV_EXT_1_RESERVED_SHIFT                                 (0x00000010U)
#define CSL_DSS_VID_BA_UV_EXT_1_RESERVED_MAX                                   (0x0000FFFFU)

/* CSC_COEF7 */

#define CSL_DSS_VID_CSC_COEF7_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VID_CSC_COEF7_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VID_CSC_COEF7_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VID_CSC_COEF7_POSTOFFSET2_MASK                                 (0x0000FFF8U)
#define CSL_DSS_VID_CSC_COEF7_POSTOFFSET2_SHIFT                                (0x00000003U)
#define CSL_DSS_VID_CSC_COEF7_POSTOFFSET2_MAX                                  (0x00001FFFU)

#define CSL_DSS_VID_CSC_COEF7_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VID_CSC_COEF7_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VID_CSC_COEF7_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VID_CSC_COEF7_POSTOFFSET3_MASK                                 (0xFFF80000U)
#define CSL_DSS_VID_CSC_COEF7_POSTOFFSET3_SHIFT                                (0x00000013U)
#define CSL_DSS_VID_CSC_COEF7_POSTOFFSET3_MAX                                  (0x00001FFFU)

/* ROW_INC_UV */

#define CSL_DSS_VID_ROW_INC_UV_ROWINC_MASK                                     (0xFFFFFFFFU)
#define CSL_DSS_VID_ROW_INC_UV_ROWINC_SHIFT                                    (0x00000000U)
#define CSL_DSS_VID_ROW_INC_UV_ROWINC_MAX                                      (0xFFFFFFFFU)

/* CLUT_0 */

#define CSL_DSS_VID_CLUT_0_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_0_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_0_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_0_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_0_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_0_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_0_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_0_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_0_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_0_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_0_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_0_INDEX_MAX                                           (0x000000FFU)

/* CLUT_1 */

#define CSL_DSS_VID_CLUT_1_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_1_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_1_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_1_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_1_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_1_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_1_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_1_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_1_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_1_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_1_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_1_INDEX_MAX                                           (0x000000FFU)

/* CLUT_2 */

#define CSL_DSS_VID_CLUT_2_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_2_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_2_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_2_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_2_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_2_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_2_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_2_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_2_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_2_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_2_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_2_INDEX_MAX                                           (0x000000FFU)

/* CLUT_3 */

#define CSL_DSS_VID_CLUT_3_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_3_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_3_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_3_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_3_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_3_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_3_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_3_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_3_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_3_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_3_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_3_INDEX_MAX                                           (0x000000FFU)

/* CLUT_4 */

#define CSL_DSS_VID_CLUT_4_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_4_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_4_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_4_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_4_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_4_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_4_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_4_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_4_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_4_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_4_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_4_INDEX_MAX                                           (0x000000FFU)

/* CLUT_5 */

#define CSL_DSS_VID_CLUT_5_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_5_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_5_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_5_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_5_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_5_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_5_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_5_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_5_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_5_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_5_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_5_INDEX_MAX                                           (0x000000FFU)

/* CLUT_6 */

#define CSL_DSS_VID_CLUT_6_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_6_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_6_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_6_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_6_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_6_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_6_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_6_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_6_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_6_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_6_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_6_INDEX_MAX                                           (0x000000FFU)

/* CLUT_7 */

#define CSL_DSS_VID_CLUT_7_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_7_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_7_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_7_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_7_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_7_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_7_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_7_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_7_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_7_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_7_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_7_INDEX_MAX                                           (0x000000FFU)

/* CLUT_8 */

#define CSL_DSS_VID_CLUT_8_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_8_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_8_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_8_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_8_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_8_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_8_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_8_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_8_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_8_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_8_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_8_INDEX_MAX                                           (0x000000FFU)

/* CLUT_9 */

#define CSL_DSS_VID_CLUT_9_VALUE_B_MASK                                        (0x000000FFU)
#define CSL_DSS_VID_CLUT_9_VALUE_B_SHIFT                                       (0x00000000U)
#define CSL_DSS_VID_CLUT_9_VALUE_B_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_9_VALUE_G_MASK                                        (0x0000FF00U)
#define CSL_DSS_VID_CLUT_9_VALUE_G_SHIFT                                       (0x00000008U)
#define CSL_DSS_VID_CLUT_9_VALUE_G_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_9_VALUE_R_MASK                                        (0x00FF0000U)
#define CSL_DSS_VID_CLUT_9_VALUE_R_SHIFT                                       (0x00000010U)
#define CSL_DSS_VID_CLUT_9_VALUE_R_MAX                                         (0x000000FFU)

#define CSL_DSS_VID_CLUT_9_INDEX_MASK                                          (0xFF000000U)
#define CSL_DSS_VID_CLUT_9_INDEX_SHIFT                                         (0x00000018U)
#define CSL_DSS_VID_CLUT_9_INDEX_MAX                                           (0x000000FFU)

/* CLUT_10 */

#define CSL_DSS_VID_CLUT_10_VALUE_B_MASK                                       (0x000000FFU)
#define CSL_DSS_VID_CLUT_10_VALUE_B_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_CLUT_10_VALUE_B_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_10_VALUE_G_MASK                                       (0x0000FF00U)
#define CSL_DSS_VID_CLUT_10_VALUE_G_SHIFT                                      (0x00000008U)
#define CSL_DSS_VID_CLUT_10_VALUE_G_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_10_VALUE_R_MASK                                       (0x00FF0000U)
#define CSL_DSS_VID_CLUT_10_VALUE_R_SHIFT                                      (0x00000010U)
#define CSL_DSS_VID_CLUT_10_VALUE_R_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_10_INDEX_MASK                                         (0xFF000000U)
#define CSL_DSS_VID_CLUT_10_INDEX_SHIFT                                        (0x00000018U)
#define CSL_DSS_VID_CLUT_10_INDEX_MAX                                          (0x000000FFU)

/* CLUT_11 */

#define CSL_DSS_VID_CLUT_11_VALUE_B_MASK                                       (0x000000FFU)
#define CSL_DSS_VID_CLUT_11_VALUE_B_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_CLUT_11_VALUE_B_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_11_VALUE_G_MASK                                       (0x0000FF00U)
#define CSL_DSS_VID_CLUT_11_VALUE_G_SHIFT                                      (0x00000008U)
#define CSL_DSS_VID_CLUT_11_VALUE_G_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_11_VALUE_R_MASK                                       (0x00FF0000U)
#define CSL_DSS_VID_CLUT_11_VALUE_R_SHIFT                                      (0x00000010U)
#define CSL_DSS_VID_CLUT_11_VALUE_R_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_11_INDEX_MASK                                         (0xFF000000U)
#define CSL_DSS_VID_CLUT_11_INDEX_SHIFT                                        (0x00000018U)
#define CSL_DSS_VID_CLUT_11_INDEX_MAX                                          (0x000000FFU)

/* CLUT_12 */

#define CSL_DSS_VID_CLUT_12_VALUE_B_MASK                                       (0x000000FFU)
#define CSL_DSS_VID_CLUT_12_VALUE_B_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_CLUT_12_VALUE_B_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_12_VALUE_G_MASK                                       (0x0000FF00U)
#define CSL_DSS_VID_CLUT_12_VALUE_G_SHIFT                                      (0x00000008U)
#define CSL_DSS_VID_CLUT_12_VALUE_G_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_12_VALUE_R_MASK                                       (0x00FF0000U)
#define CSL_DSS_VID_CLUT_12_VALUE_R_SHIFT                                      (0x00000010U)
#define CSL_DSS_VID_CLUT_12_VALUE_R_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_12_INDEX_MASK                                         (0xFF000000U)
#define CSL_DSS_VID_CLUT_12_INDEX_SHIFT                                        (0x00000018U)
#define CSL_DSS_VID_CLUT_12_INDEX_MAX                                          (0x000000FFU)

/* CLUT_13 */

#define CSL_DSS_VID_CLUT_13_VALUE_B_MASK                                       (0x000000FFU)
#define CSL_DSS_VID_CLUT_13_VALUE_B_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_CLUT_13_VALUE_B_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_13_VALUE_G_MASK                                       (0x0000FF00U)
#define CSL_DSS_VID_CLUT_13_VALUE_G_SHIFT                                      (0x00000008U)
#define CSL_DSS_VID_CLUT_13_VALUE_G_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_13_VALUE_R_MASK                                       (0x00FF0000U)
#define CSL_DSS_VID_CLUT_13_VALUE_R_SHIFT                                      (0x00000010U)
#define CSL_DSS_VID_CLUT_13_VALUE_R_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_13_INDEX_MASK                                         (0xFF000000U)
#define CSL_DSS_VID_CLUT_13_INDEX_SHIFT                                        (0x00000018U)
#define CSL_DSS_VID_CLUT_13_INDEX_MAX                                          (0x000000FFU)

/* CLUT_14 */

#define CSL_DSS_VID_CLUT_14_VALUE_B_MASK                                       (0x000000FFU)
#define CSL_DSS_VID_CLUT_14_VALUE_B_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_CLUT_14_VALUE_B_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_14_VALUE_G_MASK                                       (0x0000FF00U)
#define CSL_DSS_VID_CLUT_14_VALUE_G_SHIFT                                      (0x00000008U)
#define CSL_DSS_VID_CLUT_14_VALUE_G_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_14_VALUE_R_MASK                                       (0x00FF0000U)
#define CSL_DSS_VID_CLUT_14_VALUE_R_SHIFT                                      (0x00000010U)
#define CSL_DSS_VID_CLUT_14_VALUE_R_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_14_INDEX_MASK                                         (0xFF000000U)
#define CSL_DSS_VID_CLUT_14_INDEX_SHIFT                                        (0x00000018U)
#define CSL_DSS_VID_CLUT_14_INDEX_MAX                                          (0x000000FFU)

/* CLUT_15 */

#define CSL_DSS_VID_CLUT_15_VALUE_B_MASK                                       (0x000000FFU)
#define CSL_DSS_VID_CLUT_15_VALUE_B_SHIFT                                      (0x00000000U)
#define CSL_DSS_VID_CLUT_15_VALUE_B_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_15_VALUE_G_MASK                                       (0x0000FF00U)
#define CSL_DSS_VID_CLUT_15_VALUE_G_SHIFT                                      (0x00000008U)
#define CSL_DSS_VID_CLUT_15_VALUE_G_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_15_VALUE_R_MASK                                       (0x00FF0000U)
#define CSL_DSS_VID_CLUT_15_VALUE_R_SHIFT                                      (0x00000010U)
#define CSL_DSS_VID_CLUT_15_VALUE_R_MAX                                        (0x000000FFU)

#define CSL_DSS_VID_CLUT_15_INDEX_MASK                                         (0xFF000000U)
#define CSL_DSS_VID_CLUT_15_INDEX_SHIFT                                        (0x00000018U)
#define CSL_DSS_VID_CLUT_15_INDEX_MAX                                          (0x000000FFU)

/* SAFETY_ATTRIBUTES */

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_ENABLE_MASK                              (0x00000001U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_ENABLE_SHIFT                             (0x00000000U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_ENABLE_MAX                               (0x00000001U)

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_CAPTUREMODE_MASK                         (0x00000002U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_CAPTUREMODE_SHIFT                        (0x00000001U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_CAPTUREMODE_MAX                          (0x00000001U)

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_CAPTUREMODE_VAL_DATACHECK                (0x1U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_CAPTUREMODE_VAL_FRAMEFREEZE              (0x0U)

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_SEEDSELECT_MASK                          (0x00000004U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_SEEDSELECT_SHIFT                         (0x00000002U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_SEEDSELECT_MAX                           (0x00000001U)

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_SEEDSELECT_VAL_ENABLE                    (0x1U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_SEEDSELECT_VAL_DISABLE                   (0x0U)

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_THRESHOLD_MASK                           (0x000007F8U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_THRESHOLD_SHIFT                          (0x00000003U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_THRESHOLD_MAX                            (0x000000FFU)

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_FRAMESKIP_MASK                           (0x00001800U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_FRAMESKIP_SHIFT                          (0x0000000BU)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_FRAMESKIP_MAX                            (0x00000003U)

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_DISABLE                    (0x0U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_EVEN                       (0x1U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_ODD                        (0x2U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_RESERVED                   (0x3U)

#define CSL_DSS_VID_SAFETY_ATTRIBUTES_RESERVED_MASK                            (0xFFFFE000U)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_RESERVED_SHIFT                           (0x0000000DU)
#define CSL_DSS_VID_SAFETY_ATTRIBUTES_RESERVED_MAX                             (0x0007FFFFU)

/* SAFETY_CAPT_SIGNATURE */

#define CSL_DSS_VID_SAFETY_CAPT_SIGNATURE_SIGNATURE_MASK                       (0xFFFFFFFFU)
#define CSL_DSS_VID_SAFETY_CAPT_SIGNATURE_SIGNATURE_SHIFT                      (0x00000000U)
#define CSL_DSS_VID_SAFETY_CAPT_SIGNATURE_SIGNATURE_MAX                        (0xFFFFFFFFU)

/* SAFETY_POSITION */

#define CSL_DSS_VID_SAFETY_POSITION_POSX_MASK                                  (0x00000FFFU)
#define CSL_DSS_VID_SAFETY_POSITION_POSX_SHIFT                                 (0x00000000U)
#define CSL_DSS_VID_SAFETY_POSITION_POSX_MAX                                   (0x00000FFFU)

#define CSL_DSS_VID_SAFETY_POSITION_RESERVED1_MASK                             (0x0000F000U)
#define CSL_DSS_VID_SAFETY_POSITION_RESERVED1_SHIFT                            (0x0000000CU)
#define CSL_DSS_VID_SAFETY_POSITION_RESERVED1_MAX                              (0x0000000FU)

#define CSL_DSS_VID_SAFETY_POSITION_POSY_MASK                                  (0x0FFF0000U)
#define CSL_DSS_VID_SAFETY_POSITION_POSY_SHIFT                                 (0x00000010U)
#define CSL_DSS_VID_SAFETY_POSITION_POSY_MAX                                   (0x00000FFFU)

#define CSL_DSS_VID_SAFETY_POSITION_RESERVED_MASK                              (0xF0000000U)
#define CSL_DSS_VID_SAFETY_POSITION_RESERVED_SHIFT                             (0x0000001CU)
#define CSL_DSS_VID_SAFETY_POSITION_RESERVED_MAX                               (0x0000000FU)

/* SAFETY_REF_SIGNATURE */

#define CSL_DSS_VID_SAFETY_REF_SIGNATURE_SIGNATURE_MASK                        (0xFFFFFFFFU)
#define CSL_DSS_VID_SAFETY_REF_SIGNATURE_SIGNATURE_SHIFT                       (0x00000000U)
#define CSL_DSS_VID_SAFETY_REF_SIGNATURE_SIGNATURE_MAX                         (0xFFFFFFFFU)

/* SAFETY_SIZE */

#define CSL_DSS_VID_SAFETY_SIZE_SIZEX_MASK                                     (0x00000FFFU)
#define CSL_DSS_VID_SAFETY_SIZE_SIZEX_SHIFT                                    (0x00000000U)
#define CSL_DSS_VID_SAFETY_SIZE_SIZEX_MAX                                      (0x00000FFFU)

#define CSL_DSS_VID_SAFETY_SIZE_RESERVED1_MASK                                 (0x0000F000U)
#define CSL_DSS_VID_SAFETY_SIZE_RESERVED1_SHIFT                                (0x0000000CU)
#define CSL_DSS_VID_SAFETY_SIZE_RESERVED1_MAX                                  (0x0000000FU)

#define CSL_DSS_VID_SAFETY_SIZE_SIZEY_MASK                                     (0x0FFF0000U)
#define CSL_DSS_VID_SAFETY_SIZE_SIZEY_SHIFT                                    (0x00000010U)
#define CSL_DSS_VID_SAFETY_SIZE_SIZEY_MAX                                      (0x00000FFFU)

#define CSL_DSS_VID_SAFETY_SIZE_RESERVED_MASK                                  (0xF0000000U)
#define CSL_DSS_VID_SAFETY_SIZE_RESERVED_SHIFT                                 (0x0000001CU)
#define CSL_DSS_VID_SAFETY_SIZE_RESERVED_MAX                                   (0x0000000FU)

/* SAFETY_LFSR_SEED */

#define CSL_DSS_VID_SAFETY_LFSR_SEED_SEED_MASK                                 (0xFFFFFFFFU)
#define CSL_DSS_VID_SAFETY_LFSR_SEED_SEED_SHIFT                                (0x00000000U)
#define CSL_DSS_VID_SAFETY_LFSR_SEED_SEED_MAX                                  (0xFFFFFFFFU)

/* LUMAKEY */

#define CSL_DSS_VID_LUMAKEY_RESERVED1_MASK                                     (0xF0000000U)
#define CSL_DSS_VID_LUMAKEY_RESERVED1_SHIFT                                    (0x0000001CU)
#define CSL_DSS_VID_LUMAKEY_RESERVED1_MAX                                      (0x0000000FU)

#define CSL_DSS_VID_LUMAKEY_LUMAKEYMAX_MASK                                    (0x0FFF0000U)
#define CSL_DSS_VID_LUMAKEY_LUMAKEYMAX_SHIFT                                   (0x00000010U)
#define CSL_DSS_VID_LUMAKEY_LUMAKEYMAX_MAX                                     (0x00000FFFU)

#define CSL_DSS_VID_LUMAKEY_RESERVED_MASK                                      (0x0000F000U)
#define CSL_DSS_VID_LUMAKEY_RESERVED_SHIFT                                     (0x0000000CU)
#define CSL_DSS_VID_LUMAKEY_RESERVED_MAX                                       (0x0000000FU)

#define CSL_DSS_VID_LUMAKEY_LUMAKEYMIN_MASK                                    (0x00000FFFU)
#define CSL_DSS_VID_LUMAKEY_LUMAKEYMIN_SHIFT                                   (0x00000000U)
#define CSL_DSS_VID_LUMAKEY_LUMAKEYMIN_MAX                                     (0x00000FFFU)

/**************************************************************************
* Hardware Region  : OVR1 Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t CONFIG;                    /* CONFIG */
    volatile uint8_t  Resv_8[4];
    volatile uint32_t DEFAULT_COLOR;             /* DEFAULT_COLOR */
    volatile uint32_t DEFAULT_COLOR2;            /* DEFAULT_COLOR2 */
    volatile uint32_t TRANS_COLOR_MAX;           /* TRANS_COLOR_MAX */
    volatile uint32_t TRANS_COLOR_MAX2;          /* TRANS_COLOR_MAX2 */
    volatile uint32_t TRANS_COLOR_MIN;           /* TRANS_COLOR_MIN */
    volatile uint32_t TRANS_COLOR_MIN2;          /* TRANS_COLOR_MIN2 */
    volatile uint32_t ATTRIBUTES[4U];            /* ATTRIBUTES 0..3 */
} CSL_dss_ovr1Regs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_DSS_OVR1_CONFIG                                                    (0x00000000U)
#define CSL_DSS_OVR1_DEFAULT_COLOR                                             (0x00000008U)
#define CSL_DSS_OVR1_DEFAULT_COLOR2                                            (0x0000000CU)
#define CSL_DSS_OVR1_TRANS_COLOR_MAX                                           (0x00000010U)
#define CSL_DSS_OVR1_TRANS_COLOR_MAX2                                          (0x00000014U)
#define CSL_DSS_OVR1_TRANS_COLOR_MIN                                           (0x00000018U)
#define CSL_DSS_OVR1_TRANS_COLOR_MIN2                                          (0x0000001CU)
#define CSL_DSS_OVR1_ATTRIBUTES(index)                                         (0x00000020U+((uint32_t)(index)*0x4U))

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* CONFIG */

#define CSL_DSS_OVR1_CONFIG_RESERVED6_MASK                                     (0x00000001U)
#define CSL_DSS_OVR1_CONFIG_RESERVED6_SHIFT                                    (0x00000000U)
#define CSL_DSS_OVR1_CONFIG_RESERVED6_MAX                                      (0x00000001U)

#define CSL_DSS_OVR1_CONFIG_COLORBAREN_MASK                                    (0x00000002U)
#define CSL_DSS_OVR1_CONFIG_COLORBAREN_SHIFT                                   (0x00000001U)
#define CSL_DSS_OVR1_CONFIG_COLORBAREN_MAX                                     (0x00000001U)

#define CSL_DSS_OVR1_CONFIG_COLORBAREN_VAL_COLORBARDIS                         (0x0U)
#define CSL_DSS_OVR1_CONFIG_COLORBAREN_VAL_COLORBAREN                          (0x1U)

#define CSL_DSS_OVR1_CONFIG_RESERVED_MASK                                      (0x000003FCU)
#define CSL_DSS_OVR1_CONFIG_RESERVED_SHIFT                                     (0x00000002U)
#define CSL_DSS_OVR1_CONFIG_RESERVED_MAX                                       (0x000000FFU)

#define CSL_DSS_OVR1_CONFIG_TCKLCDENABLE_MASK                                  (0x00000400U)
#define CSL_DSS_OVR1_CONFIG_TCKLCDENABLE_SHIFT                                 (0x0000000AU)
#define CSL_DSS_OVR1_CONFIG_TCKLCDENABLE_MAX                                   (0x00000001U)

#define CSL_DSS_OVR1_CONFIG_TCKLCDENABLE_VAL_DISTCK                            (0x0U)
#define CSL_DSS_OVR1_CONFIG_TCKLCDENABLE_VAL_ENBTCK                            (0x1U)

#define CSL_DSS_OVR1_CONFIG_TCKLCDSELECTION_MASK                               (0x00000800U)
#define CSL_DSS_OVR1_CONFIG_TCKLCDSELECTION_SHIFT                              (0x0000000BU)
#define CSL_DSS_OVR1_CONFIG_TCKLCDSELECTION_MAX                                (0x00000001U)

#define CSL_DSS_OVR1_CONFIG_TCKLCDSELECTION_VAL_GDTK                           (0x0U)
#define CSL_DSS_OVR1_CONFIG_TCKLCDSELECTION_VAL_VSTK                           (0x1U)

#define CSL_DSS_OVR1_CONFIG_RESERVED2_MASK                                     (0x00001000U)
#define CSL_DSS_OVR1_CONFIG_RESERVED2_SHIFT                                    (0x0000000CU)
#define CSL_DSS_OVR1_CONFIG_RESERVED2_MAX                                      (0x00000001U)

#define CSL_DSS_OVR1_CONFIG_RESERVED3_MASK                                     (0x00002000U)
#define CSL_DSS_OVR1_CONFIG_RESERVED3_SHIFT                                    (0x0000000DU)
#define CSL_DSS_OVR1_CONFIG_RESERVED3_MAX                                      (0x00000001U)

#define CSL_DSS_OVR1_CONFIG_RESERVED1_MASK                                     (0xFFFFC000U)
#define CSL_DSS_OVR1_CONFIG_RESERVED1_SHIFT                                    (0x0000000EU)
#define CSL_DSS_OVR1_CONFIG_RESERVED1_MAX                                      (0x0003FFFFU)

/* DEFAULT_COLOR */

#define CSL_DSS_OVR1_DEFAULT_COLOR_DEFAULTCOLOR_MASK                           (0xFFFFFFFFU)
#define CSL_DSS_OVR1_DEFAULT_COLOR_DEFAULTCOLOR_SHIFT                          (0x00000000U)
#define CSL_DSS_OVR1_DEFAULT_COLOR_DEFAULTCOLOR_MAX                            (0xFFFFFFFFU)

/* DEFAULT_COLOR2 */

#define CSL_DSS_OVR1_DEFAULT_COLOR2_DEFAULTCOLOR_MASK                          (0x0000FFFFU)
#define CSL_DSS_OVR1_DEFAULT_COLOR2_DEFAULTCOLOR_SHIFT                         (0x00000000U)
#define CSL_DSS_OVR1_DEFAULT_COLOR2_DEFAULTCOLOR_MAX                           (0x0000FFFFU)

#define CSL_DSS_OVR1_DEFAULT_COLOR2_RESERVED_MASK                              (0xFFFF0000U)
#define CSL_DSS_OVR1_DEFAULT_COLOR2_RESERVED_SHIFT                             (0x00000010U)
#define CSL_DSS_OVR1_DEFAULT_COLOR2_RESERVED_MAX                               (0x0000FFFFU)

/* TRANS_COLOR_MAX */

#define CSL_DSS_OVR1_TRANS_COLOR_MAX_TRANSCOLORKEY_MASK                        (0xFFFFFFFFU)
#define CSL_DSS_OVR1_TRANS_COLOR_MAX_TRANSCOLORKEY_SHIFT                       (0x00000000U)
#define CSL_DSS_OVR1_TRANS_COLOR_MAX_TRANSCOLORKEY_MAX                         (0xFFFFFFFFU)

/* TRANS_COLOR_MAX2 */

#define CSL_DSS_OVR1_TRANS_COLOR_MAX2_TRANSCOLORKEY_MASK                       (0x0000000FU)
#define CSL_DSS_OVR1_TRANS_COLOR_MAX2_TRANSCOLORKEY_SHIFT                      (0x00000000U)
#define CSL_DSS_OVR1_TRANS_COLOR_MAX2_TRANSCOLORKEY_MAX                        (0x0000000FU)

#define CSL_DSS_OVR1_TRANS_COLOR_MAX2_RESERVED_MASK                            (0xFFFFFFF0U)
#define CSL_DSS_OVR1_TRANS_COLOR_MAX2_RESERVED_SHIFT                           (0x00000004U)
#define CSL_DSS_OVR1_TRANS_COLOR_MAX2_RESERVED_MAX                             (0x0FFFFFFFU)

/* TRANS_COLOR_MIN */

#define CSL_DSS_OVR1_TRANS_COLOR_MIN_TRANSCOLORKEY_MASK                        (0xFFFFFFFFU)
#define CSL_DSS_OVR1_TRANS_COLOR_MIN_TRANSCOLORKEY_SHIFT                       (0x00000000U)
#define CSL_DSS_OVR1_TRANS_COLOR_MIN_TRANSCOLORKEY_MAX                         (0xFFFFFFFFU)

/* TRANS_COLOR_MIN2 */

#define CSL_DSS_OVR1_TRANS_COLOR_MIN2_TRANSCOLORKEY_MASK                       (0x0000000FU)
#define CSL_DSS_OVR1_TRANS_COLOR_MIN2_TRANSCOLORKEY_SHIFT                      (0x00000000U)
#define CSL_DSS_OVR1_TRANS_COLOR_MIN2_TRANSCOLORKEY_MAX                        (0x0000000FU)

#define CSL_DSS_OVR1_TRANS_COLOR_MIN2_RESERVED_MASK                            (0xFFFFFFF0U)
#define CSL_DSS_OVR1_TRANS_COLOR_MIN2_RESERVED_SHIFT                           (0x00000004U)
#define CSL_DSS_OVR1_TRANS_COLOR_MIN2_RESERVED_MAX                             (0x0FFFFFFFU)

/* ATTRIBUTES */

#define CSL_DSS_OVR1_ATTRIBUTES_POSX_MASK                                      (0x0003FFC0U)
#define CSL_DSS_OVR1_ATTRIBUTES_POSX_SHIFT                                     (0x00000006U)
#define CSL_DSS_OVR1_ATTRIBUTES_POSX_MAX                                       (0x00000FFFU)

#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED_MASK                                  (0x00040000U)
#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED_SHIFT                                 (0x00000012U)
#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED_MAX                                   (0x00000001U)

#define CSL_DSS_OVR1_ATTRIBUTES_POSY_MASK                                      (0x7FF80000U)
#define CSL_DSS_OVR1_ATTRIBUTES_POSY_SHIFT                                     (0x00000013U)
#define CSL_DSS_OVR1_ATTRIBUTES_POSY_MAX                                       (0x00000FFFU)

#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED1_MASK                                 (0x80000000U)
#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED1_SHIFT                                (0x0000001FU)
#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED1_MAX                                  (0x00000001U)

#define CSL_DSS_OVR1_ATTRIBUTES_ENABLE_MASK                                    (0x00000001U)
#define CSL_DSS_OVR1_ATTRIBUTES_ENABLE_SHIFT                                   (0x00000000U)
#define CSL_DSS_OVR1_ATTRIBUTES_ENABLE_MAX                                     (0x00000001U)

#define CSL_DSS_OVR1_ATTRIBUTES_CHANNELIN_MASK                                 (0x0000001EU)
#define CSL_DSS_OVR1_ATTRIBUTES_CHANNELIN_SHIFT                                (0x00000001U)
#define CSL_DSS_OVR1_ATTRIBUTES_CHANNELIN_MAX                                  (0x0000000FU)

#define CSL_DSS_OVR1_ATTRIBUTES_CHANNELIN_VAL_VID                              (0x0U)
#define CSL_DSS_OVR1_ATTRIBUTES_CHANNELIN_VAL_VIDL1                            (0x1U)
#define CSL_DSS_OVR1_ATTRIBUTES_CHANNELIN_VAL_VIDL2                            (0x2U)
#define CSL_DSS_OVR1_ATTRIBUTES_CHANNELIN_VAL_VIDL3                            (0x3U)

#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED2_MASK                                 (0x00000020U)
#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED2_SHIFT                                (0x00000005U)
#define CSL_DSS_OVR1_ATTRIBUTES_RESERVED2_MAX                                  (0x00000001U)

/**************************************************************************
* Hardware Region  : OVR2 Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t CONFIG;                    /* CONFIG */
    volatile uint8_t  Resv_8[4];
    volatile uint32_t DEFAULT_COLOR;             /* DEFAULT_COLOR */
    volatile uint32_t DEFAULT_COLOR2;            /* DEFAULT_COLOR2 */
    volatile uint32_t TRANS_COLOR_MAX;           /* TRANS_COLOR_MAX */
    volatile uint32_t TRANS_COLOR_MAX2;          /* TRANS_COLOR_MAX2 */
    volatile uint32_t TRANS_COLOR_MIN;           /* TRANS_COLOR_MIN */
    volatile uint32_t TRANS_COLOR_MIN2;          /* TRANS_COLOR_MIN2 */
    volatile uint32_t ATTRIBUTES[4U];            /* ATTRIBUTES 0..3 */
} CSL_dss_ovr2Regs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_DSS_OVR2_CONFIG                                                    (0x00000000U)
#define CSL_DSS_OVR2_DEFAULT_COLOR                                             (0x00000008U)
#define CSL_DSS_OVR2_DEFAULT_COLOR2                                            (0x0000000CU)
#define CSL_DSS_OVR2_TRANS_COLOR_MAX                                           (0x00000010U)
#define CSL_DSS_OVR2_TRANS_COLOR_MAX2                                          (0x00000014U)
#define CSL_DSS_OVR2_TRANS_COLOR_MIN                                           (0x00000018U)
#define CSL_DSS_OVR2_TRANS_COLOR_MIN2                                          (0x0000001CU)
#define CSL_DSS_OVR2_ATTRIBUTES(index)                                         (0x00000020U+((uint32_t)(index)*0x4U))

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* CONFIG */

#define CSL_DSS_OVR2_CONFIG_RESERVED6_MASK                                     (0x00000001U)
#define CSL_DSS_OVR2_CONFIG_RESERVED6_SHIFT                                    (0x00000000U)
#define CSL_DSS_OVR2_CONFIG_RESERVED6_MAX                                      (0x00000001U)

#define CSL_DSS_OVR2_CONFIG_COLORBAREN_MASK                                    (0x00000002U)
#define CSL_DSS_OVR2_CONFIG_COLORBAREN_SHIFT                                   (0x00000001U)
#define CSL_DSS_OVR2_CONFIG_COLORBAREN_MAX                                     (0x00000001U)

#define CSL_DSS_OVR2_CONFIG_COLORBAREN_VAL_COLORBARDIS                         (0x0U)
#define CSL_DSS_OVR2_CONFIG_COLORBAREN_VAL_COLORBAREN                          (0x1U)

#define CSL_DSS_OVR2_CONFIG_RESERVED_MASK                                      (0x000003FCU)
#define CSL_DSS_OVR2_CONFIG_RESERVED_SHIFT                                     (0x00000002U)
#define CSL_DSS_OVR2_CONFIG_RESERVED_MAX                                       (0x000000FFU)

#define CSL_DSS_OVR2_CONFIG_TCKLCDENABLE_MASK                                  (0x00000400U)
#define CSL_DSS_OVR2_CONFIG_TCKLCDENABLE_SHIFT                                 (0x0000000AU)
#define CSL_DSS_OVR2_CONFIG_TCKLCDENABLE_MAX                                   (0x00000001U)

#define CSL_DSS_OVR2_CONFIG_TCKLCDENABLE_VAL_DISTCK                            (0x0U)
#define CSL_DSS_OVR2_CONFIG_TCKLCDENABLE_VAL_ENBTCK                            (0x1U)

#define CSL_DSS_OVR2_CONFIG_TCKLCDSELECTION_MASK                               (0x00000800U)
#define CSL_DSS_OVR2_CONFIG_TCKLCDSELECTION_SHIFT                              (0x0000000BU)
#define CSL_DSS_OVR2_CONFIG_TCKLCDSELECTION_MAX                                (0x00000001U)

#define CSL_DSS_OVR2_CONFIG_TCKLCDSELECTION_VAL_GDTK                           (0x0U)
#define CSL_DSS_OVR2_CONFIG_TCKLCDSELECTION_VAL_VSTK                           (0x1U)

#define CSL_DSS_OVR2_CONFIG_RESERVED2_MASK                                     (0x00001000U)
#define CSL_DSS_OVR2_CONFIG_RESERVED2_SHIFT                                    (0x0000000CU)
#define CSL_DSS_OVR2_CONFIG_RESERVED2_MAX                                      (0x00000001U)

#define CSL_DSS_OVR2_CONFIG_RESERVED3_MASK                                     (0x00002000U)
#define CSL_DSS_OVR2_CONFIG_RESERVED3_SHIFT                                    (0x0000000DU)
#define CSL_DSS_OVR2_CONFIG_RESERVED3_MAX                                      (0x00000001U)

#define CSL_DSS_OVR2_CONFIG_RESERVED1_MASK                                     (0xFFFFC000U)
#define CSL_DSS_OVR2_CONFIG_RESERVED1_SHIFT                                    (0x0000000EU)
#define CSL_DSS_OVR2_CONFIG_RESERVED1_MAX                                      (0x0003FFFFU)

/* DEFAULT_COLOR */

#define CSL_DSS_OVR2_DEFAULT_COLOR_DEFAULTCOLOR_MASK                           (0xFFFFFFFFU)
#define CSL_DSS_OVR2_DEFAULT_COLOR_DEFAULTCOLOR_SHIFT                          (0x00000000U)
#define CSL_DSS_OVR2_DEFAULT_COLOR_DEFAULTCOLOR_MAX                            (0xFFFFFFFFU)

/* DEFAULT_COLOR2 */

#define CSL_DSS_OVR2_DEFAULT_COLOR2_DEFAULTCOLOR_MASK                          (0x0000FFFFU)
#define CSL_DSS_OVR2_DEFAULT_COLOR2_DEFAULTCOLOR_SHIFT                         (0x00000000U)
#define CSL_DSS_OVR2_DEFAULT_COLOR2_DEFAULTCOLOR_MAX                           (0x0000FFFFU)

#define CSL_DSS_OVR2_DEFAULT_COLOR2_RESERVED_MASK                              (0xFFFF0000U)
#define CSL_DSS_OVR2_DEFAULT_COLOR2_RESERVED_SHIFT                             (0x00000010U)
#define CSL_DSS_OVR2_DEFAULT_COLOR2_RESERVED_MAX                               (0x0000FFFFU)

/* TRANS_COLOR_MAX */

#define CSL_DSS_OVR2_TRANS_COLOR_MAX_TRANSCOLORKEY_MASK                        (0xFFFFFFFFU)
#define CSL_DSS_OVR2_TRANS_COLOR_MAX_TRANSCOLORKEY_SHIFT                       (0x00000000U)
#define CSL_DSS_OVR2_TRANS_COLOR_MAX_TRANSCOLORKEY_MAX                         (0xFFFFFFFFU)

/* TRANS_COLOR_MAX2 */

#define CSL_DSS_OVR2_TRANS_COLOR_MAX2_TRANSCOLORKEY_MASK                       (0x0000000FU)
#define CSL_DSS_OVR2_TRANS_COLOR_MAX2_TRANSCOLORKEY_SHIFT                      (0x00000000U)
#define CSL_DSS_OVR2_TRANS_COLOR_MAX2_TRANSCOLORKEY_MAX                        (0x0000000FU)

#define CSL_DSS_OVR2_TRANS_COLOR_MAX2_RESERVED_MASK                            (0xFFFFFFF0U)
#define CSL_DSS_OVR2_TRANS_COLOR_MAX2_RESERVED_SHIFT                           (0x00000004U)
#define CSL_DSS_OVR2_TRANS_COLOR_MAX2_RESERVED_MAX                             (0x0FFFFFFFU)

/* TRANS_COLOR_MIN */

#define CSL_DSS_OVR2_TRANS_COLOR_MIN_TRANSCOLORKEY_MASK                        (0xFFFFFFFFU)
#define CSL_DSS_OVR2_TRANS_COLOR_MIN_TRANSCOLORKEY_SHIFT                       (0x00000000U)
#define CSL_DSS_OVR2_TRANS_COLOR_MIN_TRANSCOLORKEY_MAX                         (0xFFFFFFFFU)

/* TRANS_COLOR_MIN2 */

#define CSL_DSS_OVR2_TRANS_COLOR_MIN2_TRANSCOLORKEY_MASK                       (0x0000000FU)
#define CSL_DSS_OVR2_TRANS_COLOR_MIN2_TRANSCOLORKEY_SHIFT                      (0x00000000U)
#define CSL_DSS_OVR2_TRANS_COLOR_MIN2_TRANSCOLORKEY_MAX                        (0x0000000FU)

#define CSL_DSS_OVR2_TRANS_COLOR_MIN2_RESERVED_MASK                            (0xFFFFFFF0U)
#define CSL_DSS_OVR2_TRANS_COLOR_MIN2_RESERVED_SHIFT                           (0x00000004U)
#define CSL_DSS_OVR2_TRANS_COLOR_MIN2_RESERVED_MAX                             (0x0FFFFFFFU)

/* ATTRIBUTES */

#define CSL_DSS_OVR2_ATTRIBUTES_POSX_MASK                                      (0x0003FFC0U)
#define CSL_DSS_OVR2_ATTRIBUTES_POSX_SHIFT                                     (0x00000006U)
#define CSL_DSS_OVR2_ATTRIBUTES_POSX_MAX                                       (0x00000FFFU)

#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED_MASK                                  (0x00040000U)
#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED_SHIFT                                 (0x00000012U)
#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED_MAX                                   (0x00000001U)

#define CSL_DSS_OVR2_ATTRIBUTES_POSY_MASK                                      (0x7FF80000U)
#define CSL_DSS_OVR2_ATTRIBUTES_POSY_SHIFT                                     (0x00000013U)
#define CSL_DSS_OVR2_ATTRIBUTES_POSY_MAX                                       (0x00000FFFU)

#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED1_MASK                                 (0x80000000U)
#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED1_SHIFT                                (0x0000001FU)
#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED1_MAX                                  (0x00000001U)

#define CSL_DSS_OVR2_ATTRIBUTES_ENABLE_MASK                                    (0x00000001U)
#define CSL_DSS_OVR2_ATTRIBUTES_ENABLE_SHIFT                                   (0x00000000U)
#define CSL_DSS_OVR2_ATTRIBUTES_ENABLE_MAX                                     (0x00000001U)

#define CSL_DSS_OVR2_ATTRIBUTES_CHANNELIN_MASK                                 (0x0000001EU)
#define CSL_DSS_OVR2_ATTRIBUTES_CHANNELIN_SHIFT                                (0x00000001U)
#define CSL_DSS_OVR2_ATTRIBUTES_CHANNELIN_MAX                                  (0x0000000FU)

#define CSL_DSS_OVR2_ATTRIBUTES_CHANNELIN_VAL_VID                              (0x0U)
#define CSL_DSS_OVR2_ATTRIBUTES_CHANNELIN_VAL_VIDL1                            (0x1U)
#define CSL_DSS_OVR2_ATTRIBUTES_CHANNELIN_VAL_VIDL2                            (0x2U)
#define CSL_DSS_OVR2_ATTRIBUTES_CHANNELIN_VAL_VIDL3                            (0x3U)

#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED2_MASK                                 (0x00000020U)
#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED2_SHIFT                                (0x00000005U)
#define CSL_DSS_OVR2_ATTRIBUTES_RESERVED2_MAX                                  (0x00000001U)

/**************************************************************************
* Hardware Region  : VP1 Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t CONFIG;                    /* CONFIG */
    volatile uint32_t CONTROL;                   /* CONTROL */
    volatile uint32_t CSC_COEF0;                 /* CSC_COEF0 */
    volatile uint32_t CSC_COEF1;                 /* CSC_COEF1 */
    volatile uint32_t CSC_COEF2;                 /* CSC_COEF2 */
    volatile uint32_t DATA_CYCLE_0;              /* DATA_CYCLE_0 */
    volatile uint32_t DATA_CYCLE_1;              /* DATA_CYCLE_1 */
    volatile uint32_t DATA_CYCLE_2;              /* DATA_CYCLE_2 */
    volatile uint8_t  Resv_68[36];
    volatile uint32_t LINE_NUMBER;               /* LINE_NUMBER */
    volatile uint8_t  Resv_76[4];
    volatile uint32_t POL_FREQ;                  /* POL_FREQ */
    volatile uint32_t SIZE_SCREEN;               /* SIZE_SCREEN */
    volatile uint32_t TIMING_H;                  /* TIMING_H */
    volatile uint32_t TIMING_V;                  /* TIMING_V */
    volatile uint32_t CSC_COEF3;                 /* CSC_COEF3 */
    volatile uint32_t CSC_COEF4;                 /* CSC_COEF4 */
    volatile uint32_t CSC_COEF5;                 /* CSC_COEF5 */
    volatile uint32_t CSC_COEF6;                 /* CSC_COEF6 */
    volatile uint32_t CSC_COEF7;                 /* CSC_COEF7 */
    volatile uint32_t SAFETY_ATTRIBUTES[4U];     /* SAFETY_ATTRIBUTES 0..3 */
    volatile uint8_t  Resv_144[16];
    volatile uint32_t SAFETY_CAPT_SIGNATURE[4U]; /* SAFETY_CAPT_SIGNATURE 0..3 */
    volatile uint8_t  Resv_176[16];
    volatile uint32_t SAFETY_POSITION[4U];       /* SAFETY_POSITION 0..3 */
    volatile uint8_t  Resv_208[16];
    volatile uint32_t SAFETY_REF_SIGNATURE[4U];  /* SAFETY_REF_SIGNATURE 0..3 */
    volatile uint8_t  Resv_240[16];
    volatile uint32_t SAFETY_SIZE[4U];           /* SAFETY_SIZE 0..3 */
    volatile uint8_t  Resv_272[16];
    volatile uint32_t SAFETY_LFSR_SEED;          /* SAFETY_LFSR_SEED */
    volatile uint8_t  Resv_288[12];
    volatile uint32_t GAMMA_TABLE_0;             /* GAMMA_TABLE_0 */
    volatile uint32_t GAMMA_TABLE_1;             /* GAMMA_TABLE_1 */
    volatile uint32_t GAMMA_TABLE_2;             /* GAMMA_TABLE_2 */
    volatile uint32_t GAMMA_TABLE_3;             /* GAMMA_TABLE_3 */
    volatile uint32_t GAMMA_TABLE_4;             /* GAMMA_TABLE_4 */
    volatile uint32_t GAMMA_TABLE_5;             /* GAMMA_TABLE_5 */
    volatile uint32_t GAMMA_TABLE_6;             /* GAMMA_TABLE_6 */
    volatile uint32_t GAMMA_TABLE_7;             /* GAMMA_TABLE_7 */
    volatile uint32_t GAMMA_TABLE_8;             /* GAMMA_TABLE_8 */
    volatile uint32_t GAMMA_TABLE_9;             /* GAMMA_TABLE_9 */
    volatile uint32_t GAMMA_TABLE_10;            /* GAMMA_TABLE_10 */
    volatile uint32_t GAMMA_TABLE_11;            /* GAMMA_TABLE_11 */
    volatile uint32_t GAMMA_TABLE_12;            /* GAMMA_TABLE_12 */
    volatile uint32_t GAMMA_TABLE_13;            /* GAMMA_TABLE_13 */
    volatile uint32_t GAMMA_TABLE_14;            /* GAMMA_TABLE_14 */
    volatile uint32_t GAMMA_TABLE_15;            /* GAMMA_TABLE_15 */
    volatile uint32_t DSS_OLDI_CFG;              /* DSS_OLDI_CFG */
    volatile uint32_t DSS_OLDI_STATUS;           /* DSS_OLDI_STATUS */
    volatile uint32_t DSS_OLDI_LB;               /* DSS_OLDI_LB */
} CSL_dss_vp1Regs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_DSS_VP1_CONFIG                                                     (0x00000000U)
#define CSL_DSS_VP1_CONTROL                                                    (0x00000004U)
#define CSL_DSS_VP1_CSC_COEF0                                                  (0x00000008U)
#define CSL_DSS_VP1_CSC_COEF1                                                  (0x0000000CU)
#define CSL_DSS_VP1_CSC_COEF2                                                  (0x00000010U)
#define CSL_DSS_VP1_DATA_CYCLE_0                                               (0x00000014U)
#define CSL_DSS_VP1_DATA_CYCLE_1                                               (0x00000018U)
#define CSL_DSS_VP1_DATA_CYCLE_2                                               (0x0000001CU)
#define CSL_DSS_VP1_LINE_NUMBER                                                (0x00000044U)
#define CSL_DSS_VP1_POL_FREQ                                                   (0x0000004CU)
#define CSL_DSS_VP1_SIZE_SCREEN                                                (0x00000050U)
#define CSL_DSS_VP1_TIMING_H                                                   (0x00000054U)
#define CSL_DSS_VP1_TIMING_V                                                   (0x00000058U)
#define CSL_DSS_VP1_CSC_COEF3                                                  (0x0000005CU)
#define CSL_DSS_VP1_CSC_COEF4                                                  (0x00000060U)
#define CSL_DSS_VP1_CSC_COEF5                                                  (0x00000064U)
#define CSL_DSS_VP1_CSC_COEF6                                                  (0x00000068U)
#define CSL_DSS_VP1_CSC_COEF7                                                  (0x0000006CU)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES(index)                                   (0x00000070U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP1_SAFETY_CAPT_SIGNATURE(index)                               (0x00000090U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP1_SAFETY_POSITION(index)                                     (0x000000B0U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP1_SAFETY_REF_SIGNATURE(index)                                (0x000000D0U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP1_SAFETY_SIZE(index)                                         (0x000000F0U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP1_SAFETY_LFSR_SEED                                           (0x00000110U)
#define CSL_DSS_VP1_GAMMA_TABLE_0                                              (0x00000120U)
#define CSL_DSS_VP1_GAMMA_TABLE_1                                              (0x00000124U)
#define CSL_DSS_VP1_GAMMA_TABLE_2                                              (0x00000128U)
#define CSL_DSS_VP1_GAMMA_TABLE_3                                              (0x0000012CU)
#define CSL_DSS_VP1_GAMMA_TABLE_4                                              (0x00000130U)
#define CSL_DSS_VP1_GAMMA_TABLE_5                                              (0x00000134U)
#define CSL_DSS_VP1_GAMMA_TABLE_6                                              (0x00000138U)
#define CSL_DSS_VP1_GAMMA_TABLE_7                                              (0x0000013CU)
#define CSL_DSS_VP1_GAMMA_TABLE_8                                              (0x00000140U)
#define CSL_DSS_VP1_GAMMA_TABLE_9                                              (0x00000144U)
#define CSL_DSS_VP1_GAMMA_TABLE_10                                             (0x00000148U)
#define CSL_DSS_VP1_GAMMA_TABLE_11                                             (0x0000014CU)
#define CSL_DSS_VP1_GAMMA_TABLE_12                                             (0x00000150U)
#define CSL_DSS_VP1_GAMMA_TABLE_13                                             (0x00000154U)
#define CSL_DSS_VP1_GAMMA_TABLE_14                                             (0x00000158U)
#define CSL_DSS_VP1_GAMMA_TABLE_15                                             (0x0000015CU)
#define CSL_DSS_VP1_DSS_OLDI_CFG                                               (0x00000160U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS                                            (0x00000164U)
#define CSL_DSS_VP1_DSS_OLDI_LB                                                (0x00000168U)

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* CONFIG */

#define CSL_DSS_VP1_CONFIG_PIXELGATED_MASK                                     (0x00000001U)
#define CSL_DSS_VP1_CONFIG_PIXELGATED_SHIFT                                    (0x00000000U)
#define CSL_DSS_VP1_CONFIG_PIXELGATED_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONFIG_PIXELGATED_VAL_PCLKTOGA                             (0x0U)
#define CSL_DSS_VP1_CONFIG_PIXELGATED_VAL_PCLKTOGV                             (0x1U)

#define CSL_DSS_VP1_CONFIG_DATAENABLEGATED_MASK                                (0x00000002U)
#define CSL_DSS_VP1_CONFIG_DATAENABLEGATED_SHIFT                               (0x00000001U)
#define CSL_DSS_VP1_CONFIG_DATAENABLEGATED_MAX                                 (0x00000001U)

#define CSL_DSS_VP1_CONFIG_DATAENABLEGATED_VAL_DEGDIS                          (0x0U)
#define CSL_DSS_VP1_CONFIG_DATAENABLEGATED_VAL_DEGENB                          (0x1U)

#define CSL_DSS_VP1_CONFIG_GAMMAENABLE_MASK                                    (0x00000004U)
#define CSL_DSS_VP1_CONFIG_GAMMAENABLE_SHIFT                                   (0x00000002U)
#define CSL_DSS_VP1_CONFIG_GAMMAENABLE_MAX                                     (0x00000001U)

#define CSL_DSS_VP1_CONFIG_GAMMAENABLE_VAL_GAMMADIS                            (0x0U)
#define CSL_DSS_VP1_CONFIG_GAMMAENABLE_VAL_GAMMAENB                            (0x1U)

#define CSL_DSS_VP1_CONFIG_HDMIMODE_MASK                                       (0x00000008U)
#define CSL_DSS_VP1_CONFIG_HDMIMODE_SHIFT                                      (0x00000003U)
#define CSL_DSS_VP1_CONFIG_HDMIMODE_MAX                                        (0x00000001U)

#define CSL_DSS_VP1_CONFIG_PIXELDATAGATED_MASK                                 (0x00000010U)
#define CSL_DSS_VP1_CONFIG_PIXELDATAGATED_SHIFT                                (0x00000004U)
#define CSL_DSS_VP1_CONFIG_PIXELDATAGATED_MAX                                  (0x00000001U)

#define CSL_DSS_VP1_CONFIG_PIXELDATAGATED_VAL_PDGDIS                           (0x0U)
#define CSL_DSS_VP1_CONFIG_PIXELDATAGATED_VAL_PDGENB                           (0x1U)

#define CSL_DSS_VP1_CONFIG_PIXELCLOCKGATED_MASK                                (0x00000020U)
#define CSL_DSS_VP1_CONFIG_PIXELCLOCKGATED_SHIFT                               (0x00000005U)
#define CSL_DSS_VP1_CONFIG_PIXELCLOCKGATED_MAX                                 (0x00000001U)

#define CSL_DSS_VP1_CONFIG_PIXELCLOCKGATED_VAL_PCGDIS                          (0x0U)
#define CSL_DSS_VP1_CONFIG_PIXELCLOCKGATED_VAL_PCGENB                          (0x1U)

#define CSL_DSS_VP1_CONFIG_HSYNCGATED_MASK                                     (0x00000040U)
#define CSL_DSS_VP1_CONFIG_HSYNCGATED_SHIFT                                    (0x00000006U)
#define CSL_DSS_VP1_CONFIG_HSYNCGATED_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONFIG_HSYNCGATED_VAL_HGDIS                                (0x0U)
#define CSL_DSS_VP1_CONFIG_HSYNCGATED_VAL_HGENB                                (0x1U)

#define CSL_DSS_VP1_CONFIG_VSYNCGATED_MASK                                     (0x00000080U)
#define CSL_DSS_VP1_CONFIG_VSYNCGATED_SHIFT                                    (0x00000007U)
#define CSL_DSS_VP1_CONFIG_VSYNCGATED_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONFIG_VSYNCGATED_VAL_VGDIS                                (0x0U)
#define CSL_DSS_VP1_CONFIG_VSYNCGATED_VAL_VGENB                                (0x1U)

#define CSL_DSS_VP1_CONFIG_EXTERNALSYNCEN_MASK                                 (0x00000100U)
#define CSL_DSS_VP1_CONFIG_EXTERNALSYNCEN_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_CONFIG_EXTERNALSYNCEN_MAX                                  (0x00000001U)

#define CSL_DSS_VP1_CONFIG_RESERVED1_MASK                                      (0x00007E00U)
#define CSL_DSS_VP1_CONFIG_RESERVED1_SHIFT                                     (0x00000009U)
#define CSL_DSS_VP1_CONFIG_RESERVED1_MAX                                       (0x0000003FU)

#define CSL_DSS_VP1_CONFIG_CPR_MASK                                            (0x00008000U)
#define CSL_DSS_VP1_CONFIG_CPR_SHIFT                                           (0x0000000FU)
#define CSL_DSS_VP1_CONFIG_CPR_MAX                                             (0x00000001U)

#define CSL_DSS_VP1_CONFIG_BUFFERHANDSHAKE_MASK                                (0x00010000U)
#define CSL_DSS_VP1_CONFIG_BUFFERHANDSHAKE_SHIFT                               (0x00000010U)
#define CSL_DSS_VP1_CONFIG_BUFFERHANDSHAKE_MAX                                 (0x00000001U)

#define CSL_DSS_VP1_CONFIG_RESERVED2_MASK                                      (0x000E0000U)
#define CSL_DSS_VP1_CONFIG_RESERVED2_SHIFT                                     (0x00000011U)
#define CSL_DSS_VP1_CONFIG_RESERVED2_MAX                                       (0x00000007U)

#define CSL_DSS_VP1_CONFIG_BT656ENABLE_MASK                                    (0x00100000U)
#define CSL_DSS_VP1_CONFIG_BT656ENABLE_SHIFT                                   (0x00000014U)
#define CSL_DSS_VP1_CONFIG_BT656ENABLE_MAX                                     (0x00000001U)

#define CSL_DSS_VP1_CONFIG_BT656ENABLE_VAL_DISABLE                             (0x0U)
#define CSL_DSS_VP1_CONFIG_BT656ENABLE_VAL_ENABLE                              (0x1U)

#define CSL_DSS_VP1_CONFIG_BT1120ENABLE_MASK                                   (0x00200000U)
#define CSL_DSS_VP1_CONFIG_BT1120ENABLE_SHIFT                                  (0x00000015U)
#define CSL_DSS_VP1_CONFIG_BT1120ENABLE_MAX                                    (0x00000001U)

#define CSL_DSS_VP1_CONFIG_BT1120ENABLE_VAL_DISABLE                            (0x0U)
#define CSL_DSS_VP1_CONFIG_BT1120ENABLE_VAL_ENABLE                             (0x1U)

#define CSL_DSS_VP1_CONFIG_OUTPUTMODEENABLE_MASK                               (0x00400000U)
#define CSL_DSS_VP1_CONFIG_OUTPUTMODEENABLE_SHIFT                              (0x00000016U)
#define CSL_DSS_VP1_CONFIG_OUTPUTMODEENABLE_MAX                                (0x00000001U)

#define CSL_DSS_VP1_CONFIG_OUTPUTMODEENABLE_VAL_DISABLE                        (0x0U)
#define CSL_DSS_VP1_CONFIG_OUTPUTMODEENABLE_VAL_ENABLE                         (0x1U)

#define CSL_DSS_VP1_CONFIG_FIDFIRST_MASK                                       (0x00800000U)
#define CSL_DSS_VP1_CONFIG_FIDFIRST_SHIFT                                      (0x00000017U)
#define CSL_DSS_VP1_CONFIG_FIDFIRST_MAX                                        (0x00000001U)

#define CSL_DSS_VP1_CONFIG_FIDFIRST_VAL_EVEN                                   (0x0U)
#define CSL_DSS_VP1_CONFIG_FIDFIRST_VAL_ODD                                    (0x1U)

#define CSL_DSS_VP1_CONFIG_COLORCONVENABLE_MASK                                (0x01000000U)
#define CSL_DSS_VP1_CONFIG_COLORCONVENABLE_SHIFT                               (0x00000018U)
#define CSL_DSS_VP1_CONFIG_COLORCONVENABLE_MAX                                 (0x00000001U)

#define CSL_DSS_VP1_CONFIG_COLORCONVENABLE_VAL_COLSPCDIS                       (0x0U)
#define CSL_DSS_VP1_CONFIG_COLORCONVENABLE_VAL_COLSPCENB                       (0x1U)

#define CSL_DSS_VP1_CONFIG_FULLRANGE_MASK                                      (0x02000000U)
#define CSL_DSS_VP1_CONFIG_FULLRANGE_SHIFT                                     (0x00000019U)
#define CSL_DSS_VP1_CONFIG_FULLRANGE_MAX                                       (0x00000001U)

#define CSL_DSS_VP1_CONFIG_FULLRANGE_VAL_LIMRANGE                              (0x0U)
#define CSL_DSS_VP1_CONFIG_FULLRANGE_VAL_FULLRANGE                             (0x1U)

#define CSL_DSS_VP1_CONFIG_COLORCONVPOS_MASK                                   (0x04000000U)
#define CSL_DSS_VP1_CONFIG_COLORCONVPOS_SHIFT                                  (0x0000001AU)
#define CSL_DSS_VP1_CONFIG_COLORCONVPOS_MAX                                    (0x00000001U)

#define CSL_DSS_VP1_CONFIG_COLORCONVPOS_VAL_AFTERGAMMA                         (0x0U)
#define CSL_DSS_VP1_CONFIG_COLORCONVPOS_VAL_BEFOREGAMMA                        (0x1U)

#define CSL_DSS_VP1_CONFIG_RESERVED3_MASK                                      (0xF8000000U)
#define CSL_DSS_VP1_CONFIG_RESERVED3_SHIFT                                     (0x0000001BU)
#define CSL_DSS_VP1_CONFIG_RESERVED3_MAX                                       (0x0000001FU)

/* CONTROL */

#define CSL_DSS_VP1_CONTROL_ENABLE_MASK                                        (0x00000001U)
#define CSL_DSS_VP1_CONTROL_ENABLE_SHIFT                                       (0x00000000U)
#define CSL_DSS_VP1_CONTROL_ENABLE_MAX                                         (0x00000001U)

#define CSL_DSS_VP1_CONTROL_ENABLE_VAL_LCDOPDIS                                (0x0U)
#define CSL_DSS_VP1_CONTROL_ENABLE_VAL_LCDOPENB                                (0x1U)

#define CSL_DSS_VP1_CONTROL_VPPROGLINENUMBERMODULO_MASK                        (0x00000002U)
#define CSL_DSS_VP1_CONTROL_VPPROGLINENUMBERMODULO_SHIFT                       (0x00000001U)
#define CSL_DSS_VP1_CONTROL_VPPROGLINENUMBERMODULO_MAX                         (0x00000001U)

#define CSL_DSS_VP1_CONTROL_VPPROGLINENUMBERMODULO_VAL_MODDIS                  (0x0U)
#define CSL_DSS_VP1_CONTROL_VPPROGLINENUMBERMODULO_VAL_MODEN                   (0x1U)

#define CSL_DSS_VP1_CONTROL_MONOCOLOR_MASK                                     (0x00000004U)
#define CSL_DSS_VP1_CONTROL_MONOCOLOR_SHIFT                                    (0x00000002U)
#define CSL_DSS_VP1_CONTROL_MONOCOLOR_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONTROL_STN_MASK                                           (0x00000008U)
#define CSL_DSS_VP1_CONTROL_STN_SHIFT                                          (0x00000003U)
#define CSL_DSS_VP1_CONTROL_STN_MAX                                            (0x00000001U)

#define CSL_DSS_VP1_CONTROL_M8B_MASK                                           (0x00000010U)
#define CSL_DSS_VP1_CONTROL_M8B_SHIFT                                          (0x00000004U)
#define CSL_DSS_VP1_CONTROL_M8B_MAX                                            (0x00000001U)

#define CSL_DSS_VP1_CONTROL_GOBIT_MASK                                         (0x00000020U)
#define CSL_DSS_VP1_CONTROL_GOBIT_SHIFT                                        (0x00000005U)
#define CSL_DSS_VP1_CONTROL_GOBIT_MAX                                          (0x00000001U)

#define CSL_DSS_VP1_CONTROL_GOBIT_VAL_HFUISR                                   (0x0U)
#define CSL_DSS_VP1_CONTROL_GOBIT_VAL_UFPSR                                    (0x1U)

#define CSL_DSS_VP1_CONTROL_DPIENABLE_MASK                                     (0x00000040U)
#define CSL_DSS_VP1_CONTROL_DPIENABLE_SHIFT                                    (0x00000006U)
#define CSL_DSS_VP1_CONTROL_DPIENABLE_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONTROL_DPIENABLE_VAL_DPIOPDIS                             (0x0U)
#define CSL_DSS_VP1_CONTROL_DPIENABLE_VAL_DPIOPENB                             (0x1U)

#define CSL_DSS_VP1_CONTROL_STDITHERENABLE_MASK                                (0x00000080U)
#define CSL_DSS_VP1_CONTROL_STDITHERENABLE_SHIFT                               (0x00000007U)
#define CSL_DSS_VP1_CONTROL_STDITHERENABLE_MAX                                 (0x00000001U)

#define CSL_DSS_VP1_CONTROL_STDITHERENABLE_VAL_STDITHDIS                       (0x0U)
#define CSL_DSS_VP1_CONTROL_STDITHERENABLE_VAL_STDITHENB                       (0x1U)

#define CSL_DSS_VP1_CONTROL_DATALINES_MASK                                     (0x00000700U)
#define CSL_DSS_VP1_CONTROL_DATALINES_SHIFT                                    (0x00000008U)
#define CSL_DSS_VP1_CONTROL_DATALINES_MAX                                      (0x00000007U)

#define CSL_DSS_VP1_CONTROL_DATALINES_VAL_OALSB12B                             (0x0U)
#define CSL_DSS_VP1_CONTROL_DATALINES_VAL_OALSB16B                             (0x1U)
#define CSL_DSS_VP1_CONTROL_DATALINES_VAL_OALSB18B                             (0x2U)
#define CSL_DSS_VP1_CONTROL_DATALINES_VAL_OALSB24B                             (0x3U)
#define CSL_DSS_VP1_CONTROL_DATALINES_VAL_OALSB30B                             (0x4U)
#define CSL_DSS_VP1_CONTROL_DATALINES_VAL_OALSB36B                             (0x5U)

#define CSL_DSS_VP1_CONTROL_STALLMODE_MASK                                     (0x00000800U)
#define CSL_DSS_VP1_CONTROL_STALLMODE_SHIFT                                    (0x0000000BU)
#define CSL_DSS_VP1_CONTROL_STALLMODE_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONTROL_RESERVED6_MASK                                     (0x00001000U)
#define CSL_DSS_VP1_CONTROL_RESERVED6_SHIFT                                    (0x0000000CU)
#define CSL_DSS_VP1_CONTROL_RESERVED6_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONTROL_RESERVED3_MASK                                     (0x00002000U)
#define CSL_DSS_VP1_CONTROL_RESERVED3_SHIFT                                    (0x0000000DU)
#define CSL_DSS_VP1_CONTROL_RESERVED3_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONTROL_HT_MASK                                            (0x0001C000U)
#define CSL_DSS_VP1_CONTROL_HT_SHIFT                                           (0x0000000EU)
#define CSL_DSS_VP1_CONTROL_HT_MAX                                             (0x00000007U)

#define CSL_DSS_VP1_CONTROL_RESERVED1_MASK                                     (0x000E0000U)
#define CSL_DSS_VP1_CONTROL_RESERVED1_SHIFT                                    (0x00000011U)
#define CSL_DSS_VP1_CONTROL_RESERVED1_MAX                                      (0x00000007U)

#define CSL_DSS_VP1_CONTROL_TDMENABLE_MASK                                     (0x00100000U)
#define CSL_DSS_VP1_CONTROL_TDMENABLE_SHIFT                                    (0x00000014U)
#define CSL_DSS_VP1_CONTROL_TDMENABLE_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_CONTROL_TDMENABLE_VAL_TDMDIS                               (0x0U)
#define CSL_DSS_VP1_CONTROL_TDMENABLE_VAL_TDMENB                               (0x1U)

#define CSL_DSS_VP1_CONTROL_TDMPARALLELMODE_MASK                               (0x00600000U)
#define CSL_DSS_VP1_CONTROL_TDMPARALLELMODE_SHIFT                              (0x00000015U)
#define CSL_DSS_VP1_CONTROL_TDMPARALLELMODE_MAX                                (0x00000003U)

#define CSL_DSS_VP1_CONTROL_TDMPARALLELMODE_VAL_8BPARAINT                      (0x0U)
#define CSL_DSS_VP1_CONTROL_TDMPARALLELMODE_VAL_9BPARAINT                      (0x1U)
#define CSL_DSS_VP1_CONTROL_TDMPARALLELMODE_VAL_12BPARAINT                     (0x2U)
#define CSL_DSS_VP1_CONTROL_TDMPARALLELMODE_VAL_16BPARAINT                     (0x3U)

#define CSL_DSS_VP1_CONTROL_TDMCYCLEFORMAT_MASK                                (0x01800000U)
#define CSL_DSS_VP1_CONTROL_TDMCYCLEFORMAT_SHIFT                               (0x00000017U)
#define CSL_DSS_VP1_CONTROL_TDMCYCLEFORMAT_MAX                                 (0x00000003U)

#define CSL_DSS_VP1_CONTROL_TDMCYCLEFORMAT_VAL_1CYCPERPIX                      (0x0U)
#define CSL_DSS_VP1_CONTROL_TDMCYCLEFORMAT_VAL_2CYCPERPIX                      (0x1U)
#define CSL_DSS_VP1_CONTROL_TDMCYCLEFORMAT_VAL_3CYCPERPIX                      (0x2U)
#define CSL_DSS_VP1_CONTROL_TDMCYCLEFORMAT_VAL_3CYCPER2PIX                     (0x3U)

#define CSL_DSS_VP1_CONTROL_TDMUNUSEDBITS_MASK                                 (0x06000000U)
#define CSL_DSS_VP1_CONTROL_TDMUNUSEDBITS_SHIFT                                (0x00000019U)
#define CSL_DSS_VP1_CONTROL_TDMUNUSEDBITS_MAX                                  (0x00000003U)

#define CSL_DSS_VP1_CONTROL_TDMUNUSEDBITS_VAL_LOWLEVEL                         (0x0U)
#define CSL_DSS_VP1_CONTROL_TDMUNUSEDBITS_VAL_HIGHLEVEL                        (0x1U)
#define CSL_DSS_VP1_CONTROL_TDMUNUSEDBITS_VAL_UNCHANGED                        (0x2U)
#define CSL_DSS_VP1_CONTROL_TDMUNUSEDBITS_VAL_RES                              (0x3U)

#define CSL_DSS_VP1_CONTROL_RESERVED_MASK                                      (0x38000000U)
#define CSL_DSS_VP1_CONTROL_RESERVED_SHIFT                                     (0x0000001BU)
#define CSL_DSS_VP1_CONTROL_RESERVED_MAX                                       (0x00000007U)

#define CSL_DSS_VP1_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_MASK                (0xC0000000U)
#define CSL_DSS_VP1_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_SHIFT               (0x0000001EU)
#define CSL_DSS_VP1_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_MAX                 (0x00000003U)

#define CSL_DSS_VP1_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_VAL_ONEFRAME        (0x0U)
#define CSL_DSS_VP1_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_VAL_TWOFRAMES       (0x1U)
#define CSL_DSS_VP1_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_VAL_FOURFRAMES      (0x2U)
#define CSL_DSS_VP1_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_VAL_RESERVED        (0x3U)

/* CSC_COEF0 */

#define CSL_DSS_VP1_CSC_COEF0_C00_MASK                                         (0x000007FFU)
#define CSL_DSS_VP1_CSC_COEF0_C00_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP1_CSC_COEF0_C00_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF0_RESERVED_53_MASK                                 (0x0000F800U)
#define CSL_DSS_VP1_CSC_COEF0_RESERVED_53_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP1_CSC_COEF0_RESERVED_53_MAX                                  (0x0000001FU)

#define CSL_DSS_VP1_CSC_COEF0_C01_MASK                                         (0x07FF0000U)
#define CSL_DSS_VP1_CSC_COEF0_C01_SHIFT                                        (0x00000010U)
#define CSL_DSS_VP1_CSC_COEF0_C01_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF0_RESERVED_52_MASK                                 (0xF8000000U)
#define CSL_DSS_VP1_CSC_COEF0_RESERVED_52_SHIFT                                (0x0000001BU)
#define CSL_DSS_VP1_CSC_COEF0_RESERVED_52_MAX                                  (0x0000001FU)

/* CSC_COEF1 */

#define CSL_DSS_VP1_CSC_COEF1_C02_MASK                                         (0x000007FFU)
#define CSL_DSS_VP1_CSC_COEF1_C02_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP1_CSC_COEF1_C02_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF1_RESERVED_55_MASK                                 (0x0000F800U)
#define CSL_DSS_VP1_CSC_COEF1_RESERVED_55_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP1_CSC_COEF1_RESERVED_55_MAX                                  (0x0000001FU)

#define CSL_DSS_VP1_CSC_COEF1_C10_MASK                                         (0x07FF0000U)
#define CSL_DSS_VP1_CSC_COEF1_C10_SHIFT                                        (0x00000010U)
#define CSL_DSS_VP1_CSC_COEF1_C10_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF1_RESERVED_54_MASK                                 (0xF8000000U)
#define CSL_DSS_VP1_CSC_COEF1_RESERVED_54_SHIFT                                (0x0000001BU)
#define CSL_DSS_VP1_CSC_COEF1_RESERVED_54_MAX                                  (0x0000001FU)

/* CSC_COEF2 */

#define CSL_DSS_VP1_CSC_COEF2_C11_MASK                                         (0x000007FFU)
#define CSL_DSS_VP1_CSC_COEF2_C11_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP1_CSC_COEF2_C11_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF2_RESERVED_57_MASK                                 (0x0000F800U)
#define CSL_DSS_VP1_CSC_COEF2_RESERVED_57_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP1_CSC_COEF2_RESERVED_57_MAX                                  (0x0000001FU)

#define CSL_DSS_VP1_CSC_COEF2_C12_MASK                                         (0x07FF0000U)
#define CSL_DSS_VP1_CSC_COEF2_C12_SHIFT                                        (0x00000010U)
#define CSL_DSS_VP1_CSC_COEF2_C12_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF2_RESERVED_56_MASK                                 (0xF8000000U)
#define CSL_DSS_VP1_CSC_COEF2_RESERVED_56_SHIFT                                (0x0000001BU)
#define CSL_DSS_VP1_CSC_COEF2_RESERVED_56_MAX                                  (0x0000001FU)

/* DATA_CYCLE_0 */

#define CSL_DSS_VP1_DATA_CYCLE_0_NBBITSPIXEL1_MASK                             (0x0000001FU)
#define CSL_DSS_VP1_DATA_CYCLE_0_NBBITSPIXEL1_SHIFT                            (0x00000000U)
#define CSL_DSS_VP1_DATA_CYCLE_0_NBBITSPIXEL1_MAX                              (0x0000001FU)

#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_4_MASK                               (0x000000E0U)
#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_4_SHIFT                              (0x00000005U)
#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_4_MAX                                (0x00000007U)

#define CSL_DSS_VP1_DATA_CYCLE_0_BITALIGNMENTPIXEL1_MASK                       (0x00000F00U)
#define CSL_DSS_VP1_DATA_CYCLE_0_BITALIGNMENTPIXEL1_SHIFT                      (0x00000008U)
#define CSL_DSS_VP1_DATA_CYCLE_0_BITALIGNMENTPIXEL1_MAX                        (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_3_MASK                               (0x0000F000U)
#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_3_SHIFT                              (0x0000000CU)
#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_3_MAX                                (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_0_NBBITSPIXEL2_MASK                             (0x001F0000U)
#define CSL_DSS_VP1_DATA_CYCLE_0_NBBITSPIXEL2_SHIFT                            (0x00000010U)
#define CSL_DSS_VP1_DATA_CYCLE_0_NBBITSPIXEL2_MAX                              (0x0000001FU)

#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_6_MASK                               (0x00E00000U)
#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_6_SHIFT                              (0x00000015U)
#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_6_MAX                                (0x00000007U)

#define CSL_DSS_VP1_DATA_CYCLE_0_BITALIGNMENTPIXEL2_MASK                       (0x0F000000U)
#define CSL_DSS_VP1_DATA_CYCLE_0_BITALIGNMENTPIXEL2_SHIFT                      (0x00000018U)
#define CSL_DSS_VP1_DATA_CYCLE_0_BITALIGNMENTPIXEL2_MAX                        (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_5_MASK                               (0xF0000000U)
#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_5_SHIFT                              (0x0000001CU)
#define CSL_DSS_VP1_DATA_CYCLE_0_RESERVED_5_MAX                                (0x0000000FU)

/* DATA_CYCLE_1 */

#define CSL_DSS_VP1_DATA_CYCLE_1_NBBITSPIXEL1_MASK                             (0x0000001FU)
#define CSL_DSS_VP1_DATA_CYCLE_1_NBBITSPIXEL1_SHIFT                            (0x00000000U)
#define CSL_DSS_VP1_DATA_CYCLE_1_NBBITSPIXEL1_MAX                              (0x0000001FU)

#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_4_MASK                               (0x000000E0U)
#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_4_SHIFT                              (0x00000005U)
#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_4_MAX                                (0x00000007U)

#define CSL_DSS_VP1_DATA_CYCLE_1_BITALIGNMENTPIXEL1_MASK                       (0x00000F00U)
#define CSL_DSS_VP1_DATA_CYCLE_1_BITALIGNMENTPIXEL1_SHIFT                      (0x00000008U)
#define CSL_DSS_VP1_DATA_CYCLE_1_BITALIGNMENTPIXEL1_MAX                        (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_3_MASK                               (0x0000F000U)
#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_3_SHIFT                              (0x0000000CU)
#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_3_MAX                                (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_1_NBBITSPIXEL2_MASK                             (0x001F0000U)
#define CSL_DSS_VP1_DATA_CYCLE_1_NBBITSPIXEL2_SHIFT                            (0x00000010U)
#define CSL_DSS_VP1_DATA_CYCLE_1_NBBITSPIXEL2_MAX                              (0x0000001FU)

#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_6_MASK                               (0x00E00000U)
#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_6_SHIFT                              (0x00000015U)
#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_6_MAX                                (0x00000007U)

#define CSL_DSS_VP1_DATA_CYCLE_1_BITALIGNMENTPIXEL2_MASK                       (0x0F000000U)
#define CSL_DSS_VP1_DATA_CYCLE_1_BITALIGNMENTPIXEL2_SHIFT                      (0x00000018U)
#define CSL_DSS_VP1_DATA_CYCLE_1_BITALIGNMENTPIXEL2_MAX                        (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_5_MASK                               (0xF0000000U)
#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_5_SHIFT                              (0x0000001CU)
#define CSL_DSS_VP1_DATA_CYCLE_1_RESERVED_5_MAX                                (0x0000000FU)

/* DATA_CYCLE_2 */

#define CSL_DSS_VP1_DATA_CYCLE_2_NBBITSPIXEL1_MASK                             (0x0000001FU)
#define CSL_DSS_VP1_DATA_CYCLE_2_NBBITSPIXEL1_SHIFT                            (0x00000000U)
#define CSL_DSS_VP1_DATA_CYCLE_2_NBBITSPIXEL1_MAX                              (0x0000001FU)

#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_4_MASK                               (0x000000E0U)
#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_4_SHIFT                              (0x00000005U)
#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_4_MAX                                (0x00000007U)

#define CSL_DSS_VP1_DATA_CYCLE_2_BITALIGNMENTPIXEL1_MASK                       (0x00000F00U)
#define CSL_DSS_VP1_DATA_CYCLE_2_BITALIGNMENTPIXEL1_SHIFT                      (0x00000008U)
#define CSL_DSS_VP1_DATA_CYCLE_2_BITALIGNMENTPIXEL1_MAX                        (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_3_MASK                               (0x0000F000U)
#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_3_SHIFT                              (0x0000000CU)
#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_3_MAX                                (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_2_NBBITSPIXEL2_MASK                             (0x001F0000U)
#define CSL_DSS_VP1_DATA_CYCLE_2_NBBITSPIXEL2_SHIFT                            (0x00000010U)
#define CSL_DSS_VP1_DATA_CYCLE_2_NBBITSPIXEL2_MAX                              (0x0000001FU)

#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_6_MASK                               (0x00E00000U)
#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_6_SHIFT                              (0x00000015U)
#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_6_MAX                                (0x00000007U)

#define CSL_DSS_VP1_DATA_CYCLE_2_BITALIGNMENTPIXEL2_MASK                       (0x0F000000U)
#define CSL_DSS_VP1_DATA_CYCLE_2_BITALIGNMENTPIXEL2_SHIFT                      (0x00000018U)
#define CSL_DSS_VP1_DATA_CYCLE_2_BITALIGNMENTPIXEL2_MAX                        (0x0000000FU)

#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_5_MASK                               (0xF0000000U)
#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_5_SHIFT                              (0x0000001CU)
#define CSL_DSS_VP1_DATA_CYCLE_2_RESERVED_5_MAX                                (0x0000000FU)

/* LINE_NUMBER */

#define CSL_DSS_VP1_LINE_NUMBER_LINENUMBER_MASK                                (0x00000FFFU)
#define CSL_DSS_VP1_LINE_NUMBER_LINENUMBER_SHIFT                               (0x00000000U)
#define CSL_DSS_VP1_LINE_NUMBER_LINENUMBER_MAX                                 (0x00000FFFU)

#define CSL_DSS_VP1_LINE_NUMBER_RESERVED_MASK                                  (0xFFFFF000U)
#define CSL_DSS_VP1_LINE_NUMBER_RESERVED_SHIFT                                 (0x0000000CU)
#define CSL_DSS_VP1_LINE_NUMBER_RESERVED_MAX                                   (0x000FFFFFU)

/* POL_FREQ */

#define CSL_DSS_VP1_POL_FREQ_ACB_MASK                                          (0x000000FFU)
#define CSL_DSS_VP1_POL_FREQ_ACB_SHIFT                                         (0x00000000U)
#define CSL_DSS_VP1_POL_FREQ_ACB_MAX                                           (0x000000FFU)

#define CSL_DSS_VP1_POL_FREQ_ACBI_MASK                                         (0x00000F00U)
#define CSL_DSS_VP1_POL_FREQ_ACBI_SHIFT                                        (0x00000008U)
#define CSL_DSS_VP1_POL_FREQ_ACBI_MAX                                          (0x0000000FU)

#define CSL_DSS_VP1_POL_FREQ_IVS_MASK                                          (0x00001000U)
#define CSL_DSS_VP1_POL_FREQ_IVS_SHIFT                                         (0x0000000CU)
#define CSL_DSS_VP1_POL_FREQ_IVS_MAX                                           (0x00000001U)

#define CSL_DSS_VP1_POL_FREQ_IVS_VAL_FCKPINAH                                  (0x0U)
#define CSL_DSS_VP1_POL_FREQ_IVS_VAL_FCKPINAL                                  (0x1U)

#define CSL_DSS_VP1_POL_FREQ_IHS_MASK                                          (0x00002000U)
#define CSL_DSS_VP1_POL_FREQ_IHS_SHIFT                                         (0x0000000DU)
#define CSL_DSS_VP1_POL_FREQ_IHS_MAX                                           (0x00000001U)

#define CSL_DSS_VP1_POL_FREQ_IHS_VAL_LCKPINAH                                  (0x0U)
#define CSL_DSS_VP1_POL_FREQ_IHS_VAL_LCKPINAL                                  (0x1U)

#define CSL_DSS_VP1_POL_FREQ_IPC_MASK                                          (0x00004000U)
#define CSL_DSS_VP1_POL_FREQ_IPC_SHIFT                                         (0x0000000EU)
#define CSL_DSS_VP1_POL_FREQ_IPC_MAX                                           (0x00000001U)

#define CSL_DSS_VP1_POL_FREQ_IPC_VAL_DRPCK                                     (0x0U)
#define CSL_DSS_VP1_POL_FREQ_IPC_VAL_DFPCK                                     (0x1U)

#define CSL_DSS_VP1_POL_FREQ_IEO_MASK                                          (0x00008000U)
#define CSL_DSS_VP1_POL_FREQ_IEO_SHIFT                                         (0x0000000FU)
#define CSL_DSS_VP1_POL_FREQ_IEO_MAX                                           (0x00000001U)

#define CSL_DSS_VP1_POL_FREQ_IEO_VAL_ACBAHIGH                                  (0x0U)
#define CSL_DSS_VP1_POL_FREQ_IEO_VAL_ACBALOW                                   (0x1U)

#define CSL_DSS_VP1_POL_FREQ_RF_MASK                                           (0x00010000U)
#define CSL_DSS_VP1_POL_FREQ_RF_SHIFT                                          (0x00000010U)
#define CSL_DSS_VP1_POL_FREQ_RF_MAX                                            (0x00000001U)

#define CSL_DSS_VP1_POL_FREQ_RF_VAL_DFEDPCK                                    (0x0U)
#define CSL_DSS_VP1_POL_FREQ_RF_VAL_DRIEDPCK                                   (0x1U)

#define CSL_DSS_VP1_POL_FREQ_ONOFF_MASK                                        (0x00020000U)
#define CSL_DSS_VP1_POL_FREQ_ONOFF_SHIFT                                       (0x00000011U)
#define CSL_DSS_VP1_POL_FREQ_ONOFF_MAX                                         (0x00000001U)

#define CSL_DSS_VP1_POL_FREQ_ONOFF_VAL_DOPEDPCK                                (0x0U)
#define CSL_DSS_VP1_POL_FREQ_ONOFF_VAL_DBIT16                                  (0x1U)

#define CSL_DSS_VP1_POL_FREQ_ALIGN_MASK                                        (0x00040000U)
#define CSL_DSS_VP1_POL_FREQ_ALIGN_SHIFT                                       (0x00000012U)
#define CSL_DSS_VP1_POL_FREQ_ALIGN_MAX                                         (0x00000001U)

#define CSL_DSS_VP1_POL_FREQ_ALIGN_VAL_NOTALIGNED                              (0x0U)
#define CSL_DSS_VP1_POL_FREQ_ALIGN_VAL_ALIGNED                                 (0x1U)

#define CSL_DSS_VP1_POL_FREQ_RESERVED_MASK                                     (0xFFF80000U)
#define CSL_DSS_VP1_POL_FREQ_RESERVED_SHIFT                                    (0x00000013U)
#define CSL_DSS_VP1_POL_FREQ_RESERVED_MAX                                      (0x00001FFFU)

/* SIZE_SCREEN */

#define CSL_DSS_VP1_SIZE_SCREEN_PPL_MASK                                       (0x00000FFFU)
#define CSL_DSS_VP1_SIZE_SCREEN_PPL_SHIFT                                      (0x00000000U)
#define CSL_DSS_VP1_SIZE_SCREEN_PPL_MAX                                        (0x00000FFFU)

#define CSL_DSS_VP1_SIZE_SCREEN_RESERVED_MASK                                  (0x00003000U)
#define CSL_DSS_VP1_SIZE_SCREEN_RESERVED_SHIFT                                 (0x0000000CU)
#define CSL_DSS_VP1_SIZE_SCREEN_RESERVED_MAX                                   (0x00000003U)

#define CSL_DSS_VP1_SIZE_SCREEN_DELTA_LPP_MASK                                 (0x0000C000U)
#define CSL_DSS_VP1_SIZE_SCREEN_DELTA_LPP_SHIFT                                (0x0000000EU)
#define CSL_DSS_VP1_SIZE_SCREEN_DELTA_LPP_MAX                                  (0x00000003U)

#define CSL_DSS_VP1_SIZE_SCREEN_DELTA_LPP_VAL_SAME                             (0x0U)
#define CSL_DSS_VP1_SIZE_SCREEN_DELTA_LPP_VAL_PLUSONE                          (0x1U)
#define CSL_DSS_VP1_SIZE_SCREEN_DELTA_LPP_VAL_MINUSONE                         (0x2U)

#define CSL_DSS_VP1_SIZE_SCREEN_LPP_MASK                                       (0x0FFF0000U)
#define CSL_DSS_VP1_SIZE_SCREEN_LPP_SHIFT                                      (0x00000010U)
#define CSL_DSS_VP1_SIZE_SCREEN_LPP_MAX                                        (0x00000FFFU)

#define CSL_DSS_VP1_SIZE_SCREEN_RESERVED1_MASK                                 (0xF0000000U)
#define CSL_DSS_VP1_SIZE_SCREEN_RESERVED1_SHIFT                                (0x0000001CU)
#define CSL_DSS_VP1_SIZE_SCREEN_RESERVED1_MAX                                  (0x0000000FU)

/* TIMING_H */

#define CSL_DSS_VP1_TIMING_H_HSW_MASK                                          (0x000000FFU)
#define CSL_DSS_VP1_TIMING_H_HSW_SHIFT                                         (0x00000000U)
#define CSL_DSS_VP1_TIMING_H_HSW_MAX                                           (0x000000FFU)

#define CSL_DSS_VP1_TIMING_H_HFP_MASK                                          (0x000FFF00U)
#define CSL_DSS_VP1_TIMING_H_HFP_SHIFT                                         (0x00000008U)
#define CSL_DSS_VP1_TIMING_H_HFP_MAX                                           (0x00000FFFU)

#define CSL_DSS_VP1_TIMING_H_HBP_MASK                                          (0xFFF00000U)
#define CSL_DSS_VP1_TIMING_H_HBP_SHIFT                                         (0x00000014U)
#define CSL_DSS_VP1_TIMING_H_HBP_MAX                                           (0x00000FFFU)

/* TIMING_V */

#define CSL_DSS_VP1_TIMING_V_VSW_MASK                                          (0x000000FFU)
#define CSL_DSS_VP1_TIMING_V_VSW_SHIFT                                         (0x00000000U)
#define CSL_DSS_VP1_TIMING_V_VSW_MAX                                           (0x000000FFU)

#define CSL_DSS_VP1_TIMING_V_VFP_MASK                                          (0x000FFF00U)
#define CSL_DSS_VP1_TIMING_V_VFP_SHIFT                                         (0x00000008U)
#define CSL_DSS_VP1_TIMING_V_VFP_MAX                                           (0x00000FFFU)

#define CSL_DSS_VP1_TIMING_V_VBP_MASK                                          (0xFFF00000U)
#define CSL_DSS_VP1_TIMING_V_VBP_SHIFT                                         (0x00000014U)
#define CSL_DSS_VP1_TIMING_V_VBP_MAX                                           (0x00000FFFU)

/* CSC_COEF3 */

#define CSL_DSS_VP1_CSC_COEF3_C20_MASK                                         (0x000007FFU)
#define CSL_DSS_VP1_CSC_COEF3_C20_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP1_CSC_COEF3_C20_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF3_RESERVED_59_MASK                                 (0x0000F800U)
#define CSL_DSS_VP1_CSC_COEF3_RESERVED_59_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP1_CSC_COEF3_RESERVED_59_MAX                                  (0x0000001FU)

#define CSL_DSS_VP1_CSC_COEF3_C21_MASK                                         (0x07FF0000U)
#define CSL_DSS_VP1_CSC_COEF3_C21_SHIFT                                        (0x00000010U)
#define CSL_DSS_VP1_CSC_COEF3_C21_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF3_RESERVED_58_MASK                                 (0xF8000000U)
#define CSL_DSS_VP1_CSC_COEF3_RESERVED_58_SHIFT                                (0x0000001BU)
#define CSL_DSS_VP1_CSC_COEF3_RESERVED_58_MAX                                  (0x0000001FU)

/* CSC_COEF4 */

#define CSL_DSS_VP1_CSC_COEF4_C22_MASK                                         (0x000007FFU)
#define CSL_DSS_VP1_CSC_COEF4_C22_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP1_CSC_COEF4_C22_MAX                                          (0x000007FFU)

#define CSL_DSS_VP1_CSC_COEF4_RESERVED_60_MASK                                 (0xFFFFF800U)
#define CSL_DSS_VP1_CSC_COEF4_RESERVED_60_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP1_CSC_COEF4_RESERVED_60_MAX                                  (0x001FFFFFU)

/* CSC_COEF5 */

#define CSL_DSS_VP1_CSC_COEF5_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VP1_CSC_COEF5_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VP1_CSC_COEF5_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VP1_CSC_COEF5_PREOFFSET1_MASK                                  (0x0000FFF8U)
#define CSL_DSS_VP1_CSC_COEF5_PREOFFSET1_SHIFT                                 (0x00000003U)
#define CSL_DSS_VP1_CSC_COEF5_PREOFFSET1_MAX                                   (0x00001FFFU)

#define CSL_DSS_VP1_CSC_COEF5_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VP1_CSC_COEF5_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VP1_CSC_COEF5_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VP1_CSC_COEF5_PREOFFSET2_MASK                                  (0xFFF80000U)
#define CSL_DSS_VP1_CSC_COEF5_PREOFFSET2_SHIFT                                 (0x00000013U)
#define CSL_DSS_VP1_CSC_COEF5_PREOFFSET2_MAX                                   (0x00001FFFU)

/* CSC_COEF6 */

#define CSL_DSS_VP1_CSC_COEF6_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VP1_CSC_COEF6_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VP1_CSC_COEF6_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VP1_CSC_COEF6_PREOFFSET3_MASK                                  (0x0000FFF8U)
#define CSL_DSS_VP1_CSC_COEF6_PREOFFSET3_SHIFT                                 (0x00000003U)
#define CSL_DSS_VP1_CSC_COEF6_PREOFFSET3_MAX                                   (0x00001FFFU)

#define CSL_DSS_VP1_CSC_COEF6_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VP1_CSC_COEF6_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VP1_CSC_COEF6_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VP1_CSC_COEF6_POSTOFFSET1_MASK                                 (0xFFF80000U)
#define CSL_DSS_VP1_CSC_COEF6_POSTOFFSET1_SHIFT                                (0x00000013U)
#define CSL_DSS_VP1_CSC_COEF6_POSTOFFSET1_MAX                                  (0x00001FFFU)

/* CSC_COEF7 */

#define CSL_DSS_VP1_CSC_COEF7_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VP1_CSC_COEF7_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VP1_CSC_COEF7_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VP1_CSC_COEF7_POSTOFFSET2_MASK                                 (0x0000FFF8U)
#define CSL_DSS_VP1_CSC_COEF7_POSTOFFSET2_SHIFT                                (0x00000003U)
#define CSL_DSS_VP1_CSC_COEF7_POSTOFFSET2_MAX                                  (0x00001FFFU)

#define CSL_DSS_VP1_CSC_COEF7_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VP1_CSC_COEF7_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VP1_CSC_COEF7_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VP1_CSC_COEF7_POSTOFFSET3_MASK                                 (0xFFF80000U)
#define CSL_DSS_VP1_CSC_COEF7_POSTOFFSET3_SHIFT                                (0x00000013U)
#define CSL_DSS_VP1_CSC_COEF7_POSTOFFSET3_MAX                                  (0x00001FFFU)

/* SAFETY_ATTRIBUTES */

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_ENABLE_MASK                              (0x00000001U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_ENABLE_SHIFT                             (0x00000000U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_ENABLE_MAX                               (0x00000001U)

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_CAPTUREMODE_MASK                         (0x00000002U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_CAPTUREMODE_SHIFT                        (0x00000001U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_CAPTUREMODE_MAX                          (0x00000001U)

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_CAPTUREMODE_VAL_FRAMEFREEZE              (0x0U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_CAPTUREMODE_VAL_DATACHECK                (0x1U)

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_SEEDSELECT_MASK                          (0x00000004U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_SEEDSELECT_SHIFT                         (0x00000002U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_SEEDSELECT_MAX                           (0x00000001U)

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_SEEDSELECT_VAL_DISABLE                   (0x0U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_SEEDSELECT_VAL_ENABLE                    (0x1U)

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_THRESHOLD_MASK                           (0x000007F8U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_THRESHOLD_SHIFT                          (0x00000003U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_THRESHOLD_MAX                            (0x000000FFU)

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_FRAMESKIP_MASK                           (0x00001800U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_FRAMESKIP_SHIFT                          (0x0000000BU)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_FRAMESKIP_MAX                            (0x00000003U)

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_NOSKIP                     (0x0U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_EVEN                       (0x1U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_ODD                        (0x2U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_RESERVED                   (0x3U)

#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_RESERVED_MASK                            (0xFFFFE000U)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_RESERVED_SHIFT                           (0x0000000DU)
#define CSL_DSS_VP1_SAFETY_ATTRIBUTES_RESERVED_MAX                             (0x0007FFFFU)

/* SAFETY_CAPT_SIGNATURE */

#define CSL_DSS_VP1_SAFETY_CAPT_SIGNATURE_SIGNATURE_MASK                       (0xFFFFFFFFU)
#define CSL_DSS_VP1_SAFETY_CAPT_SIGNATURE_SIGNATURE_SHIFT                      (0x00000000U)
#define CSL_DSS_VP1_SAFETY_CAPT_SIGNATURE_SIGNATURE_MAX                        (0xFFFFFFFFU)

/* SAFETY_POSITION */

#define CSL_DSS_VP1_SAFETY_POSITION_POSX_MASK                                  (0x00000FFFU)
#define CSL_DSS_VP1_SAFETY_POSITION_POSX_SHIFT                                 (0x00000000U)
#define CSL_DSS_VP1_SAFETY_POSITION_POSX_MAX                                   (0x00000FFFU)

#define CSL_DSS_VP1_SAFETY_POSITION_RESERVED1_MASK                             (0x0000F000U)
#define CSL_DSS_VP1_SAFETY_POSITION_RESERVED1_SHIFT                            (0x0000000CU)
#define CSL_DSS_VP1_SAFETY_POSITION_RESERVED1_MAX                              (0x0000000FU)

#define CSL_DSS_VP1_SAFETY_POSITION_POSY_MASK                                  (0x0FFF0000U)
#define CSL_DSS_VP1_SAFETY_POSITION_POSY_SHIFT                                 (0x00000010U)
#define CSL_DSS_VP1_SAFETY_POSITION_POSY_MAX                                   (0x00000FFFU)

#define CSL_DSS_VP1_SAFETY_POSITION_RESERVED_MASK                              (0xF0000000U)
#define CSL_DSS_VP1_SAFETY_POSITION_RESERVED_SHIFT                             (0x0000001CU)
#define CSL_DSS_VP1_SAFETY_POSITION_RESERVED_MAX                               (0x0000000FU)

/* SAFETY_REF_SIGNATURE */

#define CSL_DSS_VP1_SAFETY_REF_SIGNATURE_SIGNATURE_MASK                        (0xFFFFFFFFU)
#define CSL_DSS_VP1_SAFETY_REF_SIGNATURE_SIGNATURE_SHIFT                       (0x00000000U)
#define CSL_DSS_VP1_SAFETY_REF_SIGNATURE_SIGNATURE_MAX                         (0xFFFFFFFFU)

/* SAFETY_SIZE */

#define CSL_DSS_VP1_SAFETY_SIZE_SIZEX_MASK                                     (0x00000FFFU)
#define CSL_DSS_VP1_SAFETY_SIZE_SIZEX_SHIFT                                    (0x00000000U)
#define CSL_DSS_VP1_SAFETY_SIZE_SIZEX_MAX                                      (0x00000FFFU)

#define CSL_DSS_VP1_SAFETY_SIZE_RESERVED1_MASK                                 (0x0000F000U)
#define CSL_DSS_VP1_SAFETY_SIZE_RESERVED1_SHIFT                                (0x0000000CU)
#define CSL_DSS_VP1_SAFETY_SIZE_RESERVED1_MAX                                  (0x0000000FU)

#define CSL_DSS_VP1_SAFETY_SIZE_SIZEY_MASK                                     (0x0FFF0000U)
#define CSL_DSS_VP1_SAFETY_SIZE_SIZEY_SHIFT                                    (0x00000010U)
#define CSL_DSS_VP1_SAFETY_SIZE_SIZEY_MAX                                      (0x00000FFFU)

#define CSL_DSS_VP1_SAFETY_SIZE_RESERVED_MASK                                  (0xF0000000U)
#define CSL_DSS_VP1_SAFETY_SIZE_RESERVED_SHIFT                                 (0x0000001CU)
#define CSL_DSS_VP1_SAFETY_SIZE_RESERVED_MAX                                   (0x0000000FU)

/* SAFETY_LFSR_SEED */

#define CSL_DSS_VP1_SAFETY_LFSR_SEED_SEED_MASK                                 (0xFFFFFFFFU)
#define CSL_DSS_VP1_SAFETY_LFSR_SEED_SEED_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_SAFETY_LFSR_SEED_SEED_MAX                                  (0xFFFFFFFFU)

/* GAMMA_TABLE_0 */

#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_0_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_0_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_0_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_0_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_1 */

#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_1_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_1_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_1_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_1_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_2 */

#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_2_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_2_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_2_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_2_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_3 */

#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_3_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_3_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_3_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_3_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_4 */

#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_4_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_4_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_4_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_4_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_5 */

#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_5_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_5_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_5_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_5_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_6 */

#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_6_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_6_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_6_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_6_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_7 */

#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_7_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_7_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_7_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_7_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_8 */

#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_8_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_8_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_8_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_8_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_9 */

#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_9_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_9_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_9_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_9_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_10 */

#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_10_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_10_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_10_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_10_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_11 */

#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_11_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_11_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_11_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_11_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_12 */

#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_12_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_12_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_12_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_12_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_13 */

#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_13_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_13_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_13_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_13_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_14 */

#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_14_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_14_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_14_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_14_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_15 */

#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP1_GAMMA_TABLE_15_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP1_GAMMA_TABLE_15_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP1_GAMMA_TABLE_15_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP1_GAMMA_TABLE_15_INDEX_MAX                                   (0x000000FFU)

/* DSS_OLDI_CFG */

#define CSL_DSS_VP1_DSS_OLDI_CFG_ENABLE_MASK                                   (0x00000001U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_ENABLE_SHIFT                                  (0x00000000U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_ENABLE_MAX                                    (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_ENABLE_VAL_DISABLED                           (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_ENABLE_VAL_ENABLED                            (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_MASK                                      (0x0000000EU)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_SHIFT                                     (0x00000001U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_MAX                                       (0x00000007U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_VAL_TYPE_A                                (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_VAL_TYPE_B                                (0x1U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_VAL_TYPE_C                                (0x2U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_VAL_TYPE_D                                (0x4U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_VAL_TYPE_E                                (0x5U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MAP_VAL_TYPE_F                                (0x6U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_SRC_MASK                                      (0x00000010U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_SRC_SHIFT                                     (0x00000004U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_SRC_MAX                                       (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_SRC_VAL_CHANNEL0                              (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_SRC_VAL_CHANNEL1                              (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_MODE_MASK                                     (0x00000020U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MODE_SHIFT                                    (0x00000005U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MODE_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_MODE_VAL_SINGLE                               (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MODE_VAL_DUPLICATE                            (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_MASTERSLAVE_MASK                              (0x00000040U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MASTERSLAVE_SHIFT                             (0x00000006U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MASTERSLAVE_MAX                               (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_MASTERSLAVE_VAL_MASTER                        (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MASTERSLAVE_VAL_SLAVE                         (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_DEPOL_MASK                                    (0x00000080U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_DEPOL_SHIFT                                   (0x00000007U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_DEPOL_MAX                                     (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_DEPOL_VAL_HIGH                                (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_DEPOL_VAL_LOW                                 (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_MSB_MASK                                      (0x00000100U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MSB_SHIFT                                     (0x00000008U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MSB_MAX                                       (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_MSB_VAL_18B                                   (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_MSB_VAL_24B                                   (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_LBEN_MASK                                     (0x00000200U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_LBEN_SHIFT                                    (0x00000009U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_LBEN_MAX                                      (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_LBEN_VAL_DISABLE                              (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_LBEN_VAL_ENABLE                               (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_LBDATA_MASK                                   (0x00000400U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_LBDATA_SHIFT                                  (0x0000000AU)
#define CSL_DSS_VP1_DSS_OLDI_CFG_LBDATA_MAX                                    (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_DUALMODESYNC_MASK                             (0x00000800U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_DUALMODESYNC_SHIFT                            (0x0000000BU)
#define CSL_DSS_VP1_DSS_OLDI_CFG_DUALMODESYNC_MAX                              (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_DUALMODESYNC_VAL_DISABLE                      (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_DUALMODESYNC_VAL_ENABLE                       (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_SOFTRST_MASK                                  (0x00001000U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_SOFTRST_SHIFT                                 (0x0000000CU)
#define CSL_DSS_VP1_DSS_OLDI_CFG_SOFTRST_MAX                                   (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_SOFTRST_VAL_ASSERT                            (0x0U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_SOFTRST_VAL_DEASSERT                          (0x1U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_TPATCFG_MASK                                  (0x00002000U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_TPATCFG_SHIFT                                 (0x0000000DU)
#define CSL_DSS_VP1_DSS_OLDI_CFG_TPATCFG_MAX                                   (0x00000001U)

#define CSL_DSS_VP1_DSS_OLDI_CFG_RESERVED_MASK                                 (0xFFFFC000U)
#define CSL_DSS_VP1_DSS_OLDI_CFG_RESERVED_SHIFT                                (0x0000000EU)
#define CSL_DSS_VP1_DSS_OLDI_CFG_RESERVED_MAX                                  (0x0003FFFFU)

/* DSS_OLDI_STATUS */

#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVMIN_MASK                                (0x0000003FU)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVMIN_SHIFT                               (0x00000000U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVMIN_MAX                                 (0x0000003FU)

#define CSL_DSS_VP1_DSS_OLDI_STATUS_CUSTOM_MASK                                (0x000000C0U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_CUSTOM_SHIFT                               (0x00000006U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_CUSTOM_MAX                                 (0x00000003U)

#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVMAJOR_MASK                              (0x00000700U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVMAJOR_SHIFT                             (0x00000008U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVMAJOR_MAX                               (0x00000007U)

#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVRTL_MASK                                (0x0000F800U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVRTL_SHIFT                               (0x0000000BU)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_REVRTL_MAX                                 (0x0000001FU)

#define CSL_DSS_VP1_DSS_OLDI_STATUS_MODID_MASK                                 (0xFFFF0000U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_MODID_SHIFT                                (0x00000010U)
#define CSL_DSS_VP1_DSS_OLDI_STATUS_MODID_MAX                                  (0x0000FFFFU)

/* DSS_OLDI_LB */

#define CSL_DSS_VP1_DSS_OLDI_LB_LBRDATA_MASK                                   (0x000003FFU)
#define CSL_DSS_VP1_DSS_OLDI_LB_LBRDATA_SHIFT                                  (0x00000000U)
#define CSL_DSS_VP1_DSS_OLDI_LB_LBRDATA_MAX                                    (0x000003FFU)

#define CSL_DSS_VP1_DSS_OLDI_LB_RESERVED_MASK                                  (0xFFFFFC00U)
#define CSL_DSS_VP1_DSS_OLDI_LB_RESERVED_SHIFT                                 (0x0000000AU)
#define CSL_DSS_VP1_DSS_OLDI_LB_RESERVED_MAX                                   (0x003FFFFFU)

/**************************************************************************
* Hardware Region  : VP2 Registers
**************************************************************************/


/**************************************************************************
* Register Overlay Structure
**************************************************************************/

typedef struct {
    volatile uint32_t CONFIG;                    /* CONFIG */
    volatile uint32_t CONTROL;                   /* CONTROL */
    volatile uint32_t CSC_COEF0;                 /* CSC_COEF0 */
    volatile uint32_t CSC_COEF1;                 /* CSC_COEF1 */
    volatile uint32_t CSC_COEF2;                 /* CSC_COEF2 */
    volatile uint32_t DATA_CYCLE_0;              /* DATA_CYCLE_0 */
    volatile uint32_t DATA_CYCLE_1;              /* DATA_CYCLE_1 */
    volatile uint32_t DATA_CYCLE_2;              /* DATA_CYCLE_2 */
    volatile uint8_t  Resv_68[36];
    volatile uint32_t LINE_NUMBER;               /* LINE_NUMBER */
    volatile uint8_t  Resv_76[4];
    volatile uint32_t POL_FREQ;                  /* POL_FREQ */
    volatile uint32_t SIZE_SCREEN;               /* SIZE_SCREEN */
    volatile uint32_t TIMING_H;                  /* TIMING_H */
    volatile uint32_t TIMING_V;                  /* TIMING_V */
    volatile uint32_t CSC_COEF3;                 /* CSC_COEF3 */
    volatile uint32_t CSC_COEF4;                 /* CSC_COEF4 */
    volatile uint32_t CSC_COEF5;                 /* CSC_COEF5 */
    volatile uint32_t CSC_COEF6;                 /* CSC_COEF6 */
    volatile uint32_t CSC_COEF7;                 /* CSC_COEF7 */
    volatile uint32_t SAFETY_ATTRIBUTES[4U];     /* SAFETY_ATTRIBUTES 0..3 */
    volatile uint8_t  Resv_144[16];
    volatile uint32_t SAFETY_CAPT_SIGNATURE[4U]; /* SAFETY_CAPT_SIGNATURE 0..3 */
    volatile uint8_t  Resv_176[16];
    volatile uint32_t SAFETY_POSITION[4U];       /* SAFETY_POSITION 0..3 */
    volatile uint8_t  Resv_208[16];
    volatile uint32_t SAFETY_REF_SIGNATURE[4U];  /* SAFETY_REF_SIGNATURE 0..3 */
    volatile uint8_t  Resv_240[16];
    volatile uint32_t SAFETY_SIZE[4U];           /* SAFETY_SIZE 0..3 */
    volatile uint8_t  Resv_272[16];
    volatile uint32_t SAFETY_LFSR_SEED;          /* SAFETY_LFSR_SEED */
    volatile uint8_t  Resv_288[12];
    volatile uint32_t GAMMA_TABLE_0;             /* GAMMA_TABLE_0 */
    volatile uint32_t GAMMA_TABLE_1;             /* GAMMA_TABLE_1 */
    volatile uint32_t GAMMA_TABLE_2;             /* GAMMA_TABLE_2 */
    volatile uint32_t GAMMA_TABLE_3;             /* GAMMA_TABLE_3 */
    volatile uint32_t GAMMA_TABLE_4;             /* GAMMA_TABLE_4 */
    volatile uint32_t GAMMA_TABLE_5;             /* GAMMA_TABLE_5 */
    volatile uint32_t GAMMA_TABLE_6;             /* GAMMA_TABLE_6 */
    volatile uint32_t GAMMA_TABLE_7;             /* GAMMA_TABLE_7 */
    volatile uint32_t GAMMA_TABLE_8;             /* GAMMA_TABLE_8 */
    volatile uint32_t GAMMA_TABLE_9;             /* GAMMA_TABLE_9 */
    volatile uint32_t GAMMA_TABLE_10;            /* GAMMA_TABLE_10 */
    volatile uint32_t GAMMA_TABLE_11;            /* GAMMA_TABLE_11 */
    volatile uint32_t GAMMA_TABLE_12;            /* GAMMA_TABLE_12 */
    volatile uint32_t GAMMA_TABLE_13;            /* GAMMA_TABLE_13 */
    volatile uint32_t GAMMA_TABLE_14;            /* GAMMA_TABLE_14 */
    volatile uint32_t GAMMA_TABLE_15;            /* GAMMA_TABLE_15 */
    volatile uint32_t DSS_OLDI_CFG;              /* DSS_OLDI_CFG */
    volatile uint32_t DSS_OLDI_STATUS;           /* DSS_OLDI_STATUS */
    volatile uint32_t DSS_OLDI_LB;               /* DSS_OLDI_LB */
} CSL_dss_vp2Regs;


/**************************************************************************
* Register Macros
**************************************************************************/

#define CSL_DSS_VP2_CONFIG                                                     (0x00000000U)
#define CSL_DSS_VP2_CONTROL                                                    (0x00000004U)
#define CSL_DSS_VP2_CSC_COEF0                                                  (0x00000008U)
#define CSL_DSS_VP2_CSC_COEF1                                                  (0x0000000CU)
#define CSL_DSS_VP2_CSC_COEF2                                                  (0x00000010U)
#define CSL_DSS_VP2_DATA_CYCLE_0                                               (0x00000014U)
#define CSL_DSS_VP2_DATA_CYCLE_1                                               (0x00000018U)
#define CSL_DSS_VP2_DATA_CYCLE_2                                               (0x0000001CU)
#define CSL_DSS_VP2_LINE_NUMBER                                                (0x00000044U)
#define CSL_DSS_VP2_POL_FREQ                                                   (0x0000004CU)
#define CSL_DSS_VP2_SIZE_SCREEN                                                (0x00000050U)
#define CSL_DSS_VP2_TIMING_H                                                   (0x00000054U)
#define CSL_DSS_VP2_TIMING_V                                                   (0x00000058U)
#define CSL_DSS_VP2_CSC_COEF3                                                  (0x0000005CU)
#define CSL_DSS_VP2_CSC_COEF4                                                  (0x00000060U)
#define CSL_DSS_VP2_CSC_COEF5                                                  (0x00000064U)
#define CSL_DSS_VP2_CSC_COEF6                                                  (0x00000068U)
#define CSL_DSS_VP2_CSC_COEF7                                                  (0x0000006CU)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES(index)                                   (0x00000070U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP2_SAFETY_CAPT_SIGNATURE(index)                               (0x00000090U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP2_SAFETY_POSITION(index)                                     (0x000000B0U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP2_SAFETY_REF_SIGNATURE(index)                                (0x000000D0U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP2_SAFETY_SIZE(index)                                         (0x000000F0U+((uint32_t)(index)*0x4U))
#define CSL_DSS_VP2_SAFETY_LFSR_SEED                                           (0x00000110U)
#define CSL_DSS_VP2_GAMMA_TABLE_0                                              (0x00000120U)
#define CSL_DSS_VP2_GAMMA_TABLE_1                                              (0x00000124U)
#define CSL_DSS_VP2_GAMMA_TABLE_2                                              (0x00000128U)
#define CSL_DSS_VP2_GAMMA_TABLE_3                                              (0x0000012CU)
#define CSL_DSS_VP2_GAMMA_TABLE_4                                              (0x00000130U)
#define CSL_DSS_VP2_GAMMA_TABLE_5                                              (0x00000134U)
#define CSL_DSS_VP2_GAMMA_TABLE_6                                              (0x00000138U)
#define CSL_DSS_VP2_GAMMA_TABLE_7                                              (0x0000013CU)
#define CSL_DSS_VP2_GAMMA_TABLE_8                                              (0x00000140U)
#define CSL_DSS_VP2_GAMMA_TABLE_9                                              (0x00000144U)
#define CSL_DSS_VP2_GAMMA_TABLE_10                                             (0x00000148U)
#define CSL_DSS_VP2_GAMMA_TABLE_11                                             (0x0000014CU)
#define CSL_DSS_VP2_GAMMA_TABLE_12                                             (0x00000150U)
#define CSL_DSS_VP2_GAMMA_TABLE_13                                             (0x00000154U)
#define CSL_DSS_VP2_GAMMA_TABLE_14                                             (0x00000158U)
#define CSL_DSS_VP2_GAMMA_TABLE_15                                             (0x0000015CU)
#define CSL_DSS_VP2_DSS_OLDI_CFG                                               (0x00000160U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS                                            (0x00000164U)
#define CSL_DSS_VP2_DSS_OLDI_LB                                                (0x00000168U)

/**************************************************************************
* Field Definition Macros
**************************************************************************/


/* CONFIG */

#define CSL_DSS_VP2_CONFIG_PIXELGATED_MASK                                     (0x00000001U)
#define CSL_DSS_VP2_CONFIG_PIXELGATED_SHIFT                                    (0x00000000U)
#define CSL_DSS_VP2_CONFIG_PIXELGATED_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONFIG_PIXELGATED_VAL_PCLKTOGA                             (0x0U)
#define CSL_DSS_VP2_CONFIG_PIXELGATED_VAL_PCLKTOGV                             (0x1U)

#define CSL_DSS_VP2_CONFIG_DATAENABLEGATED_MASK                                (0x00000002U)
#define CSL_DSS_VP2_CONFIG_DATAENABLEGATED_SHIFT                               (0x00000001U)
#define CSL_DSS_VP2_CONFIG_DATAENABLEGATED_MAX                                 (0x00000001U)

#define CSL_DSS_VP2_CONFIG_DATAENABLEGATED_VAL_DEGDIS                          (0x0U)
#define CSL_DSS_VP2_CONFIG_DATAENABLEGATED_VAL_DEGENB                          (0x1U)

#define CSL_DSS_VP2_CONFIG_GAMMAENABLE_MASK                                    (0x00000004U)
#define CSL_DSS_VP2_CONFIG_GAMMAENABLE_SHIFT                                   (0x00000002U)
#define CSL_DSS_VP2_CONFIG_GAMMAENABLE_MAX                                     (0x00000001U)

#define CSL_DSS_VP2_CONFIG_GAMMAENABLE_VAL_GAMMADIS                            (0x0U)
#define CSL_DSS_VP2_CONFIG_GAMMAENABLE_VAL_GAMMAENB                            (0x1U)

#define CSL_DSS_VP2_CONFIG_HDMIMODE_MASK                                       (0x00000008U)
#define CSL_DSS_VP2_CONFIG_HDMIMODE_SHIFT                                      (0x00000003U)
#define CSL_DSS_VP2_CONFIG_HDMIMODE_MAX                                        (0x00000001U)

#define CSL_DSS_VP2_CONFIG_PIXELDATAGATED_MASK                                 (0x00000010U)
#define CSL_DSS_VP2_CONFIG_PIXELDATAGATED_SHIFT                                (0x00000004U)
#define CSL_DSS_VP2_CONFIG_PIXELDATAGATED_MAX                                  (0x00000001U)

#define CSL_DSS_VP2_CONFIG_PIXELDATAGATED_VAL_PDGDIS                           (0x0U)
#define CSL_DSS_VP2_CONFIG_PIXELDATAGATED_VAL_PDGENB                           (0x1U)

#define CSL_DSS_VP2_CONFIG_PIXELCLOCKGATED_MASK                                (0x00000020U)
#define CSL_DSS_VP2_CONFIG_PIXELCLOCKGATED_SHIFT                               (0x00000005U)
#define CSL_DSS_VP2_CONFIG_PIXELCLOCKGATED_MAX                                 (0x00000001U)

#define CSL_DSS_VP2_CONFIG_PIXELCLOCKGATED_VAL_PCGDIS                          (0x0U)
#define CSL_DSS_VP2_CONFIG_PIXELCLOCKGATED_VAL_PCGENB                          (0x1U)

#define CSL_DSS_VP2_CONFIG_HSYNCGATED_MASK                                     (0x00000040U)
#define CSL_DSS_VP2_CONFIG_HSYNCGATED_SHIFT                                    (0x00000006U)
#define CSL_DSS_VP2_CONFIG_HSYNCGATED_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONFIG_HSYNCGATED_VAL_HGDIS                                (0x0U)
#define CSL_DSS_VP2_CONFIG_HSYNCGATED_VAL_HGENB                                (0x1U)

#define CSL_DSS_VP2_CONFIG_VSYNCGATED_MASK                                     (0x00000080U)
#define CSL_DSS_VP2_CONFIG_VSYNCGATED_SHIFT                                    (0x00000007U)
#define CSL_DSS_VP2_CONFIG_VSYNCGATED_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONFIG_VSYNCGATED_VAL_VGDIS                                (0x0U)
#define CSL_DSS_VP2_CONFIG_VSYNCGATED_VAL_VGENB                                (0x1U)

#define CSL_DSS_VP2_CONFIG_EXTERNALSYNCEN_MASK                                 (0x00000100U)
#define CSL_DSS_VP2_CONFIG_EXTERNALSYNCEN_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_CONFIG_EXTERNALSYNCEN_MAX                                  (0x00000001U)

#define CSL_DSS_VP2_CONFIG_RESERVED1_MASK                                      (0x00007E00U)
#define CSL_DSS_VP2_CONFIG_RESERVED1_SHIFT                                     (0x00000009U)
#define CSL_DSS_VP2_CONFIG_RESERVED1_MAX                                       (0x0000003FU)

#define CSL_DSS_VP2_CONFIG_CPR_MASK                                            (0x00008000U)
#define CSL_DSS_VP2_CONFIG_CPR_SHIFT                                           (0x0000000FU)
#define CSL_DSS_VP2_CONFIG_CPR_MAX                                             (0x00000001U)

#define CSL_DSS_VP2_CONFIG_BUFFERHANDSHAKE_MASK                                (0x00010000U)
#define CSL_DSS_VP2_CONFIG_BUFFERHANDSHAKE_SHIFT                               (0x00000010U)
#define CSL_DSS_VP2_CONFIG_BUFFERHANDSHAKE_MAX                                 (0x00000001U)

#define CSL_DSS_VP2_CONFIG_RESERVED2_MASK                                      (0x000E0000U)
#define CSL_DSS_VP2_CONFIG_RESERVED2_SHIFT                                     (0x00000011U)
#define CSL_DSS_VP2_CONFIG_RESERVED2_MAX                                       (0x00000007U)

#define CSL_DSS_VP2_CONFIG_BT656ENABLE_MASK                                    (0x00100000U)
#define CSL_DSS_VP2_CONFIG_BT656ENABLE_SHIFT                                   (0x00000014U)
#define CSL_DSS_VP2_CONFIG_BT656ENABLE_MAX                                     (0x00000001U)

#define CSL_DSS_VP2_CONFIG_BT656ENABLE_VAL_DISABLE                             (0x0U)
#define CSL_DSS_VP2_CONFIG_BT656ENABLE_VAL_ENABLE                              (0x1U)

#define CSL_DSS_VP2_CONFIG_BT1120ENABLE_MASK                                   (0x00200000U)
#define CSL_DSS_VP2_CONFIG_BT1120ENABLE_SHIFT                                  (0x00000015U)
#define CSL_DSS_VP2_CONFIG_BT1120ENABLE_MAX                                    (0x00000001U)

#define CSL_DSS_VP2_CONFIG_BT1120ENABLE_VAL_DISABLE                            (0x0U)
#define CSL_DSS_VP2_CONFIG_BT1120ENABLE_VAL_ENABLE                             (0x1U)

#define CSL_DSS_VP2_CONFIG_OUTPUTMODEENABLE_MASK                               (0x00400000U)
#define CSL_DSS_VP2_CONFIG_OUTPUTMODEENABLE_SHIFT                              (0x00000016U)
#define CSL_DSS_VP2_CONFIG_OUTPUTMODEENABLE_MAX                                (0x00000001U)

#define CSL_DSS_VP2_CONFIG_OUTPUTMODEENABLE_VAL_DISABLE                        (0x0U)
#define CSL_DSS_VP2_CONFIG_OUTPUTMODEENABLE_VAL_ENABLE                         (0x1U)

#define CSL_DSS_VP2_CONFIG_FIDFIRST_MASK                                       (0x00800000U)
#define CSL_DSS_VP2_CONFIG_FIDFIRST_SHIFT                                      (0x00000017U)
#define CSL_DSS_VP2_CONFIG_FIDFIRST_MAX                                        (0x00000001U)

#define CSL_DSS_VP2_CONFIG_FIDFIRST_VAL_EVEN                                   (0x0U)
#define CSL_DSS_VP2_CONFIG_FIDFIRST_VAL_ODD                                    (0x1U)

#define CSL_DSS_VP2_CONFIG_COLORCONVENABLE_MASK                                (0x01000000U)
#define CSL_DSS_VP2_CONFIG_COLORCONVENABLE_SHIFT                               (0x00000018U)
#define CSL_DSS_VP2_CONFIG_COLORCONVENABLE_MAX                                 (0x00000001U)

#define CSL_DSS_VP2_CONFIG_COLORCONVENABLE_VAL_COLSPCDIS                       (0x0U)
#define CSL_DSS_VP2_CONFIG_COLORCONVENABLE_VAL_COLSPCENB                       (0x1U)

#define CSL_DSS_VP2_CONFIG_FULLRANGE_MASK                                      (0x02000000U)
#define CSL_DSS_VP2_CONFIG_FULLRANGE_SHIFT                                     (0x00000019U)
#define CSL_DSS_VP2_CONFIG_FULLRANGE_MAX                                       (0x00000001U)

#define CSL_DSS_VP2_CONFIG_FULLRANGE_VAL_LIMRANGE                              (0x0U)
#define CSL_DSS_VP2_CONFIG_FULLRANGE_VAL_FULLRANGE                             (0x1U)

#define CSL_DSS_VP2_CONFIG_COLORCONVPOS_MASK                                   (0x04000000U)
#define CSL_DSS_VP2_CONFIG_COLORCONVPOS_SHIFT                                  (0x0000001AU)
#define CSL_DSS_VP2_CONFIG_COLORCONVPOS_MAX                                    (0x00000001U)

#define CSL_DSS_VP2_CONFIG_COLORCONVPOS_VAL_AFTERGAMMA                         (0x0U)
#define CSL_DSS_VP2_CONFIG_COLORCONVPOS_VAL_BEFOREGAMMA                        (0x1U)

#define CSL_DSS_VP2_CONFIG_RESERVED3_MASK                                      (0xF8000000U)
#define CSL_DSS_VP2_CONFIG_RESERVED3_SHIFT                                     (0x0000001BU)
#define CSL_DSS_VP2_CONFIG_RESERVED3_MAX                                       (0x0000001FU)

/* CONTROL */

#define CSL_DSS_VP2_CONTROL_ENABLE_MASK                                        (0x00000001U)
#define CSL_DSS_VP2_CONTROL_ENABLE_SHIFT                                       (0x00000000U)
#define CSL_DSS_VP2_CONTROL_ENABLE_MAX                                         (0x00000001U)

#define CSL_DSS_VP2_CONTROL_ENABLE_VAL_LCDOPDIS                                (0x0U)
#define CSL_DSS_VP2_CONTROL_ENABLE_VAL_LCDOPENB                                (0x1U)

#define CSL_DSS_VP2_CONTROL_VPPROGLINENUMBERMODULO_MASK                        (0x00000002U)
#define CSL_DSS_VP2_CONTROL_VPPROGLINENUMBERMODULO_SHIFT                       (0x00000001U)
#define CSL_DSS_VP2_CONTROL_VPPROGLINENUMBERMODULO_MAX                         (0x00000001U)

#define CSL_DSS_VP2_CONTROL_VPPROGLINENUMBERMODULO_VAL_MODDIS                  (0x0U)
#define CSL_DSS_VP2_CONTROL_VPPROGLINENUMBERMODULO_VAL_MODEN                   (0x1U)

#define CSL_DSS_VP2_CONTROL_MONOCOLOR_MASK                                     (0x00000004U)
#define CSL_DSS_VP2_CONTROL_MONOCOLOR_SHIFT                                    (0x00000002U)
#define CSL_DSS_VP2_CONTROL_MONOCOLOR_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONTROL_STN_MASK                                           (0x00000008U)
#define CSL_DSS_VP2_CONTROL_STN_SHIFT                                          (0x00000003U)
#define CSL_DSS_VP2_CONTROL_STN_MAX                                            (0x00000001U)

#define CSL_DSS_VP2_CONTROL_M8B_MASK                                           (0x00000010U)
#define CSL_DSS_VP2_CONTROL_M8B_SHIFT                                          (0x00000004U)
#define CSL_DSS_VP2_CONTROL_M8B_MAX                                            (0x00000001U)

#define CSL_DSS_VP2_CONTROL_GOBIT_MASK                                         (0x00000020U)
#define CSL_DSS_VP2_CONTROL_GOBIT_SHIFT                                        (0x00000005U)
#define CSL_DSS_VP2_CONTROL_GOBIT_MAX                                          (0x00000001U)

#define CSL_DSS_VP2_CONTROL_GOBIT_VAL_HFUISR                                   (0x0U)
#define CSL_DSS_VP2_CONTROL_GOBIT_VAL_UFPSR                                    (0x1U)

#define CSL_DSS_VP2_CONTROL_DPIENABLE_MASK                                     (0x00000040U)
#define CSL_DSS_VP2_CONTROL_DPIENABLE_SHIFT                                    (0x00000006U)
#define CSL_DSS_VP2_CONTROL_DPIENABLE_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONTROL_DPIENABLE_VAL_DPIOPDIS                             (0x0U)
#define CSL_DSS_VP2_CONTROL_DPIENABLE_VAL_DPIOPENB                             (0x1U)

#define CSL_DSS_VP2_CONTROL_STDITHERENABLE_MASK                                (0x00000080U)
#define CSL_DSS_VP2_CONTROL_STDITHERENABLE_SHIFT                               (0x00000007U)
#define CSL_DSS_VP2_CONTROL_STDITHERENABLE_MAX                                 (0x00000001U)

#define CSL_DSS_VP2_CONTROL_STDITHERENABLE_VAL_STDITHDIS                       (0x0U)
#define CSL_DSS_VP2_CONTROL_STDITHERENABLE_VAL_STDITHENB                       (0x1U)

#define CSL_DSS_VP2_CONTROL_DATALINES_MASK                                     (0x00000700U)
#define CSL_DSS_VP2_CONTROL_DATALINES_SHIFT                                    (0x00000008U)
#define CSL_DSS_VP2_CONTROL_DATALINES_MAX                                      (0x00000007U)

#define CSL_DSS_VP2_CONTROL_DATALINES_VAL_OALSB12B                             (0x0U)
#define CSL_DSS_VP2_CONTROL_DATALINES_VAL_OALSB16B                             (0x1U)
#define CSL_DSS_VP2_CONTROL_DATALINES_VAL_OALSB18B                             (0x2U)
#define CSL_DSS_VP2_CONTROL_DATALINES_VAL_OALSB24B                             (0x3U)
#define CSL_DSS_VP2_CONTROL_DATALINES_VAL_OALSB30B                             (0x4U)
#define CSL_DSS_VP2_CONTROL_DATALINES_VAL_OALSB36B                             (0x5U)

#define CSL_DSS_VP2_CONTROL_STALLMODE_MASK                                     (0x00000800U)
#define CSL_DSS_VP2_CONTROL_STALLMODE_SHIFT                                    (0x0000000BU)
#define CSL_DSS_VP2_CONTROL_STALLMODE_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONTROL_RESERVED6_MASK                                     (0x00001000U)
#define CSL_DSS_VP2_CONTROL_RESERVED6_SHIFT                                    (0x0000000CU)
#define CSL_DSS_VP2_CONTROL_RESERVED6_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONTROL_RESERVED3_MASK                                     (0x00002000U)
#define CSL_DSS_VP2_CONTROL_RESERVED3_SHIFT                                    (0x0000000DU)
#define CSL_DSS_VP2_CONTROL_RESERVED3_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONTROL_HT_MASK                                            (0x0001C000U)
#define CSL_DSS_VP2_CONTROL_HT_SHIFT                                           (0x0000000EU)
#define CSL_DSS_VP2_CONTROL_HT_MAX                                             (0x00000007U)

#define CSL_DSS_VP2_CONTROL_RESERVED1_MASK                                     (0x000E0000U)
#define CSL_DSS_VP2_CONTROL_RESERVED1_SHIFT                                    (0x00000011U)
#define CSL_DSS_VP2_CONTROL_RESERVED1_MAX                                      (0x00000007U)

#define CSL_DSS_VP2_CONTROL_TDMENABLE_MASK                                     (0x00100000U)
#define CSL_DSS_VP2_CONTROL_TDMENABLE_SHIFT                                    (0x00000014U)
#define CSL_DSS_VP2_CONTROL_TDMENABLE_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_CONTROL_TDMENABLE_VAL_TDMDIS                               (0x0U)
#define CSL_DSS_VP2_CONTROL_TDMENABLE_VAL_TDMENB                               (0x1U)

#define CSL_DSS_VP2_CONTROL_TDMPARALLELMODE_MASK                               (0x00600000U)
#define CSL_DSS_VP2_CONTROL_TDMPARALLELMODE_SHIFT                              (0x00000015U)
#define CSL_DSS_VP2_CONTROL_TDMPARALLELMODE_MAX                                (0x00000003U)

#define CSL_DSS_VP2_CONTROL_TDMPARALLELMODE_VAL_8BPARAINT                      (0x0U)
#define CSL_DSS_VP2_CONTROL_TDMPARALLELMODE_VAL_9BPARAINT                      (0x1U)
#define CSL_DSS_VP2_CONTROL_TDMPARALLELMODE_VAL_12BPARAINT                     (0x2U)
#define CSL_DSS_VP2_CONTROL_TDMPARALLELMODE_VAL_16BPARAINT                     (0x3U)

#define CSL_DSS_VP2_CONTROL_TDMCYCLEFORMAT_MASK                                (0x01800000U)
#define CSL_DSS_VP2_CONTROL_TDMCYCLEFORMAT_SHIFT                               (0x00000017U)
#define CSL_DSS_VP2_CONTROL_TDMCYCLEFORMAT_MAX                                 (0x00000003U)

#define CSL_DSS_VP2_CONTROL_TDMCYCLEFORMAT_VAL_1CYCPERPIX                      (0x0U)
#define CSL_DSS_VP2_CONTROL_TDMCYCLEFORMAT_VAL_2CYCPERPIX                      (0x1U)
#define CSL_DSS_VP2_CONTROL_TDMCYCLEFORMAT_VAL_3CYCPERPIX                      (0x2U)
#define CSL_DSS_VP2_CONTROL_TDMCYCLEFORMAT_VAL_3CYCPER2PIX                     (0x3U)

#define CSL_DSS_VP2_CONTROL_TDMUNUSEDBITS_MASK                                 (0x06000000U)
#define CSL_DSS_VP2_CONTROL_TDMUNUSEDBITS_SHIFT                                (0x00000019U)
#define CSL_DSS_VP2_CONTROL_TDMUNUSEDBITS_MAX                                  (0x00000003U)

#define CSL_DSS_VP2_CONTROL_TDMUNUSEDBITS_VAL_LOWLEVEL                         (0x0U)
#define CSL_DSS_VP2_CONTROL_TDMUNUSEDBITS_VAL_HIGHLEVEL                        (0x1U)
#define CSL_DSS_VP2_CONTROL_TDMUNUSEDBITS_VAL_UNCHANGED                        (0x2U)
#define CSL_DSS_VP2_CONTROL_TDMUNUSEDBITS_VAL_RES                              (0x3U)

#define CSL_DSS_VP2_CONTROL_RESERVED_MASK                                      (0x38000000U)
#define CSL_DSS_VP2_CONTROL_RESERVED_SHIFT                                     (0x0000001BU)
#define CSL_DSS_VP2_CONTROL_RESERVED_MAX                                       (0x00000007U)

#define CSL_DSS_VP2_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_MASK                (0xC0000000U)
#define CSL_DSS_VP2_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_SHIFT               (0x0000001EU)
#define CSL_DSS_VP2_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_MAX                 (0x00000003U)

#define CSL_DSS_VP2_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_VAL_ONEFRAME        (0x0U)
#define CSL_DSS_VP2_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_VAL_TWOFRAMES       (0x1U)
#define CSL_DSS_VP2_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_VAL_FOURFRAMES      (0x2U)
#define CSL_DSS_VP2_CONTROL_SPATIALTEMPORALDITHERINGFRAMES_VAL_RESERVED        (0x3U)

/* CSC_COEF0 */

#define CSL_DSS_VP2_CSC_COEF0_C00_MASK                                         (0x000007FFU)
#define CSL_DSS_VP2_CSC_COEF0_C00_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP2_CSC_COEF0_C00_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF0_RESERVED_53_MASK                                 (0x0000F800U)
#define CSL_DSS_VP2_CSC_COEF0_RESERVED_53_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP2_CSC_COEF0_RESERVED_53_MAX                                  (0x0000001FU)

#define CSL_DSS_VP2_CSC_COEF0_C01_MASK                                         (0x07FF0000U)
#define CSL_DSS_VP2_CSC_COEF0_C01_SHIFT                                        (0x00000010U)
#define CSL_DSS_VP2_CSC_COEF0_C01_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF0_RESERVED_52_MASK                                 (0xF8000000U)
#define CSL_DSS_VP2_CSC_COEF0_RESERVED_52_SHIFT                                (0x0000001BU)
#define CSL_DSS_VP2_CSC_COEF0_RESERVED_52_MAX                                  (0x0000001FU)

/* CSC_COEF1 */

#define CSL_DSS_VP2_CSC_COEF1_C02_MASK                                         (0x000007FFU)
#define CSL_DSS_VP2_CSC_COEF1_C02_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP2_CSC_COEF1_C02_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF1_RESERVED_55_MASK                                 (0x0000F800U)
#define CSL_DSS_VP2_CSC_COEF1_RESERVED_55_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP2_CSC_COEF1_RESERVED_55_MAX                                  (0x0000001FU)

#define CSL_DSS_VP2_CSC_COEF1_C10_MASK                                         (0x07FF0000U)
#define CSL_DSS_VP2_CSC_COEF1_C10_SHIFT                                        (0x00000010U)
#define CSL_DSS_VP2_CSC_COEF1_C10_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF1_RESERVED_54_MASK                                 (0xF8000000U)
#define CSL_DSS_VP2_CSC_COEF1_RESERVED_54_SHIFT                                (0x0000001BU)
#define CSL_DSS_VP2_CSC_COEF1_RESERVED_54_MAX                                  (0x0000001FU)

/* CSC_COEF2 */

#define CSL_DSS_VP2_CSC_COEF2_C11_MASK                                         (0x000007FFU)
#define CSL_DSS_VP2_CSC_COEF2_C11_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP2_CSC_COEF2_C11_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF2_RESERVED_57_MASK                                 (0x0000F800U)
#define CSL_DSS_VP2_CSC_COEF2_RESERVED_57_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP2_CSC_COEF2_RESERVED_57_MAX                                  (0x0000001FU)

#define CSL_DSS_VP2_CSC_COEF2_C12_MASK                                         (0x07FF0000U)
#define CSL_DSS_VP2_CSC_COEF2_C12_SHIFT                                        (0x00000010U)
#define CSL_DSS_VP2_CSC_COEF2_C12_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF2_RESERVED_56_MASK                                 (0xF8000000U)
#define CSL_DSS_VP2_CSC_COEF2_RESERVED_56_SHIFT                                (0x0000001BU)
#define CSL_DSS_VP2_CSC_COEF2_RESERVED_56_MAX                                  (0x0000001FU)

/* DATA_CYCLE_0 */

#define CSL_DSS_VP2_DATA_CYCLE_0_NBBITSPIXEL1_MASK                             (0x0000001FU)
#define CSL_DSS_VP2_DATA_CYCLE_0_NBBITSPIXEL1_SHIFT                            (0x00000000U)
#define CSL_DSS_VP2_DATA_CYCLE_0_NBBITSPIXEL1_MAX                              (0x0000001FU)

#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_4_MASK                               (0x000000E0U)
#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_4_SHIFT                              (0x00000005U)
#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_4_MAX                                (0x00000007U)

#define CSL_DSS_VP2_DATA_CYCLE_0_BITALIGNMENTPIXEL1_MASK                       (0x00000F00U)
#define CSL_DSS_VP2_DATA_CYCLE_0_BITALIGNMENTPIXEL1_SHIFT                      (0x00000008U)
#define CSL_DSS_VP2_DATA_CYCLE_0_BITALIGNMENTPIXEL1_MAX                        (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_3_MASK                               (0x0000F000U)
#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_3_SHIFT                              (0x0000000CU)
#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_3_MAX                                (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_0_NBBITSPIXEL2_MASK                             (0x001F0000U)
#define CSL_DSS_VP2_DATA_CYCLE_0_NBBITSPIXEL2_SHIFT                            (0x00000010U)
#define CSL_DSS_VP2_DATA_CYCLE_0_NBBITSPIXEL2_MAX                              (0x0000001FU)

#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_6_MASK                               (0x00E00000U)
#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_6_SHIFT                              (0x00000015U)
#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_6_MAX                                (0x00000007U)

#define CSL_DSS_VP2_DATA_CYCLE_0_BITALIGNMENTPIXEL2_MASK                       (0x0F000000U)
#define CSL_DSS_VP2_DATA_CYCLE_0_BITALIGNMENTPIXEL2_SHIFT                      (0x00000018U)
#define CSL_DSS_VP2_DATA_CYCLE_0_BITALIGNMENTPIXEL2_MAX                        (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_5_MASK                               (0xF0000000U)
#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_5_SHIFT                              (0x0000001CU)
#define CSL_DSS_VP2_DATA_CYCLE_0_RESERVED_5_MAX                                (0x0000000FU)

/* DATA_CYCLE_1 */

#define CSL_DSS_VP2_DATA_CYCLE_1_NBBITSPIXEL1_MASK                             (0x0000001FU)
#define CSL_DSS_VP2_DATA_CYCLE_1_NBBITSPIXEL1_SHIFT                            (0x00000000U)
#define CSL_DSS_VP2_DATA_CYCLE_1_NBBITSPIXEL1_MAX                              (0x0000001FU)

#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_4_MASK                               (0x000000E0U)
#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_4_SHIFT                              (0x00000005U)
#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_4_MAX                                (0x00000007U)

#define CSL_DSS_VP2_DATA_CYCLE_1_BITALIGNMENTPIXEL1_MASK                       (0x00000F00U)
#define CSL_DSS_VP2_DATA_CYCLE_1_BITALIGNMENTPIXEL1_SHIFT                      (0x00000008U)
#define CSL_DSS_VP2_DATA_CYCLE_1_BITALIGNMENTPIXEL1_MAX                        (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_3_MASK                               (0x0000F000U)
#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_3_SHIFT                              (0x0000000CU)
#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_3_MAX                                (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_1_NBBITSPIXEL2_MASK                             (0x001F0000U)
#define CSL_DSS_VP2_DATA_CYCLE_1_NBBITSPIXEL2_SHIFT                            (0x00000010U)
#define CSL_DSS_VP2_DATA_CYCLE_1_NBBITSPIXEL2_MAX                              (0x0000001FU)

#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_6_MASK                               (0x00E00000U)
#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_6_SHIFT                              (0x00000015U)
#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_6_MAX                                (0x00000007U)

#define CSL_DSS_VP2_DATA_CYCLE_1_BITALIGNMENTPIXEL2_MASK                       (0x0F000000U)
#define CSL_DSS_VP2_DATA_CYCLE_1_BITALIGNMENTPIXEL2_SHIFT                      (0x00000018U)
#define CSL_DSS_VP2_DATA_CYCLE_1_BITALIGNMENTPIXEL2_MAX                        (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_5_MASK                               (0xF0000000U)
#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_5_SHIFT                              (0x0000001CU)
#define CSL_DSS_VP2_DATA_CYCLE_1_RESERVED_5_MAX                                (0x0000000FU)

/* DATA_CYCLE_2 */

#define CSL_DSS_VP2_DATA_CYCLE_2_NBBITSPIXEL1_MASK                             (0x0000001FU)
#define CSL_DSS_VP2_DATA_CYCLE_2_NBBITSPIXEL1_SHIFT                            (0x00000000U)
#define CSL_DSS_VP2_DATA_CYCLE_2_NBBITSPIXEL1_MAX                              (0x0000001FU)

#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_4_MASK                               (0x000000E0U)
#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_4_SHIFT                              (0x00000005U)
#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_4_MAX                                (0x00000007U)

#define CSL_DSS_VP2_DATA_CYCLE_2_BITALIGNMENTPIXEL1_MASK                       (0x00000F00U)
#define CSL_DSS_VP2_DATA_CYCLE_2_BITALIGNMENTPIXEL1_SHIFT                      (0x00000008U)
#define CSL_DSS_VP2_DATA_CYCLE_2_BITALIGNMENTPIXEL1_MAX                        (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_3_MASK                               (0x0000F000U)
#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_3_SHIFT                              (0x0000000CU)
#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_3_MAX                                (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_2_NBBITSPIXEL2_MASK                             (0x001F0000U)
#define CSL_DSS_VP2_DATA_CYCLE_2_NBBITSPIXEL2_SHIFT                            (0x00000010U)
#define CSL_DSS_VP2_DATA_CYCLE_2_NBBITSPIXEL2_MAX                              (0x0000001FU)

#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_6_MASK                               (0x00E00000U)
#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_6_SHIFT                              (0x00000015U)
#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_6_MAX                                (0x00000007U)

#define CSL_DSS_VP2_DATA_CYCLE_2_BITALIGNMENTPIXEL2_MASK                       (0x0F000000U)
#define CSL_DSS_VP2_DATA_CYCLE_2_BITALIGNMENTPIXEL2_SHIFT                      (0x00000018U)
#define CSL_DSS_VP2_DATA_CYCLE_2_BITALIGNMENTPIXEL2_MAX                        (0x0000000FU)

#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_5_MASK                               (0xF0000000U)
#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_5_SHIFT                              (0x0000001CU)
#define CSL_DSS_VP2_DATA_CYCLE_2_RESERVED_5_MAX                                (0x0000000FU)

/* LINE_NUMBER */

#define CSL_DSS_VP2_LINE_NUMBER_LINENUMBER_MASK                                (0x00000FFFU)
#define CSL_DSS_VP2_LINE_NUMBER_LINENUMBER_SHIFT                               (0x00000000U)
#define CSL_DSS_VP2_LINE_NUMBER_LINENUMBER_MAX                                 (0x00000FFFU)

#define CSL_DSS_VP2_LINE_NUMBER_RESERVED_MASK                                  (0xFFFFF000U)
#define CSL_DSS_VP2_LINE_NUMBER_RESERVED_SHIFT                                 (0x0000000CU)
#define CSL_DSS_VP2_LINE_NUMBER_RESERVED_MAX                                   (0x000FFFFFU)

/* POL_FREQ */

#define CSL_DSS_VP2_POL_FREQ_ACB_MASK                                          (0x000000FFU)
#define CSL_DSS_VP2_POL_FREQ_ACB_SHIFT                                         (0x00000000U)
#define CSL_DSS_VP2_POL_FREQ_ACB_MAX                                           (0x000000FFU)

#define CSL_DSS_VP2_POL_FREQ_ACBI_MASK                                         (0x00000F00U)
#define CSL_DSS_VP2_POL_FREQ_ACBI_SHIFT                                        (0x00000008U)
#define CSL_DSS_VP2_POL_FREQ_ACBI_MAX                                          (0x0000000FU)

#define CSL_DSS_VP2_POL_FREQ_IVS_MASK                                          (0x00001000U)
#define CSL_DSS_VP2_POL_FREQ_IVS_SHIFT                                         (0x0000000CU)
#define CSL_DSS_VP2_POL_FREQ_IVS_MAX                                           (0x00000001U)

#define CSL_DSS_VP2_POL_FREQ_IVS_VAL_FCKPINAH                                  (0x0U)
#define CSL_DSS_VP2_POL_FREQ_IVS_VAL_FCKPINAL                                  (0x1U)

#define CSL_DSS_VP2_POL_FREQ_IHS_MASK                                          (0x00002000U)
#define CSL_DSS_VP2_POL_FREQ_IHS_SHIFT                                         (0x0000000DU)
#define CSL_DSS_VP2_POL_FREQ_IHS_MAX                                           (0x00000001U)

#define CSL_DSS_VP2_POL_FREQ_IHS_VAL_LCKPINAH                                  (0x0U)
#define CSL_DSS_VP2_POL_FREQ_IHS_VAL_LCKPINAL                                  (0x1U)

#define CSL_DSS_VP2_POL_FREQ_IPC_MASK                                          (0x00004000U)
#define CSL_DSS_VP2_POL_FREQ_IPC_SHIFT                                         (0x0000000EU)
#define CSL_DSS_VP2_POL_FREQ_IPC_MAX                                           (0x00000001U)

#define CSL_DSS_VP2_POL_FREQ_IPC_VAL_DRPCK                                     (0x0U)
#define CSL_DSS_VP2_POL_FREQ_IPC_VAL_DFPCK                                     (0x1U)

#define CSL_DSS_VP2_POL_FREQ_IEO_MASK                                          (0x00008000U)
#define CSL_DSS_VP2_POL_FREQ_IEO_SHIFT                                         (0x0000000FU)
#define CSL_DSS_VP2_POL_FREQ_IEO_MAX                                           (0x00000001U)

#define CSL_DSS_VP2_POL_FREQ_IEO_VAL_ACBAHIGH                                  (0x0U)
#define CSL_DSS_VP2_POL_FREQ_IEO_VAL_ACBALOW                                   (0x1U)

#define CSL_DSS_VP2_POL_FREQ_RF_MASK                                           (0x00010000U)
#define CSL_DSS_VP2_POL_FREQ_RF_SHIFT                                          (0x00000010U)
#define CSL_DSS_VP2_POL_FREQ_RF_MAX                                            (0x00000001U)

#define CSL_DSS_VP2_POL_FREQ_RF_VAL_DFEDPCK                                    (0x0U)
#define CSL_DSS_VP2_POL_FREQ_RF_VAL_DRIEDPCK                                   (0x1U)

#define CSL_DSS_VP2_POL_FREQ_ONOFF_MASK                                        (0x00020000U)
#define CSL_DSS_VP2_POL_FREQ_ONOFF_SHIFT                                       (0x00000011U)
#define CSL_DSS_VP2_POL_FREQ_ONOFF_MAX                                         (0x00000001U)

#define CSL_DSS_VP2_POL_FREQ_ONOFF_VAL_DOPEDPCK                                (0x0U)
#define CSL_DSS_VP2_POL_FREQ_ONOFF_VAL_DBIT16                                  (0x1U)

#define CSL_DSS_VP2_POL_FREQ_ALIGN_MASK                                        (0x00040000U)
#define CSL_DSS_VP2_POL_FREQ_ALIGN_SHIFT                                       (0x00000012U)
#define CSL_DSS_VP2_POL_FREQ_ALIGN_MAX                                         (0x00000001U)

#define CSL_DSS_VP2_POL_FREQ_ALIGN_VAL_NOTALIGNED                              (0x0U)
#define CSL_DSS_VP2_POL_FREQ_ALIGN_VAL_ALIGNED                                 (0x1U)

#define CSL_DSS_VP2_POL_FREQ_RESERVED_MASK                                     (0xFFF80000U)
#define CSL_DSS_VP2_POL_FREQ_RESERVED_SHIFT                                    (0x00000013U)
#define CSL_DSS_VP2_POL_FREQ_RESERVED_MAX                                      (0x00001FFFU)

/* SIZE_SCREEN */

#define CSL_DSS_VP2_SIZE_SCREEN_PPL_MASK                                       (0x00000FFFU)
#define CSL_DSS_VP2_SIZE_SCREEN_PPL_SHIFT                                      (0x00000000U)
#define CSL_DSS_VP2_SIZE_SCREEN_PPL_MAX                                        (0x00000FFFU)

#define CSL_DSS_VP2_SIZE_SCREEN_RESERVED_MASK                                  (0x00003000U)
#define CSL_DSS_VP2_SIZE_SCREEN_RESERVED_SHIFT                                 (0x0000000CU)
#define CSL_DSS_VP2_SIZE_SCREEN_RESERVED_MAX                                   (0x00000003U)

#define CSL_DSS_VP2_SIZE_SCREEN_DELTA_LPP_MASK                                 (0x0000C000U)
#define CSL_DSS_VP2_SIZE_SCREEN_DELTA_LPP_SHIFT                                (0x0000000EU)
#define CSL_DSS_VP2_SIZE_SCREEN_DELTA_LPP_MAX                                  (0x00000003U)

#define CSL_DSS_VP2_SIZE_SCREEN_DELTA_LPP_VAL_SAME                             (0x0U)
#define CSL_DSS_VP2_SIZE_SCREEN_DELTA_LPP_VAL_PLUSONE                          (0x1U)
#define CSL_DSS_VP2_SIZE_SCREEN_DELTA_LPP_VAL_MINUSONE                         (0x2U)

#define CSL_DSS_VP2_SIZE_SCREEN_LPP_MASK                                       (0x0FFF0000U)
#define CSL_DSS_VP2_SIZE_SCREEN_LPP_SHIFT                                      (0x00000010U)
#define CSL_DSS_VP2_SIZE_SCREEN_LPP_MAX                                        (0x00000FFFU)

#define CSL_DSS_VP2_SIZE_SCREEN_RESERVED1_MASK                                 (0xF0000000U)
#define CSL_DSS_VP2_SIZE_SCREEN_RESERVED1_SHIFT                                (0x0000001CU)
#define CSL_DSS_VP2_SIZE_SCREEN_RESERVED1_MAX                                  (0x0000000FU)

/* TIMING_H */

#define CSL_DSS_VP2_TIMING_H_HSW_MASK                                          (0x000000FFU)
#define CSL_DSS_VP2_TIMING_H_HSW_SHIFT                                         (0x00000000U)
#define CSL_DSS_VP2_TIMING_H_HSW_MAX                                           (0x000000FFU)

#define CSL_DSS_VP2_TIMING_H_HFP_MASK                                          (0x000FFF00U)
#define CSL_DSS_VP2_TIMING_H_HFP_SHIFT                                         (0x00000008U)
#define CSL_DSS_VP2_TIMING_H_HFP_MAX                                           (0x00000FFFU)

#define CSL_DSS_VP2_TIMING_H_HBP_MASK                                          (0xFFF00000U)
#define CSL_DSS_VP2_TIMING_H_HBP_SHIFT                                         (0x00000014U)
#define CSL_DSS_VP2_TIMING_H_HBP_MAX                                           (0x00000FFFU)

/* TIMING_V */

#define CSL_DSS_VP2_TIMING_V_VSW_MASK                                          (0x000000FFU)
#define CSL_DSS_VP2_TIMING_V_VSW_SHIFT                                         (0x00000000U)
#define CSL_DSS_VP2_TIMING_V_VSW_MAX                                           (0x000000FFU)

#define CSL_DSS_VP2_TIMING_V_VFP_MASK                                          (0x000FFF00U)
#define CSL_DSS_VP2_TIMING_V_VFP_SHIFT                                         (0x00000008U)
#define CSL_DSS_VP2_TIMING_V_VFP_MAX                                           (0x00000FFFU)

#define CSL_DSS_VP2_TIMING_V_VBP_MASK                                          (0xFFF00000U)
#define CSL_DSS_VP2_TIMING_V_VBP_SHIFT                                         (0x00000014U)
#define CSL_DSS_VP2_TIMING_V_VBP_MAX                                           (0x00000FFFU)

/* CSC_COEF3 */

#define CSL_DSS_VP2_CSC_COEF3_C20_MASK                                         (0x000007FFU)
#define CSL_DSS_VP2_CSC_COEF3_C20_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP2_CSC_COEF3_C20_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF3_RESERVED_59_MASK                                 (0x0000F800U)
#define CSL_DSS_VP2_CSC_COEF3_RESERVED_59_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP2_CSC_COEF3_RESERVED_59_MAX                                  (0x0000001FU)

#define CSL_DSS_VP2_CSC_COEF3_C21_MASK                                         (0x07FF0000U)
#define CSL_DSS_VP2_CSC_COEF3_C21_SHIFT                                        (0x00000010U)
#define CSL_DSS_VP2_CSC_COEF3_C21_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF3_RESERVED_58_MASK                                 (0xF8000000U)
#define CSL_DSS_VP2_CSC_COEF3_RESERVED_58_SHIFT                                (0x0000001BU)
#define CSL_DSS_VP2_CSC_COEF3_RESERVED_58_MAX                                  (0x0000001FU)

/* CSC_COEF4 */

#define CSL_DSS_VP2_CSC_COEF4_C22_MASK                                         (0x000007FFU)
#define CSL_DSS_VP2_CSC_COEF4_C22_SHIFT                                        (0x00000000U)
#define CSL_DSS_VP2_CSC_COEF4_C22_MAX                                          (0x000007FFU)

#define CSL_DSS_VP2_CSC_COEF4_RESERVED_60_MASK                                 (0xFFFFF800U)
#define CSL_DSS_VP2_CSC_COEF4_RESERVED_60_SHIFT                                (0x0000000BU)
#define CSL_DSS_VP2_CSC_COEF4_RESERVED_60_MAX                                  (0x001FFFFFU)

/* CSC_COEF5 */

#define CSL_DSS_VP2_CSC_COEF5_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VP2_CSC_COEF5_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VP2_CSC_COEF5_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VP2_CSC_COEF5_PREOFFSET1_MASK                                  (0x0000FFF8U)
#define CSL_DSS_VP2_CSC_COEF5_PREOFFSET1_SHIFT                                 (0x00000003U)
#define CSL_DSS_VP2_CSC_COEF5_PREOFFSET1_MAX                                   (0x00001FFFU)

#define CSL_DSS_VP2_CSC_COEF5_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VP2_CSC_COEF5_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VP2_CSC_COEF5_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VP2_CSC_COEF5_PREOFFSET2_MASK                                  (0xFFF80000U)
#define CSL_DSS_VP2_CSC_COEF5_PREOFFSET2_SHIFT                                 (0x00000013U)
#define CSL_DSS_VP2_CSC_COEF5_PREOFFSET2_MAX                                   (0x00001FFFU)

/* CSC_COEF6 */

#define CSL_DSS_VP2_CSC_COEF6_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VP2_CSC_COEF6_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VP2_CSC_COEF6_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VP2_CSC_COEF6_PREOFFSET3_MASK                                  (0x0000FFF8U)
#define CSL_DSS_VP2_CSC_COEF6_PREOFFSET3_SHIFT                                 (0x00000003U)
#define CSL_DSS_VP2_CSC_COEF6_PREOFFSET3_MAX                                   (0x00001FFFU)

#define CSL_DSS_VP2_CSC_COEF6_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VP2_CSC_COEF6_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VP2_CSC_COEF6_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VP2_CSC_COEF6_POSTOFFSET1_MASK                                 (0xFFF80000U)
#define CSL_DSS_VP2_CSC_COEF6_POSTOFFSET1_SHIFT                                (0x00000013U)
#define CSL_DSS_VP2_CSC_COEF6_POSTOFFSET1_MAX                                  (0x00001FFFU)

/* CSC_COEF7 */

#define CSL_DSS_VP2_CSC_COEF7_RESERVED_MASK                                    (0x00000007U)
#define CSL_DSS_VP2_CSC_COEF7_RESERVED_SHIFT                                   (0x00000000U)
#define CSL_DSS_VP2_CSC_COEF7_RESERVED_MAX                                     (0x00000007U)

#define CSL_DSS_VP2_CSC_COEF7_POSTOFFSET2_MASK                                 (0x0000FFF8U)
#define CSL_DSS_VP2_CSC_COEF7_POSTOFFSET2_SHIFT                                (0x00000003U)
#define CSL_DSS_VP2_CSC_COEF7_POSTOFFSET2_MAX                                  (0x00001FFFU)

#define CSL_DSS_VP2_CSC_COEF7_RESERVED1_MASK                                   (0x00070000U)
#define CSL_DSS_VP2_CSC_COEF7_RESERVED1_SHIFT                                  (0x00000010U)
#define CSL_DSS_VP2_CSC_COEF7_RESERVED1_MAX                                    (0x00000007U)

#define CSL_DSS_VP2_CSC_COEF7_POSTOFFSET3_MASK                                 (0xFFF80000U)
#define CSL_DSS_VP2_CSC_COEF7_POSTOFFSET3_SHIFT                                (0x00000013U)
#define CSL_DSS_VP2_CSC_COEF7_POSTOFFSET3_MAX                                  (0x00001FFFU)

/* SAFETY_ATTRIBUTES */

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_ENABLE_MASK                              (0x00000001U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_ENABLE_SHIFT                             (0x00000000U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_ENABLE_MAX                               (0x00000001U)

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_CAPTUREMODE_MASK                         (0x00000002U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_CAPTUREMODE_SHIFT                        (0x00000001U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_CAPTUREMODE_MAX                          (0x00000001U)

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_CAPTUREMODE_VAL_FRAMEFREEZE              (0x0U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_CAPTUREMODE_VAL_DATACHECK                (0x1U)

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_SEEDSELECT_MASK                          (0x00000004U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_SEEDSELECT_SHIFT                         (0x00000002U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_SEEDSELECT_MAX                           (0x00000001U)

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_SEEDSELECT_VAL_DISABLE                   (0x0U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_SEEDSELECT_VAL_ENABLE                    (0x1U)

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_THRESHOLD_MASK                           (0x000007F8U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_THRESHOLD_SHIFT                          (0x00000003U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_THRESHOLD_MAX                            (0x000000FFU)

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_FRAMESKIP_MASK                           (0x00001800U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_FRAMESKIP_SHIFT                          (0x0000000BU)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_FRAMESKIP_MAX                            (0x00000003U)

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_NOSKIP                     (0x0U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_EVEN                       (0x1U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_ODD                        (0x2U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_FRAMESKIP_VAL_RESERVED                   (0x3U)

#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_RESERVED_MASK                            (0xFFFFE000U)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_RESERVED_SHIFT                           (0x0000000DU)
#define CSL_DSS_VP2_SAFETY_ATTRIBUTES_RESERVED_MAX                             (0x0007FFFFU)

/* SAFETY_CAPT_SIGNATURE */

#define CSL_DSS_VP2_SAFETY_CAPT_SIGNATURE_SIGNATURE_MASK                       (0xFFFFFFFFU)
#define CSL_DSS_VP2_SAFETY_CAPT_SIGNATURE_SIGNATURE_SHIFT                      (0x00000000U)
#define CSL_DSS_VP2_SAFETY_CAPT_SIGNATURE_SIGNATURE_MAX                        (0xFFFFFFFFU)

/* SAFETY_POSITION */

#define CSL_DSS_VP2_SAFETY_POSITION_POSX_MASK                                  (0x00000FFFU)
#define CSL_DSS_VP2_SAFETY_POSITION_POSX_SHIFT                                 (0x00000000U)
#define CSL_DSS_VP2_SAFETY_POSITION_POSX_MAX                                   (0x00000FFFU)

#define CSL_DSS_VP2_SAFETY_POSITION_RESERVED1_MASK                             (0x0000F000U)
#define CSL_DSS_VP2_SAFETY_POSITION_RESERVED1_SHIFT                            (0x0000000CU)
#define CSL_DSS_VP2_SAFETY_POSITION_RESERVED1_MAX                              (0x0000000FU)

#define CSL_DSS_VP2_SAFETY_POSITION_POSY_MASK                                  (0x0FFF0000U)
#define CSL_DSS_VP2_SAFETY_POSITION_POSY_SHIFT                                 (0x00000010U)
#define CSL_DSS_VP2_SAFETY_POSITION_POSY_MAX                                   (0x00000FFFU)

#define CSL_DSS_VP2_SAFETY_POSITION_RESERVED_MASK                              (0xF0000000U)
#define CSL_DSS_VP2_SAFETY_POSITION_RESERVED_SHIFT                             (0x0000001CU)
#define CSL_DSS_VP2_SAFETY_POSITION_RESERVED_MAX                               (0x0000000FU)

/* SAFETY_REF_SIGNATURE */

#define CSL_DSS_VP2_SAFETY_REF_SIGNATURE_SIGNATURE_MASK                        (0xFFFFFFFFU)
#define CSL_DSS_VP2_SAFETY_REF_SIGNATURE_SIGNATURE_SHIFT                       (0x00000000U)
#define CSL_DSS_VP2_SAFETY_REF_SIGNATURE_SIGNATURE_MAX                         (0xFFFFFFFFU)

/* SAFETY_SIZE */

#define CSL_DSS_VP2_SAFETY_SIZE_SIZEX_MASK                                     (0x00000FFFU)
#define CSL_DSS_VP2_SAFETY_SIZE_SIZEX_SHIFT                                    (0x00000000U)
#define CSL_DSS_VP2_SAFETY_SIZE_SIZEX_MAX                                      (0x00000FFFU)

#define CSL_DSS_VP2_SAFETY_SIZE_RESERVED1_MASK                                 (0x0000F000U)
#define CSL_DSS_VP2_SAFETY_SIZE_RESERVED1_SHIFT                                (0x0000000CU)
#define CSL_DSS_VP2_SAFETY_SIZE_RESERVED1_MAX                                  (0x0000000FU)

#define CSL_DSS_VP2_SAFETY_SIZE_SIZEY_MASK                                     (0x0FFF0000U)
#define CSL_DSS_VP2_SAFETY_SIZE_SIZEY_SHIFT                                    (0x00000010U)
#define CSL_DSS_VP2_SAFETY_SIZE_SIZEY_MAX                                      (0x00000FFFU)

#define CSL_DSS_VP2_SAFETY_SIZE_RESERVED_MASK                                  (0xF0000000U)
#define CSL_DSS_VP2_SAFETY_SIZE_RESERVED_SHIFT                                 (0x0000001CU)
#define CSL_DSS_VP2_SAFETY_SIZE_RESERVED_MAX                                   (0x0000000FU)

/* SAFETY_LFSR_SEED */

#define CSL_DSS_VP2_SAFETY_LFSR_SEED_SEED_MASK                                 (0xFFFFFFFFU)
#define CSL_DSS_VP2_SAFETY_LFSR_SEED_SEED_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_SAFETY_LFSR_SEED_SEED_MAX                                  (0xFFFFFFFFU)

/* GAMMA_TABLE_0 */

#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_0_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_0_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_0_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_0_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_1 */

#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_1_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_1_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_1_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_1_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_2 */

#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_2_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_2_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_2_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_2_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_3 */

#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_3_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_3_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_3_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_3_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_4 */

#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_4_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_4_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_4_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_4_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_5 */

#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_5_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_5_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_5_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_5_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_6 */

#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_6_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_6_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_6_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_6_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_7 */

#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_7_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_7_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_7_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_7_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_8 */

#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_8_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_8_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_8_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_8_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_9 */

#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_B_MASK                                 (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_B_SHIFT                                (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_B_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_G_MASK                                 (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_G_SHIFT                                (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_G_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_R_MASK                                 (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_R_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_9_VALUE_R_MAX                                  (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_9_INDEX_MASK                                   (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_9_INDEX_SHIFT                                  (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_9_INDEX_MAX                                    (0x000000FFU)

/* GAMMA_TABLE_10 */

#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_10_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_10_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_10_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_10_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_11 */

#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_11_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_11_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_11_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_11_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_12 */

#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_12_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_12_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_12_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_12_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_13 */

#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_13_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_13_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_13_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_13_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_14 */

#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_14_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_14_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_14_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_14_INDEX_MAX                                   (0x000000FFU)

/* GAMMA_TABLE_15 */

#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_B_MASK                                (0x000000FFU)
#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_B_SHIFT                               (0x00000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_B_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_G_MASK                                (0x0000FF00U)
#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_G_SHIFT                               (0x00000008U)
#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_G_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_R_MASK                                (0x00FF0000U)
#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_R_SHIFT                               (0x00000010U)
#define CSL_DSS_VP2_GAMMA_TABLE_15_VALUE_R_MAX                                 (0x000000FFU)

#define CSL_DSS_VP2_GAMMA_TABLE_15_INDEX_MASK                                  (0xFF000000U)
#define CSL_DSS_VP2_GAMMA_TABLE_15_INDEX_SHIFT                                 (0x00000018U)
#define CSL_DSS_VP2_GAMMA_TABLE_15_INDEX_MAX                                   (0x000000FFU)

/* DSS_OLDI_CFG */

#define CSL_DSS_VP2_DSS_OLDI_CFG_ENABLE_MASK                                   (0x00000001U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_ENABLE_SHIFT                                  (0x00000000U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_ENABLE_MAX                                    (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_ENABLE_VAL_DISABLED                           (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_ENABLE_VAL_ENABLED                            (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_MASK                                      (0x0000000EU)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_SHIFT                                     (0x00000001U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_MAX                                       (0x00000007U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_VAL_TYPE_A                                (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_VAL_TYPE_B                                (0x1U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_VAL_TYPE_C                                (0x2U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_VAL_TYPE_D                                (0x4U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_VAL_TYPE_E                                (0x5U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MAP_VAL_TYPE_F                                (0x6U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_SRC_MASK                                      (0x00000010U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_SRC_SHIFT                                     (0x00000004U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_SRC_MAX                                       (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_SRC_VAL_CHANNEL0                              (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_SRC_VAL_CHANNEL1                              (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_MODE_MASK                                     (0x00000020U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MODE_SHIFT                                    (0x00000005U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MODE_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_MODE_VAL_SINGLE                               (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MODE_VAL_DUPLICATE                            (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_MASTERSLAVE_MASK                              (0x00000040U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MASTERSLAVE_SHIFT                             (0x00000006U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MASTERSLAVE_MAX                               (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_MASTERSLAVE_VAL_MASTER                        (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MASTERSLAVE_VAL_SLAVE                         (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_DEPOL_MASK                                    (0x00000080U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_DEPOL_SHIFT                                   (0x00000007U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_DEPOL_MAX                                     (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_DEPOL_VAL_HIGH                                (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_DEPOL_VAL_LOW                                 (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_MSB_MASK                                      (0x00000100U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MSB_SHIFT                                     (0x00000008U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MSB_MAX                                       (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_MSB_VAL_18B                                   (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_MSB_VAL_24B                                   (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_LBEN_MASK                                     (0x00000200U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_LBEN_SHIFT                                    (0x00000009U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_LBEN_MAX                                      (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_LBEN_VAL_DISABLE                              (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_LBEN_VAL_ENABLE                               (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_LBDATA_MASK                                   (0x00000400U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_LBDATA_SHIFT                                  (0x0000000AU)
#define CSL_DSS_VP2_DSS_OLDI_CFG_LBDATA_MAX                                    (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_DUALMODESYNC_MASK                             (0x00000800U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_DUALMODESYNC_SHIFT                            (0x0000000BU)
#define CSL_DSS_VP2_DSS_OLDI_CFG_DUALMODESYNC_MAX                              (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_DUALMODESYNC_VAL_DISABLE                      (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_DUALMODESYNC_VAL_ENABLE                       (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_SOFTRST_MASK                                  (0x00001000U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_SOFTRST_SHIFT                                 (0x0000000CU)
#define CSL_DSS_VP2_DSS_OLDI_CFG_SOFTRST_MAX                                   (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_SOFTRST_VAL_ASSERT                            (0x0U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_SOFTRST_VAL_DEASSERT                          (0x1U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_TPATCFG_MASK                                  (0x00002000U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_TPATCFG_SHIFT                                 (0x0000000DU)
#define CSL_DSS_VP2_DSS_OLDI_CFG_TPATCFG_MAX                                   (0x00000001U)

#define CSL_DSS_VP2_DSS_OLDI_CFG_RESERVED_MASK                                 (0xFFFFC000U)
#define CSL_DSS_VP2_DSS_OLDI_CFG_RESERVED_SHIFT                                (0x0000000EU)
#define CSL_DSS_VP2_DSS_OLDI_CFG_RESERVED_MAX                                  (0x0003FFFFU)

/* DSS_OLDI_STATUS */

#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVMIN_MASK                                (0x0000003FU)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVMIN_SHIFT                               (0x00000000U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVMIN_MAX                                 (0x0000003FU)

#define CSL_DSS_VP2_DSS_OLDI_STATUS_CUSTOM_MASK                                (0x000000C0U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_CUSTOM_SHIFT                               (0x00000006U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_CUSTOM_MAX                                 (0x00000003U)

#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVMAJOR_MASK                              (0x00000700U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVMAJOR_SHIFT                             (0x00000008U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVMAJOR_MAX                               (0x00000007U)

#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVRTL_MASK                                (0x0000F800U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVRTL_SHIFT                               (0x0000000BU)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_REVRTL_MAX                                 (0x0000001FU)

#define CSL_DSS_VP2_DSS_OLDI_STATUS_MODID_MASK                                 (0xFFFF0000U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_MODID_SHIFT                                (0x00000010U)
#define CSL_DSS_VP2_DSS_OLDI_STATUS_MODID_MAX                                  (0x0000FFFFU)

/* DSS_OLDI_LB */

#define CSL_DSS_VP2_DSS_OLDI_LB_LBRDATA_MASK                                   (0x000003FFU)
#define CSL_DSS_VP2_DSS_OLDI_LB_LBRDATA_SHIFT                                  (0x00000000U)
#define CSL_DSS_VP2_DSS_OLDI_LB_LBRDATA_MAX                                    (0x000003FFU)

#define CSL_DSS_VP2_DSS_OLDI_LB_RESERVED_MASK                                  (0xFFFFFC00U)
#define CSL_DSS_VP2_DSS_OLDI_LB_RESERVED_SHIFT                                 (0x0000000AU)
#define CSL_DSS_VP2_DSS_OLDI_LB_RESERVED_MAX                                   (0x003FFFFFU)

#ifdef __cplusplus
}
#endif
#endif
