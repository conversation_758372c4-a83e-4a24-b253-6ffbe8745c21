MODULE_NAME = rm_pm_hal_src

SRCDIR = src/rm_pm_hal_src
INCDIR = src/rm_pm_hal_src src/
# Can be removed
PDK_CFLAGS =  -DBUILD_MCU1_0 -DBUILD_MCU

CFLAGS_LOCAL_COMMON = $(PDK_CFLAGS)

# RM & PM HAL Configuration
CONFIG_ADDR_REMAP_OFFSET=0x00000000
CONFIG_LPM_DM=y
CONFIG_DM_BUILD=y
CONFIG_PM=y
CONFIG_PSC=y
CONFIG_CLOCK=y
CONFIG_CLK_PLL_16FFT=y
CONFIG_LPM_CLK=y
CONFIG_LPM_LIMIT_IR_TRACKING=y
CONFIG_RM=y
CONFIG_RM_IRQ=y
CONFIG_RM_RA=y
CONFIG_RM_RA_NAV_RING=y
CONFIG_RM_UDMAP=y
CONFIG_TRACE=y
CONFIG_TRACE_BUFFER=y
CONFIG_TRACE_UART=y
CONFIG_SOC_FOLDER_STRING=am62x
CONFIG_CLK_PLL_16FFT_FRACF_CALIBRATION=y
CONFIG_RM_RA_DMSS_RING=y
CONFIG_INTERRUPT_AGGREGATOR_UNMAPPED_EVENTS=y
CONFIG_SEC_PROXY_DRIVER=y
CONFIG_UDMAP_BCDMA=y
CONFIG_UDMAP_PKTDMA=y
CONFIG_GET_FW_CAPS=y
SCICLIENT_SOCVER = V5

RM_PM_HAL_BUILD_PATH=rm_pm_hal_src
