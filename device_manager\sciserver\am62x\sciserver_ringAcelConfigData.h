/*
 * System Firmware Source File
 *
 * SoC defines for RA configs for AM62X device
 *
 * Data version: 220527_134115
 *
 * Copyright (C) 2021-2022 Texas Instruments Incorporated - http://www.ti.com/
 * ALL RIGHTS RESERVED
 */
#ifndef SOC_AM62X_RA_CONFIG_DATA_H
#define SOC_AM62X_RA_CONFIG_DATA_H

/** Number of RA Config instances */
#define SOC_MAX_RING_CONFIG_INSTANCES (0x10U)

#define AM62X_DMASS0_RINGACC_0_RING_0019_CONF_000 (0x00U)
#define AM62X_DMASS0_RINGACC_0_RING_0018_CONF_001 (0x01U)
#define AM62X_DMASS0_RINGACC_0_RING_0017_CONF_002 (0x02U)
#define AM62X_DMASS0_RINGACC_0_RING_0016_CONF_003 (0x03U)
#define AM62X_DMASS0_RINGACC_0_RING_0015_CONF_004 (0x04U)
#define AM62X_DMASS0_RINGACC_0_RING_0014_CONF_005 (0x05U)
#define AM62X_DMASS0_RINGACC_0_RING_0013_CONF_006 (0x06U)
#define AM62X_DMASS0_RINGACC_0_RING_0012_CONF_007 (0x07U)
#define AM62X_DMASS0_RINGACC_0_RING_0011_CONF_008 (0x08U)
#define AM62X_DMASS0_RINGACC_0_RING_0010_CONF_009 (0x09U)
#define AM62X_DMASS0_RINGACC_0_RING_0009_CONF_010 (0x0AU)
#define AM62X_DMASS0_RINGACC_0_RING_0008_CONF_011 (0x0BU)
#define AM62X_DMASS0_RINGACC_0_RING_0007_CONF_012 (0x0CU)
#define AM62X_DMASS0_RINGACC_0_RING_0006_CONF_013 (0x0DU)
#define AM62X_DMASS0_RINGACC_0_RING_0005_CONF_014 (0x0EU)
#define AM62X_DMASS0_RINGACC_0_RING_0004_CONF_015 (0x0FU)

#endif /* SOC_AM62X_RA_CONFIG_DATA_H */
