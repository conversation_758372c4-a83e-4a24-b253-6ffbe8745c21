/********************************************************************
*
* GICSS0_COMMON_0 INTERRUPT MAP. header file
*
* Copyright (C) 2015-2020 Texas Instruments Incorporated.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*
*    Redistributions of source code must retain the above copyright
*    notice, this list of conditions and the following disclaimer.
*
*    Redistributions in binary form must reproduce the above copyright
*    notice, this list of conditions and the following disclaimer in the
*    documentation and/or other materials provided with the
*    distribution.
*
*    Neither the name of Texas Instruments Incorporated nor the names of
*    its contributors may be used to endorse or promote products derived
*    from this software without specific prior written permission.
*
*  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
*  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
*  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
*  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
*  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
*  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
*  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
*  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
*  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
*  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
*  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*
*/
#ifndef CSLR_GICSS0_COMMON_0_INTERRUPT_MAP_H_
#define CSLR_GICSS0_COMMON_0_INTERRUPT_MAP_H_

#include <drivers/hw_include/cslr.h>
#include <drivers/hw_include/tistdtypes.h>

#ifdef __cplusplus
extern "C"
{
#endif

/*
* List of intr sources for receiver: GICSS0_COMMON_0
*/

#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_CTIIRQ0_0                                               (17U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_CTIIRQ0_0                                               (17U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_CTIIRQ0_0                                               (17U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_CTIIRQ1_0                                               (18U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_CTIIRQ1_0                                               (18U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_CTIIRQ1_0                                               (18U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_CTIIRQ2_0                                               (19U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_CTIIRQ2_0                                               (19U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_CTIIRQ2_0                                               (19U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_CTIIRQ3_0                                               (20U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_CTIIRQ3_0                                               (20U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_CTIIRQ3_0                                               (20U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_COMMIRQ0_0                                              (22U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_COMMIRQ1_0                                              (22U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_COMMIRQ2_0                                              (22U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_COMMIRQ3_0                                              (22U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_PMUIRQ0_0                                               (23U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_PMUIRQ1_0                                               (23U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_PMUIRQ2_0                                               (23U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_PMUIRQ3_0                                               (23U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_CTIIRQ0_0                                               (24U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_CTIIRQ1_0                                               (24U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_CTIIRQ2_0                                               (24U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_CTIIRQ3_0                                               (24U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_VCPUMNTIRQ0_0                                           (25U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_VCPUMNTIRQ1_0                                           (25U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_VCPUMNTIRQ2_0                                           (25U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_VCPUMNTIRQ3_0                                           (25U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_CNTHPIRQ0_0                                             (26U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_CNTHPIRQ1_0                                             (26U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_CNTHPIRQ2_0                                             (26U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_CNTHPIRQ3_0                                             (26U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_CNTVIRQ0_0                                              (27U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_CNTVIRQ1_0                                              (27U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_CNTVIRQ2_0                                              (27U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_CNTVIRQ3_0                                              (27U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_CNTPSIRQ0_0                                             (29U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_CNTPSIRQ1_0                                             (29U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_CNTPSIRQ2_0                                             (29U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_CNTPSIRQ3_0                                             (29U)
#define CSLR_GICSS0_COMMON_0_PPI0_0_A53SS0_CNTPNSIRQ0_0                                            (30U)
#define CSLR_GICSS0_COMMON_0_PPI0_1_A53SS0_CNTPNSIRQ1_0                                            (30U)
#define CSLR_GICSS0_COMMON_0_PPI0_2_A53SS0_CNTPNSIRQ2_0                                            (30U)
#define CSLR_GICSS0_COMMON_0_PPI0_3_A53SS0_CNTPNSIRQ3_0                                            (30U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_0                                    (32U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_1                                    (33U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_2                                    (34U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_3                                    (35U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_4                                    (36U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_5                                    (37U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_6                                    (38U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_7                                    (39U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_8                                    (40U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_9                                    (41U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_10                                   (42U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_11                                   (43U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_12                                   (44U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_13                                   (45U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_14                                   (46U)
#define CSLR_GICSS0_COMMON_0_SPI_MAIN_GPIOMUX_INTROUTER0_OUTP_15                                   (47U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_0                                       (48U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_1                                       (49U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_2                                       (50U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_3                                       (51U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_4                                       (52U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_5                                       (53U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_6                                       (54U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_7                                       (55U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_8                                       (56U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_9                                       (57U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_10                                      (58U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_11                                      (59U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_12                                      (60U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_13                                      (61U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_14                                      (62U)
#define CSLR_GICSS0_COMMON_0_SPI_CMP_EVENT_INTROUTER0_OUTP_15                                      (63U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_0                             (64U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_1                             (65U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_2                             (66U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_3                             (67U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_4                             (68U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_5                             (69U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_6                             (70U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_7                             (71U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_8                             (72U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_9                             (73U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_10                            (74U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_11                            (75U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_12                            (76U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_13                            (77U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_14                            (78U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_15                            (79U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_16                            (80U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_17                            (81U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_18                            (82U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_19                            (83U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_20                            (84U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_21                            (85U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_22                            (86U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_23                            (87U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_24                            (88U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_25                            (89U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_26                            (90U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_27                            (91U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_28                            (92U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_29                            (93U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_30                            (94U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_31                            (95U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_32                            (96U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_33                            (97U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_34                            (98U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_35                            (99U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_36                            (100U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_37                            (101U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_38                            (102U)
#define CSLR_GICSS0_COMMON_0_SPI_DMASS0_INTAGGR_0_INTAGGR_VINTR_PEND_39                            (103U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_MCU_GPIOMUX_INTROUTER0_OUTP_0                                (104U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_MCU_GPIOMUX_INTROUTER0_OUTP_1                                (105U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_MCU_GPIOMUX_INTROUTER0_OUTP_2                                (106U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_MCU_GPIOMUX_INTROUTER0_OUTP_3                                (107U)
#define CSLR_GICSS0_COMMON_0_SPI_MAILBOX0_CLUSTER_0_MAILBOX_CLUSTER_PEND_0                         (108U)
#define CSLR_GICSS0_COMMON_0_SPI_MAILBOX0_CLUSTER_0_MAILBOX_CLUSTER_PEND_1                         (109U)
#define CSLR_GICSS0_COMMON_0_SPI_GLUELOGIC_MAINRESET_REQUEST_GLUE_MAIN_PORZ_SYNC_STRETCH_0         (110U)
#define CSLR_GICSS0_COMMON_0_SPI_GLUELOGIC_MAINRESET_REQUEST_GLUE_MAIN_RESETZ_SYNC_STRETCH_0       (111U)
#define CSLR_GICSS0_COMMON_0_SPI_SA3_SS0_INTAGGR_0_INTAGGR_VINTR_4                                 (112U)
#define CSLR_GICSS0_COMMON_0_SPI_SA3_SS0_INTAGGR_0_INTAGGR_VINTR_5                                 (113U)
#define CSLR_GICSS0_COMMON_0_SPI_MMCSD2_COMMON_0_EMMCSDSS_INTR_0                                   (114U)
#define CSLR_GICSS0_COMMON_0_SPI_MMCSD1_COMMON_0_EMMCSDSS_INTR_0                                   (115U)
#define CSLR_GICSS0_COMMON_0_SPI_DSS0_DISPC_INTR_REQ_0_0                                           (116U)
#define CSLR_GICSS0_COMMON_0_SPI_DSS0_DISPC_INTR_REQ_1_0                                           (117U)
#define CSLR_GICSS0_COMMON_0_SPI_GPU0_GPU_OS_IRQ_0                                                 (118U)
#define CSLR_GICSS0_COMMON_0_SPI_GPU0_GPU_OS_IRQ_1                                                 (119U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_HOST_INTR_PEND_0                              (120U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_HOST_INTR_PEND_1                              (121U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_HOST_INTR_PEND_2                              (122U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_HOST_INTR_PEND_3                              (123U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_HOST_INTR_PEND_4                              (124U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_HOST_INTR_PEND_5                              (125U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_HOST_INTR_PEND_6                              (126U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_HOST_INTR_PEND_7                              (127U)
#define CSLR_GICSS0_COMMON_0_SPI_GLUELOGIC_MAIN_DCC_DONE_GLUE_DCC_DONE_0                           (128U)
#define CSLR_GICSS0_COMMON_0_SPI_GLUELOGIC_SOC_ACCESS_ERR_INTR_GLUE_OUT_0                          (129U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_RTCSS0_RTC_EVENT_PEND_0                                      (132U)
#define CSLR_GICSS0_COMMON_0_SPI_GLUELOGIC_SOC_CBASS_ERR_INTR_GLUE_MAIN_CBASS_AGG_ERR_INTR_0       (133U)
#define CSLR_GICSS0_COMMON_0_SPI_CPSW0_EVNT_PEND_0                                                 (134U)
#define CSLR_GICSS0_COMMON_0_SPI_CPSW0_MDIO_PEND_0                                                 (135U)
#define CSLR_GICSS0_COMMON_0_SPI_CPSW0_STAT_PEND_0                                                 (136U)
#define CSLR_GICSS0_COMMON_0_SPI_GPMC0_GPMC_SINTERRUPT_0                                           (138U)
#define CSLR_GICSS0_COMMON_0_SPI_MCU_I2C0_POINTRPEND_0                                             (139U)
#define CSLR_GICSS0_COMMON_0_SPI_SMS0_TIFS_CBASS_0_FW_EXCEPTION_INTR_0                             (143U)
#define CSLR_GICSS0_COMMON_0_SPI_SMS0_COMMON_0_COMBINED_SEC_IN_0                                   (144U)
#define CSLR_GICSS0_COMMON_0_SPI_ECAP0_ECAP_INT_0                                                  (145U)
#define CSLR_GICSS0_COMMON_0_SPI_ECAP1_ECAP_INT_0                                                  (146U)
#define CSLR_GICSS0_COMMON_0_SPI_ECAP2_ECAP_INT_0                                                  (147U)
#define CSLR_GICSS0_COMMON_0_SPI_EQEP0_EQEP_INT_0                                                  (148U)
#define CSLR_GICSS0_COMMON_0_SPI_EQEP1_EQEP_INT_0                                                  (149U)
#define CSLR_GICSS0_COMMON_0_SPI_EQEP2_EQEP_INT_0                                                  (150U)
#define CSLR_GICSS0_COMMON_0_SPI_DDR16SS0_DDRSS_CONTROLLER_0                                       (151U)
#define CSLR_GICSS0_COMMON_0_SPI_TIMER0_INTR_PEND_0                                                (152U)
#define CSLR_GICSS0_COMMON_0_SPI_TIMER1_INTR_PEND_0                                                (153U)
#define CSLR_GICSS0_COMMON_0_SPI_TIMER2_INTR_PEND_0                                                (154U)
#define CSLR_GICSS0_COMMON_0_SPI_TIMER3_INTR_PEND_0                                                (155U)
#define CSLR_GICSS0_COMMON_0_SPI_TIMER4_INTR_PEND_0                                                (156U)
#define CSLR_GICSS0_COMMON_0_SPI_TIMER5_INTR_PEND_0                                                (157U)
#define CSLR_GICSS0_COMMON_0_SPI_TIMER6_INTR_PEND_0                                                (158U)
#define CSLR_GICSS0_COMMON_0_SPI_TIMER7_INTR_PEND_0                                                (159U)
#define CSLR_GICSS0_COMMON_0_SPI_SA3_SS0_SA_UL_0_SA_UL_PKA_0                                       (160U)
#define CSLR_GICSS0_COMMON_0_SPI_SA3_SS0_SA_UL_0_SA_UL_TRNG_0                                      (161U)
#define CSLR_GICSS0_COMMON_0_SPI_ELM0_ELM_POROCPSINTERRUPT_LVL_0                                   (164U)
#define CSLR_GICSS0_COMMON_0_SPI_MMCSD0_COMMON_0_EMMCSDSS_INTR_0                                   (165U)
#define CSLR_GICSS0_COMMON_0_SPI_MCRC64_0_INT_MCRC_0                                               (166U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_ISO_RESET_PROTCOL_ACK_0                           (167U)
#define CSLR_GICSS0_COMMON_0_SPI_FSS0_OSPI_0_OSPI_LVL_INTR_0                                       (171U)
#define CSLR_GICSS0_COMMON_0_SPI_DDR16SS0_DDRSS_PLL_FREQ_CHANGE_REQ_0                              (172U)
#define CSLR_GICSS0_COMMON_0_SPI_CSI_RX_IF0_COMMON_0_CSI_IRQ_0                                     (173U)
#define CSLR_GICSS0_COMMON_0_SPI_CSI_RX_IF0_COMMON_0_CSI_LEVEL_0                                   (174U)
#define CSLR_GICSS0_COMMON_0_SPI_CSI_RX_IF0_COMMON_0_CSI_ERR_IRQ_0                                 (175U)
#define CSLR_GICSS0_COMMON_0_SPI_SMS0_AESEIP38T_0_AES_SINTREQUEST_P_0                              (176U)
#define CSLR_GICSS0_COMMON_0_SPI_DDPA0_DDPA_INTR_0                                                 (177U)
#define CSLR_GICSS0_COMMON_0_SPI_RTI15_INTR_WWD_0                                                  (178U)
#define CSLR_GICSS0_COMMON_0_SPI_ESM0_ESM_INT_CFG_LVL_0                                            (180U)
#define CSLR_GICSS0_COMMON_0_SPI_ESM0_ESM_INT_HI_LVL_0                                             (181U)
#define CSLR_GICSS0_COMMON_0_SPI_ESM0_ESM_INT_LOW_LVL_0                                            (182U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_VTM0_COMMON_0_THERM_LVL_GT_TH1_INTR_0                        (183U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_VTM0_COMMON_0_THERM_LVL_GT_TH2_INTR_0                        (184U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_VTM0_COMMON_0_THERM_LVL_LT_TH0_INTR_0                        (185U)
#define CSLR_GICSS0_COMMON_0_SPI_MCAN0_COMMON_0_MCANSS_EXT_TS_ROLLOVER_LVL_INT_0                   (186U)
#define CSLR_GICSS0_COMMON_0_SPI_MCAN0_COMMON_0_MCANSS_MCAN_LVL_INT_0                              (187U)
#define CSLR_GICSS0_COMMON_0_SPI_MCAN0_COMMON_0_MCANSS_MCAN_LVL_INT_1                              (188U)
#define CSLR_GICSS0_COMMON_0_SPI_MCU_MCRC64_0_INT_MCRC_0                                           (192U)
#define CSLR_GICSS0_COMMON_0_SPI_I2C0_POINTRPEND_0                                                 (193U)
#define CSLR_GICSS0_COMMON_0_SPI_I2C1_POINTRPEND_0                                                 (194U)
#define CSLR_GICSS0_COMMON_0_SPI_I2C2_POINTRPEND_0                                                 (195U)
#define CSLR_GICSS0_COMMON_0_SPI_I2C3_POINTRPEND_0                                                 (196U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_I2C0_POINTRPEND_0                                            (197U)
#define CSLR_GICSS0_COMMON_0_SPI_SMS0_AESEIP38T_0_AES_SINTREQUEST_S_0                              (198U)
#define CSLR_GICSS0_COMMON_0_SPI_DEBUGSS0_AQCMPINTR_LEVEL_0                                        (201U)
#define CSLR_GICSS0_COMMON_0_SPI_DEBUGSS0_CTM_LEVEL_0                                              (202U)
#define CSLR_GICSS0_COMMON_0_SPI_PSC0_PSC_ALLINT_0                                                 (203U)
#define CSLR_GICSS0_COMMON_0_SPI_MCSPI0_INTR_SPI_0                                                 (204U)
#define CSLR_GICSS0_COMMON_0_SPI_MCSPI1_INTR_SPI_0                                                 (205U)
#define CSLR_GICSS0_COMMON_0_SPI_MCSPI2_INTR_SPI_0                                                 (206U)
#define CSLR_GICSS0_COMMON_0_SPI_MCU_MCSPI0_INTR_SPI_0                                             (208U)
#define CSLR_GICSS0_COMMON_0_SPI_MCU_MCSPI1_INTR_SPI_0                                             (209U)
#define CSLR_GICSS0_COMMON_0_SPI_UART0_USART_IRQ_0                                                 (210U)
#define CSLR_GICSS0_COMMON_0_SPI_UART1_USART_IRQ_0                                                 (211U)
#define CSLR_GICSS0_COMMON_0_SPI_UART2_USART_IRQ_0                                                 (212U)
#define CSLR_GICSS0_COMMON_0_SPI_UART3_USART_IRQ_0                                                 (213U)
#define CSLR_GICSS0_COMMON_0_SPI_UART4_USART_IRQ_0                                                 (214U)
#define CSLR_GICSS0_COMMON_0_SPI_UART5_USART_IRQ_0                                                 (215U)
#define CSLR_GICSS0_COMMON_0_SPI_UART6_USART_IRQ_0                                                 (216U)
#define CSLR_GICSS0_COMMON_0_SPI_MCU_UART0_USART_IRQ_0                                             (217U)
#define CSLR_GICSS0_COMMON_0_SPI_WKUP_UART0_USART_IRQ_0                                            (218U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_IRQ_0                                               (220U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_IRQ_1                                               (221U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_IRQ_2                                               (222U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_IRQ_3                                               (223U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_IRQ_4                                               (224U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_IRQ_5                                               (225U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_IRQ_6                                               (226U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_IRQ_7                                               (227U)
#define CSLR_GICSS0_COMMON_0_SPI_USB0_COMMON_0_MISC_LEVEL_0                                        (228U)
#define CSLR_GICSS0_COMMON_0_SPI_EPWM0_EPWM_ETINT_0                                                (229U)
#define CSLR_GICSS0_COMMON_0_SPI_EPWM0_EPWM_TRIPZINT_0                                             (230U)
#define CSLR_GICSS0_COMMON_0_SPI_EPWM1_EPWM_ETINT_0                                                (231U)
#define CSLR_GICSS0_COMMON_0_SPI_EPWM1_EPWM_TRIPZINT_0                                             (233U)
#define CSLR_GICSS0_COMMON_0_SPI_EPWM2_EPWM_ETINT_0                                                (234U)
#define CSLR_GICSS0_COMMON_0_SPI_EPWM2_EPWM_TRIPZINT_0                                             (235U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_RX_SOF_INTR_REQ_0                             (244U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_RX_SOF_INTR_REQ_1                             (245U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_TX_SOF_INTR_REQ_0                             (246U)
#define CSLR_GICSS0_COMMON_0_SPI_ICSSM0_COMMON_0_PR1_TX_SOF_INTR_REQ_1                             (247U)
#define CSLR_GICSS0_COMMON_0_SPI_RTI0_INTR_WWD_0                                                   (252U)
#define CSLR_GICSS0_COMMON_0_SPI_RTI1_INTR_WWD_0                                                   (253U)
#define CSLR_GICSS0_COMMON_0_SPI_RTI2_INTR_WWD_0                                                   (254U)
#define CSLR_GICSS0_COMMON_0_SPI_RTI3_INTR_WWD_0                                                   (255U)
#define CSLR_GICSS0_COMMON_0_SPI_GLUELOGIC_GLUE_EXT_INTN_OUT_0                                     (256U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_IRQ_0                                               (258U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_IRQ_1                                               (259U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_IRQ_2                                               (260U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_IRQ_3                                               (261U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_IRQ_4                                               (262U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_IRQ_5                                               (263U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_IRQ_6                                               (264U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_IRQ_7                                               (265U)
#define CSLR_GICSS0_COMMON_0_SPI_USB1_COMMON_0_MISC_LEVEL_0                                        (266U)
#define CSLR_GICSS0_COMMON_0_SPI_MCASP0_REC_INTR_PEND_0                                            (267U)
#define CSLR_GICSS0_COMMON_0_SPI_MCASP0_XMIT_INTR_PEND_0                                           (268U)
#define CSLR_GICSS0_COMMON_0_SPI_MCASP1_REC_INTR_PEND_0                                            (269U)
#define CSLR_GICSS0_COMMON_0_SPI_MCASP1_XMIT_INTR_PEND_0                                           (270U)
#define CSLR_GICSS0_COMMON_0_SPI_MCASP2_REC_INTR_PEND_0                                            (271U)
#define CSLR_GICSS0_COMMON_0_SPI_MCASP2_XMIT_INTR_PEND_0                                           (272U)

#ifdef __cplusplus
}
#endif
#endif /* CSLR_GICSS0_COMMON_0_INTERRUPT_MAP_H_ */

