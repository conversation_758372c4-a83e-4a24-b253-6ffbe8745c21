/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_ADDRESS_SLICE_0_MACROS_H_
#define REG_LPDDR4_ADDRESS_SLICE_0_MACROS_H_

#define LPDDR4__DENALI_PHY_1024_READ_MASK                            0x000107FFU
#define LPDDR4__DENALI_PHY_1024_WRITE_MASK                           0x000107FFU
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0_SHIFT    0U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0_WIDTH   11U
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0__REG DENALI_PHY_1024
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_0_MASK  0x00010000U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_0_SHIFT         16U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_0_WIDTH          1U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_0_WOCLR          0U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_0_WOSET          0U
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_0__REG DENALI_PHY_1024
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_0__FLD LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_0

#define LPDDR4__DENALI_PHY_1024__SC_PHY_ADR_MANUAL_CLEAR_0_MASK      0x07000000U
#define LPDDR4__DENALI_PHY_1024__SC_PHY_ADR_MANUAL_CLEAR_0_SHIFT             24U
#define LPDDR4__DENALI_PHY_1024__SC_PHY_ADR_MANUAL_CLEAR_0_WIDTH              3U
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_0__REG DENALI_PHY_1024
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_0__FLD LPDDR4__DENALI_PHY_1024__SC_PHY_ADR_MANUAL_CLEAR_0

#define LPDDR4__DENALI_PHY_1025_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1025_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1025__PHY_ADR_LPBK_RESULT_OBS_0_MASK      0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1025__PHY_ADR_LPBK_RESULT_OBS_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_1025__PHY_ADR_LPBK_RESULT_OBS_0_WIDTH             32U
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_0__REG DENALI_PHY_1025
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_0__FLD LPDDR4__DENALI_PHY_1025__PHY_ADR_LPBK_RESULT_OBS_0

#define LPDDR4__DENALI_PHY_1026_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1026_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_LPBK_ERROR_COUNT_OBS_0_MASK 0x0000FFFFU
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_LPBK_ERROR_COUNT_OBS_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_LPBK_ERROR_COUNT_OBS_0_WIDTH        16U
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_0__REG DENALI_PHY_1026
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_0__FLD LPDDR4__DENALI_PHY_1026__PHY_ADR_LPBK_ERROR_COUNT_OBS_0

#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MEAS_DLY_STEP_VALUE_0_MASK  0x00FF0000U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MEAS_DLY_STEP_VALUE_0_SHIFT         16U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MEAS_DLY_STEP_VALUE_0_WIDTH          8U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_0__REG DENALI_PHY_1026
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_0__FLD LPDDR4__DENALI_PHY_1026__PHY_ADR_MEAS_DLY_STEP_VALUE_0

#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0_MASK 0x0F000000U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0_SHIFT  24U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0_WIDTH   4U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0__REG DENALI_PHY_1026
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_1026__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_1027_READ_MASK                            0xFF7F07FFU
#define LPDDR4__DENALI_PHY_1027_WRITE_MASK                           0xFF7F07FFU
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_MASTER_DLY_LOCK_OBS_0_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_MASTER_DLY_LOCK_OBS_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_MASTER_DLY_LOCK_OBS_0_WIDTH         11U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_0__REG DENALI_PHY_1027
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_0__FLD LPDDR4__DENALI_PHY_1027__PHY_ADR_MASTER_DLY_LOCK_OBS_0

#define LPDDR4__DENALI_PHY_1027__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0_MASK 0x007F0000U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0_SHIFT        16U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0_WIDTH         7U
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_1027
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_1027__PHY_ADR_BASE_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_1027__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0_SHIFT       24U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0_WIDTH        8U
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0__REG DENALI_PHY_1027
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0__FLD LPDDR4__DENALI_PHY_1027__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_0

#define LPDDR4__DENALI_PHY_1028_READ_MASK                            0x01000707U
#define LPDDR4__DENALI_PHY_1028_WRITE_MASK                           0x01000707U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0_MASK 0x00000007U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0_WIDTH        3U
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0__REG DENALI_PHY_1028
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0__FLD LPDDR4__DENALI_PHY_1028__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_0

#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0_MASK 0x00000700U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0_SHIFT       8U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0_WIDTH       3U
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0__REG DENALI_PHY_1028
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_1028__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_0_MASK     0x00010000U
#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_0_WIDTH             1U
#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_0_WOCLR             0U
#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_0_WOSET             0U
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_0__REG DENALI_PHY_1028
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_0__FLD LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_0

#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_0_MASK          0x01000000U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_0_SHIFT                 24U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_0_WIDTH                  1U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_0_WOCLR                  0U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_0_WOSET                  0U
#define LPDDR4__PHY_ADR_TSEL_ENABLE_0__REG DENALI_PHY_1028
#define LPDDR4__PHY_ADR_TSEL_ENABLE_0__FLD LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_0

#define LPDDR4__DENALI_PHY_1029_READ_MASK                            0x011F7F7FU
#define LPDDR4__DENALI_PHY_1029_WRITE_MASK                           0x011F7F7FU
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_LPBK_CONTROL_0_MASK         0x0000007FU
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_LPBK_CONTROL_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_LPBK_CONTROL_0_WIDTH                 7U
#define LPDDR4__PHY_ADR_LPBK_CONTROL_0__REG DENALI_PHY_1029
#define LPDDR4__PHY_ADR_LPBK_CONTROL_0__FLD LPDDR4__DENALI_PHY_1029__PHY_ADR_LPBK_CONTROL_0

#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_START_0_MASK   0x00007F00U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_START_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_START_0_WIDTH           7U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_0__REG DENALI_PHY_1029
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_0__FLD LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_START_0

#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_MASK_0_MASK    0x001F0000U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_MASK_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_MASK_0_WIDTH            5U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_0__REG DENALI_PHY_1029
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_0__FLD LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_MASK_0

#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_0_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_0_SHIFT             24U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_0_WIDTH              1U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_0_WOCLR              0U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_0_WOSET              0U
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_0__REG DENALI_PHY_1029
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_0__FLD LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_0

#define LPDDR4__DENALI_PHY_1030_READ_MASK                            0x01070301U
#define LPDDR4__DENALI_PHY_1030_WRITE_MASK                           0x01070301U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_SHIFT    0U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_WIDTH    1U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_WOCLR    0U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0_WOSET    0U
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0__REG DENALI_PHY_1030
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0__FLD LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_0

#define LPDDR4__DENALI_PHY_1030__PHY_ADR_TYPE_0_MASK                 0x00000300U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_TYPE_0_SHIFT                         8U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_TYPE_0_WIDTH                         2U
#define LPDDR4__PHY_ADR_TYPE_0__REG DENALI_PHY_1030
#define LPDDR4__PHY_ADR_TYPE_0__FLD LPDDR4__DENALI_PHY_1030__PHY_ADR_TYPE_0

#define LPDDR4__DENALI_PHY_1030__PHY_ADR_WRADDR_SHIFT_OBS_0_MASK     0x00070000U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_WRADDR_SHIFT_OBS_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_WRADDR_SHIFT_OBS_0_WIDTH             3U
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_0__REG DENALI_PHY_1030
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_0__FLD LPDDR4__DENALI_PHY_1030__PHY_ADR_WRADDR_SHIFT_OBS_0

#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_0_MASK              0x01000000U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_0_SHIFT                     24U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_0_WIDTH                      1U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_0_WOCLR                      0U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_0_WOSET                      0U
#define LPDDR4__PHY_ADR_IE_MODE_0__REG DENALI_PHY_1030
#define LPDDR4__PHY_ADR_IE_MODE_0__FLD LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_0

#define LPDDR4__DENALI_PHY_1031_READ_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1031_WRITE_MASK                           0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1031__PHY_ADR_DDL_MODE_0_MASK             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1031__PHY_ADR_DDL_MODE_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1031__PHY_ADR_DDL_MODE_0_WIDTH                    27U
#define LPDDR4__PHY_ADR_DDL_MODE_0__REG DENALI_PHY_1031
#define LPDDR4__PHY_ADR_DDL_MODE_0__FLD LPDDR4__DENALI_PHY_1031__PHY_ADR_DDL_MODE_0

#define LPDDR4__DENALI_PHY_1032_READ_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_1032_WRITE_MASK                           0x0000003FU
#define LPDDR4__DENALI_PHY_1032__PHY_ADR_DDL_MASK_0_MASK             0x0000003FU
#define LPDDR4__DENALI_PHY_1032__PHY_ADR_DDL_MASK_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1032__PHY_ADR_DDL_MASK_0_WIDTH                     6U
#define LPDDR4__PHY_ADR_DDL_MASK_0__REG DENALI_PHY_1032
#define LPDDR4__PHY_ADR_DDL_MASK_0__FLD LPDDR4__DENALI_PHY_1032__PHY_ADR_DDL_MASK_0

#define LPDDR4__DENALI_PHY_1033_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1033_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1033__PHY_ADR_DDL_TEST_OBS_0_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1033__PHY_ADR_DDL_TEST_OBS_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1033__PHY_ADR_DDL_TEST_OBS_0_WIDTH                32U
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_0__REG DENALI_PHY_1033
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_0__FLD LPDDR4__DENALI_PHY_1033__PHY_ADR_DDL_TEST_OBS_0

#define LPDDR4__DENALI_PHY_1034_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1034_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1034__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1034__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0_SHIFT        0U
#define LPDDR4__DENALI_PHY_1034__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0_WIDTH       32U
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0__REG DENALI_PHY_1034
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0__FLD LPDDR4__DENALI_PHY_1034__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_0

#define LPDDR4__DENALI_PHY_1035_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1035_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_START_0_MASK          0x000007FFU
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_START_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_START_0_WIDTH                 11U
#define LPDDR4__PHY_ADR_CALVL_START_0__REG DENALI_PHY_1035
#define LPDDR4__PHY_ADR_CALVL_START_0__FLD LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_START_0

#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_COARSE_DLY_0_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_COARSE_DLY_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_COARSE_DLY_0_WIDTH            11U
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_0__REG DENALI_PHY_1035
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_0__FLD LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_COARSE_DLY_0

#define LPDDR4__DENALI_PHY_1036_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1036_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1036__PHY_ADR_CALVL_QTR_0_MASK            0x000007FFU
#define LPDDR4__DENALI_PHY_1036__PHY_ADR_CALVL_QTR_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1036__PHY_ADR_CALVL_QTR_0_WIDTH                   11U
#define LPDDR4__PHY_ADR_CALVL_QTR_0__REG DENALI_PHY_1036
#define LPDDR4__PHY_ADR_CALVL_QTR_0__FLD LPDDR4__DENALI_PHY_1036__PHY_ADR_CALVL_QTR_0

#define LPDDR4__DENALI_PHY_1037_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1037_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1037__PHY_ADR_CALVL_SWIZZLE0_0_MASK       0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1037__PHY_ADR_CALVL_SWIZZLE0_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_1037__PHY_ADR_CALVL_SWIZZLE0_0_WIDTH              24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_0__REG DENALI_PHY_1037
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_0__FLD LPDDR4__DENALI_PHY_1037__PHY_ADR_CALVL_SWIZZLE0_0

#define LPDDR4__DENALI_PHY_1038_READ_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1038_WRITE_MASK                           0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_SWIZZLE1_0_MASK       0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_SWIZZLE1_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_SWIZZLE1_0_WIDTH              24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_0__REG DENALI_PHY_1038
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_0__FLD LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_SWIZZLE1_0

#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_RANK_CTRL_0_MASK      0x03000000U
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_RANK_CTRL_0_SHIFT             24U
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_RANK_CTRL_0_WIDTH              2U
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_0__REG DENALI_PHY_1038
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_0__FLD LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_RANK_CTRL_0

#define LPDDR4__DENALI_PHY_1039_READ_MASK                            0x01FF0F03U
#define LPDDR4__DENALI_PHY_1039_WRITE_MASK                           0x01FF0F03U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_NUM_PATTERNS_0_MASK   0x00000003U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_NUM_PATTERNS_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_NUM_PATTERNS_0_WIDTH           2U
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_0__REG DENALI_PHY_1039
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_0__FLD LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_NUM_PATTERNS_0

#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_RESP_WAIT_CNT_0_MASK  0x00000F00U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_RESP_WAIT_CNT_0_SHIFT          8U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_RESP_WAIT_CNT_0_WIDTH          4U
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_0__REG DENALI_PHY_1039
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_0__FLD LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_RESP_WAIT_CNT_0

#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0_MASK 0x01FF0000U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0_SHIFT 16U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0_WIDTH  9U
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0__REG DENALI_PHY_1039
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0__FLD LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_PERIODIC_START_OFFSET_0

#define LPDDR4__DENALI_PHY_1040_READ_MASK                            0x07000001U
#define LPDDR4__DENALI_PHY_1040_WRITE_MASK                           0x07000001U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_0_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_0_WIDTH             1U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_0_WOCLR             0U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_0_WOSET             0U
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_0__REG DENALI_PHY_1040
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_0__FLD LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_0

#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_0_MASK  0x00000100U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_0_SHIFT          8U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_0_WIDTH          1U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_0_WOCLR          0U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_0_WOSET          0U
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_0__REG DENALI_PHY_1040
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_0__FLD LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_0

#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_0_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_0_WIDTH           1U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_0_WOCLR           0U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_0_WOSET           0U
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_0__REG DENALI_PHY_1040
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_0__FLD LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_0

#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_OBS_SELECT_0_MASK     0x07000000U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_OBS_SELECT_0_SHIFT            24U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_OBS_SELECT_0_WIDTH             3U
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_0__REG DENALI_PHY_1040
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_0__FLD LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_OBS_SELECT_0

#define LPDDR4__DENALI_PHY_1041_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1041_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1041__PHY_ADR_CALVL_CH0_OBS0_0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1041__PHY_ADR_CALVL_CH0_OBS0_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_1041__PHY_ADR_CALVL_CH0_OBS0_0_WIDTH              32U
#define LPDDR4__PHY_ADR_CALVL_CH0_OBS0_0__REG DENALI_PHY_1041
#define LPDDR4__PHY_ADR_CALVL_CH0_OBS0_0__FLD LPDDR4__DENALI_PHY_1041__PHY_ADR_CALVL_CH0_OBS0_0

#define LPDDR4__DENALI_PHY_1042_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1042_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1042__PHY_ADR_CALVL_CH1_OBS0_0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1042__PHY_ADR_CALVL_CH1_OBS0_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_1042__PHY_ADR_CALVL_CH1_OBS0_0_WIDTH              32U
#define LPDDR4__PHY_ADR_CALVL_CH1_OBS0_0__REG DENALI_PHY_1042
#define LPDDR4__PHY_ADR_CALVL_CH1_OBS0_0__FLD LPDDR4__DENALI_PHY_1042__PHY_ADR_CALVL_CH1_OBS0_0

#define LPDDR4__DENALI_PHY_1043_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1043_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1043__PHY_ADR_CALVL_OBS1_0_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1043__PHY_ADR_CALVL_OBS1_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1043__PHY_ADR_CALVL_OBS1_0_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS1_0__REG DENALI_PHY_1043
#define LPDDR4__PHY_ADR_CALVL_OBS1_0__FLD LPDDR4__DENALI_PHY_1043__PHY_ADR_CALVL_OBS1_0

#define LPDDR4__DENALI_PHY_1044_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1044_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1044__PHY_ADR_CALVL_OBS2_0_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1044__PHY_ADR_CALVL_OBS2_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1044__PHY_ADR_CALVL_OBS2_0_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS2_0__REG DENALI_PHY_1044
#define LPDDR4__PHY_ADR_CALVL_OBS2_0__FLD LPDDR4__DENALI_PHY_1044__PHY_ADR_CALVL_OBS2_0

#define LPDDR4__DENALI_PHY_1045_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1045_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1045__PHY_ADR_CALVL_FG_0_0_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1045__PHY_ADR_CALVL_FG_0_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1045__PHY_ADR_CALVL_FG_0_0_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_0_0__REG DENALI_PHY_1045
#define LPDDR4__PHY_ADR_CALVL_FG_0_0__FLD LPDDR4__DENALI_PHY_1045__PHY_ADR_CALVL_FG_0_0

#define LPDDR4__DENALI_PHY_1046_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1046_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1046__PHY_ADR_CALVL_BG_0_0_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1046__PHY_ADR_CALVL_BG_0_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1046__PHY_ADR_CALVL_BG_0_0_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_0_0__REG DENALI_PHY_1046
#define LPDDR4__PHY_ADR_CALVL_BG_0_0__FLD LPDDR4__DENALI_PHY_1046__PHY_ADR_CALVL_BG_0_0

#define LPDDR4__DENALI_PHY_1047_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1047_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1047__PHY_ADR_CALVL_FG_1_0_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1047__PHY_ADR_CALVL_FG_1_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1047__PHY_ADR_CALVL_FG_1_0_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_1_0__REG DENALI_PHY_1047
#define LPDDR4__PHY_ADR_CALVL_FG_1_0__FLD LPDDR4__DENALI_PHY_1047__PHY_ADR_CALVL_FG_1_0

#define LPDDR4__DENALI_PHY_1048_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1048_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1048__PHY_ADR_CALVL_BG_1_0_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1048__PHY_ADR_CALVL_BG_1_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1048__PHY_ADR_CALVL_BG_1_0_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_1_0__REG DENALI_PHY_1048
#define LPDDR4__PHY_ADR_CALVL_BG_1_0__FLD LPDDR4__DENALI_PHY_1048__PHY_ADR_CALVL_BG_1_0

#define LPDDR4__DENALI_PHY_1049_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1049_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1049__PHY_ADR_CALVL_FG_2_0_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1049__PHY_ADR_CALVL_FG_2_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1049__PHY_ADR_CALVL_FG_2_0_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_2_0__REG DENALI_PHY_1049
#define LPDDR4__PHY_ADR_CALVL_FG_2_0__FLD LPDDR4__DENALI_PHY_1049__PHY_ADR_CALVL_FG_2_0

#define LPDDR4__DENALI_PHY_1050_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1050_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1050__PHY_ADR_CALVL_BG_2_0_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1050__PHY_ADR_CALVL_BG_2_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1050__PHY_ADR_CALVL_BG_2_0_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_2_0__REG DENALI_PHY_1050
#define LPDDR4__PHY_ADR_CALVL_BG_2_0__FLD LPDDR4__DENALI_PHY_1050__PHY_ADR_CALVL_BG_2_0

#define LPDDR4__DENALI_PHY_1051_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1051_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1051__PHY_ADR_CALVL_FG_3_0_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1051__PHY_ADR_CALVL_FG_3_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1051__PHY_ADR_CALVL_FG_3_0_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_3_0__REG DENALI_PHY_1051
#define LPDDR4__PHY_ADR_CALVL_FG_3_0__FLD LPDDR4__DENALI_PHY_1051__PHY_ADR_CALVL_FG_3_0

#define LPDDR4__DENALI_PHY_1052_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1052_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1052__PHY_ADR_CALVL_BG_3_0_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1052__PHY_ADR_CALVL_BG_3_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1052__PHY_ADR_CALVL_BG_3_0_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_3_0__REG DENALI_PHY_1052
#define LPDDR4__PHY_ADR_CALVL_BG_3_0__FLD LPDDR4__DENALI_PHY_1052__PHY_ADR_CALVL_BG_3_0

#define LPDDR4__DENALI_PHY_1053_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1053_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_ADDR_SEL_0_MASK             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_ADDR_SEL_0_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_ADDR_SEL_0_WIDTH                    30U
#define LPDDR4__PHY_ADR_ADDR_SEL_0__REG DENALI_PHY_1053
#define LPDDR4__PHY_ADR_ADDR_SEL_0__FLD LPDDR4__DENALI_PHY_1053__PHY_ADR_ADDR_SEL_0

#define LPDDR4__DENALI_PHY_1054_READ_MASK                            0x3F3F03FFU
#define LPDDR4__DENALI_PHY_1054_WRITE_MASK                           0x3F3F03FFU
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_LP4_BOOT_SLV_DELAY_0_MASK   0x000003FFU
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_LP4_BOOT_SLV_DELAY_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_LP4_BOOT_SLV_DELAY_0_WIDTH          10U
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_0__REG DENALI_PHY_1054
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_0__FLD LPDDR4__DENALI_PHY_1054__PHY_ADR_LP4_BOOT_SLV_DELAY_0

#define LPDDR4__DENALI_PHY_1054__PHY_ADR_BIT_MASK_0_MASK             0x003F0000U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_BIT_MASK_0_SHIFT                    16U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_BIT_MASK_0_WIDTH                     6U
#define LPDDR4__PHY_ADR_BIT_MASK_0__REG DENALI_PHY_1054
#define LPDDR4__PHY_ADR_BIT_MASK_0__FLD LPDDR4__DENALI_PHY_1054__PHY_ADR_BIT_MASK_0

#define LPDDR4__DENALI_PHY_1054__PHY_ADR_SEG_MASK_0_MASK             0x3F000000U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_SEG_MASK_0_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_SEG_MASK_0_WIDTH                     6U
#define LPDDR4__PHY_ADR_SEG_MASK_0__REG DENALI_PHY_1054
#define LPDDR4__PHY_ADR_SEG_MASK_0__FLD LPDDR4__DENALI_PHY_1054__PHY_ADR_SEG_MASK_0

#define LPDDR4__DENALI_PHY_1055_READ_MASK                            0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_1055_WRITE_MASK                           0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_CALVL_TRAIN_MASK_0_MASK     0x0000003FU
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_CALVL_TRAIN_MASK_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_CALVL_TRAIN_MASK_0_WIDTH             6U
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_0__REG DENALI_PHY_1055
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_0__FLD LPDDR4__DENALI_PHY_1055__PHY_ADR_CALVL_TRAIN_MASK_0

#define LPDDR4__DENALI_PHY_1055__PHY_ADR_CSLVL_TRAIN_MASK_0_MASK     0x00003F00U
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_CSLVL_TRAIN_MASK_0_SHIFT             8U
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_CSLVL_TRAIN_MASK_0_WIDTH             6U
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_0__REG DENALI_PHY_1055
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_0__FLD LPDDR4__DENALI_PHY_1055__PHY_ADR_CSLVL_TRAIN_MASK_0

#define LPDDR4__DENALI_PHY_1055__PHY_ADR_STATIC_TOG_DISABLE_0_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_STATIC_TOG_DISABLE_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_STATIC_TOG_DISABLE_0_WIDTH           4U
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_0__REG DENALI_PHY_1055
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_0__FLD LPDDR4__DENALI_PHY_1055__PHY_ADR_STATIC_TOG_DISABLE_0

#define LPDDR4__DENALI_PHY_1055__PHY_ADR_SW_TXIO_CTRL_0_MASK         0x3F000000U
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_SW_TXIO_CTRL_0_SHIFT                24U
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_SW_TXIO_CTRL_0_WIDTH                 6U
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_0__REG DENALI_PHY_1055
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_0__FLD LPDDR4__DENALI_PHY_1055__PHY_ADR_SW_TXIO_CTRL_0

#define LPDDR4__DENALI_PHY_1056_READ_MASK                            0xFFFFFF03U
#define LPDDR4__DENALI_PHY_1056_WRITE_MASK                           0xFFFFFF03U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_INIT_DISABLE_0_MASK      0x00000003U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_INIT_DISABLE_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_INIT_DISABLE_0_WIDTH              2U
#define LPDDR4__PHY_ADR_DC_INIT_DISABLE_0__REG DENALI_PHY_1056
#define LPDDR4__PHY_ADR_DC_INIT_DISABLE_0__FLD LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_INIT_DISABLE_0

#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR0_CLK_ADJUST_0_MASK   0x0000FF00U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR0_CLK_ADJUST_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR0_CLK_ADJUST_0_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR0_CLK_ADJUST_0__REG DENALI_PHY_1056
#define LPDDR4__PHY_ADR_DC_ADR0_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR0_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR1_CLK_ADJUST_0_MASK   0x00FF0000U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR1_CLK_ADJUST_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR1_CLK_ADJUST_0_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR1_CLK_ADJUST_0__REG DENALI_PHY_1056
#define LPDDR4__PHY_ADR_DC_ADR1_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR1_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR2_CLK_ADJUST_0_MASK   0xFF000000U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR2_CLK_ADJUST_0_SHIFT          24U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR2_CLK_ADJUST_0_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR2_CLK_ADJUST_0__REG DENALI_PHY_1056
#define LPDDR4__PHY_ADR_DC_ADR2_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_1056__PHY_ADR_DC_ADR2_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_1057_READ_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1057_WRITE_MASK                           0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR3_CLK_ADJUST_0_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR3_CLK_ADJUST_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR3_CLK_ADJUST_0_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR3_CLK_ADJUST_0__REG DENALI_PHY_1057
#define LPDDR4__PHY_ADR_DC_ADR3_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR3_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR4_CLK_ADJUST_0_MASK   0x0000FF00U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR4_CLK_ADJUST_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR4_CLK_ADJUST_0_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR4_CLK_ADJUST_0__REG DENALI_PHY_1057
#define LPDDR4__PHY_ADR_DC_ADR4_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR4_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR5_CLK_ADJUST_0_MASK   0x00FF0000U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR5_CLK_ADJUST_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR5_CLK_ADJUST_0_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_ADR5_CLK_ADJUST_0__REG DENALI_PHY_1057
#define LPDDR4__PHY_ADR_DC_ADR5_CLK_ADJUST_0__FLD LPDDR4__DENALI_PHY_1057__PHY_ADR_DC_ADR5_CLK_ADJUST_0

#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_0_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_0_SHIFT 24U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_0_WIDTH  1U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_0_WOCLR  0U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_0_WOSET  0U
#define LPDDR4__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_0__REG DENALI_PHY_1057
#define LPDDR4__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_0__FLD LPDDR4__DENALI_PHY_1057__PHY_ADR_DCC_RXCAL_CTRL_GATE_DISABLE_0

#define LPDDR4__DENALI_PHY_1058_READ_MASK                            0x3F03FFFFU
#define LPDDR4__DENALI_PHY_1058_WRITE_MASK                           0x3F03FFFFU
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_CAL_SAMPLE_WAIT_0_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_CAL_SAMPLE_WAIT_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_CAL_SAMPLE_WAIT_0_WIDTH           8U
#define LPDDR4__PHY_ADR_DC_CAL_SAMPLE_WAIT_0__REG DENALI_PHY_1058
#define LPDDR4__PHY_ADR_DC_CAL_SAMPLE_WAIT_0__FLD LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_CAL_SAMPLE_WAIT_0

#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_CAL_TIMEOUT_0_MASK       0x0000FF00U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_CAL_TIMEOUT_0_SHIFT               8U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_CAL_TIMEOUT_0_WIDTH               8U
#define LPDDR4__PHY_ADR_DC_CAL_TIMEOUT_0__REG DENALI_PHY_1058
#define LPDDR4__PHY_ADR_DC_CAL_TIMEOUT_0__FLD LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_CAL_TIMEOUT_0

#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_WEIGHT_0_MASK            0x00030000U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_WEIGHT_0_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_WEIGHT_0_WIDTH                    2U
#define LPDDR4__PHY_ADR_DC_WEIGHT_0__REG DENALI_PHY_1058
#define LPDDR4__PHY_ADR_DC_WEIGHT_0__FLD LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_WEIGHT_0

#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_ADJUST_START_0_MASK      0x3F000000U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_ADJUST_START_0_SHIFT             24U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_ADJUST_START_0_WIDTH              6U
#define LPDDR4__PHY_ADR_DC_ADJUST_START_0__REG DENALI_PHY_1058
#define LPDDR4__PHY_ADR_DC_ADJUST_START_0__FLD LPDDR4__DENALI_PHY_1058__PHY_ADR_DC_ADJUST_START_0

#define LPDDR4__DENALI_PHY_1059_READ_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_PHY_1059_WRITE_MASK                           0x0101FFFFU
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_SAMPLE_CNT_0_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_SAMPLE_CNT_0_SHIFT         0U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_SAMPLE_CNT_0_WIDTH         8U
#define LPDDR4__PHY_ADR_DC_ADJUST_SAMPLE_CNT_0__REG DENALI_PHY_1059
#define LPDDR4__PHY_ADR_DC_ADJUST_SAMPLE_CNT_0__FLD LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_SAMPLE_CNT_0

#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_THRSHLD_0_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_THRSHLD_0_SHIFT            8U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_THRSHLD_0_WIDTH            8U
#define LPDDR4__PHY_ADR_DC_ADJUST_THRSHLD_0__REG DENALI_PHY_1059
#define LPDDR4__PHY_ADR_DC_ADJUST_THRSHLD_0__FLD LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_THRSHLD_0

#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_DIRECT_0_MASK     0x00010000U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_DIRECT_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_DIRECT_0_WIDTH             1U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_DIRECT_0_WOCLR             0U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_DIRECT_0_WOSET             0U
#define LPDDR4__PHY_ADR_DC_ADJUST_DIRECT_0__REG DENALI_PHY_1059
#define LPDDR4__PHY_ADR_DC_ADJUST_DIRECT_0__FLD LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_ADJUST_DIRECT_0

#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_CAL_POLARITY_0_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_CAL_POLARITY_0_SHIFT             24U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_CAL_POLARITY_0_WIDTH              1U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_CAL_POLARITY_0_WOCLR              0U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_CAL_POLARITY_0_WOSET              0U
#define LPDDR4__PHY_ADR_DC_CAL_POLARITY_0__REG DENALI_PHY_1059
#define LPDDR4__PHY_ADR_DC_CAL_POLARITY_0__FLD LPDDR4__DENALI_PHY_1059__PHY_ADR_DC_CAL_POLARITY_0

#define LPDDR4__DENALI_PHY_1060_READ_MASK                            0x00003F01U
#define LPDDR4__DENALI_PHY_1060_WRITE_MASK                           0x00003F01U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR_DC_CAL_START_0_MASK         0x00000001U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR_DC_CAL_START_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR_DC_CAL_START_0_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR_DC_CAL_START_0_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR_DC_CAL_START_0_WOSET                 0U
#define LPDDR4__PHY_ADR_DC_CAL_START_0__REG DENALI_PHY_1060
#define LPDDR4__PHY_ADR_DC_CAL_START_0__FLD LPDDR4__DENALI_PHY_1060__PHY_ADR_DC_CAL_START_0

#define LPDDR4__DENALI_PHY_1060__PHY_ADR_SW_TXPWR_CTRL_0_MASK        0x00003F00U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR_SW_TXPWR_CTRL_0_SHIFT                8U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR_SW_TXPWR_CTRL_0_WIDTH                6U
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_0__REG DENALI_PHY_1060
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_0__FLD LPDDR4__DENALI_PHY_1060__PHY_ADR_SW_TXPWR_CTRL_0

#define LPDDR4__DENALI_PHY_1061_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1061_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1061__PHY_ADR_TSEL_SELECT_0_MASK          0x000000FFU
#define LPDDR4__DENALI_PHY_1061__PHY_ADR_TSEL_SELECT_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1061__PHY_ADR_TSEL_SELECT_0_WIDTH                  8U
#define LPDDR4__PHY_ADR_TSEL_SELECT_0__REG DENALI_PHY_1061
#define LPDDR4__PHY_ADR_TSEL_SELECT_0__FLD LPDDR4__DENALI_PHY_1061__PHY_ADR_TSEL_SELECT_0

#define LPDDR4__DENALI_PHY_1061__PHY_ADR_DC_CAL_CLK_SEL_0_MASK       0x00000700U
#define LPDDR4__DENALI_PHY_1061__PHY_ADR_DC_CAL_CLK_SEL_0_SHIFT               8U
#define LPDDR4__DENALI_PHY_1061__PHY_ADR_DC_CAL_CLK_SEL_0_WIDTH               3U
#define LPDDR4__PHY_ADR_DC_CAL_CLK_SEL_0__REG DENALI_PHY_1061
#define LPDDR4__PHY_ADR_DC_CAL_CLK_SEL_0__FLD LPDDR4__DENALI_PHY_1061__PHY_ADR_DC_CAL_CLK_SEL_0

#define LPDDR4__DENALI_PHY_1061__PHY_PAD_ADR_IO_CFG_0_MASK           0x07FF0000U
#define LPDDR4__DENALI_PHY_1061__PHY_PAD_ADR_IO_CFG_0_SHIFT                  16U
#define LPDDR4__DENALI_PHY_1061__PHY_PAD_ADR_IO_CFG_0_WIDTH                  11U
#define LPDDR4__PHY_PAD_ADR_IO_CFG_0__REG DENALI_PHY_1061
#define LPDDR4__PHY_PAD_ADR_IO_CFG_0__FLD LPDDR4__DENALI_PHY_1061__PHY_PAD_ADR_IO_CFG_0

#define LPDDR4__DENALI_PHY_1062_READ_MASK                            0x07FF1F07U
#define LPDDR4__DENALI_PHY_1062_WRITE_MASK                           0x07FF1F07U
#define LPDDR4__DENALI_PHY_1062__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0_MASK  0x00000007U
#define LPDDR4__DENALI_PHY_1062__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_1062__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0_WIDTH          3U
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0__REG DENALI_PHY_1062
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0__FLD LPDDR4__DENALI_PHY_1062__PHY_PAD_ADR_RX_PCLK_CLK_SEL_0

#define LPDDR4__DENALI_PHY_1062__PHY_ADR0_SW_WRADDR_SHIFT_0_MASK     0x00001F00U
#define LPDDR4__DENALI_PHY_1062__PHY_ADR0_SW_WRADDR_SHIFT_0_SHIFT             8U
#define LPDDR4__DENALI_PHY_1062__PHY_ADR0_SW_WRADDR_SHIFT_0_WIDTH             5U
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_0__REG DENALI_PHY_1062
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_1062__PHY_ADR0_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_1062__PHY_ADR0_CLK_WR_SLAVE_DELAY_0_MASK  0x07FF0000U
#define LPDDR4__DENALI_PHY_1062__PHY_ADR0_CLK_WR_SLAVE_DELAY_0_SHIFT         16U
#define LPDDR4__DENALI_PHY_1062__PHY_ADR0_CLK_WR_SLAVE_DELAY_0_WIDTH         11U
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_1062
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1062__PHY_ADR0_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1063_READ_MASK                            0x1F07FF1FU
#define LPDDR4__DENALI_PHY_1063_WRITE_MASK                           0x1F07FF1FU
#define LPDDR4__DENALI_PHY_1063__PHY_ADR1_SW_WRADDR_SHIFT_0_MASK     0x0000001FU
#define LPDDR4__DENALI_PHY_1063__PHY_ADR1_SW_WRADDR_SHIFT_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR1_SW_WRADDR_SHIFT_0_WIDTH             5U
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_0__REG DENALI_PHY_1063
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_1063__PHY_ADR1_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_1063__PHY_ADR1_CLK_WR_SLAVE_DELAY_0_MASK  0x0007FF00U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR1_CLK_WR_SLAVE_DELAY_0_SHIFT          8U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR1_CLK_WR_SLAVE_DELAY_0_WIDTH         11U
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_1063
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1063__PHY_ADR1_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1063__PHY_ADR2_SW_WRADDR_SHIFT_0_MASK     0x1F000000U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR2_SW_WRADDR_SHIFT_0_SHIFT            24U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR2_SW_WRADDR_SHIFT_0_WIDTH             5U
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_0__REG DENALI_PHY_1063
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_1063__PHY_ADR2_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_1064_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1064_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1064__PHY_ADR2_CLK_WR_SLAVE_DELAY_0_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1064__PHY_ADR2_CLK_WR_SLAVE_DELAY_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR2_CLK_WR_SLAVE_DELAY_0_WIDTH         11U
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_1064
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1064__PHY_ADR2_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1064__PHY_ADR3_SW_WRADDR_SHIFT_0_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR3_SW_WRADDR_SHIFT_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR3_SW_WRADDR_SHIFT_0_WIDTH             5U
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_0__REG DENALI_PHY_1064
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_1064__PHY_ADR3_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_1065_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1065_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1065__PHY_ADR3_CLK_WR_SLAVE_DELAY_0_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1065__PHY_ADR3_CLK_WR_SLAVE_DELAY_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_1065__PHY_ADR3_CLK_WR_SLAVE_DELAY_0_WIDTH         11U
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_1065
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1065__PHY_ADR3_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1065__PHY_ADR4_SW_WRADDR_SHIFT_0_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1065__PHY_ADR4_SW_WRADDR_SHIFT_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_1065__PHY_ADR4_SW_WRADDR_SHIFT_0_WIDTH             5U
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_0__REG DENALI_PHY_1065
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_1065__PHY_ADR4_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_1066_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1066_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1066__PHY_ADR4_CLK_WR_SLAVE_DELAY_0_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1066__PHY_ADR4_CLK_WR_SLAVE_DELAY_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_1066__PHY_ADR4_CLK_WR_SLAVE_DELAY_0_WIDTH         11U
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_1066
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1066__PHY_ADR4_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1066__PHY_ADR5_SW_WRADDR_SHIFT_0_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1066__PHY_ADR5_SW_WRADDR_SHIFT_0_SHIFT            16U
#define LPDDR4__DENALI_PHY_1066__PHY_ADR5_SW_WRADDR_SHIFT_0_WIDTH             5U
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_0__REG DENALI_PHY_1066
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_0__FLD LPDDR4__DENALI_PHY_1066__PHY_ADR5_SW_WRADDR_SHIFT_0

#define LPDDR4__DENALI_PHY_1067_READ_MASK                            0x000F07FFU
#define LPDDR4__DENALI_PHY_1067_WRITE_MASK                           0x000F07FFU
#define LPDDR4__DENALI_PHY_1067__PHY_ADR5_CLK_WR_SLAVE_DELAY_0_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1067__PHY_ADR5_CLK_WR_SLAVE_DELAY_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_1067__PHY_ADR5_CLK_WR_SLAVE_DELAY_0_WIDTH         11U
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_0__REG DENALI_PHY_1067
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1067__PHY_ADR5_CLK_WR_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1067__PHY_ADR_SW_MASTER_MODE_0_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_1067__PHY_ADR_SW_MASTER_MODE_0_SHIFT              16U
#define LPDDR4__DENALI_PHY_1067__PHY_ADR_SW_MASTER_MODE_0_WIDTH               4U
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_0__REG DENALI_PHY_1067
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_0__FLD LPDDR4__DENALI_PHY_1067__PHY_ADR_SW_MASTER_MODE_0

#define LPDDR4__DENALI_PHY_1068_READ_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_1068_WRITE_MASK                           0xFF3F07FFU
#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_START_0_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_START_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_START_0_WIDTH          11U
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_0__REG DENALI_PHY_1068
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_0__FLD LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_START_0

#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_STEP_0_MASK    0x003F0000U
#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_STEP_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_STEP_0_WIDTH            6U
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_0__REG DENALI_PHY_1068
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_0__FLD LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_STEP_0

#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_WAIT_0_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_WAIT_0_SHIFT           24U
#define LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_WAIT_0_WIDTH            8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_0__REG DENALI_PHY_1068
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_0__FLD LPDDR4__DENALI_PHY_1068__PHY_ADR_MASTER_DELAY_WAIT_0

#define LPDDR4__DENALI_PHY_1069_READ_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_1069_WRITE_MASK                           0x0103FFFFU
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0_SHIFT    0U
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0_WIDTH    8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0__REG DENALI_PHY_1069
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0__FLD LPDDR4__DENALI_PHY_1069__PHY_ADR_MASTER_DELAY_HALF_MEASURE_0

#define LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_0_MASK     0x0003FF00U
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_0_SHIFT             8U
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_0_WIDTH            10U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_0__REG DENALI_PHY_1069
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_0__FLD LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_0

#define LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_MASK  0x01000000U
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_SHIFT         24U
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_WIDTH          1U
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_WOCLR          0U
#define LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_EN_0_WOSET          0U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_0__REG DENALI_PHY_1069
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_0__FLD LPDDR4__DENALI_PHY_1069__PHY_ADR_SW_CALVL_DVW_MIN_EN_0

#define LPDDR4__DENALI_PHY_1070_READ_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_1070_WRITE_MASK                           0x0000000FU
#define LPDDR4__DENALI_PHY_1070__PHY_ADR_CALVL_DLY_STEP_0_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_1070__PHY_ADR_CALVL_DLY_STEP_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_1070__PHY_ADR_CALVL_DLY_STEP_0_WIDTH               4U
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_0__REG DENALI_PHY_1070
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_0__FLD LPDDR4__DENALI_PHY_1070__PHY_ADR_CALVL_DLY_STEP_0

#define LPDDR4__DENALI_PHY_1071_READ_MASK                            0x03FF010FU
#define LPDDR4__DENALI_PHY_1071_WRITE_MASK                           0x03FF010FU
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_CALVL_CAPTURE_CNT_0_MASK    0x0000000FU
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_CALVL_CAPTURE_CNT_0_SHIFT            0U
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_CALVL_CAPTURE_CNT_0_WIDTH            4U
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_0__REG DENALI_PHY_1071
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_0__FLD LPDDR4__DENALI_PHY_1071__PHY_ADR_CALVL_CAPTURE_CNT_0

#define LPDDR4__DENALI_PHY_1071__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_MASK 0x00000100U
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_SHIFT         8U
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_WIDTH         1U
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_WOCLR         0U
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_MEAS_DLY_STEP_ENABLE_0_WOSET         0U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_0__REG DENALI_PHY_1071
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_0__FLD LPDDR4__DENALI_PHY_1071__PHY_ADR_MEAS_DLY_STEP_ENABLE_0

#define LPDDR4__DENALI_PHY_1071__PHY_ADR_DC_INIT_SLV_DELAY_0_MASK    0x03FF0000U
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_DC_INIT_SLV_DELAY_0_SHIFT           16U
#define LPDDR4__DENALI_PHY_1071__PHY_ADR_DC_INIT_SLV_DELAY_0_WIDTH           10U
#define LPDDR4__PHY_ADR_DC_INIT_SLV_DELAY_0__REG DENALI_PHY_1071
#define LPDDR4__PHY_ADR_DC_INIT_SLV_DELAY_0__FLD LPDDR4__DENALI_PHY_1071__PHY_ADR_DC_INIT_SLV_DELAY_0

#define LPDDR4__DENALI_PHY_1072_READ_MASK                            0x0000FF01U
#define LPDDR4__DENALI_PHY_1072_WRITE_MASK                           0x0000FF01U
#define LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_CALVL_ENABLE_0_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_CALVL_ENABLE_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_CALVL_ENABLE_0_WIDTH              1U
#define LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_CALVL_ENABLE_0_WOCLR              0U
#define LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_CALVL_ENABLE_0_WOSET              0U
#define LPDDR4__PHY_ADR_DC_CALVL_ENABLE_0__REG DENALI_PHY_1072
#define LPDDR4__PHY_ADR_DC_CALVL_ENABLE_0__FLD LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_CALVL_ENABLE_0

#define LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_DM_CLK_THRSHLD_0_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_DM_CLK_THRSHLD_0_SHIFT            8U
#define LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_DM_CLK_THRSHLD_0_WIDTH            8U
#define LPDDR4__PHY_ADR_DC_DM_CLK_THRSHLD_0__REG DENALI_PHY_1072
#define LPDDR4__PHY_ADR_DC_DM_CLK_THRSHLD_0__FLD LPDDR4__DENALI_PHY_1072__PHY_ADR_DC_DM_CLK_THRSHLD_0

#endif /* REG_LPDDR4_ADDRESS_SLICE_0_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

