/*
 * Copyright (C) 2021 Texas Instruments Incorporated
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 *   Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 *   Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in the
 *   documentation and/or other materials provided with the
 *   distribution.
 *
 *   Neither the name of Texas Instruments Incorporated nor the names of
 *   its contributors may be used to endorse or promote products derived
 *   from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/**
 *  \defgroup DRV_GTC_MODULE APIs for GTC
 *  \ingroup DRV_MODULE
 *
 *  This module contains APIs to program and use GTC.
 *
 *  @{
 */

/**
 *  \file v0/gtc.h
 *
 *  \brief GTC Driver API/interface file.
 */

#ifndef GTC_V0_H_
#define GTC_V0_H_

#ifdef __cplusplus
extern "C" {
#endif

/**
 * \brief This API sets the FID for GTC
 *
 * \return SystemP_SUCCESS on success, else failure
 */
int32_t GTC_setFID(void);

/**
 * \brief This API enables the GTC timer
 *
 */
void GTC_enable(void);

/**
 * \brief Initialize GTC after setting FID
 *
 * \return SystemP_SUCCESS on success, else failure
 */
int32_t GTC_init(void);

/**
 * \brief Get the GTC count value
 *
 * \return Returns GTC count
 */
uint64_t GTC_getCount64(void);

#ifdef __cplusplus
}
#endif

#endif  /* #ifndef GTC_V0_H_ */

/** @} */
