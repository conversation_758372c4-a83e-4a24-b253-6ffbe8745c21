

/*
 *  Copyright (C) 2022 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */


#ifndef SCICLIENT_DEFAULTBOARDCFG_HEX_H_
#define SCICLIENT_DEFAULTBOARDCFG_HEX_H_

#ifdef __cplusplus
extern "C"
{
#endif

#define SCICLIENT_BOARDCFG_SIZE_IN_BYTES (29U)

#define SCICLIENT_BOARDCFG { \
    0x00U, 0x01U, 0xd3U, 0xc1U, 0x07U, 0x00U, 0x5aU, 0x02U, 0x00U, 0x07U, 0x12U, 0x07U, 0x00U, 0x01U, 0x01U, 0x00U,  \
    0xc3U, 0xa5U, 0x05U, 0x00U, 0x00U, 0x0cU, 0x02U, 0x08U, 0x00U, 0x00U, 0x00U, 0x00U, 0x00U,  \
} /* 29 bytes */

#ifdef __cplusplus
}
#endif

#endif /* SCICLIENT_DEFAULTBOARDCFG_HEX_H_ */
