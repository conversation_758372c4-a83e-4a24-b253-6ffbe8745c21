DEFCONFIG=$(srctree)/configs/$(DEFAULT_TARGET).config
CONFIG=$(objtree)/.config

# If we are only doing a make clean, don't require a .config
all: $(if $(just_clean:1=),$(CONFIG))
	@(exit 0)

FORCE:

$(CONFIG):
	$(if $(srctree:$(objtree)=),mkdir -p $(dir $@))
	cmp -s $(CONFIG) $(DEFCONFIG) || cat $(DEFCONFIG) > $@

defconfig: $(DEFCONFIG) FORCE
	$(if $(srctree:$(objtree)=),mkdir -p $(dir $@))
	cmp -s $< $(CONFIG) || cat $< > $(CONFIG)

%_defconfig: $(srctree)/configs/%.config FORCE
	$(if $(srctree:$(objtree)=),mkdir -p $(dir $@))
	cmp -s $< $(CONFIG) || cat $< > $(CONFIG)
