# AM62x Reset Detection - Makefile
# 用于编译内核驱动和用户空间程序

# 内核模块名称
MODULE_NAME = am62x_reset_detection
obj-m += $(MODULE_NAME).o

# 用户空间程序名称
USERSPACE_PROG = am62x_reset_userspace

# 内核源码路径（默认为当前运行内核）
KERNEL_DIR ?= /lib/modules/$(shell uname -r)/build

# 交叉编译工具链（如果需要）
CROSS_COMPILE ?=
CC = $(CROSS_COMPILE)gcc

# 编译选项
CFLAGS = -Wall -Wextra -O2 -std=c99

# 默认目标
all: kernel_module userspace

# 编译内核模块
kernel_module:
	@echo "编译AM62x复位检测内核驱动..."
	make -C $(KERNEL_DIR) M=$(PWD) modules

# 编译用户空间程序
userspace: $(USERSPACE_PROG)

$(USERSPACE_PROG): $(USERSPACE_PROG).c
	@echo "编译用户空间程序..."
	$(CC) $(CFLAGS) -o $@ $<

# 清理编译产物
clean:
	@echo "清理编译产物..."
	make -C $(KERNEL_DIR) M=$(PWD) clean
	rm -f $(USERSPACE_PROG)
	rm -f *.o *.ko *.mod.c *.mod *.order *.symvers

# 安装内核模块
install_module: kernel_module
	@echo "安装内核模块..."
	sudo make -C $(KERNEL_DIR) M=$(PWD) modules_install
	sudo depmod -a
	@echo "内核模块安装完成"

# 加载内核模块
load_module: install_module
	@echo "加载内核模块..."
	sudo modprobe $(MODULE_NAME)
	@echo "内核模块加载完成"

# 卸载内核模块
unload_module:
	@echo "卸载内核模块..."
	-sudo rmmod $(MODULE_NAME)
	@echo "内核模块卸载完成"

# 安装用户空间程序
install_userspace: userspace
	@echo "安装用户空间程序..."
	sudo cp $(USERSPACE_PROG) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(USERSPACE_PROG)
	@echo "用户空间程序安装完成"

# 完整安装
install: install_module install_userspace
	@echo "AM62x复位检测工具安装完成"

# 测试功能
test: load_module userspace
	@echo "测试AM62x复位检测功能..."
	@echo "1. 通过proc文件系统读取："
	-cat /proc/am62x_reset_info
	@echo ""
	@echo "2. 通过用户空间程序读取："
	-./$(USERSPACE_PROG)
	@echo ""
	@echo "3. 显示寄存器位映射："
	-./$(USERSPACE_PROG) --map

# 创建设备树覆盖文件
dtb: am62x_reset_detection.dts
	@echo "编译设备树覆盖文件..."
	dtc -@ -I dts -O dtb -o am62x_reset_detection.dtbo am62x_reset_detection.dts
	@echo "设备树覆盖文件编译完成"

# 安装设备树覆盖文件
install_dtb: dtb
	@echo "安装设备树覆盖文件..."
	sudo cp am62x_reset_detection.dtbo /boot/overlays/
	@echo "设备树覆盖文件安装完成"
	@echo "请在/boot/config.txt中添加: dtoverlay=am62x_reset_detection"

# 生成文档
doc:
	@echo "生成文档..."
	@echo "技术文档已生成: AM62x_Reset_Detection_Guide_CN.md"
	@echo "设备树示例: am62x_reset_detection.dts"
	@echo "用户空间示例: am62x_reset_userspace.c"
	@echo "内核驱动: am62x_reset_detection_driver.c"

# 打包发布
package: clean
	@echo "创建发布包..."
	mkdir -p am62x_reset_detection_package
	cp *.c *.h *.dts *.md Makefile am62x_reset_detection_package/
	tar -czf am62x_reset_detection.tar.gz am62x_reset_detection_package/
	rm -rf am62x_reset_detection_package/
	@echo "发布包创建完成: am62x_reset_detection.tar.gz"

# 显示帮助信息
help:
	@echo "AM62x Reset Detection Makefile"
	@echo "==============================="
	@echo ""
	@echo "可用目标:"
	@echo "  all              - 编译内核模块和用户空间程序"
	@echo "  kernel_module    - 仅编译内核模块"
	@echo "  userspace        - 仅编译用户空间程序"
	@echo "  clean            - 清理编译产物"
	@echo ""
	@echo "安装目标:"
	@echo "  install_module   - 安装内核模块"
	@echo "  load_module      - 加载内核模块"
	@echo "  unload_module    - 卸载内核模块"
	@echo "  install_userspace- 安装用户空间程序"
	@echo "  install          - 完整安装"
	@echo ""
	@echo "设备树目标:"
	@echo "  dtb              - 编译设备树覆盖文件"
	@echo "  install_dtb      - 安装设备树覆盖文件"
	@echo ""
	@echo "其他目标:"
	@echo "  test             - 测试功能"
	@echo "  doc              - 生成文档"
	@echo "  package          - 创建发布包"
	@echo "  help             - 显示此帮助信息"
	@echo ""
	@echo "环境变量:"
	@echo "  KERNEL_DIR       - 内核源码路径 (默认: /lib/modules/$(shell uname -r)/build)"
	@echo "  CROSS_COMPILE    - 交叉编译工具链前缀"
	@echo ""
	@echo "使用示例:"
	@echo "  make all                    # 编译所有组件"
	@echo "  make install               # 安装所有组件"
	@echo "  make test                  # 测试功能"
	@echo "  make KERNEL_DIR=/path/to/kernel kernel_module  # 指定内核路径编译"

# 声明伪目标
.PHONY: all kernel_module userspace clean install_module load_module unload_module \
        install_userspace install test dtb install_dtb doc package help
