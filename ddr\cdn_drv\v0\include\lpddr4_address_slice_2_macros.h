/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_ADDRESS_SLICE_2_MACROS_H_
#define REG_LPDDR4_ADDRESS_SLICE_2_MACROS_H_

#define LPDDR4__DENALI_PHY_1024_READ_MASK                            0x000107FFU
#define LPDDR4__DENALI_PHY_1024_WRITE_MASK                           0x000107FFU
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2_SHIFT    0U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2_WIDTH   11U
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2__REG DENALI_PHY_1024
#define LPDDR4__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_WR_BYPASS_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_2_MASK  0x00010000U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_2_SHIFT         16U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_2_WIDTH          1U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_2_WOCLR          0U
#define LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_2_WOSET          0U
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_2__REG DENALI_PHY_1024
#define LPDDR4__PHY_ADR_CLK_BYPASS_OVERRIDE_2__FLD LPDDR4__DENALI_PHY_1024__PHY_ADR_CLK_BYPASS_OVERRIDE_2

#define LPDDR4__DENALI_PHY_1024__SC_PHY_ADR_MANUAL_CLEAR_2_MASK      0x07000000U
#define LPDDR4__DENALI_PHY_1024__SC_PHY_ADR_MANUAL_CLEAR_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_1024__SC_PHY_ADR_MANUAL_CLEAR_2_WIDTH              3U
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_2__REG DENALI_PHY_1024
#define LPDDR4__SC_PHY_ADR_MANUAL_CLEAR_2__FLD LPDDR4__DENALI_PHY_1024__SC_PHY_ADR_MANUAL_CLEAR_2

#define LPDDR4__DENALI_PHY_1025_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1025_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1025__PHY_ADR_LPBK_RESULT_OBS_2_MASK      0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1025__PHY_ADR_LPBK_RESULT_OBS_2_SHIFT              0U
#define LPDDR4__DENALI_PHY_1025__PHY_ADR_LPBK_RESULT_OBS_2_WIDTH             32U
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_2__REG DENALI_PHY_1025
#define LPDDR4__PHY_ADR_LPBK_RESULT_OBS_2__FLD LPDDR4__DENALI_PHY_1025__PHY_ADR_LPBK_RESULT_OBS_2

#define LPDDR4__DENALI_PHY_1026_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1026_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_LPBK_ERROR_COUNT_OBS_2_MASK 0x0000FFFFU
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_LPBK_ERROR_COUNT_OBS_2_SHIFT         0U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_LPBK_ERROR_COUNT_OBS_2_WIDTH        16U
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_2__REG DENALI_PHY_1026
#define LPDDR4__PHY_ADR_LPBK_ERROR_COUNT_OBS_2__FLD LPDDR4__DENALI_PHY_1026__PHY_ADR_LPBK_ERROR_COUNT_OBS_2

#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MEAS_DLY_STEP_VALUE_2_MASK  0x00FF0000U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MEAS_DLY_STEP_VALUE_2_SHIFT         16U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MEAS_DLY_STEP_VALUE_2_WIDTH          8U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_2__REG DENALI_PHY_1026
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_VALUE_2__FLD LPDDR4__DENALI_PHY_1026__PHY_ADR_MEAS_DLY_STEP_VALUE_2

#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2_MASK 0x0F000000U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2_SHIFT  24U
#define LPDDR4__DENALI_PHY_1026__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2_WIDTH   4U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2__REG DENALI_PHY_1026
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_1026__PHY_ADR_MASTER_DLY_LOCK_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_1027_READ_MASK                            0xFF7F07FFU
#define LPDDR4__DENALI_PHY_1027_WRITE_MASK                           0xFF7F07FFU
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_MASTER_DLY_LOCK_OBS_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_MASTER_DLY_LOCK_OBS_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_MASTER_DLY_LOCK_OBS_2_WIDTH         11U
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_2__REG DENALI_PHY_1027
#define LPDDR4__PHY_ADR_MASTER_DLY_LOCK_OBS_2__FLD LPDDR4__DENALI_PHY_1027__PHY_ADR_MASTER_DLY_LOCK_OBS_2

#define LPDDR4__DENALI_PHY_1027__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2_MASK 0x007F0000U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2_SHIFT        16U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2_WIDTH         7U
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_1027
#define LPDDR4__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_1027__PHY_ADR_BASE_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_1027__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2_SHIFT       24U
#define LPDDR4__DENALI_PHY_1027__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2_WIDTH        8U
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2__REG DENALI_PHY_1027
#define LPDDR4__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2__FLD LPDDR4__DENALI_PHY_1027__PHY_ADR_ADDER_SLV_DLY_ENC_OBS_2

#define LPDDR4__DENALI_PHY_1028_READ_MASK                            0x01000707U
#define LPDDR4__DENALI_PHY_1028_WRITE_MASK                           0x01000707U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2_MASK 0x00000007U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2_WIDTH        3U
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2__REG DENALI_PHY_1028
#define LPDDR4__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2__FLD LPDDR4__DENALI_PHY_1028__PHY_ADR_SLAVE_LOOP_CNT_UPDATE_2

#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2_MASK 0x00000700U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2_SHIFT       8U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2_WIDTH       3U
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2__REG DENALI_PHY_1028
#define LPDDR4__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_1028__PHY_ADR_SLV_DLY_ENC_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_2_MASK     0x00010000U
#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_2_WOSET             0U
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_2__REG DENALI_PHY_1028
#define LPDDR4__SC_PHY_ADR_SNAP_OBS_REGS_2__FLD LPDDR4__DENALI_PHY_1028__SC_PHY_ADR_SNAP_OBS_REGS_2

#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_2_MASK          0x01000000U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_2_SHIFT                 24U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_2_WIDTH                  1U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_2_WOCLR                  0U
#define LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_2_WOSET                  0U
#define LPDDR4__PHY_ADR_TSEL_ENABLE_2__REG DENALI_PHY_1028
#define LPDDR4__PHY_ADR_TSEL_ENABLE_2__FLD LPDDR4__DENALI_PHY_1028__PHY_ADR_TSEL_ENABLE_2

#define LPDDR4__DENALI_PHY_1029_READ_MASK                            0x011F7F7FU
#define LPDDR4__DENALI_PHY_1029_WRITE_MASK                           0x011F7F7FU
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_LPBK_CONTROL_2_MASK         0x0000007FU
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_LPBK_CONTROL_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_LPBK_CONTROL_2_WIDTH                 7U
#define LPDDR4__PHY_ADR_LPBK_CONTROL_2__REG DENALI_PHY_1029
#define LPDDR4__PHY_ADR_LPBK_CONTROL_2__FLD LPDDR4__DENALI_PHY_1029__PHY_ADR_LPBK_CONTROL_2

#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_START_2_MASK   0x00007F00U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_START_2_SHIFT           8U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_START_2_WIDTH           7U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_2__REG DENALI_PHY_1029
#define LPDDR4__PHY_ADR_PRBS_PATTERN_START_2__FLD LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_START_2

#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_MASK_2_MASK    0x001F0000U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_MASK_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_MASK_2_WIDTH            5U
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_2__REG DENALI_PHY_1029
#define LPDDR4__PHY_ADR_PRBS_PATTERN_MASK_2__FLD LPDDR4__DENALI_PHY_1029__PHY_ADR_PRBS_PATTERN_MASK_2

#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_2_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_2_WIDTH              1U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_2_WOCLR              0U
#define LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_2_WOSET              0U
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_2__REG DENALI_PHY_1029
#define LPDDR4__PHY_ADR_PWR_RDC_DISABLE_2__FLD LPDDR4__DENALI_PHY_1029__PHY_ADR_PWR_RDC_DISABLE_2

#define LPDDR4__DENALI_PHY_1030_READ_MASK                            0x01070301U
#define LPDDR4__DENALI_PHY_1030_WRITE_MASK                           0x01070301U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_SHIFT    0U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_WIDTH    1U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_WOCLR    0U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2_WOSET    0U
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2__REG DENALI_PHY_1030
#define LPDDR4__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2__FLD LPDDR4__DENALI_PHY_1030__PHY_ADR_SLV_DLY_CTRL_GATE_DISABLE_2

#define LPDDR4__DENALI_PHY_1030__PHY_ADR_TYPE_2_MASK                 0x00000300U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_TYPE_2_SHIFT                         8U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_TYPE_2_WIDTH                         2U
#define LPDDR4__PHY_ADR_TYPE_2__REG DENALI_PHY_1030
#define LPDDR4__PHY_ADR_TYPE_2__FLD LPDDR4__DENALI_PHY_1030__PHY_ADR_TYPE_2

#define LPDDR4__DENALI_PHY_1030__PHY_ADR_WRADDR_SHIFT_OBS_2_MASK     0x00070000U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_WRADDR_SHIFT_OBS_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_WRADDR_SHIFT_OBS_2_WIDTH             3U
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_2__REG DENALI_PHY_1030
#define LPDDR4__PHY_ADR_WRADDR_SHIFT_OBS_2__FLD LPDDR4__DENALI_PHY_1030__PHY_ADR_WRADDR_SHIFT_OBS_2

#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_2_MASK              0x01000000U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_2_SHIFT                     24U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_2_WIDTH                      1U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_2_WOCLR                      0U
#define LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_2_WOSET                      0U
#define LPDDR4__PHY_ADR_IE_MODE_2__REG DENALI_PHY_1030
#define LPDDR4__PHY_ADR_IE_MODE_2__FLD LPDDR4__DENALI_PHY_1030__PHY_ADR_IE_MODE_2

#define LPDDR4__DENALI_PHY_1031_READ_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1031_WRITE_MASK                           0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1031__PHY_ADR_DDL_MODE_2_MASK             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_1031__PHY_ADR_DDL_MODE_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1031__PHY_ADR_DDL_MODE_2_WIDTH                    27U
#define LPDDR4__PHY_ADR_DDL_MODE_2__REG DENALI_PHY_1031
#define LPDDR4__PHY_ADR_DDL_MODE_2__FLD LPDDR4__DENALI_PHY_1031__PHY_ADR_DDL_MODE_2

#define LPDDR4__DENALI_PHY_1032_READ_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_1032_WRITE_MASK                           0x0000003FU
#define LPDDR4__DENALI_PHY_1032__PHY_ADR_DDL_MASK_2_MASK             0x0000003FU
#define LPDDR4__DENALI_PHY_1032__PHY_ADR_DDL_MASK_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1032__PHY_ADR_DDL_MASK_2_WIDTH                     6U
#define LPDDR4__PHY_ADR_DDL_MASK_2__REG DENALI_PHY_1032
#define LPDDR4__PHY_ADR_DDL_MASK_2__FLD LPDDR4__DENALI_PHY_1032__PHY_ADR_DDL_MASK_2

#define LPDDR4__DENALI_PHY_1033_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1033_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1033__PHY_ADR_DDL_TEST_OBS_2_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1033__PHY_ADR_DDL_TEST_OBS_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1033__PHY_ADR_DDL_TEST_OBS_2_WIDTH                32U
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_2__REG DENALI_PHY_1033
#define LPDDR4__PHY_ADR_DDL_TEST_OBS_2__FLD LPDDR4__DENALI_PHY_1033__PHY_ADR_DDL_TEST_OBS_2

#define LPDDR4__DENALI_PHY_1034_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1034_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1034__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1034__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2_SHIFT        0U
#define LPDDR4__DENALI_PHY_1034__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2_WIDTH       32U
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2__REG DENALI_PHY_1034
#define LPDDR4__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2__FLD LPDDR4__DENALI_PHY_1034__PHY_ADR_DDL_TEST_MSTR_DLY_OBS_2

#define LPDDR4__DENALI_PHY_1035_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1035_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_START_2_MASK          0x000007FFU
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_START_2_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_START_2_WIDTH                 11U
#define LPDDR4__PHY_ADR_CALVL_START_2__REG DENALI_PHY_1035
#define LPDDR4__PHY_ADR_CALVL_START_2__FLD LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_START_2

#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_COARSE_DLY_2_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_COARSE_DLY_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_COARSE_DLY_2_WIDTH            11U
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_2__REG DENALI_PHY_1035
#define LPDDR4__PHY_ADR_CALVL_COARSE_DLY_2__FLD LPDDR4__DENALI_PHY_1035__PHY_ADR_CALVL_COARSE_DLY_2

#define LPDDR4__DENALI_PHY_1036_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1036_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1036__PHY_ADR_CALVL_QTR_2_MASK            0x000007FFU
#define LPDDR4__DENALI_PHY_1036__PHY_ADR_CALVL_QTR_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1036__PHY_ADR_CALVL_QTR_2_WIDTH                   11U
#define LPDDR4__PHY_ADR_CALVL_QTR_2__REG DENALI_PHY_1036
#define LPDDR4__PHY_ADR_CALVL_QTR_2__FLD LPDDR4__DENALI_PHY_1036__PHY_ADR_CALVL_QTR_2

#define LPDDR4__DENALI_PHY_1037_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1037_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1037__PHY_ADR_CALVL_SWIZZLE0_2_MASK       0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1037__PHY_ADR_CALVL_SWIZZLE0_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_1037__PHY_ADR_CALVL_SWIZZLE0_2_WIDTH              24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_2__REG DENALI_PHY_1037
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE0_2__FLD LPDDR4__DENALI_PHY_1037__PHY_ADR_CALVL_SWIZZLE0_2

#define LPDDR4__DENALI_PHY_1038_READ_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1038_WRITE_MASK                           0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_SWIZZLE1_2_MASK       0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_SWIZZLE1_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_SWIZZLE1_2_WIDTH              24U
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_2__REG DENALI_PHY_1038
#define LPDDR4__PHY_ADR_CALVL_SWIZZLE1_2__FLD LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_SWIZZLE1_2

#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_RANK_CTRL_2_MASK      0x03000000U
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_RANK_CTRL_2_SHIFT             24U
#define LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_RANK_CTRL_2_WIDTH              2U
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_2__REG DENALI_PHY_1038
#define LPDDR4__PHY_ADR_CALVL_RANK_CTRL_2__FLD LPDDR4__DENALI_PHY_1038__PHY_ADR_CALVL_RANK_CTRL_2

#define LPDDR4__DENALI_PHY_1039_READ_MASK                            0x01FF0F03U
#define LPDDR4__DENALI_PHY_1039_WRITE_MASK                           0x01FF0F03U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_NUM_PATTERNS_2_MASK   0x00000003U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_NUM_PATTERNS_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_NUM_PATTERNS_2_WIDTH           2U
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_2__REG DENALI_PHY_1039
#define LPDDR4__PHY_ADR_CALVL_NUM_PATTERNS_2__FLD LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_NUM_PATTERNS_2

#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_RESP_WAIT_CNT_2_MASK  0x00000F00U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_RESP_WAIT_CNT_2_SHIFT          8U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_RESP_WAIT_CNT_2_WIDTH          4U
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_2__REG DENALI_PHY_1039
#define LPDDR4__PHY_ADR_CALVL_RESP_WAIT_CNT_2__FLD LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_RESP_WAIT_CNT_2

#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2_MASK 0x01FF0000U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2_SHIFT 16U
#define LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2_WIDTH  9U
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2__REG DENALI_PHY_1039
#define LPDDR4__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2__FLD LPDDR4__DENALI_PHY_1039__PHY_ADR_CALVL_PERIODIC_START_OFFSET_2

#define LPDDR4__DENALI_PHY_1040_READ_MASK                            0x07000001U
#define LPDDR4__DENALI_PHY_1040_WRITE_MASK                           0x07000001U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_2_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_2_WIDTH             1U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_2_WOCLR             0U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_2_WOSET             0U
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_2__REG DENALI_PHY_1040
#define LPDDR4__PHY_ADR_CALVL_DEBUG_MODE_2__FLD LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_DEBUG_MODE_2

#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_2_MASK  0x00000100U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_2_SHIFT          8U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_2_WIDTH          1U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_2_WOCLR          0U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_2_WOSET          0U
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_2__REG DENALI_PHY_1040
#define LPDDR4__SC_PHY_ADR_CALVL_DEBUG_CONT_2__FLD LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_DEBUG_CONT_2

#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_2_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_2_WIDTH           1U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_2_WOCLR           0U
#define LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_2_WOSET           0U
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_2__REG DENALI_PHY_1040
#define LPDDR4__SC_PHY_ADR_CALVL_ERROR_CLR_2__FLD LPDDR4__DENALI_PHY_1040__SC_PHY_ADR_CALVL_ERROR_CLR_2

#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_OBS_SELECT_2_MASK     0x07000000U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_OBS_SELECT_2_SHIFT            24U
#define LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_OBS_SELECT_2_WIDTH             3U
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_2__REG DENALI_PHY_1040
#define LPDDR4__PHY_ADR_CALVL_OBS_SELECT_2__FLD LPDDR4__DENALI_PHY_1040__PHY_ADR_CALVL_OBS_SELECT_2

#define LPDDR4__DENALI_PHY_1041_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1041_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1041__PHY_ADR_CALVL_OBS0_2_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1041__PHY_ADR_CALVL_OBS0_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1041__PHY_ADR_CALVL_OBS0_2_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS0_2__REG DENALI_PHY_1041
#define LPDDR4__PHY_ADR_CALVL_OBS0_2__FLD LPDDR4__DENALI_PHY_1041__PHY_ADR_CALVL_OBS0_2

#define LPDDR4__DENALI_PHY_1042_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1042_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1042__PHY_ADR_CALVL_OBS1_2_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1042__PHY_ADR_CALVL_OBS1_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1042__PHY_ADR_CALVL_OBS1_2_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS1_2__REG DENALI_PHY_1042
#define LPDDR4__PHY_ADR_CALVL_OBS1_2__FLD LPDDR4__DENALI_PHY_1042__PHY_ADR_CALVL_OBS1_2

#define LPDDR4__DENALI_PHY_1043_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1043_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1043__PHY_ADR_CALVL_OBS2_2_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1043__PHY_ADR_CALVL_OBS2_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1043__PHY_ADR_CALVL_OBS2_2_WIDTH                  32U
#define LPDDR4__PHY_ADR_CALVL_OBS2_2__REG DENALI_PHY_1043
#define LPDDR4__PHY_ADR_CALVL_OBS2_2__FLD LPDDR4__DENALI_PHY_1043__PHY_ADR_CALVL_OBS2_2

#define LPDDR4__DENALI_PHY_1044_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1044_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1044__PHY_ADR_CALVL_FG_0_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1044__PHY_ADR_CALVL_FG_0_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1044__PHY_ADR_CALVL_FG_0_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_0_2__REG DENALI_PHY_1044
#define LPDDR4__PHY_ADR_CALVL_FG_0_2__FLD LPDDR4__DENALI_PHY_1044__PHY_ADR_CALVL_FG_0_2

#define LPDDR4__DENALI_PHY_1045_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1045_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1045__PHY_ADR_CALVL_BG_0_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1045__PHY_ADR_CALVL_BG_0_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1045__PHY_ADR_CALVL_BG_0_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_0_2__REG DENALI_PHY_1045
#define LPDDR4__PHY_ADR_CALVL_BG_0_2__FLD LPDDR4__DENALI_PHY_1045__PHY_ADR_CALVL_BG_0_2

#define LPDDR4__DENALI_PHY_1046_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1046_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1046__PHY_ADR_CALVL_FG_1_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1046__PHY_ADR_CALVL_FG_1_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1046__PHY_ADR_CALVL_FG_1_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_1_2__REG DENALI_PHY_1046
#define LPDDR4__PHY_ADR_CALVL_FG_1_2__FLD LPDDR4__DENALI_PHY_1046__PHY_ADR_CALVL_FG_1_2

#define LPDDR4__DENALI_PHY_1047_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1047_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1047__PHY_ADR_CALVL_BG_1_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1047__PHY_ADR_CALVL_BG_1_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1047__PHY_ADR_CALVL_BG_1_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_1_2__REG DENALI_PHY_1047
#define LPDDR4__PHY_ADR_CALVL_BG_1_2__FLD LPDDR4__DENALI_PHY_1047__PHY_ADR_CALVL_BG_1_2

#define LPDDR4__DENALI_PHY_1048_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1048_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1048__PHY_ADR_CALVL_FG_2_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1048__PHY_ADR_CALVL_FG_2_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1048__PHY_ADR_CALVL_FG_2_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_2_2__REG DENALI_PHY_1048
#define LPDDR4__PHY_ADR_CALVL_FG_2_2__FLD LPDDR4__DENALI_PHY_1048__PHY_ADR_CALVL_FG_2_2

#define LPDDR4__DENALI_PHY_1049_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1049_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1049__PHY_ADR_CALVL_BG_2_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1049__PHY_ADR_CALVL_BG_2_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1049__PHY_ADR_CALVL_BG_2_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_2_2__REG DENALI_PHY_1049
#define LPDDR4__PHY_ADR_CALVL_BG_2_2__FLD LPDDR4__DENALI_PHY_1049__PHY_ADR_CALVL_BG_2_2

#define LPDDR4__DENALI_PHY_1050_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1050_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1050__PHY_ADR_CALVL_FG_3_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1050__PHY_ADR_CALVL_FG_3_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1050__PHY_ADR_CALVL_FG_3_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_FG_3_2__REG DENALI_PHY_1050
#define LPDDR4__PHY_ADR_CALVL_FG_3_2__FLD LPDDR4__DENALI_PHY_1050__PHY_ADR_CALVL_FG_3_2

#define LPDDR4__DENALI_PHY_1051_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1051_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1051__PHY_ADR_CALVL_BG_3_2_MASK           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1051__PHY_ADR_CALVL_BG_3_2_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1051__PHY_ADR_CALVL_BG_3_2_WIDTH                  20U
#define LPDDR4__PHY_ADR_CALVL_BG_3_2__REG DENALI_PHY_1051
#define LPDDR4__PHY_ADR_CALVL_BG_3_2__FLD LPDDR4__DENALI_PHY_1051__PHY_ADR_CALVL_BG_3_2

#define LPDDR4__DENALI_PHY_1052_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1052_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1052__PHY_ADR_ADDR_SEL_2_MASK             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1052__PHY_ADR_ADDR_SEL_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1052__PHY_ADR_ADDR_SEL_2_WIDTH                    30U
#define LPDDR4__PHY_ADR_ADDR_SEL_2__REG DENALI_PHY_1052
#define LPDDR4__PHY_ADR_ADDR_SEL_2__FLD LPDDR4__DENALI_PHY_1052__PHY_ADR_ADDR_SEL_2

#define LPDDR4__DENALI_PHY_1053_READ_MASK                            0x3F3F03FFU
#define LPDDR4__DENALI_PHY_1053_WRITE_MASK                           0x3F3F03FFU
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_LP4_BOOT_SLV_DELAY_2_MASK   0x000003FFU
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_LP4_BOOT_SLV_DELAY_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_LP4_BOOT_SLV_DELAY_2_WIDTH          10U
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_2__REG DENALI_PHY_1053
#define LPDDR4__PHY_ADR_LP4_BOOT_SLV_DELAY_2__FLD LPDDR4__DENALI_PHY_1053__PHY_ADR_LP4_BOOT_SLV_DELAY_2

#define LPDDR4__DENALI_PHY_1053__PHY_ADR_BIT_MASK_2_MASK             0x003F0000U
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_BIT_MASK_2_SHIFT                    16U
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_BIT_MASK_2_WIDTH                     6U
#define LPDDR4__PHY_ADR_BIT_MASK_2__REG DENALI_PHY_1053
#define LPDDR4__PHY_ADR_BIT_MASK_2__FLD LPDDR4__DENALI_PHY_1053__PHY_ADR_BIT_MASK_2

#define LPDDR4__DENALI_PHY_1053__PHY_ADR_SEG_MASK_2_MASK             0x3F000000U
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_SEG_MASK_2_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1053__PHY_ADR_SEG_MASK_2_WIDTH                     6U
#define LPDDR4__PHY_ADR_SEG_MASK_2__REG DENALI_PHY_1053
#define LPDDR4__PHY_ADR_SEG_MASK_2__FLD LPDDR4__DENALI_PHY_1053__PHY_ADR_SEG_MASK_2

#define LPDDR4__DENALI_PHY_1054_READ_MASK                            0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_1054_WRITE_MASK                           0x3F0F3F3FU
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_CALVL_TRAIN_MASK_2_MASK     0x0000003FU
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_CALVL_TRAIN_MASK_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_CALVL_TRAIN_MASK_2_WIDTH             6U
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_2__REG DENALI_PHY_1054
#define LPDDR4__PHY_ADR_CALVL_TRAIN_MASK_2__FLD LPDDR4__DENALI_PHY_1054__PHY_ADR_CALVL_TRAIN_MASK_2

#define LPDDR4__DENALI_PHY_1054__PHY_ADR_CSLVL_TRAIN_MASK_2_MASK     0x00003F00U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_CSLVL_TRAIN_MASK_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_CSLVL_TRAIN_MASK_2_WIDTH             6U
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_2__REG DENALI_PHY_1054
#define LPDDR4__PHY_ADR_CSLVL_TRAIN_MASK_2__FLD LPDDR4__DENALI_PHY_1054__PHY_ADR_CSLVL_TRAIN_MASK_2

#define LPDDR4__DENALI_PHY_1054__PHY_ADR_STATIC_TOG_DISABLE_2_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_STATIC_TOG_DISABLE_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_STATIC_TOG_DISABLE_2_WIDTH           4U
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_2__REG DENALI_PHY_1054
#define LPDDR4__PHY_ADR_STATIC_TOG_DISABLE_2__FLD LPDDR4__DENALI_PHY_1054__PHY_ADR_STATIC_TOG_DISABLE_2

#define LPDDR4__DENALI_PHY_1054__PHY_ADR_SW_TXIO_CTRL_2_MASK         0x3F000000U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_SW_TXIO_CTRL_2_SHIFT                24U
#define LPDDR4__DENALI_PHY_1054__PHY_ADR_SW_TXIO_CTRL_2_WIDTH                 6U
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_2__REG DENALI_PHY_1054
#define LPDDR4__PHY_ADR_SW_TXIO_CTRL_2__FLD LPDDR4__DENALI_PHY_1054__PHY_ADR_SW_TXIO_CTRL_2

#define LPDDR4__DENALI_PHY_1055_READ_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_1055_WRITE_MASK                           0x0000003FU
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_SW_TXPWR_CTRL_2_MASK        0x0000003FU
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_SW_TXPWR_CTRL_2_SHIFT                0U
#define LPDDR4__DENALI_PHY_1055__PHY_ADR_SW_TXPWR_CTRL_2_WIDTH                6U
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_2__REG DENALI_PHY_1055
#define LPDDR4__PHY_ADR_SW_TXPWR_CTRL_2__FLD LPDDR4__DENALI_PHY_1055__PHY_ADR_SW_TXPWR_CTRL_2

#define LPDDR4__DENALI_PHY_1056_READ_MASK                            0x0707FFFFU
#define LPDDR4__DENALI_PHY_1056_WRITE_MASK                           0x0707FFFFU
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_TSEL_SELECT_2_MASK          0x000000FFU
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_TSEL_SELECT_2_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1056__PHY_ADR_TSEL_SELECT_2_WIDTH                  8U
#define LPDDR4__PHY_ADR_TSEL_SELECT_2__REG DENALI_PHY_1056
#define LPDDR4__PHY_ADR_TSEL_SELECT_2__FLD LPDDR4__DENALI_PHY_1056__PHY_ADR_TSEL_SELECT_2

#define LPDDR4__DENALI_PHY_1056__PHY_PAD_ADR_IO_CFG_2_MASK           0x0007FF00U
#define LPDDR4__DENALI_PHY_1056__PHY_PAD_ADR_IO_CFG_2_SHIFT                   8U
#define LPDDR4__DENALI_PHY_1056__PHY_PAD_ADR_IO_CFG_2_WIDTH                  11U
#define LPDDR4__PHY_PAD_ADR_IO_CFG_2__REG DENALI_PHY_1056
#define LPDDR4__PHY_PAD_ADR_IO_CFG_2__FLD LPDDR4__DENALI_PHY_1056__PHY_PAD_ADR_IO_CFG_2

#define LPDDR4__DENALI_PHY_1056__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2_MASK  0x07000000U
#define LPDDR4__DENALI_PHY_1056__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2_SHIFT         24U
#define LPDDR4__DENALI_PHY_1056__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2_WIDTH          3U
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2__REG DENALI_PHY_1056
#define LPDDR4__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2__FLD LPDDR4__DENALI_PHY_1056__PHY_PAD_ADR_RX_PCLK_CLK_SEL_2

#define LPDDR4__DENALI_PHY_1057_READ_MASK                            0x1F07FF1FU
#define LPDDR4__DENALI_PHY_1057_WRITE_MASK                           0x1F07FF1FU
#define LPDDR4__DENALI_PHY_1057__PHY_ADR0_SW_WRADDR_SHIFT_2_MASK     0x0000001FU
#define LPDDR4__DENALI_PHY_1057__PHY_ADR0_SW_WRADDR_SHIFT_2_SHIFT             0U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR0_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1057
#define LPDDR4__PHY_ADR0_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1057__PHY_ADR0_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1057__PHY_ADR0_CLK_WR_SLAVE_DELAY_2_MASK  0x0007FF00U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR0_CLK_WR_SLAVE_DELAY_2_SHIFT          8U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR0_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1057
#define LPDDR4__PHY_ADR0_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1057__PHY_ADR0_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1057__PHY_ADR1_SW_WRADDR_SHIFT_2_MASK     0x1F000000U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR1_SW_WRADDR_SHIFT_2_SHIFT            24U
#define LPDDR4__DENALI_PHY_1057__PHY_ADR1_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1057
#define LPDDR4__PHY_ADR1_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1057__PHY_ADR1_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1058_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1058_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1058__PHY_ADR1_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1058__PHY_ADR1_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR1_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1058
#define LPDDR4__PHY_ADR1_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1058__PHY_ADR1_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1058__PHY_ADR2_SW_WRADDR_SHIFT_2_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR2_SW_WRADDR_SHIFT_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1058__PHY_ADR2_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1058
#define LPDDR4__PHY_ADR2_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1058__PHY_ADR2_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1059_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1059_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1059__PHY_ADR2_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1059__PHY_ADR2_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR2_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1059
#define LPDDR4__PHY_ADR2_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1059__PHY_ADR2_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1059__PHY_ADR3_SW_WRADDR_SHIFT_2_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR3_SW_WRADDR_SHIFT_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1059__PHY_ADR3_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1059
#define LPDDR4__PHY_ADR3_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1059__PHY_ADR3_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1060_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1060_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1060__PHY_ADR3_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1060__PHY_ADR3_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR3_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1060
#define LPDDR4__PHY_ADR3_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1060__PHY_ADR3_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1060__PHY_ADR4_SW_WRADDR_SHIFT_2_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR4_SW_WRADDR_SHIFT_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1060__PHY_ADR4_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1060
#define LPDDR4__PHY_ADR4_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1060__PHY_ADR4_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1061_READ_MASK                            0x001F07FFU
#define LPDDR4__DENALI_PHY_1061_WRITE_MASK                           0x001F07FFU
#define LPDDR4__DENALI_PHY_1061__PHY_ADR4_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1061__PHY_ADR4_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1061__PHY_ADR4_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1061
#define LPDDR4__PHY_ADR4_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1061__PHY_ADR4_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1061__PHY_ADR5_SW_WRADDR_SHIFT_2_MASK     0x001F0000U
#define LPDDR4__DENALI_PHY_1061__PHY_ADR5_SW_WRADDR_SHIFT_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1061__PHY_ADR5_SW_WRADDR_SHIFT_2_WIDTH             5U
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_2__REG DENALI_PHY_1061
#define LPDDR4__PHY_ADR5_SW_WRADDR_SHIFT_2__FLD LPDDR4__DENALI_PHY_1061__PHY_ADR5_SW_WRADDR_SHIFT_2

#define LPDDR4__DENALI_PHY_1062_READ_MASK                            0x000F07FFU
#define LPDDR4__DENALI_PHY_1062_WRITE_MASK                           0x000F07FFU
#define LPDDR4__DENALI_PHY_1062__PHY_ADR5_CLK_WR_SLAVE_DELAY_2_MASK  0x000007FFU
#define LPDDR4__DENALI_PHY_1062__PHY_ADR5_CLK_WR_SLAVE_DELAY_2_SHIFT          0U
#define LPDDR4__DENALI_PHY_1062__PHY_ADR5_CLK_WR_SLAVE_DELAY_2_WIDTH         11U
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_2__REG DENALI_PHY_1062
#define LPDDR4__PHY_ADR5_CLK_WR_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1062__PHY_ADR5_CLK_WR_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1062__PHY_ADR_SW_MASTER_MODE_2_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_1062__PHY_ADR_SW_MASTER_MODE_2_SHIFT              16U
#define LPDDR4__DENALI_PHY_1062__PHY_ADR_SW_MASTER_MODE_2_WIDTH               4U
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_2__REG DENALI_PHY_1062
#define LPDDR4__PHY_ADR_SW_MASTER_MODE_2__FLD LPDDR4__DENALI_PHY_1062__PHY_ADR_SW_MASTER_MODE_2

#define LPDDR4__DENALI_PHY_1063_READ_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_1063_WRITE_MASK                           0xFF3F07FFU
#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_START_2_MASK   0x000007FFU
#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_START_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_START_2_WIDTH          11U
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_2__REG DENALI_PHY_1063
#define LPDDR4__PHY_ADR_MASTER_DELAY_START_2__FLD LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_START_2

#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_STEP_2_MASK    0x003F0000U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_STEP_2_SHIFT           16U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_STEP_2_WIDTH            6U
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_2__REG DENALI_PHY_1063
#define LPDDR4__PHY_ADR_MASTER_DELAY_STEP_2__FLD LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_STEP_2

#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_WAIT_2_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_WAIT_2_SHIFT           24U
#define LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_WAIT_2_WIDTH            8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_2__REG DENALI_PHY_1063
#define LPDDR4__PHY_ADR_MASTER_DELAY_WAIT_2__FLD LPDDR4__DENALI_PHY_1063__PHY_ADR_MASTER_DELAY_WAIT_2

#define LPDDR4__DENALI_PHY_1064_READ_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_1064_WRITE_MASK                           0x0103FFFFU
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2_SHIFT    0U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2_WIDTH    8U
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2__REG DENALI_PHY_1064
#define LPDDR4__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2__FLD LPDDR4__DENALI_PHY_1064__PHY_ADR_MASTER_DELAY_HALF_MEASURE_2

#define LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_2_MASK     0x0003FF00U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_2_WIDTH            10U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_2__REG DENALI_PHY_1064
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_2__FLD LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_2

#define LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_MASK  0x01000000U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_SHIFT         24U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_WIDTH          1U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_WOCLR          0U
#define LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_EN_2_WOSET          0U
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_2__REG DENALI_PHY_1064
#define LPDDR4__PHY_ADR_SW_CALVL_DVW_MIN_EN_2__FLD LPDDR4__DENALI_PHY_1064__PHY_ADR_SW_CALVL_DVW_MIN_EN_2

#define LPDDR4__DENALI_PHY_1065_READ_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_1065_WRITE_MASK                           0x0000000FU
#define LPDDR4__DENALI_PHY_1065__PHY_ADR_CALVL_DLY_STEP_2_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_1065__PHY_ADR_CALVL_DLY_STEP_2_SHIFT               0U
#define LPDDR4__DENALI_PHY_1065__PHY_ADR_CALVL_DLY_STEP_2_WIDTH               4U
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_2__REG DENALI_PHY_1065
#define LPDDR4__PHY_ADR_CALVL_DLY_STEP_2__FLD LPDDR4__DENALI_PHY_1065__PHY_ADR_CALVL_DLY_STEP_2

#define LPDDR4__DENALI_PHY_1066_READ_MASK                            0x0000010FU
#define LPDDR4__DENALI_PHY_1066_WRITE_MASK                           0x0000010FU
#define LPDDR4__DENALI_PHY_1066__PHY_ADR_CALVL_CAPTURE_CNT_2_MASK    0x0000000FU
#define LPDDR4__DENALI_PHY_1066__PHY_ADR_CALVL_CAPTURE_CNT_2_SHIFT            0U
#define LPDDR4__DENALI_PHY_1066__PHY_ADR_CALVL_CAPTURE_CNT_2_WIDTH            4U
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_2__REG DENALI_PHY_1066
#define LPDDR4__PHY_ADR_CALVL_CAPTURE_CNT_2__FLD LPDDR4__DENALI_PHY_1066__PHY_ADR_CALVL_CAPTURE_CNT_2

#define LPDDR4__DENALI_PHY_1066__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_MASK 0x00000100U
#define LPDDR4__DENALI_PHY_1066__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_SHIFT         8U
#define LPDDR4__DENALI_PHY_1066__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_WIDTH         1U
#define LPDDR4__DENALI_PHY_1066__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_WOCLR         0U
#define LPDDR4__DENALI_PHY_1066__PHY_ADR_MEAS_DLY_STEP_ENABLE_2_WOSET         0U
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_2__REG DENALI_PHY_1066
#define LPDDR4__PHY_ADR_MEAS_DLY_STEP_ENABLE_2__FLD LPDDR4__DENALI_PHY_1066__PHY_ADR_MEAS_DLY_STEP_ENABLE_2

#endif /* REG_LPDDR4_ADDRESS_SLICE_2_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

