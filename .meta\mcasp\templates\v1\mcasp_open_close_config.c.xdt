%%{
    let common = system.getScript("/common");
    let soc = system.getScript(`/drivers/soc/drivers_${common.getSocName()}`);
    let driverVer = soc.getDriverVer("mcasp");
    let module = system.modules['/drivers/mcasp/mcasp'];
    let mcaspBcdmaInstances = [];
    let mcaspPktdmaInstances = [];
    for(let i=0; i < module.$instances.length; i++) {
        let instance = module.$instances[i];
        if(instance.transferMode == "DMA") {
            mcaspBcdmaInstances.push(module.getInstanceConfig(instance).bcDmaDriver);
        }
    }

    for(let i=0; i < module.$instances.length; i++) {
        let instance = module.$instances[i];
        if(instance.transferMode == "DMA") {
            mcaspPktdmaInstances.push(module.getInstanceConfig(instance).pktDmaDriver);
        }
    }
%%}

/*
 * MCASP
 */

% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    % if(config.enableMcaspTx == true && config.txCallbackFxn != "NULL") {
/* MCASP transmit callback */
void `config.txCallbackFxn`(MCASP_Handle handle, MCASP_Transaction *transaction);
    % }
    % if(config.enableMcaspRx == true && config.rxCallbackFxn != "NULL") {
/* MCASP receive Callback */
void `config.rxCallbackFxn`(MCASP_Handle handle, MCASP_Transaction *transaction);
    % }
    % if(config.enableMcaspTx == true && config.txLoopjobEnable == true) {
/* MCASP transmit loopjob buffer */
uint8_t `config.txLoopjobBuf`[`config.txLoopjobBufLength`] __attribute__((aligned(256))) = {0};
    % }
    % if(config.enableMcaspRx == true && config.rxLoopjobEnable == true) {
/* MCASP receive loopjob buffer */
uint8_t `config.rxLoopjobBuf`[`config.rxLoopjobBufLength`] __attribute__((aligned(256))) = {0};
    % }
% }

/* Arrays containing indices of MCASP Tx/Rx serializers used */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let serInstances = instance.mcaspSer;
    % let serTxArr = [], serRxArr = [];
    % let numTxSer = 0, numRxSer = 0;
    % for (let si = 0; si < serInstances.length; si++) {
        % let serInstance = serInstances[si];
        % if(serInstance.dataDir == "Transmit") {
            % serTxArr[numTxSer] = serInstance.serNum;
            % numTxSer++;
        %} else {
            % serRxArr[numRxSer] = serInstance.serNum;
            % numRxSer++;
            % }
    % }
uint8_t gMcasp`i`TxSersUsed[`numTxSer`] = {`serTxArr`};
uint8_t gMcasp`i`RxSersUsed[`numRxSer`] = {`serRxArr`};
% }

% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % if(instance.transferMode == "DMA") {
Udma_EventObject gBcdmaTxCqEventObj;
Udma_EventObject gBcdmaRxCqEventObj;

/* Number of ring entries */
#define UDMA_RING_ENTRIES_TX             (MCASP_TX_DMA_RING_ELEM_CNT)
#define UDMA_RING_ENTRIES_RX             (MCASP_RX_DMA_RING_ELEM_CNT)
/* Size (in bytes) of each ring entry (Size of pointer - 64-bit) */
#define MCASP_UDMA_RING_ENTRY_SIZE       (sizeof(uint64_t))

#define MCASP_RING_MEM_SIZE_TX           (MCASP_UDMA_RING_ENTRY_SIZE*UDMA_RING_ENTRIES_TX)
#define MCASP_RING_MEM_SIZE_RX           (MCASP_UDMA_RING_ENTRY_SIZE*UDMA_RING_ENTRIES_RX)

#define MCASP_UDMA_TR3_TRPD_SIZE         (UDMA_GET_TRPD_TR3_SIZE(1))
#define MCASP_UDMA_HPD_SIZE              UDMA_ALIGN_SIZE((sizeof(CSL_UdmapCppi5HMPD)))
        % break;
    % }
% }

% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    % if(instance.transferMode == "DMA") {
Udma_ChObject       gMcasp`i`UdmaTxChObj;
Udma_EventObject    gMcasp`i`_UdmaCqEventObjTx;

Udma_ChObject       gMcasp`i`UdmaRxChObj;
Udma_EventObject    gMcasp`i`_UdmaCqEventObjRx;

static uint8_t gMcasp`i`UdmaTxTrpdMem[MCASP_UDMA_TR3_TRPD_SIZE*MCASP_TX_DMA_RING_ELEM_CNT] __attribute__((aligned(UDMA_CACHELINE_ALIGNMENT)));
static uint8_t gMcasp`i`UdmaRxTrpdMem[MCASP_UDMA_HPD_SIZE*MCASP_RX_DMA_RING_ELEM_CNT] __attribute__((aligned(UDMA_CACHELINE_ALIGNMENT)));

static uint8_t gMcasp`i`TxFqRingMem[UDMA_ALIGN_SIZE(MCASP_RING_MEM_SIZE_TX)] __attribute__((aligned(UDMA_CACHELINE_ALIGNMENT)));
static uint8_t gMcasp`i`RxFqRingMem[UDMA_ALIGN_SIZE(MCASP_RING_MEM_SIZE_RX)] __attribute__((aligned(UDMA_CACHELINE_ALIGNMENT)));

static MCASP_Transaction *gMcasp`i`TxCbParam[MCASP_TX_DMA_RING_ELEM_CNT];
static MCASP_Transaction *gMcasp`i`RxCbParam[MCASP_RX_DMA_RING_ELEM_CNT];

MCASP_DmaChConfig gMcasp`i`_DmaChCfg[] =
{
    {
        .txChHandle         = &gMcasp`i`UdmaTxChObj,
        .rxChHandle         = &gMcasp`i`UdmaRxChObj,
        .cqTxEvtHandle      = &gMcasp`i`_UdmaCqEventObjTx,
        .cqRxEvtHandle      = &gMcasp`i`_UdmaCqEventObjRx,
        .txTrpdMem          = gMcasp`i`UdmaTxTrpdMem,
        .rxTrpdMem          = gMcasp`i`UdmaRxTrpdMem,
        .txRingMem          = gMcasp`i`TxFqRingMem,
        .rxRingMem          = gMcasp`i`RxFqRingMem,
        .txCbParams         = gMcasp`i`TxCbParam,
        .rxCbParams         = gMcasp`i`RxCbParam,
        .rxEvtNum           = `config.udmaPdmaChannels[0].rxCh`,
        .txEvtNum           = `config.udmaPdmaChannels[0].txCh`,
    }
};
    % }
% }

/* MCASP Driver handles */
MCASP_Handle gMcaspHandle[CONFIG_MCASP_NUM_INSTANCES];
/* MCASP Driver Open Parameters */
MCASP_OpenParams gMcaspOpenParams[CONFIG_MCASP_NUM_INSTANCES] =
{
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    {
        .transferMode = MCASP_TRANSFER_MODE_`instance.transferMode`,
        .txBufferFormat = MCASP_AUDBUFF_FORMAT_`instance.txBufferFormat`,
        .rxBufferFormat = MCASP_AUDBUFF_FORMAT_`instance.rxBufferFormat`,
        % let serInstances = instance.mcaspSer;
        % let serTxArr = [], serRxArr = [];
        % let numTxSer = 0, numRxSer = 0;
        % for (let si = 0; si < serInstances.length; si++) {
            % let serInstance = serInstances[si];
            % if(serInstance.dataDir == "Transmit") {
                % serTxArr[numTxSer] = serInstance.serNum;
                % numTxSer++;
            %} else {
                % serRxArr[numRxSer] = serInstance.serNum;
                % numRxSer++;
            % }
        % }
        .txSerUsedCount = `numTxSer`,
        .rxSerUsedCount = `numRxSer`,
        .txSerUsedArray = (uint8_t *) gMcasp`i`TxSersUsed,
        .rxSerUsedArray = (uint8_t *) gMcasp`i`RxSersUsed,
        .txSlotCount = `instance.NumTxSlots`,
        .rxSlotCount = `instance.NumRxSlots`,
        % if(config.enableMcaspTx == true && config.txCallbackFxn != "NULL") {
        .txCallbackFxn = `instance.txCallbackFxn`,
        % }
         % if(config.enableMcaspRx == true && config.rxCallbackFxn != "NULL") {
        .rxCallbackFxn = `instance.rxCallbackFxn`,
        % }
        .txLoopjobEnable = `config.txLoopjobEnable`,
        % if(config.enableMcaspTx == true && config.txLoopjobEnable == true) {
        .txLoopjobBuf = (uint8_t *) `instance.txLoopjobBuf`,
        .txLoopjobBufLength = `instance.txLoopjobBufLength / (instance.TxSlotSize / 8)`,
        .rxLoopjobEnable = `config.rxLoopjobEnable`,
        % }
        % if(config.enableMcaspRx == true && config.rxLoopjobEnable == true) {
        .rxLoopjobBuf = (uint8_t *) `instance.rxLoopjobBuf`,
        .rxLoopjobBufLength = `instance.rxLoopjobBufLength / (instance.RxSlotSize / 8)`,
        % }
        % if(config.transferMode == "DMA") {
        .dmaChCfg = &gMcasp`i`_DmaChCfg[0],
        .mcaspDmaDrvObj = &gUdmaDrvObj[`mcaspBcdmaInstances[i].$name`],
        .mcaspPktDmaDrvObj = &gUdmaDrvObj[`mcaspPktdmaInstances[i].$name`],
        % }
    },
    %}
};


void Drivers_mcaspOpen(void)
{
    uint32_t instCnt;
    int32_t  status = SystemP_SUCCESS;

    for(instCnt = 0U; instCnt < CONFIG_MCASP_NUM_INSTANCES; instCnt++)
    {
        gMcaspHandle[instCnt] = NULL;   /* Init to NULL so that we can exit gracefully */
    }

    /* Open all instances */
    for(instCnt = 0U; instCnt < CONFIG_MCASP_NUM_INSTANCES; instCnt++)
    {
        gMcaspHandle[instCnt] = MCASP_open(instCnt, &gMcaspOpenParams[instCnt]);
        if(NULL == gMcaspHandle[instCnt])
        {
            DebugP_logError("MCASP open failed for instance %d !!!\r\n", instCnt);
            status = SystemP_FAILURE;
            break;
        }
    }

    if(SystemP_FAILURE == status)
    {
        Drivers_mcaspClose();   /* Exit gracefully */
    }

    return;
}

void Drivers_mcaspClose(void)
{
    uint32_t instCnt;

    /* Close all instances that are open */
    for(instCnt = 0U; instCnt < CONFIG_MCASP_NUM_INSTANCES; instCnt++)
    {
        if(gMcaspHandle[instCnt] != NULL)
        {
            MCASP_close(gMcaspHandle[instCnt]);
        }
    }

    for(instCnt = 0U; instCnt < CONFIG_MCASP_NUM_INSTANCES; instCnt++)
    {
        if(gMcaspHandle[instCnt] != NULL)
        {
            gMcaspHandle[instCnt] = NULL;
        }
    }

    return;
}
