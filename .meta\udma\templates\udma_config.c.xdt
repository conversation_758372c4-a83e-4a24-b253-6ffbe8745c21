%%{
    let module = system.modules['/drivers/udma/udma'];
%%}
/*
 * UDMA
 */
/* UDMA driver instance object */
Udma_DrvObject          gUdmaDrvObj[CONFIG_UDMA_NUM_INSTANCES];
/* UDMA driver instance init params */
static Udma_InitPrms    gUdmaInitPrms[CONFIG_UDMA_NUM_INSTANCES] =
{
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    {
        .instId             = UDMA_INST_ID_`config.name`,
        .skipGlobalEventReg = `config.skipGlobalEventReg.toString(10).toUpperCase()`,
        .virtToPhyFxn       = `config.virtToPhyFxn`,
        .phyToVirtFxn       = `config.phyToVirtFxn`,
    },
% }
};
