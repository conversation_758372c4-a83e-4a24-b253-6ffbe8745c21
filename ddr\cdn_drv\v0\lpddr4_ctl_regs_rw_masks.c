/* parasoft-begin-suppress MISRA2012-RULE-1_1_a_c99-2 "C99 - limits, DRV-3919" parasoft-begin-suppress MISRA2012-RULE-1_1_a_c90-2 "C90 - limits, DRV-3919" */
/**********************************************************************
* Copyright (C) 2012-2022 Cadence Design Systems, Inc.
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions
* are met:
*
* 1. Redistributions of source code must retain the above copyright
* notice, this list of conditions and the following disclaimer.
* 2. Redistributions in binary form must reproduce the above copyright
* notice, this list of conditions and the following disclaimer in the
* documentation and/or other materials provided with the distribution.
* 3. Neither the name of the copyright holder nor the names of its
* contributors may be used to endorse or promote products derived from
* this software without specific prior written permission.
*
* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
* AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
* IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
* ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
* LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
* CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
* SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
* INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
* CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
* ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*
* THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
*
**********************************************************************/

#include "../common/include/cdn_errno.h"
#include "include/lpddr4_ctl_regs_rw_masks.h"

uint32_t g_lpddr4_ddr_controller_rw_mask[] = {
    0x00000F01U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x01FFFFFFU,
    0x03030300U,
    0x01030100U,
    0x1F1F013FU,
    0x0303031FU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0xFFFFFF01U,
    0x0001FFFFU,
    0x000F7FFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFF00FFFFU,
    0x0000FFFFU,
    0x00000000U,
    0x00000000U,
    0x0F3F7F7FU,
    0xFFFFFFFFU,
    0x0F3F7F7FU,
    0xFFFFFFFFU,
    0x0F3F7F7FU,
    0xFFFFFFFFU,
    0xFF1F1F07U,
    0x0001FFFFU,
    0x3F3F01FFU,
    0x1F01FFFFU,
    0x01FFFFFFU,
    0x3F3F01FFU,
    0x1F01FFFFU,
    0x01FFFFFFU,
    0x3F3F01FFU,
    0xFF01FFFFU,
    0x00FFFFFFU,
    0x1F0FFFFFU,
    0xFFFF3FFFU,
    0x0000FFFFU,
    0x1F0FFFFFU,
    0xFFFF3FFFU,
    0x0000FFFFU,
    0x1F0FFFFFU,
    0x07073FFFU,
    0xFFFF0107U,
    0xFFFFFFFFU,
    0x0101010FU,
    0x3FFFFFFFU,
    0xFFFFFFFFU,
    0x0301FFFFU,
    0x00010101U,
    0x03FFFFFFU,
    0x01000000U,
    0x03FF3F07U,
    0x000FFFFFU,
    0x000003FFU,
    0x000FFFFFU,
    0x000003FFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000003FFU,
    0x000FFFFFU,
    0x000003FFU,
    0x000FFFFFU,
    0x000003FFU,
    0x010FFFFFU,
    0x0FFFFF01U,
    0x001F1F01U,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0x00000000U,
    0x1FFFFFFFU,
    0x1F0F1F1FU,
    0x1F0F1F1FU,
    0x1F0F1F1FU,
    0x1F011F01U,
    0x00FFFF01U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x1F1F07FFU,
    0xFF1F1F1FU,
    0x1F1F1F07U,
    0x07FF1F1FU,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x07010101U,
    0x00017F00U,
    0xFFFFFFFFU,
    0x0700FFFFU,
    0xFFFFFF07U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0x000FFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0x000FFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0x010FFFFFU,
    0x00010100U,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x01FFFFFFU,
    0x0000FF00U,
    0x0001FFFFU,
    0x0F01FFFFU,
    0x00000001U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFF0100U,
    0xFFFFFFFFU,
    0x0F0F0003U,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x00013F0FU,
    0x0FFF0FFFU,
    0x0F0F0007U,
    0x000FFF07U,
    0xFFFF0FFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x01010101U,
    0x0101FF01U,
    0x00000107U,
    0xFFFFFFFFU,
    0x00FFFF0FU,
    0x00000303U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x07FFFFFFU,
    0x01FFFF00U,
    0x00000000U,
    0x00030100U,
    0x03FF03FFU,
    0x1F1F03FFU,
    0x000FFFFFU,
    0x03FF03FFU,
    0x1F1F03FFU,
    0x000FFFFFU,
    0x03FF03FFU,
    0x1F1F03FFU,
    0x000FFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x01FFFF01U,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x01FFFF00U,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU,
    0x01FFFFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU,
    0x01FFFFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0101FFFFU,
    0x00000101U,
    0x01010101U,
    0x03010101U,
    0x3F000003U,
    0x00000101U,
    0xFFFFFFFFU,
    0x00000001U,
    0xFFFFFFFFU,
    0x00000007U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x000FFF00U,
    0x1F000000U,
    0x1F1F1F1FU,
    0xFFFF070FU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x000FFF00U,
    0x0FFF0FFFU,
    0x007F0FFFU,
    0x0FFF0FFFU,
    0x0FFF0FFFU,
    0x000FFF7FU,
    0x0FFF0FFFU,
    0x037F0FFFU,
    0x0FFF0000U,
    0x0FFF0FFFU,
    0x03030101U,
    0x03030303U,
    0x0F0F0707U,
    0xFFFFFFFFU,
    0x00FFFF03U,
    0xFFFFFFFFU,
    0x03FFFF03U,
    0x1F011F01U,
    0x0101FFFFU,
    0x01010101U,
    0x03010101U,
    0x0301011FU,
    0x07010F03U,
    0x03030307U,
    0x03011F03U,
    0x01010000U,
    0x01030303U,
    0x00000101U,
    0x00010000U,
    0x00000000U,
    0xFFFFFFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0xFF000000U,
    0x0FFF0F0FU,
    0x0F0FFF0FU,
    0x01010101U,
    0x033F3F3FU,
    0x3F030303U,
    0x1F1F3F3FU,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x0F1F1F1FU,
    0x0F070F07U,
    0x07010107U,
    0xFF000007U,
    0x001FFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x007FFFFFU,
    0xFFFFFFFFU,
    0xFFFF070FU,
    0x00FFFFFFU,
    0x001FFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x007FFFFFU,
    0xFFFFFFFFU,
    0xFFFF070FU,
    0x00FFFFFFU,
    0x001FFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x007FFFFFU,
    0xFFFFFFFFU,
    0xFFFF070FU,
    0xFFFFFFFFU,
    0x000300FFU,
    0x0F0FFFFFU,
    0x0701FF07U,
    0x07070707U,
    0x0F0F0F07U,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0xFFFFFF0FU,
    0x007F7F7FU
};

uint32_t g_lpddr4_pi_rw_mask[] = {
    0x00000F01U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0xFFFF0301U,
    0x030100FFU,
    0x00000101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0000011FU,
    0xFFFFFFFFU,
    0x01030101U,
    0x0F011F03U,
    0x0101070FU,
    0x000FFFFFU,
    0x00000000U,
    0x00000000U,
    0x00000007U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010101U,
    0x3F3F0103U,
    0x0101FFFFU,
    0x01030103U,
    0x0000FF00U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x030F0F1FU,
    0x00000003U,
    0x03FFFFFFU,
    0x00000F07U,
    0x00000103U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0101010FU,
    0x01010101U,
    0x00030301U,
    0x000003FFU,
    0xFFFFFFFFU,
    0x0000FF03U,
    0xFFFFFFFFU,
    0x00FFFF00U,
    0x0F0FFFFFU,
    0x01011F1FU,
    0x03000000U,
    0x030F0101U,
    0x01010101U,
    0x0000FF03U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFF0001U,
    0x1F1F3F1FU,
    0xFF0F0F01U,
    0x017F1FFFU,
    0xFF01FFFFU,
    0x01010101U,
    0x030701FFU,
    0x1F1F0301U,
    0x01030001U,
    0x000000FFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0101FFFFU,
    0x00030001U,
    0xFFFFFFFFU,
    0x00010101U,
    0x010003FFU,
    0x01010101U,
    0x1F070303U,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x0F0F0F0FU,
    0x00000000U,
    0x00000000U,
    0x3FFFFFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x011F3F00U,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x0101011FU,
    0x00FFFF01U,
    0x00000107U,
    0x000101FFU,
    0xFFFFFFFFU,
    0x0000FF01U,
    0xFFFFFFFFU,
    0x0FFF0000U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x00000003U,
    0xFFFFFFFFU,
    0x03030703U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0000003FU,
    0x3FFFFFFFU,
    0x3FFFFFFFU,
    0x3FFFFFFFU,
    0x3FFFFFFFU,
    0x3FFFFFFFU,
    0x3FFFFFFFU,
    0x3FFFFFFFU,
    0x3FFFFFFFU,
    0x0101010FU,
    0x00010101U,
    0x01010101U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFF0101U,
    0x000000FFU,
    0x03FFFFFFU,
    0x00000100U,
    0x0001FFFFU,
    0x01000000U,
    0x01000003U,
    0x00010F07U,
    0x0F00010FU,
    0x010F0001U,
    0x00010F00U,
    0x0F00010FU,
    0x010F0001U,
    0x00000000U,
    0x00000000U,
    0x011F0000U,
    0x01010103U,
    0x01010101U,
    0x01010101U,
    0x01010101U,
    0x01010101U,
    0x00FF0101U,
    0x000001FFU,
    0x0000001FU,
    0x01031F01U,
    0x01010101U,
    0x00FFFF07U,
    0xFFFFFFFFU,
    0x00FFFFFFU,
    0x000000FFU,
    0x000000FFU,
    0x000FFFFFU,
    0x0FFF0FFFU,
    0xFF0F3F7FU,
    0x0F3F7F7FU,
    0x3F7F7FFFU,
    0x007FFF0FU,
    0x000003FFU,
    0x000FFFFFU,
    0x000003FFU,
    0x000FFFFFU,
    0x000003FFU,
    0x0F0FFFFFU,
    0x03030F0FU,
    0x0003FF03U,
    0x03FF03FFU,
    0x01FF01FFU,
    0x0F0F01FFU,
    0x0F0F0F0FU,
    0x3F3F3F3FU,
    0x03033F3FU,
    0x03030303U,
    0x03FFFFFFU,
    0x03030303U,
    0x03030303U,
    0xFF030303U,
    0xFFFFFFFFU,
    0x070707FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x1F030303U,
    0x001F3FFFU,
    0x001F3FFFU,
    0x1F1F3FFFU,
    0x03FF03FFU,
    0x03FF1F1FU,
    0x1F1F03FFU,
    0x03FF03FFU,
    0x7F7F7F7FU,
    0x0F0F7F7FU,
    0xFF1F0F0FU,
    0xFF1F0F1FU,
    0xFF1F0F1FU,
    0xFFFFFF1FU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x3FFFFFFFU,
    0x003F03FFU,
    0x003F03FFU,
    0x030303FFU,
    0x0003FF03U,
    0x7F7F03FFU,
    0x1F03030FU,
    0x03FFFFFFU,
    0x7F7F03FFU,
    0x1F03030FU,
    0x03FFFFFFU,
    0x7F7F03FFU,
    0x1F03030FU,
    0x0303FFFFU,
    0xFFFFFF03U,
    0x00FF3F1FU,
    0x000FFFFFU,
    0x3F0F01FFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFF3F1FFFU,
    0x000FFFFFU,
    0x3F0F01FFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFF3F1FFFU,
    0x000FFFFFU,
    0x3F0F01FFU,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0x001FFFFFU,
    0xFFFFFFFFU,
    0x001FFFFFU,
    0xFFFFFFFFU,
    0x001FFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x3F3FFFFFU,
    0x00FFFF3FU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0000FFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x0000FFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x0000FFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x00FFFFFFU,
    0x0FFFFFFFU,
    0x0FFF0FFFU,
    0x000FFF7FU,
    0x0FFF0FFFU,
    0x000FFF7FU,
    0x0FFF0FFFU,
    0x000FFF7FU,
    0x0FFF0FFFU,
    0x030F0F0FU,
    0x07070303U,
    0x03030303U,
    0x7F7F7F7FU,
    0x00000303U,
    0xFFFF0000U,
    0x00FFFFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU,
    0x01FFFFFFU,
    0x1F1F1FFFU,
    0x1F1F1F1FU,
    0x01FFFF1FU,
    0x0301FFFFU,
    0x00030303U,
    0xFFFFFFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0xFF01FFFFU,
    0xFFFFFFFFU
};

uint32_t g_lpddr4_data_slice_0_rw_mask[] = {
    0x07FF7F07U,
    0x0703FF0FU,
    0x010303FFU,
    0x3F3F3F3FU,
    0x3F3F3F3FU,
    0x1F030F3FU,
    0x030F0F1FU,
    0x01FF031FU,
    0x00000101U,
    0xFFFFFFFFU,
    0x00000000U,
    0x7F0101FFU,
    0x010101FFU,
    0x03FF003FU,
    0x01FF000FU,
    0x01FF0701U,
    0x00000003U,
    0x00000000U,
    0x00000003U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x070F0107U,
    0x0F0F0F0FU,
    0x3F030001U,
    0x0F3FFF0FU,
    0x1F030F3FU,
    0x03FFFFFFU,
    0x00073FFFU,
    0x0F0F07FFU,
    0x000FFFFFU,
    0x000001FFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0001FFFFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x7FFFFFFFU,
    0x0000003FU,
    0x00000000U,
    0x00000000U,
    0x01FF01FFU,
    0x01FF01FFU,
    0x01FF01FFU,
    0x01FF01FFU,
    0x000001FFU,
    0x0003FFFFU,
    0x01FF01FFU,
    0x071F07FFU,
    0x01010101U,
    0x07FFFF07U,
    0x7F03FFFFU,
    0xFF01037FU,
    0x07FF07FFU,
    0x0103FFFFU,
    0x1F1F0F3FU,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x007F1F1FU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x1F0703FFU,
    0xFFFFFFFFU,
    0xFFFFFF0FU,
    0x0FFFFFFFU,
    0x0303FFFFU,
    0x1F1F0103U,
    0x000F1F1FU,
    0xFF3F07FFU,
    0x0FFF0FFFU,
    0x001F0F3FU,
    0x03FF03FFU,
    0x01FF0FFFU,
    0x00000F01U,
    0x000003FFU,
    0x7F7F0703U,
    0x0000001FU,
    0xFFFFFFFFU,
    0x0000000FU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x03FF07FFU,
    0x0003FF03U,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF070FU,
    0x000103FFU,
    0x000F03FFU,
    0x010F07FFU,
    0x000003FFU,
    0x003FFFFFU
};

uint32_t g_lpddr4_data_slice_1_rw_mask[] = {
    0x07FF7F07U,
    0x0703FF0FU,
    0x010303FFU,
    0x3F3F3F3FU,
    0x3F3F3F3FU,
    0x1F030F3FU,
    0x030F0F1FU,
    0x01FF031FU,
    0x00000101U,
    0xFFFFFFFFU,
    0x00000000U,
    0x7F0101FFU,
    0x010101FFU,
    0x03FF003FU,
    0x01FF000FU,
    0x01FF0701U,
    0x00000003U,
    0x00000000U,
    0x00000003U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x070F0107U,
    0x0F0F0F0FU,
    0x3F030001U,
    0x0F3FFF0FU,
    0x1F030F3FU,
    0x03FFFFFFU,
    0x00073FFFU,
    0x0F0F07FFU,
    0x000FFFFFU,
    0x000001FFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x0001FFFFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x7FFFFFFFU,
    0x0000003FU,
    0x00000000U,
    0x00000000U,
    0x01FF01FFU,
    0x01FF01FFU,
    0x01FF01FFU,
    0x01FF01FFU,
    0x000001FFU,
    0x0003FFFFU,
    0x01FF01FFU,
    0x071F07FFU,
    0x01010101U,
    0x07FFFF07U,
    0x7F03FFFFU,
    0xFF01037FU,
    0x07FF07FFU,
    0x0103FFFFU,
    0x1F1F0F3FU,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x007F1F1FU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x1F0703FFU,
    0xFFFFFFFFU,
    0xFFFFFF0FU,
    0x0FFFFFFFU,
    0x0303FFFFU,
    0x1F1F0103U,
    0x000F1F1FU,
    0xFF3F07FFU,
    0x0FFF0FFFU,
    0x001F0F3FU,
    0x03FF03FFU,
    0x01FF0FFFU,
    0x00000F01U,
    0x000003FFU,
    0x7F7F0703U,
    0x0000001FU,
    0xFFFFFFFFU,
    0x0000000FU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x03FF07FFU,
    0x0003FF03U,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF03FFU,
    0x03FF070FU,
    0x000103FFU,
    0x000F03FFU,
    0x010F07FFU,
    0x000003FFU,
    0x003FFFFFU
};

uint32_t g_lpddr4_address_slice_0_rw_mask[] = {
    0x000107FFU,
    0x00000000U,
    0x0F000000U,
    0x00000000U,
    0x01000707U,
    0x011F7F7FU,
    0x01000301U,
    0x07FFFFFFU,
    0x0000003FU,
    0x00000000U,
    0x00000000U,
    0x07FF07FFU,
    0x000007FFU,
    0x00FFFFFFU,
    0x03FFFFFFU,
    0x01FF0F03U,
    0x07000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x3FFFFFFFU,
    0x3F3F03FFU,
    0x3F0F3F3FU,
    0x0000003FU,
    0x0707FFFFU,
    0x1F07FF1FU,
    0x001F07FFU,
    0x001F07FFU,
    0x001F07FFU,
    0x001F07FFU,
    0x000F07FFU,
    0xFF3F07FFU,
    0x0103FFFFU,
    0x0000000FU,
    0x0000010FU
};

uint32_t g_lpddr4_address_slice_1_rw_mask[] = {
    0x000107FFU,
    0x00000000U,
    0x0F000000U,
    0x00000000U,
    0x01000707U,
    0x011F7F7FU,
    0x01000301U,
    0x07FFFFFFU,
    0x0000003FU,
    0x00000000U,
    0x00000000U,
    0x07FF07FFU,
    0x000007FFU,
    0x00FFFFFFU,
    0x03FFFFFFU,
    0x01FF0F03U,
    0x07000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x3FFFFFFFU,
    0x3F3F03FFU,
    0x3F0F3F3FU,
    0x0000003FU,
    0x0707FFFFU,
    0x1F07FF1FU,
    0x001F07FFU,
    0x001F07FFU,
    0x001F07FFU,
    0x001F07FFU,
    0x000F07FFU,
    0xFF3F07FFU,
    0x0103FFFFU,
    0x0000000FU,
    0x0000010FU
};

uint32_t g_lpddr4_address_slice_2_rw_mask[] = {
    0x000107FFU,
    0x00000000U,
    0x0F000000U,
    0x00000000U,
    0x01000707U,
    0x011F7F7FU,
    0x01000301U,
    0x07FFFFFFU,
    0x0000003FU,
    0x00000000U,
    0x00000000U,
    0x07FF07FFU,
    0x000007FFU,
    0x00FFFFFFU,
    0x03FFFFFFU,
    0x01FF0F03U,
    0x07000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x000FFFFFU,
    0x3FFFFFFFU,
    0x3F3F03FFU,
    0x3F0F3F3FU,
    0x0000003FU,
    0x0707FFFFU,
    0x1F07FF1FU,
    0x001F07FFU,
    0x001F07FFU,
    0x001F07FFU,
    0x001F07FFU,
    0x000F07FFU,
    0xFF3F07FFU,
    0x0103FFFFU,
    0x0000000FU,
    0x0000010FU
};

uint32_t g_lpddr4_phy_core_rw_mask[] = {
    0x00000003U,
    0x1F030101U,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x1F1F1F1FU,
    0x001F1F1FU,
    0x011F07FFU,
    0x07FF0100U,
    0x000107FFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0101FF01U,
    0x0007FF03U,
    0x070F07FFU,
    0x01010300U,
    0x0F010001U,
    0x010F0F0FU,
    0x0F0F0F0FU,
    0x00010101U,
    0x010FFFFFU,
    0x00000001U,
    0x00000000U,
    0x0000FFFFU,
    0x00000001U,
    0x0F0F0F0FU,
    0x03030303U,
    0x03030303U,
    0x03030303U,
    0x03030303U,
    0xFFFF1FFFU,
    0x0000FF01U,
    0x00000000U,
    0x00000000U,
    0x0FFF0FFFU,
    0x00000000U,
    0x00000000U,
    0x0FFF0FFFU,
    0xFF0F0101U,
    0x0003FF01U,
    0x0101FFFFU,
    0x0003FFFFU,
    0x0001FFFFU,
    0x0001FFFFU,
    0x0003FFFFU,
    0x0003FFFFU,
    0x0003FFFFU,
    0x0003FFFFU,
    0x0003FFFFU,
    0x0003FFFFU,
    0x0003FFFFU,
    0x1FFF03FFU,
    0x00001FFFU,
    0xFFFFFFFFU,
    0x000007FFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x7F000000U,
    0x01FFFFFFU,
    0x00000000U,
    0x00000000U,
    0x0FFFFFFFU,
    0x000FFFFFU,
    0x01FFFFFFU,
    0x3F7FFFFFU,
    0x3F3F1F3FU,
    0x1F3F3F1FU,
    0x001F3F3FU,
    0x0000FFFFU,
    0x01FF0F03U,
    0x00000F7FU,
    0x00000000U,
    0x003F0101U,
    0x01010000U,
    0x00000001U,
    0xFFFFFFFFU,
    0x03071FFFU,
    0x00030303U,
    0xFFFFFFFFU,
    0x03FFFFFFU,
    0x00FF073FU,
    0x0707FFFFU,
    0x00000000U,
    0x00000000U,
    0x00000003U,
    0x0F1F0101U,
    0x00000000U,
    0x0003FFFFU,
    0x0007FFFFU,
    0x00000001U,
    0x00011FFFU,
    0x0F0F0FFFU,
    0x010103FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x07FF07FFU,
    0x000007FFU,
    0x000007FFU,
    0x000007FFU,
    0x000007FFU,
    0x3FFFFFFFU,
    0x0003FFFFU,
    0x7FFFFFFFU,
    0xFFFFFFFFU,
    0x3FFFFFFFU,
    0x0FFFFFFFU,
    0xFFFFFFFFU,
    0x0007FFFFU,
    0x3FFFFFFFU,
    0x0FFFFFFFU,
    0x3FFFFFFFU,
    0x0FFFFFFFU,
    0x3FFFFFFFU,
    0x0FFFFFFFU,
    0x3FFFFFFFU,
    0x0FFFFFFFU,
    0x3FFFFFFFU,
    0x0FFFFFFFU,
    0x7FFFFF07U
};

/* parasoft-end-suppress MISRA2012-RULE-1_1_a_c99-2 "C99 - limits, DRV-3919" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_a_c90-2 "C90 - limits, DRV-3919" */

