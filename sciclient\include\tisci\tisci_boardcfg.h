/*
 *  Copyright (C) 2017-2024 Texas Instruments Incorporated
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
/**
 * \ingroup TISCI
 * \defgroup tisci_boardcfg tisci_boardcfg
 *
 * DMSC controls the power management, security and resource management
 * of the device.
 *
 *
 * @{
 */
/**
 *
 *  \brief  This file contains:
 *
 *          WARNING!!: Autogenerated file from SYSFW. DO NOT MODIFY!!
 * System Firmware Source File
 *
 * Board Configuration Data Structures
 *
 */

#ifndef TISCI_BOARD_CFG_H
#define TISCI_BOARD_CFG_H

#ifdef __cplusplus
extern "C"
{
#endif




#define TISCI_BOARDCFG_ABI_MAJ_VALUE                  0x00
#define TISCI_BOARDCFG_ABI_MIN_VALUE                  0x01

#define TISCI_BOARDCFG_SEC_ABI_MAJ_VALUE               0x00
#define TISCI_BOARDCFG_SEC_ABI_MIN_VALUE               0x01

/**
 * \brief Contains a unique magic number for each substructure and the size
 *      of the associated superstructure for data validation/API
 *      compatibility checks.
 *
 * \param magic Unique magic number for data integrity check.
 * \param size `sizeof(superstructure containing this header)` for data
 *           integrity check.
 */
struct tisci_boardcfg_substructure_header {
    uint16_t    magic;
    uint16_t    size;
} __attribute__((__packed__));

/**
 * \brief Board Config data ABI version.
 *
 * \param tisci_boardcfg_abi_maj Major Board Config data ABI version.
 * \param tisci_boardcfg_abi_min Minor Board Config data ABI version.
 */
struct tisci_boardcfg_abi_rev {
    uint8_t    tisci_boardcfg_abi_maj;
    uint8_t    tisci_boardcfg_abi_min;
} __attribute__((__packed__));

/**
 * \brief Used to enable/disable features in DMSC based on usecase.
 *
 * \param subhdr Magic and size for integrity check.
 * \param main_isolation_enable Enable/disable support for DMSC main
 *                              isolation. If disabled, main isolation
 *                              SCI message will be rejected with NAK.
 * \param main_isolation_hostid    Host-ID allowed to send SCI-message for
 *                              main isolation. If mismatch, SCI message
 *                              will be rejected with NAK.
 */
struct tisci_boardcfg_control {
    struct tisci_boardcfg_substructure_header    subhdr;
    uint8_t                    main_isolation_enable;
    uint16_t                    main_isolation_hostid;
} __attribute__((__packed__));

/**
 * \brief Secure proxy configuration.
 *
 * \param subhdr Magic and size for integrity check.
 * \param scaling_factor Memory allocation for messages scaling factor. In
 *             current design, only value of "1" is supported. For
 *             future design, a value of "2" would double all memory
 *             allocations and credits, "3" would triple, and so on.
 * \param scaling_profile Memory allocation for messages profile number. In
 *              current design, only a value of "1" is supported.
 *              "0" is always invalid due to fault tolerance.
 * \param disable_main_nav_secure_proxy Do not configure main nav secure proxy.
 *                    This removes all MSMC memory demands
 *                    from DMSC but limits MPU channels to
 *                    one set of secure and one set of
 *                    insecure. Note this parameter is currently
 *                    not used. Hence the value of '0' is used.
 */
struct tisci_boardcfg_secproxy {
    struct tisci_boardcfg_substructure_header    subhdr;
    uint8_t                    scaling_factor;
    uint8_t                    scaling_profile;
    uint8_t                    disable_main_nav_secure_proxy;
} __attribute__((__packed__));

/**
 * \brief Cache configuration so that MSMC can be used for main secure proxy
 *      backing memory and ring memory.
 *
 * \param subhdr Magic and size for integrity check.
 * \param msmc_cache_size Fraction of msmc to be cache in /32 units. Rounds
 *              up. Since current msmc support /8 allocation this
 *              means that 1/32 rounds up to 1/8.
 */
struct tisci_boardcfg_msmc {
    struct tisci_boardcfg_substructure_header    subhdr;
    uint8_t                    msmc_cache_size;
} __attribute__((__packed__));

/* \brief How many masters that are permitted in proc acl for access */
#define PROCESSOR_ACL_SECONDARY_MASTERS_MAX (3U)

/**
 * \brief A single entry of Processor Access Control List
 *
 * \param processor_id - What processor ID are we restricting control over?
 * \param proc_access_master - Who is the master who can override control
 * \param proc_access_secondary - list of upto @ref PROCESSOR_ACL_SECONDARY_MASTERS_MAX
 */
struct tisci_boardcfg_proc_acl_entry {
    uint8_t    processor_id;
    uint8_t    proc_access_master;
    uint8_t    proc_access_secondary[PROCESSOR_ACL_SECONDARY_MASTERS_MAX];
} __attribute__((__packed__));

/* \brief How many access control list entries for processors */
#define PROCESSOR_ACL_ENTRIES (32U)

/**
 * \brief Control list for which hosts can control which processors
 * \param subhdr - Magic and size for integrity check
 * \param proc_acl_entries - @ref tisci_boardcfg_proc_acl_entry entries upto @ref PROCESSOR_ACL_ENTRIES entries
 */
struct tisci_boardcfg_proc_acl {
    struct tisci_boardcfg_substructure_header    subhdr;
    struct tisci_boardcfg_proc_acl_entry        proc_acl_entries[
        PROCESSOR_ACL_ENTRIES];
} __attribute__((__packed__));

/**
 * \brief A single entry of Host hierarchy List
 *
 * \param host_id - Processing entity Host ID whose supervisor is specified
 * \param supervisor_host_id - Processing entity Host ID that is the supervisor
 *                             of the host_id
 */
struct tisci_boardcfg_host_hierarchy_entry {
    uint8_t    host_id;
    uint8_t    supervisor_host_id;
} __attribute__((__packed__));

/** \brief How many host hierarchy list entries for hosts */
#define HOST_HIERARCHY_ENTRIES (32U)

/**
 * \brief List of SoC hosts and their supervising hosts
 * \param subhdr - Magic and size for integrity check
 * \param host_hierarchy_entries - @ref tisci_boardcfg_host_hierarchy_entry entries
 *                                 upto @ref HOST_HIERARCHY_ENTRIES entries
 */
struct tisci_boardcfg_host_hierarchy {
    struct tisci_boardcfg_substructure_header    subhdr;
    struct tisci_boardcfg_host_hierarchy_entry    host_hierarchy_entries[HOST_HIERARCHY_ENTRIES];
} __attribute__((__packed__));

/**
 * \brief access configuration for one OTP MMR. Each MMR is 32 bit wide.
 *
 * \param host_id Id of the host owning the MMR
 *
 * \param host_perms 2 bit wide fields specifying permissions
 *                   bit 1:0 - 10b - non-secure, any other value secure
 *                   bit 7:2 - Reserved for future use
 */
struct tisci_boardcfg_extended_otp_entry {
    uint8_t    host_id;
    uint8_t    host_perms;
} __attribute__((__packed__));

/**
 * \brief Maximum number of OTP rows allowed by design
 */
#define MAX_NUM_EXT_OTP_MMRS (32U)

/**
 * \brief Access configuration for each OTP row
 * \param subhdr Magic and size for integrity check
 * \param otp_entry access configurations for each OTP MMR
 * \param write_host_id ID of the host allowed to perform OTP write/lock operations.
 */
struct tisci_boardcfg_extended_otp {
    struct tisci_boardcfg_substructure_header    subhdr;
    struct tisci_boardcfg_extended_otp_entry    otp_entry[MAX_NUM_EXT_OTP_MMRS];
    uint8_t                    write_host_id;
} __attribute__((__packed__));


#define MAX_NUM_DKEK_ALLOWED_HOSTS (4U)

/**
 * \brief Access configuration for DKEK
 * \param subhdr Magic and size for integrity check
 * \param allowed_hosts ID's of hosts allowed to use DKEK. Use TISCI_HOST_ID_ALL if any host is allowed
 * \param allow_dkek_export_tisci If DKEK can be exported via TISCI interface to hosts. Set to 0x5A to allow.
 * \param rsvd Reserved field for future use. Set to 0.
 */
struct tisci_boardcfg_dkek {
    struct tisci_boardcfg_substructure_header    subhdr;
    uint8_t                    allowed_hosts[MAX_NUM_DKEK_ALLOWED_HOSTS];
    uint8_t                    allow_dkek_export_tisci;
    uint8_t                    rsvd[3];
} __attribute__((__packed__));

/**
 * \brief Configuration of SA2UL resources
 *
 * \param subhdr Magic and size for integrity check
 * \param auth_resource_owner ID of the host allowed to acquire/release the
 *                            authentication resources
 * \param enable_saul_psil_global_config_writes Flag for allowing pairing requests
 *                                              from PSIL. Set to 0x5A to enable
 * \param rsvd Reserved
 */
struct tisci_boardcfg_sa2ul_cfg {
    struct tisci_boardcfg_substructure_header    subhdr;
    uint8_t                    auth_resource_owner;
    uint8_t                    enable_saul_psil_global_config_writes;
    uint8_t                    safety_host_present;
    uint8_t                    safety_host;
};


/**
 * \brief Secure debug control
 *
 * \param subhdr Magic and size for integrity check
 *
 * \param allow_jtag_unlock Flag controlling runtime jtag unlock feature. Set to
 *                          0x5A to enable jtag unlock with a signed certificate.
 *
 * \param allow_wildcard_unlock Flag controlling whether a device unique
 *                              certificate is required for jtag unlock. Set to 0x5A to remove the device
 *                              uniqueness restriction on the certificate.
 *
 * \param allowed_debug_level_rsvd Reserved field to control the allowed debug level in
 *                                 future. Set to 0 currently.
 *
 *  \param rsvd reserved for future use.
 *
 * \param min_cert_rev Minimum SWREV value that must be present in the jtag
 *                     unlock certificate. Set to 0 to skip check
 *
 * \param jtag_unlock_hosts array of host ids that are allowed to unlock jtag
 *                          with a signed certificate at runtime. Set host id to 0 if unused. Set host id
 *                          to 128 if any host can unlock JTAG via the API.
 */
struct tisci_boardcfg_secure_debug_config {
    struct tisci_boardcfg_substructure_header    subhdr;
    uint8_t                    allow_jtag_unlock;
    uint8_t                    allow_wildcard_unlock;
    uint8_t                    allowed_debug_level_rsvd;
    uint8_t                    rsvd;
    uint32_t                    min_cert_rev;
    uint8_t                    jtag_unlock_hosts[TISCI_BOARDCFG_SEC_MAX_NUM_JTAG_UNLOCK_HOSTS];
} __attribute__((__packed__));

/**
 * \brief Configuration of security handover
 *
 * Only applicable to certain devices
 *
 * \param subhdr Magic and size for integrity check
 * \param handover_msg_sender Host which will send the security handover message
 * \param handover_to_host_id Host to which security functionality is handed over
 * \param rsvd Reserved
 */
struct tisci_boardcfg_sec_handover {
    struct tisci_boardcfg_substructure_header    subhdr;
    uint8_t                    handover_msg_sender;
    uint8_t                    handover_to_host_id;
    uint8_t                    rsvd[4];
};

/**
 * \brief Format of the complete board configuration.
 *
 * \param tisci_boardcfg_abi_rev Secure Board Config ABI version (separate from DMSC ABI version)
 * \param tisci_boardcfg_proc_acl Processor Access control list
 * \param tisci_boardcfg_host_hierarchy Host hierarchy list
 * \param otp_config  OTP Configuration
 * \param dkek_config  DKEK Configuration
 * \param sec_dbg_config  Secure JTAG Unlock Configuration
 * \param tisci_boardcfg_sa2ul_cfg SA2UL resource configuration
 * \param sec_handover_cfg Security handover configuration
 */
struct tisci_boardcfg_sec {
    struct tisci_boardcfg_abi_rev            rev;
    struct tisci_boardcfg_proc_acl        processor_acl_list;
    struct tisci_boardcfg_host_hierarchy        host_hierarchy;
    struct tisci_boardcfg_extended_otp        otp_config;
    struct tisci_boardcfg_dkek            dkek_config;
    struct tisci_boardcfg_sa2ul_cfg        sa2ul_auth_cfg;
    struct tisci_boardcfg_secure_debug_config    sec_dbg_config;
    struct tisci_boardcfg_sec_handover        sec_handover_cfg;
} __attribute__((__packed__));

/**
 * \def TISCI_BOARDCFG_TRACE_DST_UART0
 * Traces to UART0 in wakeupss enabled.
 *
 * \def TISCI_BOARDCFG_TRACE_DST_ITM
 * Traces to UART attached to ITM(JTAG) enabled.
 *
 * \def TISCI_BOARDCFG_TRACE_DST_MEM
 * Traces to memory buffer enabled.
 */
#define TISCI_BOARDCFG_TRACE_DST_UART0                TISCI_BIT(0)
#define TISCI_BOARDCFG_TRACE_DST_ITM                  TISCI_BIT(2)
#define TISCI_BOARDCFG_TRACE_DST_MEM                  TISCI_BIT(3)

/**
 * \def TISCI_BOARDCFG_TRACE_SRC_PM
 * Traces from power management are allowed.
 *
 * \def TISCI_BOARDCFG_TRACE_SRC_RM
 * Traces from resource management are allowed.
 *
 * \def TISCI_BOARDCFG_TRACE_SRC_SEC
 * Traces from security management are allowed.
 *
 * \def TISCI_BOARDCFG_TRACE_SRC_BASE
 * Traces from baseport are allowed.
 *
 * \def TISCI_BOARDCFG_TRACE_SRC_USER
 * Traces from user tasks are allowed.
 *
 * \def TISCI_BOARDCFG_TRACE_SRC_SUPR
 * Traces from supervisor tasks are allowed.
 */
#define TISCI_BOARDCFG_TRACE_SRC_PM                   TISCI_BIT(0)
#define TISCI_BOARDCFG_TRACE_SRC_RM                   TISCI_BIT(1)
#define TISCI_BOARDCFG_TRACE_SRC_SEC                  TISCI_BIT(2)
#define TISCI_BOARDCFG_TRACE_SRC_BASE                 TISCI_BIT(3)
#define TISCI_BOARDCFG_TRACE_SRC_USER                 TISCI_BIT(4)
#define TISCI_BOARDCFG_TRACE_SRC_SUPR                 TISCI_BIT(5)

/**
 * \brief Debug console configuration.
 *
 * \param subhdr Magic and size for integrity check.
 * \param trace_dst_enables;
 * \param trace_src_enables;
 */
struct tisci_boardcfg_dbg_cfg {
    struct tisci_boardcfg_substructure_header    subhdr;
    uint16_t                    trace_dst_enables;
    uint16_t                    trace_src_enables;
} __attribute__((__packed__));

/**
 * \brief Format of the complete board configuration.
 *
 * \param tisci_boardcfg_abi_rev Board Config ABI version (separate from DMSC ABI version)
 * \param control DMSC feature control selections
 * \param secproxy Secure proxy configuration
 * \param msmc MSMC configuration
 * \param debug_cfg Debug/trace configuration
 */
struct tisci_boardcfg {
    struct tisci_boardcfg_abi_rev        rev;
    struct tisci_boardcfg_control        control;
    struct tisci_boardcfg_secproxy    secproxy;
    struct tisci_boardcfg_msmc        msmc;
    struct tisci_boardcfg_dbg_cfg        debug_cfg;
} __attribute__((__packed__));


/**
 * \brief structure to hold the board configuration hashes received via X509 certificate
 *
 * \param sec_bcfg_hash Hash of encrypted security board configuration blob
 * \param rm_bcfg_hash Hash of RM board configuration blob
 * \param pm_bcfg_hash Hash of PM board configuration blob
 * \param core_bcfg_hash Hash of main board configuration blob
 * \param sec_bcfg_iv initial vector used during security board configuration encryption
 * \param sec_bcfg_rs byte string used to verify that security board configuration is
 *                    decrypted correctly.
 * \param sec_bcfg_ver version of the security board configuration
 * \param sec_bcfg_num_iter Number of iterations for the KDF
 * \param hashes_received Flag indicating whether SYSFW outer certificate contained tisci_boardcfg
 *                        information
 */
struct tisci_boardcfg_hashes_data {
    uint8_t    sec_bcfg_hash[TISCI_BOARDCFG_HASH_LEN_BYTES];
    uint8_t    rm_bcfg_hash[TISCI_BOARDCFG_HASH_LEN_BYTES];
    uint8_t    pm_bcfg_hash[TISCI_BOARDCFG_HASH_LEN_BYTES];
    uint8_t    core_bcfg_hash[TISCI_BOARDCFG_HASH_LEN_BYTES];
    uint8_t    sec_bcfg_iv[TISCI_BOARDCFG_SEC_IV_LEN];
    uint8_t    sec_bcfg_rs[TISCI_BOARDCFG_SEC_RS_LEN];
    uint8_t    sec_bcfg_ver;
    uint8_t    sec_bcfg_num_iter;
    uint8_t    hashes_received;
};

#ifdef __cplusplus
}
#endif

#endif          /* TISCI_BOARD_CFG_H */

/** @} */
